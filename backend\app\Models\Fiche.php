<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Fiche extends Model
{
    use HasFactory;

    protected $fillable = [
        'fiche_transmission_id',
        'code_laboratoire',
        'nature_echantillon',
        'masse_echantillon',
        'date_transmission',
        'date_remise_resultats',
        'analyses_demandees',
        'observations',
        'sample_id',
    ];
    protected $table = 'fiches';

    protected $casts = [
        'analyses_demandees' => 'array',
    ];

    // Method to calculate result date excluding weekends
    private function calculateResultsDate(Carbon $startDate, int $days)
    {
        $currentDate = clone $startDate;
        $count = 0;

        while ($count < $days) {
            $currentDate->addDay();
            if (!$currentDate->isWeekend()) {
                $count++;
            }
        }

        return $currentDate;
    }
    public function sample()
    {
        return $this->belongsTo(Sample::class);
    }
    public function setResultDate()
    {
        $this->date_remise_resultats = $this->calculateResultsDate(Carbon::parse($this->date_transmission), 5); // Example: 5 business days
        $this->save();
    }
}
