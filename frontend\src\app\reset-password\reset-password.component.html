<div class="login-container">
    <h1 class="title">Mot de passe oublié</h1>
  
    <!-- Display a global error message if needed -->
    <div *ngIf="errorMessage" class="error">
      {{ errorMessage }}
    </div>
  
    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()">
      <div class="input-group">
       <div class="description">
        veuillez entrer votre adresse email et nous vous enverrons des instructions sur la façon de réinitialiser votre mot de passe 
       </div>
        <div class="field-group">
          <label for="email" class="input-label-form">
            Email:
            <span
              *ngIf="
                forgotPasswordForm.get('email')?.touched &&
                forgotPasswordForm.get('email')?.errors
              "
              class="error"
            >
              <span *ngIf="forgotPasswordForm.get('email')?.errors?.['required']">
                L'email est requis.
              </span>
              <span *ngIf="forgotPasswordForm.get('email')?.errors?.['email']">
                Format d'email invalide.
              </span>
            </span>
          </label>
          <input
            id="email"
            type="email"
            formControlName="email"
            class="input-field-form"
          >
        </div>
      </div>
  
    
      
  
      <button
        type="submit"
        class="btn-primary"
        aria-label="Se connecter"
      >
      Envoyer
      </button>
      <div class="login-link">
        Vous avez un compte ?
        <a (click)="navigateTo('/signup')" class="btn-singup">se connecter.</a>
      </div>
      
    </form>
  </div>
  