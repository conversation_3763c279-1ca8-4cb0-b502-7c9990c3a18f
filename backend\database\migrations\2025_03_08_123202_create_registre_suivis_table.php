<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up() {
        Schema::create('registre_suivis', function (Blueprint $table) {
            $table->id();
            $table->string('demande_id'); // Keep it as a string since it's not an integer
            $table->string('nom_client');
            $table->date('date_reception');
            $table->timestamps();
        
            // Explicitly define the foreign key reference
            $table->foreign('demande_id')->references('demande_id')->on('demandes')->onDelete('cascade');
        });
        
    }

    public function down() {
        Schema::dropIfExists('registre_suivis');
    }
};
