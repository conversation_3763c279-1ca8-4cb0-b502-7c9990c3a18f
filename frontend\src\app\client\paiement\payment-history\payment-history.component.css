@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* =========================
   1. Global Container
   ========================= */
.payment-history-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  margin-top: 40px;
  margin-bottom: 40px;
}

/* Main content and loading overlay */
.main-content {
  position: relative;
  transition: opacity 0.3s ease-in-out;
}

.content-hidden {
  opacity: 0.6;
  pointer-events: none;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* =========================
   2. Header Actions & Titre principal
   ========================= */
.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  position: relative;
}

.title-print-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 26px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  border-bottom: 4px solid #2496d3;
  text-shadow: none;
}

h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(36, 150, 211, 0.1);
  border-radius: 50%;
  border-top-color: #2496d3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes glowText {
  from {
    text-shadow: 0 0 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0 0 20px rgba(36, 150, 211, 0.8);
  }
}

/* Error message */
.error {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
  background-color: #ffebee;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  text-align: center;
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
}

/* No payments message */
.no-payments {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
  font-size: 16px;
  margin: 20px 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #dee2e6;
}

/* Payment table */
.payment-table-container {
  overflow-x: auto;
  margin-top: 20px;
}

.payment-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.2);
}

.payment-table th {
  background-color: #2496d3;
  color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 15px;
  text-align: center;
}

.payment-table td {
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  padding: 14px;
  border-bottom: 1px solid #ddd;
  vertical-align: middle;
  text-align: center;
}

/* Lignes alternées */
.payment-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* Survol */
.payment-table tbody tr:hover {
  background-color: rgba(36, 150, 211, 0.05);
  transition: background 0.3s ease;
}

/* Preview row styles */
.preview-row {
  background-color: #f5f5f5;
}

.preview-cell {
  padding: 0 !important;
}

.preview-container {
  padding: 20px;
  border-top: 1px dashed #ddd;
}

/* Payment Status */
.payment-status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

/* Status tags (similar to demande-details) */
.status-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  text-transform: uppercase;
  margin-left: 10px;
}

.status-tag.pending {
  background-color: #fff3cd; /* Jaune pâle */
  color: #856404;
}

.status-tag.valid, .status-tag.approved {
  background-color: #d4edda; /* Vert pâle */
  color: #155724;
}

.status-tag.rejected {
  background-color: #f8d7da; /* Rouge pâle */
  color: #721c24;
}

/* Action buttons */
.actions-cell {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.btn-action {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #555;
}

.btn-action.view {
  background-color: #e3f2fd;
  color: #1976d2;
}

.btn-action.view:hover, .btn-action.view.active {
  background-color: #1976d2;
  color: white;
}

.btn-action.download {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.btn-action.download:hover {
  background-color: #2e7d32;
  color: white;
}

.btn-action.delete {
  background-color: #ffebee;
  color: #c62828;
}

.btn-action.delete:hover {
  background-color: #c62828;
  color: white;
}

/* Preview content styles */
.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-preview {
  width: 100%;

  margin: 0 auto;
  text-align: center;
}

.file-preview h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.image-container {
  margin-bottom: 15px;
  max-width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 600px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

.file-name {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.pdf-preview, .unknown-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.pdf-icon, .file-icon {
  margin-bottom: 15px;
  color: #f44336;
}

.file-icon {
  color: #607d8b;
}

.btn-open-pdf, .btn-download, .btn-open-direct {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 15px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
}


.btn-retry {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
}

.btn-retry:hover {
  background-color: #d32f2f;
}

/* Notification overlay */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.notification {
  background: white;
  color: #155724;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.notification-icon {
  font-size: 50px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.notification-message {
  font-size: 18px;
  font-weight: 500;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Direct image preview styles */
.direct-image-preview {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.direct-image-preview img {
  max-width: 100%;
  max-height: 600px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  object-fit: contain;
}

.direct-image-preview .btn-download {
  margin-top: 15px;
  background-color: #2e7d32;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
}

.direct-image-preview .btn-download:hover {
  background-color: #1b5e20;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .payment-history-container {
    padding: 30px;
  }

  .payment-table th,
  .payment-table td {
    padding: 8px 10px;
    font-size: 14px;
  }

  .payment-status {
    padding: 3px 8px;
    font-size: 12px;
  }

  h2 {
    font-size: 22px;
  }
}
