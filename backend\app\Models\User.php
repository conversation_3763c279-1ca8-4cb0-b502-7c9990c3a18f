<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Notifications\ResetPasswordNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use Lara<PERSON>\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Contracts\Auth\CanResetPassword;



class User extends Authenticatable implements CanResetPassword
{
    use HasApiTokens,Notifiable;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'fax',
        'nickname',
        'adress',
        'role'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime:Y-m-d H:i:s',
        ];
    }
    public function sendPasswordResetNotification($token)
    {
        // Use your custom notification
        $this->notify(new ResetPasswordNotification($token));
    }
/*************  ✨ Codeium Command ⭐  *************/
    /**
     * Get the samples associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
/******  dde92c69-a062-4869-a0a4-716fa8966ab5  *******/
    public function demandes()
    {
        return $this->hasMany(Demande::class);
    }

    /**
     * Get the payments associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }
}
