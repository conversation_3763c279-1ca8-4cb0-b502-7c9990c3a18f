<div class="analyst-dashboard">
  <!-- Titre avec Icône -->
  <div class="dashboard-title-container">
    <div class="circle-icon"><i class="fas fa-flask"></i></div>
    <h2 class="dashboard-title">Tableau de bord de l'Analyste</h2>
  </div>

  <!-- Informations Utilisateur -->
  <div class="user-info">
    <p>Bienvenue <strong>{{ name }} {{nickname}}</strong> </p>
  </div>

  <!-- Section Statistiques -->
  <div class="statistics">
    <div class="statistic-card">
      <span class="statistic-icon"><i class="fas fa-envelope"></i></span>
      <h3 class="statistic-title">Fiches de transmission en attente de résultats</h3>
      <p class="statistic-value" *ngIf="loadingFicheStats">
        <i class="fas fa-spinner fa-spin"></i>
      </p>
      <p class="statistic-value" *ngIf="!loadingFicheStats">
        {{ ficheWithoutResultsCount }}
      </p>
    </div>
  </div>

  <!-- Boutons d'Action -->
  <div class="analyst-actions">
    <button class="btn-analyst transmission" (click)="openFicheTransmission()">
      <i class="fas fa-envelope"></i> Fiches de transmission
    </button>
    <button class="btn-analyst results" (click)="openResults()">
      <i class="fas fa-file-excel"></i> Gestion des résultats
    </button>
  </div>
</div>
