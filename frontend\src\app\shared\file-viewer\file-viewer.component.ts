import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { SafeResourceUrl, DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-file-viewer',
  templateUrl: './file-viewer.component.html',
  styleUrls: ['./file-viewer.component.css']
})
export class FileViewerComponent implements OnChanges {
  @Input() fileUrl: string = '';
  @Input() fileName: string = '';
  @Input() fileType: 'pdf' | 'image' | 'unknown' = 'unknown';
  @Input() isVisible: boolean = false;
  @Output() close = new EventEmitter<void>();

  safePdfUrl: string | null = null;
  safeImageUrl: SafeResourceUrl | null = null;
  isLoading: boolean = true;
  error: string | null = null;

  constructor(private readonly sanitizer: DomSanitizer) {}

  ngOnChanges(changes: SimpleChanges): void {
    console.log('FileViewerComponent.ngOnChanges', changes);

    if (changes['isVisible'] && this.isVisible) {
      console.log('Modal opened, fileUrl:', this.fileUrl);
      // Reset state when modal is opened
      this.isLoading = true;
      this.error = null;

      // Load file when modal is opened and we have a URL
      if (this.fileUrl) {
        this.loadFile();
      }
    }

    // Also load file if URL changes while visible
    if (changes['fileUrl'] && this.fileUrl && this.isVisible) {
      console.log('File URL changed while visible:', this.fileUrl);
      this.loadFile();
    }
  }

  loadFile(): void {
    console.log('Loading file:', this.fileUrl);
    this.isLoading = true;
    this.error = null;

    if (!this.fileUrl) {
      console.error('No file URL provided');
      this.error = 'Aucune URL de fichier fournie.';
      this.isLoading = false;
      return;
    }

    try {
      // Determine file type if not provided
      if (this.fileType === 'unknown') {
        if (this.fileName.toLowerCase().endsWith('.pdf')) {
          this.fileType = 'pdf';
        } else if (
          this.fileName.toLowerCase().endsWith('.jpg') ||
          this.fileName.toLowerCase().endsWith('.jpeg') ||
          this.fileName.toLowerCase().endsWith('.png') ||
          this.fileName.toLowerCase().endsWith('.gif') ||
          this.fileName.toLowerCase().endsWith('.bmp')
        ) {
          this.fileType = 'image';
        } else {
          // Default to PDF if we can't determine
          this.fileType = 'pdf';
        }
      }

      console.log('File type determined:', this.fileType);

      // Sanitize URL for security
      // For both PDF and images, we'll use the sanitized URL
      this.safeImageUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.fileUrl);
      console.log('URL sanitized:', this.fileUrl);

      // Create a test image to check if the URL is valid
      if (this.fileType === 'image') {
        const img = new Image();
        img.onload = () => {
          console.log('Image loaded successfully');
          this.isLoading = false;
        };
        img.onerror = () => {
          console.error('Error loading image');
          this.error = 'Impossible de charger l\'image. Veuillez vérifier l\'URL.';
          this.isLoading = false;
        };
        img.src = this.fileUrl;
      } else {
        // For PDFs, we'll just set loading to false after a short delay
        setTimeout(() => {
          console.log('PDF loading timeout completed');
          this.isLoading = false;
        }, 1000);
      }
    } catch (error) {
      console.error('Error loading file:', error);
      this.error = 'Impossible de charger le fichier. Veuillez réessayer.';
      this.isLoading = false;
    }
  }

  closeViewer(): void {
    this.close.emit();
  }

  downloadFile(): void {
    if (!this.fileUrl) return;

    const a = document.createElement('a');
    a.href = this.fileUrl;
    a.download = this.fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
}
