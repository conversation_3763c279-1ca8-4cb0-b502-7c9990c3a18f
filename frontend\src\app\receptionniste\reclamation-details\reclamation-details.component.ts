import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faUser,
  faClipboard,
  faExternalLink,
  faEye,
  faDownload,
  faFilePdf,
  faFileImage,
  faFile,
  faExchangeAlt,
  faCircleDot,
  faSpinner,
  faCheckCircle,
  faReply,
  faPaperPlane,
  faComments,
  faArrowLeft,
  faTimes
} from '@fortawesome/free-solid-svg-icons';

// Define interfaces for type safety
interface ReclamationResponse {
  id: string;
  author: string;
  date: string;
  content: string;
}

interface Reclamation {
  id: string;
  date: string;
  clientName: string;
  clientNickname: string;
  clientEmail?: string;
  clientPhone?: string;
  subject: string;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved';
  demandeId?: string;
  attachmentUrl?: string;
  responses?: ReclamationResponse[];
}

@Component({
  selector: 'app-reclamation-details',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FontAwesomeModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './reclamation-details.component.html',
  styleUrl: './reclamation-details.component.css'
})
export class ReclamationDetailsComponent implements OnInit {
  // Font Awesome icons
  faUser = faUser;
  faClipboard = faClipboard;
  faExternalLink = faExternalLink;
  faEye = faEye;
  faDownload = faDownload;
  faFilePdf = faFilePdf;
  faFileImage = faFileImage;
  faFile = faFile;
  faExchangeAlt = faExchangeAlt;
  faCircleDot = faCircleDot;
  faSpinner = faSpinner;
  faCheckCircle = faCheckCircle;
  faReply = faReply;
  faPaperPlane = faPaperPlane;
  faComments = faComments;
  faArrowLeft = faArrowLeft;
  faTimes = faTimes;

  // Component state
  isLoading = true;
  errorMessage: string | null = null;
  showSuccessMessage = false;
  successMessage = '';
  reclamation: Reclamation | null = null;
  reclamationId: string | null = null;

  // Form state
  responseForm!: FormGroup;
  submitted = false;
  isSubmitting = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    // Initialize form
    this.responseForm = this.fb.group({
      response: ['', Validators.required]
    });

    // Get reclamation ID from route params
    this.route.paramMap.subscribe(params => {
      this.reclamationId = params.get('id');
      if (this.reclamationId) {
        this.loadReclamationDetails(this.reclamationId);
      } else {
        this.errorMessage = 'ID de réclamation non trouvé.';
        this.isLoading = false;
      }
    });
  }

  // Getter for easy access to form fields
  get f() { return this.responseForm.controls; }

  // Load reclamation details (mock data for static implementation)
  loadReclamationDetails(id: string): void {
    // Simulate API call
    setTimeout(() => {
      if (id === 'REC-001') {
        this.reclamation = {
          id: 'REC-001',
          date: '2023-06-15',
          clientName: 'Ahmed',
          clientNickname: 'Ben Ali',
          clientEmail: '<EMAIL>',
          clientPhone: '21234567',
          subject: 'Résultats incorrects',
          description: 'Les résultats d\'analyse reçus ne correspondent pas à ma demande initiale. J\'ai demandé une analyse complète de l\'échantillon mais je n\'ai reçu que des résultats partiels.',
          status: 'pending',
          attachmentUrl: 'resultats_incomplets.pdf'
        };
      } else if (id === 'REC-002') {
        this.reclamation = {
          id: 'REC-002',
          date: '2023-06-20',
          clientName: 'Sami',
          clientNickname: 'Trabelsi',
          clientEmail: '<EMAIL>',
          subject: 'Délai de traitement trop long',
          description: 'Ma demande d\'analyse prend beaucoup plus de temps que prévu. J\'ai été informé que les résultats seraient disponibles dans une semaine, mais cela fait maintenant trois semaines et je n\'ai toujours pas reçu les résultats.',
          status: 'in_progress',
          demandeId: 'DEM-2023-456',
          responses: [
            {
              id: 'RESP-001',
              author: 'Leila Mansour (Réceptionniste)',
              date: '2023-06-22T10:30:00',
              content: 'Bonjour M. Trabelsi, nous avons bien reçu votre réclamation. Nous sommes en train de vérifier le statut de votre demande d\'analyse et nous reviendrons vers vous dans les plus brefs délais.'
            }
          ]
        };
      } else if (id === 'REC-003') {
        this.reclamation = {
          id: 'REC-003',
          date: '2023-07-05',
          clientName: 'Client',
          clientNickname: 'Client',
          clientEmail: '<EMAIL>',
          clientPhone: '22222222',
          subject: 'Erreur de facturation',
          description: 'Le montant facturé ne correspond pas au devis initial. Le devis indiquait un montant de 450 DT, mais la facture que j\'ai reçue est de 650 DT.',
          status: 'resolved',
          demandeId: '0001-101',
          responses: [
            {
              id: 'RESP-002',
              author: '(Réceptionniste)',
              date: '2023-07-06T14:15:00',
              content: 'Bonjour Mme Client Client, nous avons vérifié votre facture et effectivement il y a eu une erreur. Une facture corrigée vous sera envoyée dans les 24 heures.'
            },
            {
              id: 'RESP-003',
              author: '(Réceptionniste)',
              date: '2023-07-07T09:45:00',
              content: 'Bonjour Mme Client Client, la facture corrigée vient de vous être envoyée par email. Veuillez nous excuser pour ce désagrément.'
            }
          ]
        };
      } else {
        this.errorMessage = 'Réclamation non trouvée.';
      }

      this.isLoading = false;
    }, 1000);
  }

  // Get status class for styling
  getStatusClass(status: string): string {
    switch (status) {
      case 'pending': return 'pending';
      case 'in_progress': return 'in-progress';
      case 'resolved': return 'resolved';
      default: return '';
    }
  }

  // Get status icon
  getStatusIcon(status: string): any {
    switch (status) {
      case 'pending': return this.faCircleDot;
      case 'in_progress': return this.faSpinner;
      case 'resolved': return this.faCheckCircle;
      default: return this.faCircleDot;
    }
  }

  // Get status label
  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending': return 'En attente';
      case 'in_progress': return 'En cours';
      case 'resolved': return 'Résolu';
      default: return 'Inconnu';
    }
  }

  // Get file icon based on file extension
  getFileIcon(fileUrl: string): any {
    if (fileUrl.endsWith('.pdf')) {
      return this.faFilePdf;
    } else if (fileUrl.endsWith('.jpg') || fileUrl.endsWith('.jpeg') || fileUrl.endsWith('.png')) {
      return this.faFileImage;
    } else {
      return this.faFile;
    }
  }

  // Get file name from URL
  getFileName(fileUrl: string): string {
    return fileUrl.split('/').pop() || fileUrl;
  }

  // View demande details
  viewDemande(demandeId: string): void {
    this.router.navigate(['/demande', demandeId]);
  }

  // Change reclamation status
  changeStatus(status: 'pending' | 'in_progress' | 'resolved'): void {
    if (!this.reclamation) return;

    this.isSubmitting = true;

    // Simulate API call
    setTimeout(() => {
      if (this.reclamation) {
        this.reclamation.status = status;
        this.showSuccessMessage = true;
        this.successMessage = `Le statut de la réclamation a été mis à jour avec succès.`;
      }
      this.isSubmitting = false;
    }, 1000);
  }

  // Submit response
  submitResponse(): void {
    this.submitted = true;

    // Stop if form is invalid
    if (this.responseForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    // Get form value
    const responseText = this.responseForm.value.response;

    // Simulate API call
    setTimeout(() => {
      if (this.reclamation) {
        // Create new response
        const newResponse: ReclamationResponse = {
          id: `RESP-${Date.now()}`,
          author: 'Vous (Réceptionniste)',
          date: new Date().toISOString(),
          content: responseText
        };

        // Add to responses array
        if (!this.reclamation.responses) {
          this.reclamation.responses = [];
        }
        this.reclamation.responses.push(newResponse);

        // Update status to in_progress if it was pending
        if (this.reclamation.status === 'pending') {
          this.reclamation.status = 'in_progress';
        }

        // Show success message
        this.showSuccessMessage = true;
        this.successMessage = 'Votre réponse a été envoyée avec succès.';

        // Reset form
        this.submitted = false;
        this.responseForm.reset();
      }

      this.isSubmitting = false;
    }, 1500);
  }

  // Close success message
  closeSuccessMessage(): void {
    this.showSuccessMessage = false;
    this.successMessage = '';
  }

  // Go back to reclamations list
  goBack(): void {
    this.router.navigate(['/receptionist/reclamations']);
  }
}
