import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DemandeService } from './demande.service';
import { Demande } from './demande.model';
import { CommonModule } from '@angular/common';
import { Devis } from '../../../models/devis';
import { NotificationService } from '../../notification/notification.service';
import { AppNotification } from './appnotification.model';
// Import the DemandeService from receptionniste module for getDemandeBy method
import { DemandeService as ReceptionnisteDemandeService } from '../../receptionniste/demandes/demande.service';
// Import PaymentService for fetching payment details
import { PaymentService } from '../../receptionniste/services/payment.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPrint } from '@fortawesome/free-solid-svg-icons';


@Component({
  selector: 'app-demande-details',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './demande-details.component.html',
  styleUrls: ['./demande-details.component.css']
})
export class DemandeDetailsClientComponent implements OnInit {
  // Font Awesome icons
  faPrint = faPrint;
  clientInfo: any = [];
  demande: Demande | null = null;
  devis: Devis[] = [];
  isLoading = true;
  isDevisLoading = false; // Added for devis loading state
  errorMessage: string | null = null;
  fromNotification: string | null = null;
  devisId: number | null = null; // Will be set from query params
  id: number | null = null;
  private _notification: AppNotification | null = null;
  expectedResultsDate: string | null = null; // To store the calculated results date
  constructor(
    private route: ActivatedRoute,
    private demandeService: DemandeService,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService,
    private receptionnisteDemandeService: ReceptionnisteDemandeService,
    private paymentService: PaymentService
  ) {}
// Getter and Setter for notification
get notification(): AppNotification | null {
  return this._notification;
}
set notification(value: AppNotification | null) {
  this._notification = value;
  console.log('Notification set to:', this._notification); // Debug setter
  this.cdr.detectChanges(); // Force change detection
}
ngOnInit(): void {
  console.log('ngOnInit started'); // Debug lifecycle
  this.route.queryParams.subscribe(params => {
    this.fromNotification = params['fromNotification'] || null;
    console.log('fromNotification value:', this.fromNotification); // Debug query param
    this.fetchDemandeDetails();
    // Temporarily force fetchNotificationDetails to run
    this.fetchNotificationDetails(); // Removed if condition for testing
  });
}
// fetchBringSampleNotification(): void {
//   const demandeId = this.route.snapshot.paramMap.get('demande_id');
//   if (!demandeId) {
//     this.errorMessage = 'No demande ID provided for notification!';
//     this.cdr.detectChanges();
//     return;
//   }

//   this.notificationService.getNotificationByDemandeId(demandeId).subscribe({
//     next: (notification) => {
//       this.notification = notification;
//       if (this.notification?.message) {
//         this.notification.message = this.extractResultsDate(this.notification.message);
//       }
//       console.log('Notification fetched:', this.notification);
//     },
//     error: (error) => {
//       console.error('Error fetching notification:', error);
//       this.errorMessage = 'Failed to load notification details.';
//       this.cdr.detectChanges();
//     }
//   });
// }
fetchNotificationDetails(): void {
  const demandeId = this.route.snapshot.paramMap.get('demande_id');
  if (!demandeId) {
    this.errorMessage = 'Aucun ID de demande fourni pour la notification !';
    this.cdr.detectChanges();
    return;
  }

  this.notificationService.getNotificationByDemandeId(demandeId).subscribe({
    next: (notification) => {
      this.notification = notification;
      if (this.notification?.message) {
        this.notification.message = this.extractResultsDate(this.notification.message);
      }
      console.log('Notification fetched:', this.notification);
    },
    error: (error) => {
      console.error('Error fetching notification:', error);

      this.cdr.detectChanges();
    }
  });
}

// Helper function to extract the desired part
extractResultsDate(message: string): string {
  const match = message.match(/Vos résultats seront prêts d'ici le \d{4}-\d{2}-\d{2}\./);
  return match ? match[0] : message; // Fallback to original message if no match
}

/**
 * Calculates the expected results date (7 working days from the given date)
 * @param startDate The date to calculate from (payment approval date or demande creation date)
 * @returns Formatted date string
 */
calculateExpectedResultsDate(startDate: string | Date): string {
  const dateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
  let workingDaysToAdd = 7;
  let currentDate = new Date(dateObj);

  // Skip weekends (Saturday and Sunday)
  while (workingDaysToAdd > 0) {
    currentDate.setDate(currentDate.getDate() + 1);

    // Skip weekends (0 = Sunday, 6 = Saturday)
    const dayOfWeek = currentDate.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      workingDaysToAdd--;
    }
  }

  // Format the date as DD/MM/YYYY
  const day = String(currentDate.getDate()).padStart(2, '0');
  const month = String(currentDate.getMonth() + 1).padStart(2, '0');
  const year = currentDate.getFullYear();

  return `${day}/${month}/${year}`;
}

/**
 * Fetch payment information for a demande
 * @param demandeId The demande ID to fetch payments for
 */
fetchPaymentDetails(demandeId: string): void {
  this.paymentService.getPaymentsByDemandeId(demandeId).subscribe({
    next: (response) => {
      console.log('Payment response:', response);
      if (response && response.status === 'success' && response.data && response.data.length > 0) {
        // Look for an approved payment
        const approvedPayment = response.data.find((payment: any) => payment.status === 'approved');

        if (approvedPayment && approvedPayment.payment_date) {
          // Calculate results date based on payment approval date
          this.expectedResultsDate = this.calculateExpectedResultsDate(approvedPayment.payment_date);
          console.log('Expected results date from payment approval:', this.expectedResultsDate);
        } else if (this.demande && this.demande.demande_date) {
          // Fall back to demande creation date if no approved payment
          this.expectedResultsDate = this.calculateExpectedResultsDate(this.demande.demande_date);
          console.log('Expected results date from demande creation:', this.expectedResultsDate);
        }

        this.cdr.detectChanges(); // Force Angular to detect changes
      } else if (this.demande && this.demande.demande_date) {
        // Fall back to demande creation date if no payments
        this.expectedResultsDate = this.calculateExpectedResultsDate(this.demande.demande_date);
        console.log('Expected results date from demande creation (fallback):', this.expectedResultsDate);
        this.cdr.detectChanges();
      }
    },
    error: (error) => {
      console.error('Error fetching payments:', error);

      // Fall back to demande creation date on error
      if (this.demande && this.demande.demande_date) {
        this.expectedResultsDate = this.calculateExpectedResultsDate(this.demande.demande_date);
        console.log('Expected results date from demande creation (error fallback):', this.expectedResultsDate);
        this.cdr.detectChanges();
      }
    }
  });
}
  fetchDemandeDetails(): void {
    const demandeId = this.route.snapshot.paramMap.get('demande_id');
    if (!demandeId) {
      this.errorMessage = 'Aucun ID de demande fourni !';
      this.isLoading = false;
      return;
    }

    this.demandeService.getDemandeDetails(demandeId).subscribe({
      next: (response) => {

        this.demande = { ...response, samples: response.samples || [] };
        this.isLoading = false;
        this.fetchDevisDetails(demandeId);
        if (this.demande && this.demande.user_id) {
          this.fetchUserDetails(this.demande.user_id);
        } else {

          this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
        }
        // Calculate expected results date (7 working days from payment approval)
        // If there's a payment with approved status, use its payment_date
        // Otherwise, fall back to demande creation date
        if (this.demande) {
          // We'll check for payment status in the fetchPaymentDetails method
          this.fetchPaymentDetails(demandeId);
        }

        this.cdr.detectChanges(); // Added to ensure UI updates
      },
      error: (error) => {
        console.error('Error fetching demande:', error);
        this.errorMessage = 'Échec du chargement des détails de la demande.';
        this.isLoading = false;
      }
    });
  }

  /**
   * Fetches user details using `user_id`, and updates `clientInfo`.
   * Uses the receptionnisteDemandeService for consistent implementation.
   */
  fetchUserDetails(user_id: number): void {
    if (!user_id) {
      this.clientInfo = { name: '⏳ Chargement...', email: '', phone: '', address: '' };
      return;
    }

    this.receptionnisteDemandeService.getUserDetails(user_id).subscribe({
      next: (response) => {
        this.clientInfo = response;
        console.log('Client info:', this.clientInfo);
        this.cdr.detectChanges(); // Force Angular to detect changes
      },
      error: () => {
        console.error(`Error fetching user ${user_id}`);
        this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
      }
    });
  }
  fetchDevisDetails(demandeId: string): void {
    console.log('Fetching devis for demande ID:', demandeId);
    console.log('Current demande status:', this.demande?.status);
    console.log('Current devis_sent value:', this.demande?.devis_sent);

    // Set devis loading state to true
    this.isDevisLoading = true;

    this.demandeService.getDevis(demandeId).subscribe({
      next: (response) => {
        this.devis = response; // response is now an array
        console.log('Devis fetched successfully:', this.devis);
        console.log('Devis length:', this.devis?.length);

        // Set devis loading state to false
        this.isDevisLoading = false;

        // Force change detection to update the view
        this.cdr.detectChanges();

        // Check if devis should be visible based on conditions
        const shouldShowDevis =
          this.demande?.status === 'valid' &&
          this.demande?.devis_sent !== null &&
          this.devis &&
          this.devis.length > 0;

        console.log('Should show devis based on conditions:', shouldShowDevis);
      },
      error: (error) => {
        console.error('Error fetching devis:', error);
        this.errorMessage = 'Échec du chargement des détails du devis : ' + (error.message || 'Erreur inconnue');

        // Set devis loading state to false even on error
        this.isDevisLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  preValidateDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    this.demandeService.updateStatusToOngoing(this.demande.demande_id).subscribe({
      next: (response) => {
        console.log("Demande pré-validée!", response);
        alert("La demande a été pré-validée. Le client est notifié d'apporter les échantillons.");
        this.fetchDemandeDetails();
      },
      error: (error) => {
        console.error("Erreur lors de la pré-validation de la demande:", error);
        alert("Échec de la pré-validation de la demande. Veuillez réessayer.");
      }
    });
  }

  validateDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    this.demandeService.updateStatusToValidated(this.demande.demande_id).subscribe({
      next: (response) => {
        console.log("Demande validée!", response);
        alert("La demande a été complètement validée.");
        if (this.demande) {
          this.demande.status = 'validated';
        }
      },
      error: (error) => {
        console.error("Erreur lors de la validation de la demande:", error);
        alert("Échec de la validation de la demande.");
      }
    });
  }

// Function to group analyses by name
groupDevisData(): any[] {
  console.log('groupDevisData called with devis:', this.devis);

  // Check if devis is available
  if (!this.devis || !Array.isArray(this.devis) || this.devis.length === 0) {
    console.log('No valid devis data available');
    return [];
  }

  const grouped = new Map<string, any>();

  try {
    this.devis.forEach((item: any) => {
      if (!item || !item.analyse) {
        console.warn('Invalid devis item:', item);
        return; // Skip this item
      }

      if (grouped.has(item.analyse)) {
        // If the analysis already exists, update quantity & total price
        let existing = grouped.get(item.analyse);
        existing.quantite += Number(item.quantite) || 0;
        existing.prix_total += (Number(item.prix_unitaire) || 0) * (Number(item.quantite) || 0);
      } else {
        // Otherwise, add it to the grouped map
        grouped.set(item.analyse, {
          ...item,
          quantite: Number(item.quantite) || 0,
          prix_unitaire: Number(item.prix_unitaire) || 0,
          prix_total: (Number(item.prix_unitaire) || 0) * (Number(item.quantite) || 0)
        });
      }
    });

    const result = Array.from(grouped.values()); // Convert Map back to an array
    console.log('Grouped devis data:', result);
    return result;
  } catch (error) {
    console.error('Error in groupDevisData:', error);
    return [];
  }
}

// Function to calculate total amount
getTotal(): number {
  try {
    const groupedData = this.groupDevisData();
    if (!groupedData || groupedData.length === 0) {
      return 0;
    }

    const total = groupedData.reduce((sum, d) => {
      const prixTotal = Number(d.prix_total) || 0;
      return sum + prixTotal;
    }, 0);

    console.log('Calculated total:', total);
    return total;
  } catch (error) {
    console.error('Error calculating total:', error);
    return 0;
  }
}

  validateWithDerogation(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    this.demandeService.updateStatusToDerogation(this.demande.demande_id).subscribe({
      next: (response) => {
        console.log("Demande validée avec dérogation!", response);
        alert("La demande a été validée avec dérogation.");
        if (this.demande) {
          this.demande.status = 'validated_with_derogation';
        }
        this.router.navigate(['/derogation-details', this.demande?.demande_id]);
      },
      error: (error) => {
        console.error("Erreur lors de la validation avec dérogation:", error);
        alert("Échec de la validation avec dérogation.");
      }
    });
  }

  /**
   * Génère et imprime un PDF de la demande avec les détails du client et des échantillons
   */
  /**
   * Navigate to the devis component to handle payment
   */
  navigateToDevis(): void {
    this.router.navigate(['/client/devis']);
  }

  printDemande(): void {
    if (!this.demande) {
      console.error('Aucune demande à imprimer');
      return;
    }

    // Générer les lignes du tableau d'échantillons
    const sampleRows = this.demande.samples.map(sample => {
      // Convertir le tableau d'analyses en liste HTML
      const analysesList = sample.analyses_demandees?.length > 0
        ? `<ul style="margin: 0; padding-left: 20px;">${sample.analyses_demandees.map(analysis => `<li>${analysis}</li>`).join('')}</ul>`
        : '—';

      // Déterminer l'état de l'échantillon avec des cases à cocher
      const etatLiq = sample.etat === 'Liquide' ? '✓' : '';
      const etatSol = sample.etat === 'Solide' ? '✓' : '';
      const etatRig = sample.etat === 'Rigide' ? '✓' : '';
      const etatCgl = sample.etat === 'Congelé' ? '✓' : '';

      return `
        <tr>
          <td>${sample.identification_echantillon || '—'}</td>
          <td>${sample.nature_echantillon || '—'}</td>
          <td>${sample.provenance || '—'}</td>
          <td>${sample.masse_echantillon || '—'}</td>
          <td>${etatLiq}</td>
          <td>${etatSol}</td>
          <td>${etatRig}</td>
          <td>${etatCgl}</td>
          <td>${analysesList}</td>
        </tr>
      `;
    }).join('');

    // Devis section removed as requested

    // Formater la date pour l'affichage
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    };

    // Calculate total pages based on samples
    const samplesPerPage = 5; // Assuming 5 samples can fit on a page
    const totalSamples = this.demande.samples.length;
    const samplePages = Math.ceil(totalSamples / samplesPerPage);
    const totalPages = Math.max(1, samplePages); // At least 1 page

    // Générer le HTML complet pour l'impression
    const printHtml = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire Client Complet</title>
    <style>
        /* Print Settings */
        @media print {
            @page {
                size: A4;
                margin: 5mm;
                counter-increment: page;
            }
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .logo-cell img {
                width: 30mm;
                height: auto;
            }
            .header-table th, .header-table td {
                font-size: 10pt;
            }
            /* Page break after sample table if needed */
            .sample-table-container {
                page-break-after: auto;
            }
            /* Page number handling */
            .page-number::before {
                content: "1 / " attr(data-total-pages);
            }
            .page-number {
                position: relative;
            }
            /* For second page and beyond */
            @page:nth-of-type(n+2) {
                .page-number::before {
                    content: "2 / " attr(data-total-pages);
                }
            }
        }

        /* Global Styles */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }


        /* Label styles */
        .label {
            font-weight: bold;
        }

        @media print {
            .editable-field {
                border: none;
                background-color: transparent;
            }
        }


.header-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px auto;
    margin-bottom: 40px;
}

.header-table th, .header-table td {
    border: 1px solid black;
    padding: 8px;
    text-align: left;
    vertical-align: top;
}

.logo-cell {
    vertical-align: middle;
    text-align: center;
}

.second-column {
    background-color: #e0e0e0;
    text-align: center;
    font-weight: bold;
    font-size: 24pt;
    vertical-align: middle;
    padding: 20px;
}

.third-column {
    text-align: right;
}
        /* Client Info Section */
        .form-section {
            max-width: 1200px;
            margin: 40px auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .client-info-container {
            margin-bottom: 40px;
        }
        .header-info {
            display: flex;
            justify-content: space-between;
            border-bottom: 2px solidrgb(0, 0, 0);
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .header-label {
            font-size: 18px;
            color:rgb(0, 0, 0);
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .client-table {
            width: 100%;
            border-collapse: collapse;
        }
        .client-table td {
            border: 1px solid #000;
            padding: 12px;
            vertical-align: top;
        }
        .label-cell {
            font-weight: bold;
            width: 25%;
            background-color: transparent;
        }
        .data-cell {
            width: 75%;
            padding-left: 15px;
        }

        /* Sample Table Section */
        .sample-table-container {
            margin: 20px auto;
        }
        .sample-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }
        .sample-table th, .sample-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .sample-table thead th {
            font-weight: bold;
        }

        /* Payment Section */
        .payment-section {
            margin: 20px auto;
            width: 100%;
            max-width: 800px;
            padding: 20px;
        }
        .payment-section h3 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
        }
        .payment-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .option {
            display: flex;
            align-items: center;
        }
        .option input {
            margin-right: 5px;
        }
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-block-left, .signature-block-right {
            width: 45%;
        }
        .signature-block-left {
            text-align: left;
        }
        .signature-block-right {
            text-align: right;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            height: 30px;
            margin-top: 10px;
        }

        /* Footer Section */
        .footer-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 40px;
            box-sizing: border-box;
            padding-top: 50px;
        }
        .bottom-text {
            border-top: 1px solid #000;
            padding-top: 20px;
            font-size: 14px;
            text-align: center;
            font-style: italic;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <!-- Document Header -->
    <table class="header-table">
        <tr>
            <td rowspan="4" class="logo-cell">
                <img src="${window.location.origin}/kls.png" alt="Logo Left" style="width: 120px; height: auto;">
            </td>
            <td rowspan="4" class="second-column">DEMANDE D’ANALYSE</td>
            <td class="third-column"><span class="label">CODE:</span> <span class="editable-field">FE/03-PRT/08</span></td>
        </tr>
        <tr>
            <td class="third-column"><span class="label">VERSION :</span> <span class="editable-field">01</span></td>
        </tr>
        <tr>
            <td class="third-column"><span class="label">DATE :</span> <span class="editable-field">${formatDate(this.demande.demande_date)}</span></td>
        </tr>
        <tr>
            <td class="third-column"><span class="label">PAGE :</span> <span class="editable-field page-number" data-total-pages="${totalPages}"></span></td>
        </tr>
    </table>

    <!-- Form Structure -->
    <div style="margin: 20px 0;">
      <style>
        .form-container {
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        }

        .form-row {
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
        }

        .form-label {
          font-weight: bold;
          margin-right: 10px;
        }

        .input-boxes {
          display: flex;
          gap: 2px;
        }

        .input-box {
          width: 30px;
          height: 30px;
          border: 1px solid black;
          display: inline-block;
          text-align: center;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .separator {
          margin: 0 5px;
        }

        .underlined-label {
          font-style: italic;
          text-decoration: underline;
          margin-bottom: 10px;
          display: block;
        }

        .dotted-line {
          border-bottom: 1px dotted black;
          flex-grow: 1;
          height: 1.2em;
          margin-right: 10px;
        }

        .amount-field {
          text-align: right;
          flex-grow: 1;
        }

        .date-group {
          display: flex;
          gap: 10px;
        }
      </style>

      <div class="form-container">
        <div class="form-row">
          <div style="flex: 1;">
            <div class="form-label">Demande N°:</div>
            <div class="input-boxes">
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(0, 1) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(1, 2) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(2, 3) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(3, 4) : ''}</div>
              <span class="separator"></span>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(4, 5) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(5, 6) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(6, 7) : ''}</div>
            </div>
          </div>

          <div style="flex: 1;">
            <div class="form-label">Date d'enregistrement :</div>
            <div class="date-group">
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(0, 1)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(1, 2)}</div>
              </div>
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(3, 4)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(4, 5)}</div>
              </div>
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(8, 9)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(9, 10)}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <span class="underlined-label">Référence document client :</span>
        </div>

        <div class="form-row">
          <div style="flex: 2;">
            <span>Quittance</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:200px; display:inline-block; margin-left:10px;" contenteditable="true"></span>
          </div>
        </div>

        <div class="form-row">
          <div style="flex: 2;">
            <span>Bon de commande</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:200px; display:inline-block; margin-left:10px;" contenteditable="true"></span>
          </div>
          <div style="flex: 1;">
            <span>Montant HT</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:100px; display:inline-block; margin-left:10px;" contenteditable="true">${this.getTotal().toFixed(2)} MAD</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Information Section -->
    <div class="form-section">
        <div class="header-info">
            <div class="header-label">
                Demande N° : <span style="margin-left: 10px; border-bottom: 1px solid #000; width: 150px; display: inline-block;">${this.demande.demande_id}</span>
            </div>
            <div class="header-label">
                Date d'enregistrement : <span style="margin-left: 10px; border-bottom: 1px solid #000; width: 150px; display: inline-block;">${formatDate(this.demande.demande_date)}</span>
            </div>
        </div>

        <table class="client-table">
            <tr>
                <td class="label-cell">Client</td>
                <td class="data-cell"><span contenteditable="true" class="editable-field">${this.clientInfo?.name || 'Non spécifié'} ${this.clientInfo?.nickname || ''}</span></td>
                <td class="label-cell">Tél.</td>
                <td class="data-cell"><span contenteditable="true" class="editable-field">${this.clientInfo?.phone || ''}</span></td>
            </tr>
            <tr>
                <td class="label-cell">Adresse</td>
                <td class="data-cell"><span contenteditable="true" class="editable-field">${this.clientInfo?.address || this.clientInfo?.adress || 'Non spécifié'}</span></td>
                <td class="label-cell">Fax</td>
                <td class="data-cell"><span contenteditable="true" class="editable-field"></span></td>
            </tr>
            <tr>
                <td class="label-cell"></td>
                <td class="data-cell"></td>
                <td class="label-cell">Mail</td>
                <td class="data-cell"><span contenteditable="true" class="editable-field">${this.clientInfo?.email || 'Non spécifié'}</span></td>
            </tr>

        </table>
    </div>

    <!-- Sample Table Section -->
    <div class="sample-table-container">
        <table class="sample-table">
            <thead>
                <tr>
                    <th rowspan="3">Identification échantillon</th>
                    <th rowspan="3">Nature échantillon</th>
                    <th rowspan="3">Provenance</th>
                    <th colspan="5">Description de l'échantillon</th>
                    <th rowspan="3">Analyses demandées*</th>
                </tr>
                <tr>
                    <th rowspan="2">Qté<br>/Echantillon</th>
                    <th colspan="4">Etat</th>
                </tr>
                <tr>
                    <th>Liq</th>
                    <th>Sol</th>
                    <th>Rig</th>
                    <th>Cgl</th>
                </tr>
            </thead>
            <tbody>
                ${sampleRows}
            </tbody>
        </table>
    </div>

    <!-- Mode de règlement Section -->
    <div style="margin: 30px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
        <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">Mode de règlement :</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="carte" ${this.demande.mode_reglement === 'Carte bancaire' ? 'checked' : ''}>
                <label for="carte" style="margin-left: 5px;">Sur présentation de facture</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="virement" ${this.demande.mode_reglement === 'Virement bancaire' ? 'checked' : ''}>
                <label for="virement" style="margin-left: 5px;">Virement bancaire</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="convention" ${this.demande.mode_reglement === 'Convention' ? 'checked' : ''}>
                <label for="convention" style="margin-left: 5px;">Convention</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="collaboration" ${this.demande.mode_reglement === 'Collaboration' ? 'checked' : ''}>
                <label for="collaboration" style="margin-left: 5px;">Collaboration</label>
            </div>
        </div>

        <!-- NB Section -->
        <div style="margin-top: 20px; font-size: 14px;">
            <p style="font-weight: bold; margin-bottom: 10px;">NB :</p>
            <p style="margin-bottom: 5px;">-La durée des analyses est d'une semaine ouvrable pour un échantillon standard.</p>
            <p style="margin-bottom: 5px;">-Si N≥6 contacter le DL pour définir le délai à communiquer au client.</p>
            <p style="margin-bottom: 5px;">**Délai d'exécution souhaité par le client, méthode d'analyse autre que celle utilisée par le laboratoire ; état de l'emballage de l'échantillon….</p>
        </div>
    </div>

    <!-- Signatures Section -->
    <div style="display: flex; justify-content: space-between; margin: 40px 0;">
        <div style="width: 45%; text-align: left;">
            <p style="font-weight: bold; margin-bottom: 10px;">Signature du Client</p>

        </div>
        <div style="width: 45%; text-align: right;">
            <p style="font-weight: bold; margin-bottom: 10px;">Signature du réceptionnaire</p>

        </div>
    </div>

    <!-- Footer Section -->
    <div class="footer-container">
        <div class="bottom-text">
            Ce document est strictement confidentiel et ne doit pas être reproduit sans autorisation
        </div>
    </div>
</body>
</html>
    `;

    // Ouvrir une nouvelle fenêtre pour l'impression
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHtml);
      printWindow.document.close();

      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
          // printWindow.close(); // Optionnel: fermer après l'impression
        }, 500);
      };
    } else {
      console.error('Impossible d\'ouvrir la fenêtre d\'impression. Veuillez autoriser les pop-ups.');
    }
  }
}