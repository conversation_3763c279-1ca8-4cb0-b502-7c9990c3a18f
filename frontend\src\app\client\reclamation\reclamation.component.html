<div class="reclamation-container">
  <div class="reclamation-title-container">
    <div class="circle-icon"><i class="fas fa-exclamation-triangle"></i></div>
    <h2 class="reclamation-title">Formulaire de Réclamation</h2>
  </div>

  <div class="view-all-link">
    <a (click)="goToReclamations()">
      <i class="fas fa-list-alt"></i> Voir toutes mes réclamations
    </a>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
    <p>Chargement en cours...</p>
  </div>

  <!-- Success Modal -->
  <div *ngIf="showSuccessModal" class="success-modal-overlay" (click)="closeSuccessModal()">
    <div class="success-modal" (click)="$event.stopPropagation()">
      <div class="success-modal-content">
        <div class="success-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>
        <h3 class="success-title">Succès!</h3>
        <p class="success-message">{{ modalMessage }}</p>
        <button class="success-close-btn" (click)="closeSuccessModal()">
          Fermer
        </button>
      </div>
    </div>
  </div>

  <!-- Reclamation Form -->
  <div *ngIf="!isLoading && !showSuccessModal" class="reclamation-form-container">
    <form [formGroup]="reclamationForm" (ngSubmit)="onSubmit()">
      <!-- Subject/Title Field -->
      <div class="form-group">
        <label for="subject">Sujet <span class="required">*</span></label>
        <input
          type="text"
          id="subject"
          formControlName="subject"
          class="form-control"
          [ngClass]="{'is-invalid': submitted && f['subject'].errors}"
        >
        <div *ngIf="submitted && f['subject'].errors" class="error-message">
          <div *ngIf="f['subject'].errors['required']">Le sujet est requis</div>
        </div>
      </div>

      <!-- Description Field -->
      <div class="form-group">
        <label for="description">Description <span class="required">*</span></label>
        <textarea
          id="description"
          formControlName="description"
          class="form-control"
          rows="5"
          [ngClass]="{'is-invalid': submitted && f['description'].errors}"
        ></textarea>
        <div *ngIf="submitted && f['description'].errors" class="error-message">
          <div *ngIf="f['description'].errors['required']">La description est requise</div>
        </div>
      </div>

      <!-- Demande Selection -->
      <div class="form-group">
        <label for="demandeId">Demande concernée (optionnel)</label>
        <div class="select-wrapper">
          <select
            id="demandeId"
            formControlName="demandeId"
            class="form-control"
          >
            <option value="">Sélectionner une demande</option>
            <option *ngFor="let demande of userDemandes" [value]="demande.demande_id">
              {{ demande.demande_id }} - {{ demande.demande_date | date:'dd/MM/yyyy' }}
            </option>
          </select>
        </div>
      </div>

      <!-- File Upload -->
      <div class="form-group">
        <label for="file">Pièce jointe (optionnel)</label>
        <div class="file-upload-container">
          <input
            type="file"
            id="file"
            class="file-upload-input"
            (change)="onFileSelected($event)"
            accept=".pdf,.jpg,.jpeg,.png"
            [disabled]="isSubmitting"
          >
          <label for="file" class="file-upload-label">
            <i class="fas fa-upload"></i>
            <span *ngIf="!fileName">Sélectionner un fichier (PDF, JPG, PNG)</span>
            <span *ngIf="fileName">{{ fileName }}</span>
          </label>
        </div>
        <div *ngIf="fileError" class="error-message">
          {{ fileError }}
        </div>
      </div>

      <!-- Submit Button -->
      <div class="form-actions">
        <button
          type="submit"
          class="btn-submit"
          [disabled]="isSubmitting"
        >
          <span *ngIf="!isSubmitting">Soumettre la réclamation</span>
          <span *ngIf="isSubmitting">
            <i class="fas fa-spinner fa-spin"></i> Traitement en cours...
          </span>
        </button>
      </div>
    </form>
  </div>
</div>
