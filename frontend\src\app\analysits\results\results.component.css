/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* =========================================================
   1. CONTENEUR PRINCIPAL
   ========================================================= */
.results-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem auto;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  font-family: 'Montserrat', sans-serif;
}

/* =========================================================
   2. HEADER
   ========================================================= */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.results-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(90deg, #000, #888);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  border-bottom: 4px solid #2496d3;
  padding-bottom: 0.25rem;
  animation: glowText 1.5s infinite alternate;
}

.title-icon {
  color: #000000; /* Blue color to match the border */
  font-size: 1.4em;

  margin-right: 5px;
  transform: rotate(-10deg); /* Slight tilt for visual interest */
}

.back-btn {
  background: #007bff;
  color: #fff;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  transition: transform 0.3s, box-shadow 0.3s;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

@keyframes glowText {
  from { text-shadow: 0 0 8px rgba(36,150,211,0.4); }
  to   { text-shadow: 0 0 16px rgba(36,150,211,0.8); }
}

/* =========================================================
   3. BARRE DE FILTRES
   ========================================================= */
.filter-bar {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 1rem;
  margin: 0 auto 1.5rem;
  width: 90%;
 
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  flex: 1 1 200px;
}

.filter-group label {
  font-weight: 600;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.filter-input,
.filter-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  padding-left: 2.5rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #2496d3;
  box-shadow: 0 0 4px rgba(36,150,211,0.3);
}

/* Boutons Appliquer / Effacer */
.filter-buttons {
  display: flex;
  gap: 0.8rem;
  margin-left: auto;
}

.btn-filter-apply,
.btn-clear {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 6px;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.btn-filter-apply {
  background: #28a745;
}

.btn-filter-apply:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40,167,69,0.3);
}

.btn-clear {
  background: #6c757d;
}

.btn-clear:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(90,98,104,0.3);
}

/* Responsive design for filter bar */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    width: 90%;
  }

  .filter-group {
    width: 100%;
  }

  .filter-buttons {
    width: 100%;
    margin-left: 0;
    margin-top: 1rem;
    justify-content: space-between;
  }

  .btn-filter-apply,
  .btn-clear {
    width: 48%;
  }

  .results-header {
    flex-direction: column;
    gap: 1rem;
  }

  .results-title {
    font-size: 1.25rem;
  }
}

/* =========================================================
   4. LOADING / ERROR / NO-DATA
   ========================================================= */
.loading-row,
.error-message,
.empty-row {
  text-align: center;
  margin: 1rem 0;
  font-size: 1rem;
  color: #6c757d;
}

.text-center {
  text-align: center;
}

/* =========================================================
   5. TABLEAU
   ========================================================= */
.results-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.results-table thead {
  background: #2496d3;
}

.results-table thead th {
  color: #fff;
  text-transform: uppercase;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  text-align: left;
}

.results-table th.actions-header {
  text-align: center !important;
  padding-left: 0;
  padding-right: 0;
}

.results-table td.actions-cell {
  text-align: center;
}

.results-table tbody td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  text-align: left;
}

.results-table tbody tr:hover td {
  background: #fafafa;
}

/* =========================================================
   6. BOUTONS DANS LE TABLEAU
   ========================================================= */
.actions-col {
  display: flex;
  align-items: center;
  gap: 1rem; /* Espacement horizontal entre les boutons */
  flex-wrap: wrap;
}

.btn-detail,
.btn-create,
.btn-download,
.btn-view,
.btn-delete {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border: none;
  min-width: 140px;
  justify-content: center;
}

.btn-detail {
  background: #17a2b8;
  color: #fff;
}

.btn-detail:hover {
  background: #138496;
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.btn-create {
  background: #28a745;
  color: #fff;
}

.btn-create:hover {
  background: #218838;
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.btn-download {
  background: #007bff;
  color: #fff;
}

.btn-download:hover {
  background: #0069d9;
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.btn-view {
  background: #6c757d;
  color: #fff;
}

.btn-view:hover {
  background: #5a6268;
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.btn-delete {
  background: #dc3545;
  color: #fff;
}

.btn-delete:hover {
  background: #c82333;
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

/* =========================================================
   7. STATUTS
   ========================================================= */
.status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.status-available {
  background: #d4edda;
  color: #155724;
}

.status-unavailable {
  background: #f8d7da;
  color: #721c24;
}

/* Responsive design for buttons */
@media (max-width: 640px) {
  .actions-col {
    flex-direction: column;
    gap: 0.5rem; /* Réduit l'espacement vertical sur mobile */
  }

  .btn-detail,
  .btn-create,
  .btn-download,
  .btn-view,
  .btn-delete {
    width: 100%; /* Boutons prennent toute la largeur sur mobile */
  }
}

pagination-controls {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  overflow: hidden;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.modal-body {
  padding: 20px;
}

.modal-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.modal-body .warning {
  color: #dc3545;
  font-weight: 500;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-cancel:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-confirm {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-confirm:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-cancel:disabled, .btn-confirm:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}
