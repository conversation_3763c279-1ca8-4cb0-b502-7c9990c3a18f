<div class="send-results-container">
  <div class="header">
    <h2>
      <fa-icon [icon]="faPaperPlane" class="icon-primary"></fa-icon>
      Envoyer les Résultats au Client
    </h2>
    <button class="back-btn" (click)="goBack()">
      <fa-icon [icon]="faArrowLeft"></fa-icon> Retour aux Résultats
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <fa-icon [icon]="faSpinner" class="fa-spin fa-2x"></fa-icon>
    <p>Chargement des détails...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message">
    <fa-icon [icon]="faExclamationTriangle"></fa-icon>
    <p>{{ errorMessage }}</p>
  </div>

  <!-- Success message -->
  <div *ngIf="successMessage" class="success-message">
    <fa-icon [icon]="faCheckCircle"></fa-icon>
    <p>{{ successMessage }}</p>
  </div>

  <!-- Details section -->
  <div *ngIf="!isLoading && reportDetails && demandeDetails" class="details-section">
    <h3>Détails de la Demande</h3>
    <div class="details-grid">
      <div class="detail-item">
        <span class="detail-label">Numéro de Demande:</span>
        <span class="detail-value">{{ demandeId }}</span>
      </div>
      
      <div class="detail-item">
        <span class="detail-label">Date de Réception:</span>
        <span class="detail-value">{{ reportDetails.creation_date || 'Non disponible' }}</span>
      </div>
    </div>
  </div>

  <!-- Files section -->
  <div *ngIf="!isLoading && reportDetails && demandeDetails" class="files-section">
    <h3>Fichiers</h3>

    <!-- Files section with both existing files and upload options -->
    <div class="files-container">
      <!-- Existing files display -->
      <div class="existing-files">
        <div class="file-list">
          <!-- Report file -->
          <div *ngIf="hasReportFile()" class="file-item">
            <div class="file-info">
              <fa-icon [icon]="faFilePdf" class="file-icon"></fa-icon>
              <span class="file-name">{{ getReportFileName() }}</span>
            </div>
            <div class="file-actions">
              <button class="file-action-btn view" (click)="viewFile('report')" title="Voir le fichier">
                <fa-icon [icon]="faEye"></fa-icon>
              </button>
              <button class="file-action-btn download" (click)="downloadFile('report')" title="Télécharger">
                <fa-icon [icon]="faDownload"></fa-icon>
              </button>
              <button class="file-action-btn delete" (click)="deleteFile('report')" title="Supprimer">
                <fa-icon [icon]="faTrash"></fa-icon>
              </button>
            </div>
          </div>

          <!-- Invoice file -->
          <div *ngIf="hasInvoiceFile()" class="file-item">
            <div class="file-info">
              <fa-icon [icon]="faFileInvoice" class="file-icon"></fa-icon>
              <span class="file-name">{{ getInvoiceFileName() }}</span>
            </div>
            <div class="file-actions">
              <button class="file-action-btn view" (click)="viewFile('invoice')" title="Voir le fichier">
                <fa-icon [icon]="faEye"></fa-icon>
              </button>
              <button class="file-action-btn download" (click)="downloadFile('invoice')" title="Télécharger">
                <fa-icon [icon]="faDownload"></fa-icon>
              </button>
              <button class="file-action-btn delete" (click)="deleteFile('invoice')" title="Supprimer">
                <fa-icon [icon]="faTrash"></fa-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- Status display and send button - only shown when at least one file exists -->
        <div *ngIf="hasUploadedFiles()" class="status-section">
          <!-- Status display -->
          <div class="status-display">
            <div class="status-label">Statut:</div>
            <div class="status-value" [ngClass]="{'status-sent': isClientStatusSent(), 'status-pending': !isClientStatusSent()}">
              {{ isClientStatusSent() ? 'Envoyé au client' : 'En attente d\'envoi' }}
            </div>
          </div>

          <!-- Send to client button (only shown when not sent) -->
          <div class="send-action" *ngIf="!isClientStatusSent()">
            <button
              class="btn send-btn"
              [disabled]="isSending"
              (click)="sendToClient()"
            >
              <fa-icon *ngIf="!isSending" [icon]="faPaperPlane"></fa-icon>
              <fa-icon *ngIf="isSending" [icon]="faSpinner" class="fa-spin"></fa-icon>
              {{ isSending ? 'Envoi en cours...' : 'Envoyer au Client' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Upload section - always shown but with conditional content -->
      <div class="upload-section">
        <!-- Only show the info text when no files are uploaded -->
        <p *ngIf="!hasUploadedFiles()" class="upload-info">Téléchargez les fichiers du rapport et de la facture de demande client.</p>

        <!-- Show specific message when only one file is missing -->
        <p *ngIf="hasUploadedFiles() && (!hasReportFile() || !hasInvoiceFile())" class="upload-info">
          Téléchargez le fichier {{ !hasReportFile() ? 'du rapport' : 'de la facture' }} manquant.
        </p>

        <div class="file-upload-container">
          <!-- Report file upload - shown when no report file exists or showReportUpload is true -->
          <div class="file-upload-box" *ngIf="!hasReportFile() || showReportUpload">
            <h4>
              <fa-icon [icon]="faFilePdf"></fa-icon>
              Rapport d'Analyse
            </h4>
            <div class="file-upload-area" [class.has-file]="reportFileName">
              <input
                type="file"
                id="report-file"
                class="file-upload-input"
                (change)="onReportFileSelected($event)"
                accept=".pdf,.jpg,.jpeg,.png"
                [disabled]="isUploading"
              >
              <label for="report-file" class="file-upload-label">
                <fa-icon [icon]="faUpload"></fa-icon>
                <span *ngIf="!reportFileName">Sélectionner le fichier du rapport (PDF, JPG, PNG)</span>
                <span *ngIf="reportFileName">{{ reportFileName }}</span>
              </label>
            </div>
          </div>

          <!-- Invoice file upload - shown when no invoice file exists or showInvoiceUpload is true -->
          <div class="file-upload-box" *ngIf="!hasInvoiceFile() || showInvoiceUpload">
            <h4>
              <fa-icon [icon]="faFileInvoice"></fa-icon>
              Facture
            </h4>
            <div class="file-upload-area" [class.has-file]="invoiceFileName">
              <input
                type="file"
                id="invoice-file"
                class="file-upload-input"
                (change)="onInvoiceFileSelected($event)"
                accept=".pdf,.jpg,.jpeg,.png"
                [disabled]="isUploading"
              >
              <label for="invoice-file" class="file-upload-label">
                <fa-icon [icon]="faUpload"></fa-icon>
                <span *ngIf="!invoiceFileName">Sélectionner le fichier de la facture (PDF, JPG, PNG)</span>
                <span *ngIf="invoiceFileName">{{ invoiceFileName }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Upload button - only shown if at least one file is selected -->
        <div class="action-buttons" *ngIf="selectedReportFile || selectedInvoiceFile">
          <button
            class="btn upload-btn"
            [disabled]="(!selectedReportFile && !selectedInvoiceFile) || isUploading"
            (click)="uploadFiles()"
          >
            <fa-icon *ngIf="!isUploading" [icon]="faUpload"></fa-icon>
            <fa-icon *ngIf="isUploading" [icon]="faSpinner" class="fa-spin"></fa-icon>
            {{ isUploading ? 'Téléchargement en cours...' : 'Télécharger les Fichiers' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Instructions -->
  <div class="instructions">
    <h4>
      
      Instructions:
    </h4>
    <ul>
      <li>Les fichiers doivent être au format PDF ou image (JPG, JPEG, PNG)</li>
      <li>Taille maximale des fichiers: 25 MB</li>
      <li>Vous pouvez télécharger le rapport et la facture séparément ou ensemble</li>
       
    </ul>
  </div>
</div>
