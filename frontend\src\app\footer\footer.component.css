@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700;400;600&display=swap');

/* ✅ Full-width footer */
.footer-section {
    background: white;
    color: #5a5a5a;
    width: 90%;
    margin: 0 auto;      /* Center horizontally */
    max-width: none;
    display: flex;
       /* Override any max-width constraints */
    padding: 1.5rem 0;   /* Remove horizontal padding if needed */
    padding: 1.5rem 2%; /* Reduced padding */
    font-family: 'Poppins', sans-serif;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* ✅ Full-width container */
.footer-container {
    max-width: 100%;
    margin: 0 auto;
    width: 100%;
    padding: 0 2%; /* Optional spacing inside */
    display: flex;
    flex-direction: column; /* Maintain original layout */
}
/* ============================================= */
/*        Bouton "Retour en haut"               */
/* ============================================= */

.back-to-top {
  display: inline-block;
  margin-top: 1.875rem; /* Augmentation de la marge supérieure */
  font-size: clamp(0.9375rem, calc(0.6rem + 0.5vw), 1.125rem); /* Augmentation de la taille du texte */
  color: #2496d3;
  text-decoration: none;
  border: 1px solid #2496d3;
  padding: 0.625rem 1.25rem; /* Augmentation des paddings pour un bouton plus large */
  border-radius: 0.375rem; /* Coins arrondis plus grands */
  transition: background-color 0.3s ease-in-out;
}

.back-to-top:hover,
.back-to-top:focus {
  background-color: #2496d3;
  color: #fff;
  outline: none;
}



/* ✅ Compact grid layout */
.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 5rem;
    align-items: start;
}

/* ✅ Smaller headings */
.footer-heading {
    font-size: clamp(0.875rem, 2vw, 1rem);
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 0.3rem;
    margin-bottom: 0.6rem;
}

/* ✅ Reduced text size */
.footer-text {
    font-size: clamp(0.75rem, 1.2vw, 0.875rem);
    line-height: 1.4;
    margin-bottom: 0.4rem;
}

/* ✅ Icon styling */
.icon {
    font-size: 1.1em;
    margin-right: 0.5rem;
    color: #2496d3;
    vertical-align: middle;
}

/* Style for FontAwesome icons */
fa-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.2em;
    height: 1.2em;
}

/* ✅ Link styling */
.footer-links a {
    text-decoration: none;
    font-size: clamp(0.75rem, 1.2vw, 0.875rem);
    color: #000000;
    font-weight: 600;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: #2496d3;
}

/* ✅ Contact formatting */
.footer-column.contact {
    text-align: left;
}

/* ✅ Compact copyright section */
.footer-bottom {
    padding: 1rem 0;
    border-top: 1px solid #f5f5f5;
    margin-top: 1.2rem;
    text-align: center;
    font-size: 0.875rem;
}

.brand {
    font-weight: 700;
    color: #2496d3;
}

/* ✅ Responsive adjustments */
@media (max-width: 768px) {
    .footer-grid {
        grid-template-columns: 1fr;
    }

    .footer-heading {
        margin-bottom: 0.5rem;
    }

    .footer-text {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .footer-section {
        padding: 1rem 3%;
    }

    .footer-heading {
        font-size: 1rem;
    }

    .footer-text {
        font-size: 0.7rem;
    }
}
