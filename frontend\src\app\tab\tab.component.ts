import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Sample {
  type: string;
  parametres: string;
  qte: string;
  conditions: string;
}

@Component({
    selector: 'app-tab',standalone: true,
    imports: [CommonModule],
    templateUrl: './tab.component.html',
    styleUrl: './tab.component.css'
})
export class TabComponent {
  samples: Sample[] = [
    { type: 'POISSONS', parametres: 'P, L, AG; HC: HIS; H; C; PH; ABVT; AA, TMA', qte: '50G CHAIR MF', conditions: 'POUR TOUTES LES ANALYSES' },
    { type: 'MOLLUSQUE (COQUILLAGE)', parametres: 'BLOX', qte: '100G CHAIR MF OU 100-200 IND. (MOULE, PALOURDE) / 20-30 IND. (HUÎTRE)', conditions: 'CONDITIONS ISOTHERMES AVEC DE LA GLACE OU CONGELÉ' },
    { type: 'MOLLUSQUE (SEICHE, POULPE)', parametres: 'P, L, AG; HC: HIS; H; C; PH; ABVT; AA, TMA', qte: '50G MF', conditions: 'POUR TOUTES LES ANALYSES' },
    { type: 'CRUSTACÉS (CREVETTES, CRABES)', parametres: 'P, L, AG; HC: HIS; H; C; PH; ABVT; AA, TMA', qte: '50G MF', conditions: 'POUR TOUTES LES ANALYSES' },
    { type: 'ALGUES', parametres: 'P, L, AG; HC: H; C; PH; AA', qte: '100G MF / 50G MS', conditions: 'PRÉFÉRENCE FINEMENT BROYÉE' },
    { type: 'MATRICE SÉCHÉE PRODUIT DE LA MER', parametres: 'P, L, AG; HC: HIS; H; C; PH; AA, TMA', qte: '50G MS', conditions: 'POUR TOUTES LES ANALYSES' }
  ];

  sortTable(column: keyof Sample) {
    this.samples.sort((a, b) => a[column].localeCompare(b[column]));
  }
}
