import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ResultsService } from '../services/results.service';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faEye,
  faSearch,
  faCalendarAlt,
  faFilter,
  faTimes,
  faCircleCheck,
  faCircleXmark,
  faCircleExclamation,
  faSpinner,
  faCircleDot,
  faEraser
} from '@fortawesome/free-solid-svg-icons';
import { NgxPaginationModule } from 'ngx-pagination';

@Component({
  selector: 'app-results-list',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule, NgxPaginationModule],
  templateUrl: './results-list.component.html',
  styleUrl: './results-list.component.css'
})
export class ResultsListComponent implements OnInit {
  // Font Awesome icons
  faEye = faEye;
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faFilter = faFilter;
  faTimes = faTimes;
  faCircleCheck = faCircleCheck;
  faCircleXmark = faCircleXmark;
  faCircleExclamation = faCircleExclamation;
  faSpinner = faSpinner;
  faCircleDot = faCircleDot;
  faEraser = faEraser;

  // Data properties
  reports: any[] = [];
  filteredReports: any[] = [];
  isLoading = true;
  showLoadingModal = true;
  hasError = false;

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Filter properties
  searchTerm: string = '';
  selectedDate: string = '';
  selectedStatus: string = '';

  constructor(
    private resultsService: ResultsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadSentRapports();
  }

  loadSentRapports(): void {
    this.isLoading = true;
    this.showLoadingModal = true;
    this.hasError = false;

    this.resultsService.getSentRapports().subscribe({
      next: (data) => {
        this.reports = data.map(report => {
         

          return {
            ...report,
            status: this.translateStatus(report.status),
            status_director: this.translateStatus(report.status_director),
            validation: this.translateValidation(report.validation)
          };
        });
        this.filteredReports = [...this.reports];
        console.log('Rapports:', this.reports);

        // Log the first report to check its structure
        if (this.reports.length > 0) {
          console.log('First report structure:', JSON.stringify(this.reports[0]));
        }

        // Hide loading indicators
        this.isLoading = false;
        this.showLoadingModal = false;
      },
      error: (error) => {
        console.error('Error fetching rapports:', error);
        this.isLoading = false;
        this.showLoadingModal = false;
        this.hasError = true;
      }
    });
  }

  viewRapportDetails(demandeId: string): void {
    if (!demandeId) {
      console.error('Demande ID is missing or null');
      return;
    }
    console.log('Navigating to resultats with demande ID:', demandeId);
    this.router.navigate(['/resultats', demandeId]);
  }

  // Helper methods for translating status values
  translateStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'sent': 'Envoyé',
      'not_sent': 'Non envoyé'
    };
    return statusMap[status] || status;
  }

  translateValidation(validation: number): string {
    if (validation === 1) {
      return 'Validé';
    } else if (validation === 0) {
      return 'Rejeté';
    } else {
      return 'En attente';
    }
  }

  // Filter methods
  onFilterChange(): void {
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredReports = this.reports.filter(report => {
      // Filter by search term (demande ID)
      const matchesSearch = this.searchTerm ?
        report.demande_id.toString().toLowerCase().includes(this.searchTerm.toLowerCase()) :
        true;

      // Filter by date
      let matchesDate = true;
      if (this.selectedDate) {
        const reportDate = new Date(report.reception_date).toISOString().split('T')[0];
        matchesDate = reportDate === this.selectedDate;
      }

      // Filter by validation status
      let matchesStatus = true;
      if (this.selectedStatus) {
        if (this.selectedStatus === 'validated') {
          matchesStatus = report.validation === 'Validé';
        } else if (this.selectedStatus === 'rejected') {
          matchesStatus = report.validation === 'Rejeté';
        } else if (this.selectedStatus === 'pending') {
          matchesStatus = report.validation === 'En attente';
        }
      }

      return matchesSearch && matchesDate && matchesStatus;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.selectedStatus = '';
    this.filteredReports = [...this.reports];
  }
}
