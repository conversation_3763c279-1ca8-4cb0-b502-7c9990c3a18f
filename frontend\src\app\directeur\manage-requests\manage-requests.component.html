<div class="requests-container">
  <h2>📄 Gestion des demandes de dérogation</h2>

  <table>
    <thead>
      <tr>
        <th><PERSON>umé<PERSON></th>
        <th>Client</th>
        <th>Date</th>
        <th>Masse demandée</th>
        <th>Masse minimale</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let request of derogationRequests">
        <td>{{ request.id }}</td>
        <td>{{ request.clientName }}</td>
        <td>{{ request.requestDate }}</td>
        <!-- Masse demandée avec unité dynamique -->
        <td [class.low-mass]="request.requestedMass < request.minimumMass">
          {{ request.requestedMass }} {{ getUnit(request.sampleType) }}
        </td>
        <!-- Masse minimale avec unité dynamique -->
        <td>{{ request.minimumMass }} {{ getUnit(request.sampleType) }}</td>
        <td class="actions-container">
          <button class="details-btn" (click)="viewDerogationDetails(request)">📄 Voir détails</button>
          <button class="validate-btn" (click)="validateRequest(request)" [disabled]="request.status !== 'pending'">✅ Valider</button>
          <button class="reject-btn" (click)="rejectRequest(request)" [disabled]="request.status !== 'pending'">❌ Rejeter</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
