<div class="facture-table-container">
  <h2>Liste des factures</h2>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro ou client:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchDemande"
          (input)="applyFilter()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="applyFilter()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="en_attente">En attente de paiement</option>
        <option value="paye">Payée</option>
        <option value="paiement_rejete">Paiement rejeté</option>
      </select>
    </div>
    <button (click)="clearFilters()" class="btn-clear"><fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>Effacer les filtres</button>
  </div>

  <!-- Tableau des factures -->
  <table class="facture-table">
    <thead>
      <tr>
        <th>Numéro de demande</th>
        <th>Montant total (DT)</th>
        <th>Date de création</th>
        <th>Statut de paiement</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="isLoading" class="loading-row">
        <td colspan="5" class="text-center">
          <div class="spinner-container">
            <div class="spinner"></div>
            <span>Chargement...</span>
          </div>
        </td>
      </tr>

      <!-- Message quand aucune facture n'est trouvée -->
      <tr *ngIf="!isLoading && filteredFactures.length === 0" class="empty-row">
        <td colspan="5" class="text-center">
          Aucune facture disponible.
        </td>
      </tr>

      <!-- Affichage des factures -->
      <ng-container *ngIf="!isLoading && filteredFactures.length > 0">
        <tr *ngFor="let facture of filteredFactures | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }">
          <td>{{ facture.demande }}</td>
          <td>{{ facture.total_amount }}</td>
          <td>{{ facture.facture_date }}</td>
          <td>
            <span class="status" [ngClass]="facture.status">
              {{ facture.status === 'en_attente' ? 'En attente de paiement' :
                 facture.status === 'paye' ? 'Payée' :
                 facture.status === 'paiement_rejete' ? 'Paiement rejeté' :
                 facture.status }}
            </span>
           
          </td>
          <td>
            <button (click)="viewDetails(facture.id)" class="btn-details">
              <fa-icon [icon]="faEye" style="margin-right: 10px;"></fa-icon>Voir détails
            </button>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    *ngIf="filteredFactures.length > 0"
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>


</div>
