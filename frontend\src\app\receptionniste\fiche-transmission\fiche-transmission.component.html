<div class="fiche-transmission-container">
  <!-- Bouton de retour -->
  

  <!-- Titre principal -->
  <h3>📑 Détails de la Fiche de Transmission n°{{ ficheTransmissionId }}</h3>

  <!-- ✅ Sous-titre / résumé -->
  <div class="fiche-summary">
   
  </div>

  <!-- Indicateur de chargement -->
  <div *ngIf="loading" class="loading-message">
    ⏳ Chargement en cours...
  </div>

  <!-- Message d’erreur, s’il y en a -->
  <div *ngIf="errorMessage" class="error">
    {{ errorMessage }}
  </div>

  <!-- TABLEAU AFFICHÉ UNIQUEMENT S’IL Y A DES DONNÉES -->
  <table *ngIf="!loading && !errorMessage && fiches.length > 0">
    <thead>
      <tr>
        <th>ID Fiche</th>
        <th>Code Laboratoire</th>
        <th>Nature Échantillon</th>
        <th>Masse (g)</th>
        <th>Date Transmission</th>
        <th>Date Remise Résultats</th>
        <th>Analyses <PERSON></th>
        <th>Observations</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let fiche of fiches; let i = index">
        <td>{{ fiche.id }}</td>
        <td>{{ fiche.code_laboratoire }}</td>
        <td>{{ fiche.nature_echantillon }}</td>
        <td>{{ fiche.masse_echantillon }}</td>
        <td>{{ fiche.date_transmission || '—' }}</td>
        <td>{{ fiche.date_remise_resultats || '—' }}</td>
        <td>
          <div class="analyses-list">
            <span
              *ngFor="let analysis of fiche.analyses_demandees"
              class="analysis-badge"
            >
              {{ analysis }}
            </span>
          </div>
        </td>
        <td>
          <!-- Si 'RAS' on peut afficher un tag vert, sinon '--' si vide -->
          <ng-container *ngIf="fiche.observations; else noObs">
            <span
              *ngIf="fiche.observations === 'RAS'; else defaultObs"
              class="observations-tag tag-green"
            >
              RAS
            </span>
            <ng-template #defaultObs>
              {{ fiche.observations }}
            </ng-template>
          </ng-container>
          <ng-template #noObs>
            —
          </ng-template>
        </td>
      </tr>
    </tbody>
  </table>

  <!-- Message si aucune fiche n’est trouvée -->
  <div *ngIf="!loading && !errorMessage && fiches.length === 0" class="no-data">
    ❌ Aucune fiche trouvée.
  </div>

  <!-- Petit bloc de résumé/stats en dessous du tableau -->
  <div *ngIf="fiches.length > 0" class="fiche-stats">
    <p>
      <strong>Total Échantillons :</strong> {{ fiches.length }}
    </p>
    <!-- Bouton Imprimer si vous voulez -->
    <button class="print-btn" (click)="printFiche()">
      Imprimer la fiche
    </button>
  </div>
 
</div>
