/* Notification Container */
.notification-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  margin-right: 20px;
}
.notification-container:hover .bell-icon {
  transform: rotate(15deg);
}

/* Bell Icon */
.bell-icon {
  position: relative;
  padding: 8px;
  transition: all 0.3s ease;
}

.bell-svg {
  fill: #666;
  transition: fill 0.3s ease;
}
/* Prevent wrapping for specific columns */
.notification-table th.no-wrap,
.notification-table td:nth-child(1), /* Catégorie column */
.notification-table td:nth-child(3) /* Date column */ {
    white-space: nowrap;
}

/* Optional: Adjust table layout if needed */
.notification-table {
    table-layout: auto; /* or 'fixed' depending on your preference */
    width: 100%;
}

/* Ensure the table container handles overflow gracefully */
.table-container {
    overflow-x: auto; /* Adds horizontal scroll if content overflows */
}

/* Optional: Set minimum widths to ensure readability */
.notification-table th.no-wrap,
.notification-table td:nth-child(1),
.notification-table td:nth-child(3) {
    min-width: 120px; /* Adjust as needed */
}
.bell-icon:hover .bell-svg {
  fill: #007bff;
}

.badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4444;
  color: white;
  font-size: 12px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Notification Dropdown */
.notification-dropdown {
  position: absolute;
  top: 40px;
  right: 0;
  width: 800px;
  max-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
      opacity: 0;
      transform: translateY(-10px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

/* Header */
.notification-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.clear-btn {
  background: #f5f5f5;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #e5e5e5;
}

/* Table Container */
.table-container {
  max-height: 350px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc #f5f5f5;
}

.notification-table {
  width: 100%;
  border-collapse: collapse;
}

.notification-table th {
  background: #f8f9fa;
  padding: 12px 15px;
  text-align: left;
  font-size: 13px;
  font-weight: 600;
  color: #555;
  text-transform: uppercase;
  position: sticky;
  top: 0;
  z-index: 1;
}

.notification-table td {
  padding: 12px 15px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.notification-table tr {
  transition: background 0.2s ease;
}

.notification-table tr.unread {
  background: #f0f8ff;
}

.notification-table tr:hover {
  background: #f5f5f5;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.unread-tag {
  background: #e6f3ff;
  color: #007bff;
}

/* No Notifications */
.no-notifications {
  padding: 30px;
  text-align: center;
  color: #666;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.empty-icon {
  fill: #999;
  opacity: 0.7;
}

/* Footer Actions */
.footer-actions {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-custom {
  font-size: 13px;
}

.voir-plus-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.voir-plus-btn:hover {
  background: #0056d2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-dropdown {
      width: 100%;
      max-width: 400px;
  }

  .notification-table td {
      font-size: 13px;
      padding: 10px;
  }
}