<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
class NotificationController extends Controller
{


    public function getValidatedNotifications()
    {
        // Get all notifications with title "Nouvelle Demande"
        $userId = auth('sanctum')->user()->id;
        $notifications = Notification::where('title', 'Validation de la Demande')
        ->where('user_id', $userId)
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getResultsNotification()
    {

        $notifications = Notification::where('title', 'Résultats disponibles')
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getDevisNotification()
    {

        $notifications = Notification::where('title', 'Devis Payé')

        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getDevisValidationNotification()
    {
        $userId = auth('sanctum')->user()->id;
        $notifications = Notification::where('title', 'Paiement Approuvé')
        ->where('user_id', $userId)
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getDevisRejectionNotification()
    {

        $notifications = Notification::where('title', 'Paiement Rejeté')
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }

    public function getPaymentApprovedNotification()
    {
        $notifications = Notification::where('title', 'Paiement Approuvé')
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getRapportClientNotification()
    {
        // Get the authenticated user
        $userId = auth('sanctum')->user()->id;

        // Get notifications with title "Rapport Prêt" for the current user
        $notifications = Notification::where('title', 'Rapport Prêt')
            ->where('user_id', $userId)
            ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }

    public function getPaymentRejectedNotification()
    {
        $notifications = Notification::where('title', 'Paiement Rejeté')
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getRapportNotificationsReceptionist()
    {

        $notifications = Notification::where('type', 'rapport receptionist')

        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getCheckedRapportNotificationsReceptionist()
    {

        $notifications = Notification::where('type', 'rapport validation')

        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getRapportNotificationsDirector()
    {
        // Get all notifications with title "Nouvelle Demande"

        $notifications = Notification::where('type', 'rapport')

        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getFicheTransmissionNotifications()
    {
        // Get all notifications with title "Nouvelle Demande"

        $notifications = Notification::where('title', 'Fiche de transmission')
        ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getBringSample($demandeId)
    {
        try {
            // Fetch the notification where demande_id matches
            $userId = auth('sanctum')->user()->id;
            $notifications = Notification::where('title', 'Validation de la Demande')
            ->where('user_id', $userId)
            ->get();

            // If no notification is found, return a 404 response


            // Return the notification as JSON
            return response()->json($notifications, 200);
        } catch (\Exception $e) {
            // Handle any errors and return a 500 response
            return response()->json([
                'message' => 'Error fetching notification',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function getRejectedReceptionist()
    {

            // Fetch the notification where demande_id matches
            $notification = Notification::where('title', 'Demande rejetée')->get();

            return response()->json([
                'notifications' => $notification,
            ]);

    }
    public function getValidatedReceptionist()
    {

            // Fetch the notification where demande_id matches
            $notification = Notification::where('type', 'demande validée receptionniste')->first();

            return response()->json([
                'notifications' => $notification,
            ]);

    }
    public function getValidatedClient()
    {

            $userId = auth('sanctum')->user()->id;
            $notifications = Notification::where('title', 'Validation de la Demande')
            ->where('user_id', $userId)
            ->get();

            return response()->json([
                'notifications' => $notifications,
            ]);

    }
    public function getRejectedClient()
    {
            $userId = auth('sanctum')->user()->id;
            $notifications = Notification::where('title', 'Rejet de la Demande')
            ->where('user_id', $userId)
            ->get();

            // Return the notification as JSON
            return response()->json([
                'notifications' => $notifications,
            ]);

    }
    public function getNotificationByDemandeId($demandeId)
    {
        try {
            // Fetch the notification where demande_id matches
            $notification = Notification::where('demande_id', $demandeId)->where('title', 'Validation de la Demande')->first();

            // If no notification is found, return a 404 response
            if (!$notification) {
                return response()->json([
                    'message' => 'Notification not found for the given demande_id',
                ], 404);
            }

            // Return the notification as JSON
            return response()->json($notification, 200);
        } catch (\Exception $e) {
            // Handle any errors and return a 500 response
            return response()->json([
                'message' => 'Error fetching notification',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function getBringSampleNotifications()
    {
        // Get the authenticated user
        $userId = auth('sanctum')->user()->id;

        // Get notifications with title "Demande Échantillon" for the current user
        $notifications = Notification::where('title', 'Demande Échantillon')
                                     ->where('user_id', $userId)
                                     ->get();

        return response()->json(['user_id' => $userId,
            'notifications' => $notifications,
        ]);
    }
    public function getValidNotifications()
    {
        // Get the authenticated user
        $userId = auth('sanctum')->user()->id;

        // Get notifications with title "Demande Échantillon" for the current user
        $notifications = Notification::where('title', 'Demande Échantillon')
                                     ->where('user_id', $userId)
                                     ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function getValidatedFromDirectorNotifications()
{
    // Get all notifications with title "Demande Échantillon"
    $notifications = Notification::where('title', 'Derogation validated')->get();

    return response()->json([
        'notifications' => $notifications,
    ]);
}
    public function getRejectedFromDirectorNotifications()
{
    // Get all notifications with title "Demande Échantillon"
    $notifications = Notification::where('title', 'Demande reject')->get();

    return response()->json([
        'notifications' => $notifications,
    ]);
}
public function getRejectedFromDirectorNotificationsRceptionist()
{
    // Get all notifications with title "Demande Échantillon"
    $notifications = Notification::where('title', 'Demande reject receptionist')->get();

    return response()->json([
        'notifications' => $notifications,
    ]);
}
    public function markAsRead($notificationId)
{
    try {
        // Get the authenticated user
        $user = auth('sanctum')->user();

        if (!$user) {
            return response()->json(['message' => 'User not authenticated.'], 401);
        }

        // Find the notification by its ID and user_id
        $notification = Notification::where('id', $notificationId)
                                   ->where('user_id', $user->id)
                                   ->first();

        // Check if the notification exists and belongs to the user
        if (!$notification) {
            return response()->json(['message' => 'Notification not found or you do not have permission to access it.'], 404);
        }

        // Mark the notification as read
        $notification->is_read = 1;
        $notification->save();

        return response()->json(['message' => 'Notification marked as read.']);

    } catch (\Exception $e) {
        return response()->json([
            'message' => 'Error marking notification as read.',
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Delete a specific notification (only if it belongs to the authenticated user)
 */
public function deleteNotification($notificationId)
{
    try {
        // Get the authenticated user
        $user = auth('sanctum')->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated.'
            ], 401);
        }

        // Find the notification by its ID and user_id
        $notification = Notification::where('id', $notificationId)
                                   ->where('user_id', $user->id)
                                   ->first();

        // Check if the notification exists and belongs to the user
        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found or you do not have permission to delete it.'
            ], 404);
        }

        // Delete the notification
        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification supprimée avec succès.'
        ], 200);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error deleting notification.',
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Delete all notifications for the authenticated user only
 */
public function deleteAllNotifications()
{
    try {
        // Get the authenticated user
        $user = auth('sanctum')->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated.'
            ], 401);
        }

        // Get count of user's notifications before deletion
        $deletedCount = Notification::where('user_id', $user->id)->count();

        // Delete only the authenticated user's notifications
        Notification::where('user_id', $user->id)->delete();

        return response()->json([
            'success' => true,
            'message' => "Toutes vos notifications ont été supprimées avec succès. ($deletedCount notifications supprimées)",
            'deleted_count' => $deletedCount
        ], 200);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error deleting your notifications.',
            'error' => $e->getMessage()
        ], 500);
    }
}

}
