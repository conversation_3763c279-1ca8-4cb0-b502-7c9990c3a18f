<div class="admin-panel">
  <h2>Validation des Rapports</h2>

  <table class="reports-table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Nom du Client</th>
        <th>Date</th>
        <th>Statut</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- On parcourt les rapports provenant du TS : this.reports -->
      <tr *ngFor="let report of reports">
        <td>{{ report.id }}</td>
        <td>{{ report.clientName }}</td>
        <!-- Affiche la date du rapport -->
        <td>{{ report.date }}</td>
        <td>{{ report.status }}</td>
        <td class="action-buttons">
          <!-- On affiche Valider / Rejeter seulement si status = 'En attente' -->
          <button
            *ngIf="report.status === 'En attente'"
            class="approve-btn"
            (click)="approveReport(report.id)"
          >
            Valider
          </button>
          <button
            *ngIf="report.status === 'En attente'"
            class="reject-btn"
            (click)="rejectReport(report.id)"
          >
            Rejeter
          </button>

          <!-- Bouton "Voir Détail" qui appelle viewReportDetails(report.id) -->
          <button class="details-btn" (click)="viewReportDetails(report.id)">
            Voir détails
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
