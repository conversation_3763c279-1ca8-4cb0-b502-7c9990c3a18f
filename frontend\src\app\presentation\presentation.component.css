@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ SECTION PRINCIPALE */
.presentation-section {
  min-height: 100vh;
  padding: 90px 5%;
  background: white;
  color: black;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  animation: fadeInBg 2s ease-in-out;
}

/* ✅ TITRE PRINCIPAL */
.presentation-title {
  font-family: 'Poppins', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 10px;
 
 
  -webkit-background-clip: text;

}

/* ✅ Texte large */
.text-large {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  line-height: 1.8;
  width: 90%;
  color: black;
  animation: fadeInUp 1s ease-in-out forwards;
}

/* ✅ GRID DES CARTES INTERACTIVES */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 50px;
  justify-content: center;
  align-items: center;
  padding: 40px;
  text-align: center;
}

/* ✅ Centrer la dernière carte */
.feature-grid .feature-card:nth-child(4) {
  grid-column: 2 / 3;
}

/* ✅ STYLE DES CARTES */
.feature-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.4s ease-in-out, box-shadow 0.4s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  animation: fadeInUp 1s ease-in-out forwards;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/* ✅ Effet au survol */
.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.2);
}

/* ✅ Bordure en ciel */
.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: #2496d3;
  transform: scaleX(0);
  transition: transform 0.4s ease-in-out;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

/* ✅ ICÔNES AVEC BORDURE CIRCULAIRE FINE */
.feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 2px solid #2496d3;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  margin-bottom: 15px;
  transition: transform 0.3s ease-in-out;
}

/* ✅ Réduction de l'image pour bien l'afficher */
.feature-icon img {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

/* ✅ Effet au survol */
.feature-card:hover .feature-icon {
  transform: rotate(10deg);
}

/* ✅ TITRE DES CARTES */
.feature-card h3 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  color: black;
}

/* ✅ DESCRIPTION DES CARTES */
.feature-card p {
  font-size: 16px;
  color: black;
}

/* ✅ STYLE DU BOUTON */
.cta-button {
  display: inline-block;
  padding: 15px 30px;
  background: #2496d3;
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: bold;
  font-size: 18px;
  transition: all 0.4s ease-in-out;
  margin-top: 40px;
  text-align: center;
  animation: pulseGlow 2s infinite alternate;
  box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5);
}

/* ✅ Effet au survol du bouton */
.cta-button:hover {
  background: black;
  transform: scale(1.1);
  box-shadow: 0px 12px 35px rgba(0, 0, 0, 0.8);
}

/* ✅ ANIMATIONS */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes glowText {
  from { text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); }
  to { text-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5); }
}

@keyframes pulseGlow {
  0% { transform: scale(1); box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5); }
  50% { transform: scale(1.05); box-shadow: 0px 12px 25px rgba(36, 150, 211, 0.8); }
  100% { transform: scale(1); box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5); }
}
