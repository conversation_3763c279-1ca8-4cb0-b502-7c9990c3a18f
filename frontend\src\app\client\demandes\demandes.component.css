/* =========================
   Google Fonts
   ========================= */
   @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

   /* =========================
      Animations @keyframes
      ========================= */

   /* Animation d'apparition */
   @keyframes fadeIn {
     0% {
       opacity: 0;
       transform: translateY(10px);
     }
     100% {
       opacity: 1;
       transform: translateY(0);
     }
   }
   input.ng-invalid.ng-touched,
   select.ng-invalid.ng-touched {
     border: 1px solid red;
   }

   /* Error text for general form errors */
   .error-text {
     color: #dc3545;
     margin: 10px 0;

     font-weight: bold;
   }


   /* Glow text en bleu */
   @keyframes glowText {
     from {
       text-shadow:
         0 0 5px rgba(36, 150, 211, 0.5),
         0 0 10px rgba(36, 150, 211, 0.3);
     }
     to {
       text-shadow:
         0 0 10px rgba(36, 150, 211, 0.8),
         0 0 20px rgba(36, 150, 211, 0.6);
     }
   }

   /* Effet glowInput si besoin */
   @keyframes glowInput {
     from {
       box-shadow: 0 0 12px rgba(36, 150, 211, 0.6);
     }
     to {
       box-shadow: 0 0 20px rgba(36, 150, 211, 0.9);
     }
   }

   /* =========================
      Style global
      ========================= */
   body {
     margin: 0;
     padding: 0;
     background: #f9f9f9; /* Fond plus clair pour contraster le container */
     font-family: 'Poppins', sans-serif;
     color: #333;
   }

   /* =========================
      Conteneur principal
      ========================= */
   .demandes-container {
     box-shadow: 0 8px 20px rgba(135, 206, 250, 0.3); /* Bleu ciel (light) */
     transition: box-shadow 0.3s;
     width: 90%;
     max-width: 1100px;
     margin: 40px auto;
     background: #fff;
     border-radius: 12px;
     padding: 70px 5%;
     display: flex;
     flex-direction: column;
     align-items: center;
     text-align: center;
     min-height: 50vh;

     /* Animation fadeIn */
     animation: fadeIn 0.6s ease-in-out;
   }
   /* Facultatif : hover sur le container */
   .demandes-container:hover {
     box-shadow: 0 8px 20px rgba(135, 206, 250, 0.5); /* Intensification au survol */
   }

   /* =========================
      Titre principal
      ========================= */
      .demandes-container h2 {
        font-family: 'Orbitron', sans-serif;
        font-size: 28px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 2px;
        color: #2496d3;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding-bottom: 30px; /* Moved padding-bottom here */
        margin-bottom: 20px;
    }

    .demandes-container h2::after {
        content: "";
        position: absolute;
        bottom: 0; /* Aligns with the bottom of h2, respecting padding */
        left: 50%;
        transform: translateX(-50%);
        width: 130px;
        height: 4px;
        background: #2496d3;
        border-radius: 2px;
        animation: underlineGlow 1.5s infinite alternate;
    }
   /* =========================
      Informations Client
      ========================= */
   .client-info {
     width: 80%;
     background: #f8f9fa;
     padding: 15px;
     border-radius: 8px;
     margin-bottom: 30px;
     box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
     text-align: left; /* Permet de left-align le contenu */
   }

   .client-row {
     display: flex;
     justify-content: space-between;
     padding: 5px 15px;
     font-size: 16px;
     font-weight: 600;
   }

   .client-row span {
     display: flex;
     align-items: center;
   }

   /* =========================
      Mode de règlement (hors form)
      ========================= */
   .input-group-half {
     display: grid;
     grid-template-columns: 250px 1fr;
     gap: 10px;
     width: 36%;
     min-width: 280px;
     margin-bottom: 15px;
   }

   /* =========================
      Notification de soumission
      ========================= */
   .notification-overlay {
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-color: rgba(0, 0, 0, 0.7);
     display: flex;
     justify-content: center;
     align-items: center;
     z-index: 1000;
     backdrop-filter: blur(3px);
     animation: fadeIn 0.3s ease-in-out;
   }

   .notification {
     background-color: white;
     border-radius: 10px;
     padding: 30px;
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
     text-align: center;
     max-width: 400px;
     width: 90%;
     animation: fadeIn 0.5s ease-in-out;
   }

   .notification-icon {
     color: #28a745;
     font-size: 50px;
     margin-bottom: 20px;
   }

   .notification-message {
     color: #155724;
     font-size: 18px;
     font-weight: bold;
     margin-bottom: 20px;
   }

   .notification-button {
     background: linear-gradient(to right, #28a745, #218838);
     color: white;
     border: none;
     padding: 10px 20px;
     border-radius: 5px;
     cursor: pointer;
     font-weight: bold;
     transition: all 0.3s ease;
   }

   .notification-button:hover {
     transform: translateY(-2px);
     box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
   }

   /* =========================
      Loading Spinner Overlay
      ========================= */
   .loading-overlay {
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-color: rgba(0, 0, 0, 0.7);
     display: flex;
     justify-content: center;
     align-items: center;
     z-index: 1000;
     backdrop-filter: blur(3px);
     animation: fadeIn 0.3s ease-in-out;
   }

   .loading-popup {
     background-color: white;
     border-radius: 10px;
     padding: 30px;
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
     text-align: center;
     max-width: 400px;
     width: 90%;
   }

   .loading-popup h3 {
     margin-top: 20px;
     color: #333;
     font-size: 18px;
   }

   .spinner {
     border: 4px solid rgba(0, 0, 0, 0.1);
     border-radius: 50%;
     border-top: 4px solid #2496d3;
     width: 50px;
     height: 50px;
     animation: spin 1s linear infinite;
     margin: 0 auto;
   }

   @keyframes spin {
     0% { transform: rotate(0deg); }
     100% { transform: rotate(360deg); }
   }

   /* =========================
      Formulaire global
      ========================= */
   form {
     width: 100%;
   }

   /* Groupes de champs */
   .input-group {
     display: grid;
     grid-template-columns: 250px 1fr;
     gap: 10px;
     width: 100%;
     margin-bottom: 15px;
     align-items: start;
   }

   /* Labels */
   .input-group label {
     font-weight: bold;
     font-size: 16px;
     color: #000;
     text-align: left;
     display: flex;

   }

   /* Label container for error messages */
   .label-container {
     display: flex;
     flex-direction: column;
     margin-bottom: 5px;
   }

   /* Error messages */
   .error {
     color: #dc3545;
     font-size: 1em;
     font-weight: normal;
     margin-top: 4px;
     margin-bottom: 4px;
     padding-left: 60px;
     display: block;
   }

   /* =========================
      Carte des échantillons
      ========================= */
   .echantillon-card {
     background: #fff;
     border-radius: 12px;
     padding: 25px;
     box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
     margin: 0 auto 20px auto;
     max-width: 900px;
     width: 100%;
     transition: transform 0.4s, box-shadow 0.4s;
   }

   .echantillon-card:hover {
     transform: scale(1.01);
     box-shadow: 0 12px 25px rgba(36, 150, 211, 0.5);
   }

   /* Texte d'instruction pour champs obligatoires */
   .required-note {
     font-size: 14px;
     color: #666;
     margin-bottom: 15px;
     font-style: italic;
     text-align: left;
   }

   .required-star {
     color: #e74c3c; /* Rouge plus moderne */
     font-weight: bold;
     margin-left: 4px; /* Espace pour bien séparer l'étoile du label */
   }

   /* =========================
      Champs (input, select, textarea)
      ========================= */
   input[type="date"],
   select {
     -webkit-appearance: none;
     -moz-appearance: none;
     appearance: none;
   }

   input[type="text"],
   input[type="number"],
   input[type="date"],
   select,
   textarea {
     padding: 12px;
     border: 1px solid #ccc;     /* Bordure plus douce */
     border-radius: 8px;
     font-size: 16px;
     width: 100%;
     box-sizing: border-box;
     transition: border-color 0.3s, box-shadow 0.3s;
   }

   /* Style spécifique pour le champ d'observation */
   textarea[formControlName="observation"] {
     min-height: 80px;
     resize: vertical;
     line-height: 1.5;
     background-color: #f9f9f9;
     border: 1px solid #ddd;
   }

   input:hover,
   select:hover,
   textarea:hover {
     border-color: #2496d3;
   }

   input:focus,
   select:focus,
   textarea:focus {
     border-color: #2496d3;
     box-shadow: 0 0 8px rgba(36, 150, 211, 0.3);
     /* Optionnel : pour conserver l'animation "glowInput", décommentez ci-dessous
        animation: glowInput 0.8s infinite alternate;
     */
   }

   /* =========================
      Dropdown personnalisé
      ========================= */
   .custom-dropdown {
     position: relative;
     width: 100%;
   }

   .dropdown-toggle {
     padding: 12px;
     border: 1px solid #ccc;
     border-radius: 8px;
     font-size: 16px;
     background: #fff;
     display: flex;
     align-items: center;
     justify-content: space-between;
     cursor: pointer;
     width: 100%;
     box-sizing: border-box;
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap;
     box-shadow: 0 4px 8px rgba(36, 150, 211, 0.2);
     transition: border-color 0.3s, box-shadow 0.3s;
   }

   .dropdown-toggle:hover {
     background: #f0f8ff;
     border-color: #2496d3;
     box-shadow: 0 6px 12px rgba(36, 150, 211, 0.3);
   }

   .dropdown-list {
     display: none;
     position: absolute;
     width: 100%;
     background: #fff;
     border: 1px solid #ccc;
     border-radius: 8px;
     box-shadow: 0 8px 20px rgba(36, 150, 211, 0.2);
     max-height: 250px;
     overflow-y: auto;
     padding: 10px;
     z-index: 10;
     opacity: 0;
     transform: translateY(-10px);
     transition: opacity 0.3s, transform 0.3s;
   }

   .dropdown-list.open {
     display: block;
     opacity: 1;
     transform: translateY(0);
   }

   .dropdown-list label {
     display: flex;
     align-items: center;
     padding: 10px;
     cursor: pointer;
     border-radius: 4px;
     transition: background 0.2s;
   }

   .dropdown-list label:hover {
     background: #e0f4ff;
   }

   /* Larger checkboxes with gap */
   .dropdown-list label input[type="checkbox"] {
     width: 20px;
     height: 20px;
     margin-right: 12px;
     cursor: pointer;
   }

   /* Analysis item styling */
   .analysis-name {
     font-weight: bold;
     margin-right: 10px;
   }

   .analysis-price {
     color: #2496d3;
     font-weight: bold;
     margin-right: 10px;
   }

   .not-accredited {
     background-color: #f8f9fa;
     color: #6c757d;
     padding: 2px 8px;
     border-radius: 4px;
     font-size: 12px;
     font-style: italic;
   }

   /* Loading indicator in analyses dropdown */
   .loading-analyses {
     display: flex;
     align-items: center;
     justify-content: center;
     padding: 15px;
     color: #2496d3;
     font-weight: bold;
     gap: 10px;
   }

   /* No analyses message */
   .no-analyses {
     padding: 15px;
     text-align: center;
     color: #6c757d;
     font-style: italic;
   }

   /* =========================
      Boutons
      ========================= */
   .btns {
     display: flex;
     gap: 15px;
     justify-content: space-between;
     margin-top: 20px;
     flex-wrap: wrap;
   }

   .btn-group-left {
     display: flex;
     gap: 15px;
     flex-wrap: wrap;
   }

   button {
     padding: 14px 25px;
     font-size: 16px;
     font-weight: bold;
     border-radius: 50px;
     border: none;
     text-transform: uppercase;
     cursor: pointer;
     display: flex;
     align-items: center;
     justify-content: center;
     gap: 8px;
     transition: transform 0.3s, box-shadow 0.3s;
   }

   /* Style pour les icônes FontAwesome */
   fa-icon {
     display: inline-flex;
     align-items: center;
     justify-content: center;
   }

   /* Bouton "Ajouter un échantillon" */
   .btn-add {
     background: linear-gradient(to right, #2496d3, #1a73e8);
     color: #fff;
     box-shadow: 0 5px 15px rgba(36, 150, 211, 0.5);
   }
   .btn-add:hover {
     transform: translateY(-2px);
     box-shadow: 0 12px 35px rgba(36, 150, 211, 0.8);
   }

   /* Bouton "Supprimer" */
   .btn-delete {
     background: #dc3545;
     color: #fff;
     margin: 10px auto 0;
   }
   .btn-delete:hover {
     transform: translateY(-2px);
     background: #b22234;
   }

   /* Bouton "Réinitialiser" */
   .btn-reset {
     background: linear-gradient(to right, #6c757d, #495057);
     color: #fff;
     box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
   }
   .btn-reset:hover {
     transform: translateY(-2px);
     background: #495057;
     box-shadow: 0 8px 20px rgba(108, 117, 125, 0.6);
   }

   /* Bouton "Envoyer la demande" */
   .btn-submit {
     background: linear-gradient(to right, #28a745, #218838);
     color: #fff;
     box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
   }
   .btn-submit:hover {
     transform: translateY(-2px);
     background: #218838;
     box-shadow: 0 8px 20px rgba(40, 167, 69, 0.6);
   }

   /* =========================
      Responsive
      ========================= */
   @media (max-width: 768px) {
     /* On repasse la grille en mono-colonne */
     .input-group {
       grid-template-columns: 1fr;
     }

     .input-group-half {
       width: 100%;
       grid-template-columns: 1fr;
     }

     .client-info {
       width: 95%;
     }

     .client-row {
       flex-direction: column;
       text-align: left;
       gap: 8px;
     }

     .echantillon-card {
       width: 100%;
       padding: 15px;
       margin-bottom: 20px;
     }

     .btns {
       flex-direction: column;
       width: 100%;
     }

     .btn-group-left {
       flex-direction: column;
       width: 100%;
       gap: 10px;
     }
     button {
       width: 100%;
     }
   }
