/* Import Fonts and Icons */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* General Styles */
.analyst-dashboard {
  text-align: center;
  padding: 3rem 5%;
  font-family: 'Montserrat', sans-serif;
  background: white;
  color: black;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Title Container */
.dashboard-title-container {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.circle-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2496d3, #0a6aa1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 8px rgba(36, 150, 211, 0.3);
}

/* Dashboard Title */
.dashboard-title {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(1.8rem, calc(1vw + 1.2rem), 2.5rem);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.15rem;
  margin: 0;
  border-bottom: 2px solid #2496d3;
  padding-bottom: 0.4rem;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* User Information */
.user-info {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  color: #555;
}
.user-info p {
  margin: 0.5rem 0;
}
.user-info strong {
  color: #2496d3;
}

/* Statistics Section */
.statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.statistic-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.statistic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.statistic-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #2496d3;
}

.statistic-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.statistic-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2496d3;
  margin: 0;
}

/* Action Buttons */
.analyst-actions {
  display: flex;
  justify-content: center;
  gap: 1.25rem;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.btn-analyst {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.transmission {
  background: linear-gradient(to right, #2496d3, #0a6aa1);
}

.transmission:hover {
  background: linear-gradient(to right, #6eb5ff, #4d9de3);
}

.results {
  background: linear-gradient(to right, #28a745, #1e7e34);
}

.results:hover {
  background: linear-gradient(to right, #34ce57, #28a745);
}

/* Responsive Design */
@media (max-width: 57.8125rem) {
  .analyst-actions {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }
  .btn-analyst {
    width: 85%;
    padding: 0.8rem 1rem;
  }
  .analyst-dashboard {
    padding: 2rem 3%;
  }
}

@media (max-width: 37.5rem) {
  .btn-analyst {
    width: 100%;
    font-size: 0.9rem;
    padding: 0.7rem 0.8rem;
  }
  .dashboard-title {
    font-size: 1.3rem;
  }
  .analyst-dashboard {
    padding: 1.5rem 2%;
  }
}
