import { Component, AfterViewInit, ViewChildren, QueryList, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-laboratoire-gestion-analyse',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './laboratoire-gestion-analyse.component.html',
  styleUrls: ['./laboratoire-gestion-analyse.component.css']
})
export class LaboratoireGestionAnalyseComponent implements AfterViewInit {
  @ViewChildren('carouselImage') images!: QueryList<ElementRef>;
  currentIndex: number = 0;
  intervalId: any;

  constructor(private readonly router: Router) {}

  ngAfterViewInit() {
    this.startAutoSlide();
  }

  // Method to navigate to the accept section
  navigateToAccept() {
    console.log('Navigating to accept section');

    // First try direct scrolling if we're already on the home page
    const acceptElement = document.getElementById('accept');
    if (acceptElement) {
      console.log('Found accept element directly, scrolling to it');
      acceptElement.scrollIntoView({ behavior: 'smooth' });
      return;
    }

    // If not found, navigate to home page with fragment
    console.log('Navigating to home page with accept fragment');
    this.router.navigate(['/'], { fragment: 'accept' });
  }

  /** ✅ Démarrer le carrousel automatique */
  startAutoSlide() {
    this.intervalId = setInterval(() => this.nextImage(), 3000);
  }

  /** ✅ Arrêter le carrousel au survol */
  stopAutoSlide() {
    clearInterval(this.intervalId);
  }

  /** ✅ Afficher l'image suivante */
  nextImage() {
    const imagesArray = this.images.toArray();
    if (imagesArray.length > 0) {
      imagesArray[this.currentIndex].nativeElement.classList.remove('active');
      this.currentIndex = (this.currentIndex + 1) % imagesArray.length;
      imagesArray[this.currentIndex].nativeElement.classList.add('active');
    }
  }

  /** ✅ Afficher l'image précédente */
  prevImage() {
    const imagesArray = this.images.toArray();
    if (imagesArray.length > 0) {
      imagesArray[this.currentIndex].nativeElement.classList.remove('active');
      this.currentIndex = (this.currentIndex - 1 + imagesArray.length) % imagesArray.length;
      imagesArray[this.currentIndex].nativeElement.classList.add('active');
    }
  }

  /** ✅ Aller à une image spécifique via les indicateurs */
  goToImage(index: number) {
    const imagesArray = this.images.toArray();
    if (imagesArray.length > 0) {
      imagesArray[this.currentIndex].nativeElement.classList.remove('active');
      this.currentIndex = index;
      imagesArray[this.currentIndex].nativeElement.classList.add('active');
    }
  }
}
