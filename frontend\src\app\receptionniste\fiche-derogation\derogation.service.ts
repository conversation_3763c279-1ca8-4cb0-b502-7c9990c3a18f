import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { Derogation } from '../../../models/derogation.model';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DerogationService {
  private apiUrl = 'http://127.0.0.1:8000/api/receptionist/create-derogation/';

  constructor(private http: HttpClient) {}

  /**
   * ✅ Create a new derogation
   * @param derogation The derogation request data
   * @returns Observable with the response
   */
  createDerogation(derogation: Derogation, demandeId: string): Observable<any> {
    console.log('Submitting derogation:', derogation, 'to:', `${this.apiUrl}${demandeId}`);
  
    return this.http.post(`${this.apiUrl}${demandeId}`, { derogation }).pipe(
      catchError((error) => {
        console.error('Error from backend:', error);
        return throwError(() => error);
      })
    );
  }
  
  
}
