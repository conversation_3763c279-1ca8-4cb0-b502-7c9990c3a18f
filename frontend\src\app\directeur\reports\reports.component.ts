import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';  // pour [(ngModel)]
import { Router } from '@angular/router';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.css'],
})
export class ReportsComponent implements OnInit {
  reports: any[] = [];
  selectedReport: any = null;

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.loadReports();
  }

  /**
   * Charge un seul rapport pour le client "Naifer"
   * avec date = aujourd’hui - 5 jours
   */
  loadReports(): void {
    const dateObj = new Date();
    dateObj.setDate(dateObj.getDate() - 5); // On recule de 5 jours
    const dateStr = dateObj.toISOString().split('T')[0];
    // => "2025-03-01" par exemple

    // On force un seul rapport, clientName = "Naifer"
    this.reports = [
      {
        id: 1,
        clientName: 'Naifer',
        date: dateStr,
        status: 'En attente', // Sera modifié par l’administrateur
      },
    ];

    // Si vous voulez l’enregistrer automatiquement dans localStorage :
    localStorage.setItem('adminReports', JSON.stringify(this.reports));
  }

  // Sélectionner un rapport pour l'afficher en modal ET/OU en formulaire
  viewReport(report: any): void {
    this.selectedReport = report;
  }

  // Fermer le modal
  closeModal(): void {
    this.selectedReport = null;
  }

  // Valider un rapport
  approveReport(reportId: number): void {
    const report = this.reports.find((r) => r.id === reportId);
    if (report) {
      report.status = 'Validé';
      this.updateLocalStorage();
    }
  }

  // Rejeter un rapport
  rejectReport(reportId: number): void {
    const report = this.reports.find((r) => r.id === reportId);
    if (report) {
      report.status = 'Rejeté';
      this.updateLocalStorage();
    }
  }

  // Naviguer vers /receptionist/rapports/1
  viewReportDetails(reportId: number): void {
    this.router.navigate(['/receptionist/rapports', reportId]);
  }

  // Enregistrer les modifications faites dans selectedReport
  saveChanges(): void {
    // selectedReport fait partie de this.reports => On sauvegarde tout
    this.updateLocalStorage();
    // Fermer la fenêtre modale (facultatif)
    this.closeModal();
  }

  private updateLocalStorage(): void {
    localStorage.setItem('adminReports', JSON.stringify(this.reports));
  }
}
