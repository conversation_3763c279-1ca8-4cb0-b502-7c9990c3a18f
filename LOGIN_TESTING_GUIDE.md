# Login Endpoint Testing Guide

This guide explains how to test your login endpoint (`POST /api/login`) using different methods.

## Endpoint Details

- **URL**: `POST /api/login`
- **Controller**: `Auth<PERSON><PERSON>roller@login`
- **Authentication**: <PERSON><PERSON> Sanctum
- **Expected Input**:
  - `email` (required, email format)
  - `password` (required, minimum 8 characters)

## Expected Response

### Success (201)
```json
{
  "message": "login successfully",
  "Token": "1|abc123...",
  "role": "client",
  "user": {
    "id": 1,
    "name": "Test User",
    "nickname": "testuser",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "adress": "123 Test Street",
    "fax": "0987654321",
    "role": "client"
  }
}
```

### Error (401)
```json
"email or password is incorrect"
```

### Validation Error (422)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "email": ["The email field is required."]
  }
}
```

## Testing Methods

### 1. Automated Tests (PHPUnit)

Run the comprehensive test suite:

```bash
cd backend
php artisan test --filter AuthenticationTest
```

Or run specific tests:
```bash
# Test valid login
php artisan test --filter test_user_can_login_with_valid_credentials

# Test invalid credentials
php artisan test --filter test_user_cannot_login_with_invalid_password

# Test validation
php artisan test --filter test_login_requires_email
```

### 2. Manual Testing with PHP Script

Use the provided test script:

```bash
cd backend
php test_login_endpoint.php
```

Make sure your Laravel server is running first:
```bash
php artisan serve
```

### 3. Testing with cURL

#### Valid Login
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Invalid Credentials
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }'
```

#### Missing Email
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "password": "password123"
  }'
```

### 4. Testing with Postman

1. **Create a new request**:
   - Method: POST
   - URL: `http://localhost:8000/api/login`

2. **Set headers**:
   - `Content-Type: application/json`
   - `Accept: application/json`

3. **Set body** (raw JSON):
   ```json
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

4. **Test different scenarios**:
   - Valid credentials
   - Invalid email
   - Invalid password
   - Missing fields
   - Invalid email format

### 5. Testing with Frontend

Test the Angular frontend login component:

```bash
cd frontend
ng serve
```

Navigate to the login page and test:
- Valid credentials
- Invalid credentials
- Form validation
- Error handling

## Test Cases to Cover

### ✅ Positive Test Cases
1. **Valid Login**: Correct email and password
2. **Different User Roles**: Test login for all user roles (client, admin, receptionist, etc.)
3. **Token Generation**: Verify that a valid Sanctum token is returned
4. **User Data**: Verify that correct user data is returned

### ❌ Negative Test Cases
1. **Invalid Email**: Non-existent email address
2. **Invalid Password**: Wrong password for existing user
3. **Missing Email**: Request without email field
4. **Missing Password**: Request without password field
5. **Invalid Email Format**: Malformed email address
6. **Short Password**: Password less than 8 characters
7. **Empty Fields**: Empty email or password

### 🔒 Security Test Cases
1. **SQL Injection**: Test with malicious SQL in email/password
2. **XSS**: Test with script tags in input
3. **Rate Limiting**: Test multiple failed login attempts
4. **Token Validation**: Test using the returned token for authenticated requests

## Setting Up Test Data

### Create Test User via Seeder
```bash
cd backend
php artisan tinker
```

```php
use App\Models\User;
use Illuminate\Support\Facades\Hash;

User::create([
    'name' => 'Test User',
    'nickname' => 'testuser',
    'email' => '<EMAIL>',
    'password' => Hash::make('password123'),
    'phone' => '1234567890',
    'adress' => '123 Test Street',
    'fax' => '0987654321',
    'role' => 'client'
]);
```

### Or use the Registration Endpoint
```bash
curl -X POST http://localhost:8000/api/register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "Test User",
    "nickname": "testuser",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "adress": "123 Test Street",
    "fax": "0987654321",
    "password": "password123",
    "password_confirmation": "password123"
  }'
```

## Testing Token Authentication

After successful login, test the token:

```bash
# Get token from login response
TOKEN="1|abc123..."

# Test authenticated endpoint
curl -X GET http://localhost:8000/api/user \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json"
```

## Common Issues and Solutions

### Issue: 419 CSRF Token Mismatch
**Solution**: Make sure you're using `/api/login` (not `/login`) and include proper headers.

### Issue: 500 Internal Server Error
**Solution**: Check Laravel logs (`storage/logs/laravel.log`) and ensure database is properly configured.

### Issue: User Factory Missing Fields
**Solution**: The UserFactory has been updated to include all required fields (nickname, phone, adress, etc.).

### Issue: Tests Failing Due to Database
**Solution**: Make sure your test database is configured and run migrations:
```bash
php artisan migrate --env=testing
```

## Running All Tests

```bash
# Run all tests
php artisan test

# Run only feature tests
php artisan test tests/Feature

# Run with coverage (if configured)
php artisan test --coverage

# Run tests in parallel (faster)
php artisan test --parallel
```
