<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            // Make demande_id nullable if it's not already
            $table->string('demande_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            // Revert demande_id to not nullable (only if safe to do so)
            // Note: This might fail if there are NULL values in the database
            $table->string('demande_id')->nullable(false)->change();
        });
    }
};
