@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ==============================
   1. Conteneur principal
   ============================== */
.devis-container {
width:80%;

  margin: 50px auto;
  padding: 30px;

  background: white;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  animation: fadeInUp 1s ease-in-out forwards;
  transition: transform 0.3s ease-in-out;
  margin-bottom: 10px;
}

.btn-details {
  background: #2496d3;
  color: #fff;
  border: none;
  /* ✅ NOUVEAU : padding vertical/horizontal cohérent */
  padding: 8px 14px;
  border-radius: 20px;

  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  line-height: 1; /* Assure une même hauteur pour tous */
  transition: all 0.3s ease-in-out;
  /* ✅ NOUVEAU : min-width pour uniformiser la largeur */
  min-width: 120px;
  display: inline-flex; /* Changed to inline-flex to better match text flow */
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  margin: 0 auto; /* Center the button horizontally */
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.btn-details:hover {
  transform: scale(1.05);
  background: #1a7bad;
  box-shadow: 0 5px 15px rgba(26, 123, 173, 0.4);
  color: white;
}
/* Effet de survol sur le conteneur */
.devis-container:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

/* ==============================
   2. Titre principal
   ============================== */
.devis-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 26px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #2496d3;
  position: relative;
 
}

/* Soulignement avec effet glow */
.devis-container h2::after {
  content: "";
  position: absolute;
  width: 120px;
  height: 4px;
  background: #2496d3;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
  animation: underlineGlow 1.5s infinite alternate;
}

/* ==============================
   3. Tableau stylisé
   ============================== */
.styled-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
}

.styled-table th {
  background: #2496d3;
  color: white;
  padding: 14px;
  text-transform: uppercase;
  font-weight: bold;
  border: none;
  /* Aligne le texte à gauche ou au centre selon vos préférences */
  text-align: center; /* ou left */
}

.styled-table td {
  border: 1px solid #ddd;
  padding: 14px;
  text-align: center; /* ou left pour aligner le texte de manière plus naturelle */
  font-size: 16px;
  vertical-align: middle;
}

/* Zebra striping */
.styled-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* ==============================
   4. Styles de lignes
   ============================== */
.table-row {
  transition: background-color 0.3s;
}
.table-row:hover {
  background-color: #f0f0f0; /* gris clair */
}

/* ==============================
   5. Action Cell
   ============================== */
.action-cell {
  /* Keep the same styling as other cells but center the content */
  text-align: center;
  /* Remove display: flex to match other cells */
  padding: 10px;
  width: 20%;
}

/* ==============================
   6. Badges de statut
   ============================== */
.status-tag {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

/* Couleurs pour chaque statut */
.status-tag.pending {
  background-color: #fff3cd; /* jaune pâle */
  color: #856404;
}
.status-tag.ongoing {
  background-color: #cce5ff; /* bleu pâle */
  color: #004085;
}
.status-tag.derogation {
  background-color: #f8d7da; /* rouge pâle */
  color: #721c24;
}
.status-tag.valid {
  background-color: #d4edda; /* vert pâle */
  color: #155724;
}
.status-tag.rejected {
  background-color: #f8d7da; /* rouge pâle */
  color: #721c24;
}

/* ==============================
   6. Messages : Chargement & Erreur
   ============================== */
.loading-message {
  font-size: 18px;
  color: #007bff;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-message::before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #007bff;
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

.error-message {
  font-size: 18px;
  color: #dc3545;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message::before {
  content: "⚠️";
  margin-right: 10px;
}

/* ==============================
   7. Filter Bar
   ============================== */
.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: flex-end; /* Aligns items at the bottom to match input fields */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 75%; /* Limits the width as in your original design */
  margin: 0 auto 20px; /* Centers the filter bar within its parent */
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}

.filter-group label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 200px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

.btn-clear {
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  height: 38px; /* Match the height of input fields */
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.btn-group {
  margin-bottom: 0;
  margin-left: 2rem; /* Add extra space between filter groups and the button */
}

.invisible {
  visibility: hidden;
  height: 0;
  margin: 0;
  padding: 0;
}

/* ==============================
   8. Pagination
   ============================== */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* ==============================
   9. Empty and Loading States
   ============================== */
.empty-row td {
  padding: 30px;
  text-align: center;
  color: #6c757d;
  font-size: 16px;
}

.text-center {
  text-align: center;
}

/* ==============================
   10. Responsive
   ============================== */
@media (max-width: 768px) {
  .styled-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .filter-bar {
    max-width: 95%;
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .filter-group input,
  .filter-group select {
    width: 100%;
  }

  .btn-group {
    margin-top: 0.5rem;
  }

  .btn-clear {
    width: 100%;
    justify-content: center;
  }
}

/* ==============================
   11. Animations
   ============================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes underlineGlow {
  from {
    box-shadow: 0px 0px 10px rgba(36, 150, 211, 0.5);
  }
  to {
    box-shadow: 0px 0px 20px rgba(36, 150, 211, 0.9);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
