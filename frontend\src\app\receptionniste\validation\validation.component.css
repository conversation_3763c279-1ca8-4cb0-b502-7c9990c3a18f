@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.demandes-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 70px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}





/* ✅ Barre de filtrage */
.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: center; /* Vertically centers all items */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 60%; /* Limits the width as in your original design */
  margin: 0 auto; /* Centers the filter bar within its parent */
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}

.filter-group label {
  font-weight: 600;
  font-size: 14px;
  color: #444;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 250px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Notification styles */
.notification {
  background: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
  text-align: center;
  width:100%;
  font-weight: bold;
}

.notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Styles pour l'indicateur de chargement et les messages vides */
.loading-row, .empty-row {
  height: 100px;
}

.loading-row td, .empty-row td {
  text-align: center;
  vertical-align: middle;
  font-size: 18px;
  color: #6c757d;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.text-center {
  text-align: center;
}



/* ✅ Titre */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* ✅ Table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ En-têtes */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Lignes */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 15px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Statuts */
.status {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
  display: inline-block;
  color: white;
}

/* Payment status styles */
.status-approved {
  background-color: #4CAF50; /* Green */
  color: white;
}

.status-rejected {
  background-color: #F44336; /* Red */
  color: white;
}

.status-pending {
  background-color: #FFC107; /* Amber */
  color: #333;
}

/* Actions column */
.actions-col {
  min-width: 200px;
}

/* Button container */
.button-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: center;
  padding: 5px 0;
}

/* ✅ Boutons de base */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 5px;
  min-width: 150px;
  max-width: 200px;
}
.icon {
  font-size: 18px;
  margin-right: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-completed {
  color: #28a745;
}
.text {
  display: inline-block;
}

/* Loading state for buttons */
.btn-loading {
  background: #6c757d !important;
  cursor: wait !important;
}

.loading-text {
  opacity: 0.8;
}

/* ✅ Bouton: Voir les détails (gris) */
.btn-detail {
  background: #6c7d70;
  color: white;
}

.btn-detail:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* ✅ Bouton: Voir détails (bleu) */
.btn-view-details {
  background: #007bff; /* Blue color */
  color: white;
}

.btn-view-details:hover {
  background: #0069d9; /* Darker blue on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* ✅ Bouton: Créer la fiche (bleu clair) */
.btn-create {
  background: #4dabf7; /* Light blue color */
  color: white;
}
.btn-create:hover {
  background: #3793dd; /* Slightly darker light blue on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(77, 171, 247, 0.3);
}

/* ✅ Bouton: Ajouter au registre (bleu clair) */
.btn-add {
  background: #4dabf7; /* Light blue color */
  color: white;
}
.btn-add:hover {
  background: #3793dd; /* Slightly darker light blue on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(77, 171, 247, 0.3);
}

/* ✅ Bouton: État "déjà complété" */
.btn-completed {
  background: #28a745; /* Vert */
  color: white;
  cursor: not-allowed;
}

/* ✅ Hover d'un tableau */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* ✅ Animation Glow sur le titre */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* ✅ Pagination styles */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* ✅ Responsive styles */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    max-width: 95%;
    height: auto;
    padding: 1rem;
  }

  .filter-group {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .filter-group input,
  .filter-group select {
    max-width: 100%;
  }

  .btn-clear {
    margin-top: 0.5rem;
    margin-left: 0;
    width: 100%;
  }

  .button-container {
    flex-direction: row;
    flex-wrap: wrap;
    padding: 8px;
    gap: 8px;
  }

  .btn {
    width: auto;
    min-width: 130px;
    justify-content: center;
    padding: 10px 12px;
    font-size: 14px;
  }

  .icon {
    font-size: 16px;
    margin-right: 5px;
  }

  .text {
    font-size: 14px;
  }
}
