import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResultsService } from './results.service';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faEye,
  faSearch,
  faFilter,
  faSpinner,
  faPaperPlane,
  faVial,
  faDownload,
  faTrash,
  faFilePdf,
  faFileInvoice,
  faExternalLinkAlt,
  faCheck,
  faTimes,
  faExclamationTriangle,
  faEraser,
  faCalendarAlt
} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-results',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
  templateUrl: './results.component.html',
  styleUrl: './results.component.css'
})
export class ResultsComponent implements OnInit {
  // Icons
  faEye = faEye;
  faSearch = faSearch;
  faFilter = faFilter;
  faSpinner = faSpinner;
  faPaperPlane = faPaperPlane;
  faVial = faVial;
  faDownload = faDownload;
  faTrash = faTrash;
  faFilePdf = faFilePdf;
  faFileInvoice = faFileInvoice;
  faExternalLinkAlt = faExternalLinkAlt;
  faCheck = faCheck;
  faTimes = faTimes;
  faExclamationTriangle = faExclamationTriangle;
  faEraser = faEraser;
  faCalendarAlt = faCalendarAlt;

  // Data
  validReports: any[] = [];
  resultatClients: any[] = [];
  combinedReports: any[] = [];
  filteredReports: any[] = [];
  loading: boolean = true;
  error: string | null = null;

  // Search and filter
  searchTerm: string = '';
  selectedDate: string = '';
  showFilters: boolean = false;

  // File preview
  showPreview: boolean = false;
  previewFile: string | null = null;
  previewType: 'rapport' | 'facture' | null = null;
  previewDemandeId: string | null = null;
  previewLoading: boolean = false;
  previewError: string | null = null;

  constructor(
    private resultsService: ResultsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadValidReports();
  }

  loadValidReports(): void {
    this.loading = true;

    // First, get all valid reports
    this.resultsService.getValidReports().subscribe({
      next: (reports) => {
        this.validReports = reports;

        // Then, get all resultat clients
        this.resultsService.getResultatClients().subscribe({
          next: (resultats) => {
            this.resultatClients = resultats;

            // Combine the data
            this.combineReportsData();
            this.loading = false;
          },
          error: (err) => {
            console.error('Error fetching resultat clients:', err);
            // Still combine with what we have
            this.combineReportsData();
            this.loading = false;
          }
        });
      },
      error: (err) => {
        console.error('Error fetching valid reports:', err);
        this.error = 'Impossible de charger les rapports validés. Veuillez réessayer plus tard.';
        this.loading = false;
      }
    });
  }

  combineReportsData(): void {
    // Create a map of demande_id to resultat client for quick lookup
    const resultatMap = new Map();
    this.resultatClients.forEach(resultat => {
      resultatMap.set(resultat.demande_id, resultat);
    });

    // Combine the data
    this.combinedReports = this.validReports.map(report => {
      const resultat = resultatMap.get(report.demande_id);
      return {
        ...report,
        resultat: resultat || null,
        // Set client status based on resultat
        clientStatus: resultat ? resultat.status : 'pending',
        // Set file paths
        rapportFile: resultat ? resultat.rapport_file : null,
        factureFile: resultat ? resultat.facture_file : null,
        // Set sent date
        sentAt: resultat ? resultat.sent_at : null
      };
    });

    // Update filtered reports
    this.filteredReports = [...this.combinedReports];
  }

  viewReportDetails(reportId: number): void {
    this.router.navigate(['/receptionist/rapports', reportId]);
  }

  applyFilters(): void {
    let filtered = [...this.combinedReports];

    // Filter by search term
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      const searchTermLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(report => {
        return (
          report.demande_id.toLowerCase().includes(searchTermLower) ||
          (report.creation_date && report.creation_date.toLowerCase().includes(searchTermLower))
        );
      });
    }

    // Filter by date
    if (this.selectedDate) {
      const filterDate = new Date(this.selectedDate);
      filtered = filtered.filter(report => {
        if (!report.creation_date) return false;

        const reportDate = new Date(report.creation_date);
        return reportDate.toDateString() === filterDate.toDateString();
      });
    }

    this.filteredReports = filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.filteredReports = [...this.combinedReports];
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  sendResults(report: any): void {
    // Navigate to the send-results component with the report ID and demande ID as parameters
    this.router.navigate(['/receptionist/results/send', report.id], {
      queryParams: { demandeId: report.demande_id }
    });
  }

  // File preview methods
  viewFile(report: any, fileType: 'rapport' | 'facture'): void {
    const filePath = fileType === 'rapport' ? report.rapportFile : report.factureFile;

    if (!filePath) {
      console.error(`No ${fileType} file available for demande ${report.demande_id}`);
      return;
    }

    // Open the file in a new tab
    const url = this.resultsService.getFileUrl(filePath);
    window.open(url, '_blank');
  }

  downloadFile(report: any, fileType: 'rapport' | 'facture'): void {
    const filePath = fileType === 'rapport' ? report.rapportFile : report.factureFile;

    if (!filePath) {
      console.error(`No ${fileType} file available for demande ${report.demande_id}`);
      return;
    }

    this.resultsService.downloadFile(filePath).subscribe({
      next: (blob) => {
        // Create a URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a link element
        const a = document.createElement('a');
        a.href = url;
        a.download = this.getFileNameFromPath(filePath);

        // Append to the document, click it, and remove it
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      },
      error: (error) => {
        console.error(`Error downloading ${fileType} file:`, error);
      }
    });
  }

  deleteFile(report: any, fileType: 'rapport' | 'facture'): void {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer ce fichier ${fileType === 'rapport' ? 'rapport' : 'facture'} ?`)) {
      return;
    }

    const deleteMethod = fileType === 'rapport'
      ? this.resultsService.deleteReportFile(report.demande_id)
      : this.resultsService.deleteInvoiceFile(report.demande_id);

    deleteMethod.subscribe({
      next: () => {
        // Reload the data
        this.loadValidReports();
      },
      error: (error) => {
        console.error(`Error deleting ${fileType} file:`, error);
      }
    });
  }

  getFileNameFromPath(path: string): string {
    if (!path) return '';
    const parts = path.split('/');
    return parts[parts.length - 1];
  }

  getClientStatusLabel(status: string): string {
    switch (status) {
      case 'sent':
        return 'Envoyé au client';
      case 'pending':
        return 'En attente d\'envoi';
      default:
        return 'Statut inconnu';
    }
  }

  getClientStatusClass(status: string): string {
    switch (status) {
      case 'sent':
        return 'status-sent';
      case 'pending':
        return 'status-pending';
      default:
        return '';
    }
  }
}
