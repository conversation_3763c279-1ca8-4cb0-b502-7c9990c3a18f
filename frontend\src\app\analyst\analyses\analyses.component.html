

<div class="container">
    <h2>Gestion des analyses</h2>

    <!-- Add Sample Button -->
     <div class="add-sample">
    <button (click)="openModal()" class="add-sample-btn"> ➕ Ajout une analyse</button>

    <!-- Sample List -->
    <h3>Liste des échantillons et analyses</h3>
  </div>
    <table class="user-table">
      <thead>
        <tr>
         
          <th>Type d'échantillon</th>
<th>Sous-type</th>
<th>Quantité minimale</th>
<th>Condition</th>
<th>Analyse</th>
<th>Paramètre</th>
<th>Prix</th>
<th>Actions</th>

        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let sample of samples">
          
          <td>{{ sample.sample_type }}</td>
          <td>{{ sample.sub_type }}</td>
          <td>{{ sample.quantite_minimal }}</td>
          <td>{{ sample.condition }}</td>
          <td>{{ sample.analysis }}</td>
          <td>{{ sample.parametre }}</td>
          <td>{{ sample.price }}</td>
          <td>
            <div class="action-btns">
            <button (click)="editSample(sample)" class="edit-btn">✏️</button>
            <button (click)="deleteSample(sample.id ?? 0)" class="delete-btn">🗑️</button>
          </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Modal -->
    <div class="modal" [ngClass]="{ 'show': isModalOpen }">
        <div class="modal-content">
            <span class="close" (click)="closeModal()"></span>
            <h3>{{ selectedSample ? 'Modifier' : 'Ajouter' }} Echantillon</h3>

            <!-- Sample Form -->
            <form [formGroup]="sampleForm" (ngSubmit)="saveSample()">
                <input type="hidden" formControlName="id">

                <div>
                    <label>Type d'échantillon:</label>
                    <input type="text" formControlName="sample_type" required>
                </div>

                <div>
                    <label>Sous type :</label>
                    <input type="text" formControlName="sub_type">
                </div>

                <div>
                    <label>Quantité minimale:</label>
                    <input type="text" formControlName="quantite_minimal" required>
                </div>

                <div>
                    <label>Condition:</label>
                    <input type="text" formControlName="condition" required>
                </div>

                <div>
                    <label>Analyse:</label>
                    <input type="text" formControlName="analysis" required>
                </div>
                <div>
                    <label>Paramètre:</label>
                    <input type="text" formControlName="parametre" required>
                </div>

                <div>
                    <label>Prix:</label>
                    <input type="number" formControlName="price" required>
                </div>
<div class="action-btns-modal">
                <button type="submit" class="save-btn">{{ selectedSample ? 'Update' : 'Ajouter' }} </button>
                <button type="button" class="cancel-btn" (click)="closeModal()">annuler</button>
              </div>
              </form>
        </div>
    </div>
</div>
