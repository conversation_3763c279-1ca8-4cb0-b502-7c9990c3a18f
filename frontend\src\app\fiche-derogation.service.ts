import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FicheDerogationService {
  private apiUrl = 'https://api.b3aqua.com/derogation'; // 🔄 Remplace par l'URL réelle de l'API

  constructor(private http: HttpClient) {}

  getFicheDerogation(id: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }

  submitFicheDerogation(data: any): Observable<any> {
    return this.http.post(this.apiUrl, data);
  }
}
