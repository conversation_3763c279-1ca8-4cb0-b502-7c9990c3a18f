/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.about-container {
    background: white;
    color: black;
    text-align: center;
    padding: 120px 5%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* ✅ Titre principal animé */
.title {
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 30px;
    display: inline-block;
    padding-bottom: 10px;
    position: relative;
    
    border-bottom: 3px solid #2496d3;
}

/* ✅ Ajout d'un effet de lumière autour du texte */
@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4); } /* ✅ Bleu ciel */
    to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8); }
}

/* ✅ Ajout d'une ligne sous le titre */
.title::after {
    content: "";
    display: block;
    width: 100px;
    height: 3px;
    margin: auto;
    margin-top: 5px;
}

/* ✅ Contenu principal */
.about-content {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
    margin-bottom: 50px;
}

/* ✅ Bloc d'information */
.about-box {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0px 10px 25px rgba(36, 150, 211, 0.3); /* ✅ Ombre bleu ciel */
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease-in-out forwards;
}

/* ✅ Effet au survol */
.about-box:hover {
    transform: scale(1.05);
    box-shadow: 0px 15px 30px rgba(36, 150, 211, 0.6); /* ✅ Ombre bleu ciel plus forte */
}

/* ✅ Animation pour l'image */
.about-image {
    width: 100px;
    height: auto;
    transition: transform 0.5s ease-in-out, filter 0.3s ease-in-out;
    filter: drop-shadow(0px 0px 10px rgba(36, 150, 211, 0.6)); /* ✅ Ombre bleue */
    opacity: 0;
    transform: scale(0.8);
    animation: fadeInZoom 1.2s ease-out forwards;
}

/* ✅ Effet de flottement sur les images */
.about-image:hover {
    transform: scale(1.1) rotate(3deg);
    filter: drop-shadow(0px 0px 20px rgba(36, 150, 211, 0.8)); /* ✅ Glow bleu ciel */
}

/* ✅ Texte */
.about-text {
    text-align: left;
}

/* ✅ Titre des questions */
.about-text h3 {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
    color: black;
}

/* ✅ Paragraphe */
.about-text p {
    font-size: 18px;
    line-height: 1.6;
    color: black;
}

/* Modern Statistics Section */
.stats-section {
    padding: 80px 20px;

    border-radius: 20px;
    margin: 40px 0;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.stats-title {
    margin-bottom: 50px;
    font-family: 'Poppins', sans-serif;
    position: relative;
}

/* Modern Stats Container */
.stats-container {
    display: flex;
    justify-content: center;
    gap: 40px; /* Increased from 30px to provide more spacing between wider boxes */
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
}

/* Modern Stats Box */
.stats-box {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    width: 250px; /* Increased from 200px to make boxes wider */
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Modern hover effect */
.stats-box:hover {
    transform: translateY(-10px);
    cursor: pointer;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.12);
    border-color: rgba(0, 0, 0, 0.1);
}

/* Stats icon */
.stats-icon {
    font-size: 24px;
    height: 60px;
    width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: #2496d3; /* Changed from #000 to blue */
    background: rgba(36, 150, 211, 0.1); /* Light blue background */
    border-radius: 50%;
    transition: all 0.3s ease;
}

.stats-box:hover .stats-icon {
    background: #2496d3; /* Changed from #000 to blue */
    color: white;
    transform: scale(1.05);
}

/* Stats number with animation */
.stats-number {
    font-size: 32px;
    font-weight: 700;
    color: #2496d3; /* Changed from #000 to blue */
    margin-bottom: 5px;
    font-family: 'Poppins', sans-serif;
    position: relative;
    display: inline-block;
}

.plus-sign {
    font-size: 20px;
    position: relative;
    top: -3px;
    font-weight: 700;
    color: #2496d3; /* Changed from #000 to blue */
}

/* Divider */
.stats-divider {
    height: 2px;
    width: 30px;
    background: linear-gradient(90deg, transparent, #2496d3, transparent); /* Changed from #000 to blue */
    margin: 12px auto;
    border-radius: 2px;
}

/* Stats label */
.stats-label {
    font-size: 16px;
    color: #444;
    font-weight: 500;
    margin: 0;
    white-space: nowrap; /* Prevent text wrapping */
}

/* ✅ Animation d'apparition fluide */
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✅ Animation de fade-in avec zoom */
@keyframes fadeInZoom {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

/* Logo carousel styles */
.logo-carousel {
    display: flex;
    animation: scroll 60s linear infinite;
}

.logo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 180px; /* Increased to accommodate larger logos */
    height: 120px; /* Fixed height for all logo containers */
    margin-left:40px; /* Slightly increased margin for better spacing */
    margin-right:40px;
}

.logo-image {
    width: 400px; /* Increased by 30% from 80px */
    height: 300px; /* Fixed height equal to width */
    object-fit: contain; /* Ensures the image maintains its aspect ratio */

    transition: filter 0.3s ease, transform 0.3s ease;
    max-width: 100%; /* Ensures the image doesn't overflow its container */
}

.logo-image:hover, .logo-image:focus {

    transform: scale(1.1);
}

/* Special class for the 11.png logo to make it bigger */
.logo-image-larger {
    width: 400px; /* About 25% larger than the standard 104px */
    height: 400px;
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* ✅ Responsive */
@media (max-width: 768px) {
    .about-content {
        flex-direction: column;
        align-items: center;
    }

    .about-box {
        flex-direction: column;
        text-align: center;
        align-items: center;
    }

    .about-image {
        width: 80px;
    }

    .stats-container {
        flex-direction: column;
        align-items: center;
    }
}
