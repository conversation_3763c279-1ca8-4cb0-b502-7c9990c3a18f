import { User } from '../models/user.model'; // Importing the existing User model

export interface Sample {
  demande_id?: number;
  codeClient: string;
  sample_type: string;
  sub_type?: string;
  provenance: string;
  masseEchantillon: string;
  etat: string;
  analysis: string[];
  observations?: string;
  lot: string;
  methodeAnalyse?: string;
  reference?: string;
  
  price: number;
  accredited: boolean;
}

export interface Demande {
  id?: number;
  delai_souhaite?:string,
  user: User; // Reference to the existing User model
  demande_id: string;
  total_enregistrements: number;
  demande_date: string;
  created_at?: string;
  updated_at?: string;
  samples?: Sample[];
  mode_reglement: string;
}
