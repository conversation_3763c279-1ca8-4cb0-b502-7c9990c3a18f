@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&display=swap');

/* ✅ Main Container */
.contact-container {
  max-width: 1200px;
  margin: 60px auto;
  padding: 0 20px;
  font-family: 'Poppins', sans-serif;
}

/* ✅ Header Section */
.contact-header {
  text-align: center;
  margin-bottom: 40px;
}

.contact-title {
  font-size: 32px;
  font-weight: 700;
  color: #2496d3;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Poppins', sans-serif;
}

.title-underline {
  height: 4px;
  width: 80px;
  background: linear-gradient(90deg, #2496d3, #4381d1);
  margin: 0 auto;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.title-underline::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    left: 100%;
  }
}

/* ✅ Content Layout */
.contact-content {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: space-between;
}

/* ✅ Form Container */
.contact-form-container {
  flex: 1 1 550px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.15);
  padding: 40px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(36, 150, 211, 0.25);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-icon {
  width: 60px;
  height: 60px;
  background: #2496d3;
  border-radius: 50%;
  margin: 0 auto 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.form-icon::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  animation: pulse 2s infinite;
}

.form-icon-svg {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

.form-header h3 {
  font-size: 22px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.contact-description {
  color: #666;
  font-size: 15px;
  line-height: 1.5;
}

/* ✅ Form Styling */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  margin-bottom: 15px;
  position: relative;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: #f9f9f9;
}

.input-wrapper:focus-within {
  border-color: #2496d3;
  box-shadow: 0 0 0 3px rgba(36, 150, 211, 0.2);
  background: white;
}

.input-wrapper.ng-invalid.ng-touched,
.input-wrapper.ng-invalid.ng-dirty {
  border-color: #dc3545;
}

.input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: #f0f0f0;
  border-right: 1px solid #e0e0e0;
}

.user-icon, .email-icon, .phone-icon, .message-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
}

.user-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.email-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z'/%3E%3C/svg%3E");
}

.phone-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z'/%3E%3C/svg%3E");
}

.message-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'/%3E%3C/svg%3E");
}

input, textarea {
  flex: 1;
  padding: 15px;
  border: none;
  outline: none;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  background: transparent;
  width: 100%;
}

.textarea-container {
  align-items: flex-start;
}

.textarea-container .input-icon {
  height: 100%;
  min-height: 50px;
}

textarea {
  min-height: 120px;
  resize: vertical;
}

/* ✅ Submit Button */
.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px 30px;
  background: #2496d3;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  min-width: 180px;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.4s ease;
}

.submit-button:hover:not([disabled]) {
  background: #1a73e8;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.4);
}

.submit-button:hover:not([disabled])::before {
  left: 100%;
}

.submit-button:active:not([disabled]) {
  transform: translateY(0);
}

.submit-button[disabled] {
  background: #a0c8e6;
  cursor: not-allowed;
  opacity: 0.8;
}

.send-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M2.01 21L23 12 2.01 3 2 10l15 2-15 2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

/* ✅ Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ✅ Validation Error Messages */
.validation-error {
  margin-top: 5px;
  color: #dc3545;
  font-size: 13px;
  text-align: left;
  animation: fadeIn 0.3s ease-in-out;
  padding-left: 50px;
}

.validation-error span {
  display: block;
  margin-bottom: 2px;
}

/* ✅ Success Message */
.success-message {
  display: flex;
  align-items: center;
  gap: 15px;
  background: #e7f7ed;
  border: 1px solid #c3e6d1;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  animation: fadeIn 0.5s ease-in-out;
}

.success-icon {
  width: 30px;
  height: 30px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.success-message p {
  color: #155724;
  margin: 0;
  font-size: 14px;
}

.countdown {
  font-weight: bold;
  color: #0d6efd;
  animation: pulse-text 1s infinite;
}

@keyframes pulse-text {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ✅ Contact Info Container */
.contact-info-container {
  flex: 1 1 350px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.15);
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.info-header {
  margin-bottom: 30px;
}

.info-header h3 {
  font-size: 22px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
  text-align: center;
}

.info-underline {
  height: 3px;
  width: 60px;
  background: linear-gradient(90deg, #2496d3, #4381d1);
  margin: 0 auto;
  border-radius: 2px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.info-icon::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
}

.location-icon::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/%3E%3C/svg%3E");
}

.phone-info-icon::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z'/%3E%3C/svg%3E");
}

.email-info-icon::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z'/%3E%3C/svg%3E");
}

.hours-icon::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232496d3'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E");
}

.info-text h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.info-text p {
  font-size: 15px;
  color: #666;
  line-height: 1.4;
}

/* ✅ Map Container */
.map-container {
  flex-grow: 1;
  margin-top: auto;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 250px;
  box-shadow: 0 4px 12px rgba(36, 150, 211, 0.2);
  border: 2px solid #f0f0f0;
}

.map-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  transition: opacity 0.3s ease;
}

.map-container:hover iframe {
  opacity: 0.9;
}

/* ✅ Responsive Adjustments */
@media (max-width: 992px) {
  .contact-content {
    flex-direction: column;
  }

  .contact-form-container, .contact-info-container {
    flex: 1 1 100%;
  }
}

@media (max-width: 768px) {
  .contact-container {
    margin: 40px auto;
  }

  .contact-form-container, .contact-info-container {
    padding: 30px;
  }

  .contact-title {
    font-size: 28px;
  }

  .form-header h3, .info-header h3 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .contact-container {
    padding: 0 15px;
    margin: 30px auto;
  }

  .contact-form-container, .contact-info-container {
    padding: 20px;
  }

  .contact-title {
    font-size: 24px;
  }

  .input-icon {
    width: 40px;
  }

  input, textarea {
    padding: 12px;
    font-size: 14px;
  }

  .submit-button {
    padding: 12px 25px;
    font-size: 15px;
  }
}
