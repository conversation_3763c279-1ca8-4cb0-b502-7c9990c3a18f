<div class="notifications-container">
    <h2>Notifications</h2>

    <!-- Success/Error Messages -->
    <div class="message-container" *ngIf="successMessage || errorMessage">
        <div class="success-message" *ngIf="successMessage">
            <fa-icon icon="check-circle" style="margin-right: 8px;"></fa-icon>{{ successMessage }}
        </div>
        <div class="error-message" *ngIf="errorMessage">
            <fa-icon icon="exclamation-triangle" style="margin-right: 8px;"></fa-icon>{{ errorMessage }}
        </div>
    </div>

    <!-- Actions pour les notifications -->
    <div class="notification-actions" *ngIf="notifications.length > 0">
        <button
            (click)="removeAllNotifications()"
            class="btn-remove-all"
            [disabled]="isDeletingAll || isDeletingNotification"
            [class.loading]="isDeletingAll">
            <fa-icon [icon]="faTrashAlt" style="margin-right: 10px;"></fa-icon>
            <span *ngIf="!isDeletingAll">Supprimer toutes mes notifications</span>
            <span *ngIf="isDeletingAll">Suppression en cours...</span>
        </button>
    </div>

    <!-- ✅ Check if there are notifications -->
    <div *ngIf="notifications.length > 0; else noNotifications">
        <table class="notification-table">
            <thead>
                <tr>
                    <th>Catégorie</th>
                    <th>Message</th>
                    <th>Date</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let notification of notifications | paginate: { itemsPerPage: itemsPerPage, currentPage: page }"
                    [class.unread]="!notification.is_read"
                    (click)="handleNotificationClick(notification)">
                    <td>{{ notification.title }}</td>
                    <td>{{ notification.message }}</td>
                    <td>{{ notification.created_at | date:'yyyy-MM-dd HH:mm' }}</td>
                    <td>
                        <span *ngIf="!notification.is_read" class="new-tag">Nouvelle</span>
                        <span *ngIf="notification.is_read" class="read-tag">Lue</span>
                    </td>
                    <td class="actions-column">
                        <button
                            (click)="$event.stopPropagation(); removeNotification(notification)"
                            class="btn-remove"
                            [disabled]="isDeletingNotification || isDeletingAll"
                            [class.loading]="isDeletingNotification"
                            title="Supprimer cette notification">
                            <fa-icon [icon]="faTrash" style="margin-right: 5px;"></fa-icon>
                            <span *ngIf="!isDeletingNotification">Supprimer</span>
                            <span *ngIf="isDeletingNotification">...</span>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- ✅ Pagination Controls -->
        <pagination-controls (pageChange)="page = $event" previousLabel="Précédent"
        nextLabel="Suivant"></pagination-controls>
    </div>

    <!-- ✅ No Notifications Message -->
    <ng-template #noNotifications>
        <p class="no-notifications">Aucune notification disponible.</p>
    </ng-template>
</div>
