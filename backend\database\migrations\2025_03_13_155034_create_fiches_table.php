<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('fiches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fiche_transmission_id')->constrained('fiches_transmission')->onDelete('cascade');
            $table->string('code_laboratoire');
            $table->string('nature_echantillon');
            $table->decimal('masse_echantillon', 8, 2);
            $table->date('date_transmission');
            $table->date('date_remise_resultats');
            $table->json('analyses_demandees');
            $table->string('observations')->default('RAS');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('fiches');
    }
};
