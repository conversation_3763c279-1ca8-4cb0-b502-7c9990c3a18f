<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test successful login with valid credentials
     */
    public function test_user_can_login_with_valid_credentials(): void
    {
        // Create a user with known credentials
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'name' => 'Test User',
            'nickname' => 'testuser',
            'phone' => '1234567890',
            'adress' => '123 Test Street',
            'fax' => '0987654321',
            'role' => 'client'
        ]);

        // Attempt login
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Assert successful response
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'Token',
                    'role',
                    'user' => [
                        'id',
                        'name',
                        'nickname',
                        'email',
                        'phone',
                        'adress',
                        'fax',
                        'role'
                    ]
                ])
                ->assertJson([
                    'message' => 'login successfully',
                    'role' => 'client',
                    'user' => [
                        'email' => '<EMAIL>',
                        'name' => 'Test User',
                        'nickname' => 'testuser',
                        'role' => 'client'
                    ]
                ]);

        // Assert token is present and not empty
        $this->assertNotEmpty($response->json('Token'));

        // Assert user data is correct
        $userData = $response->json('user');
        $this->assertEquals($user->id, $userData['id']);
        $this->assertEquals($user->email, $userData['email']);
        $this->assertEquals($user->name, $userData['name']);
    }

    /**
     * Test login fails with invalid email
     */
    public function test_user_cannot_login_with_invalid_email(): void
    {
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(401)
                ->assertJson('email or password is incorrect');
    }

    /**
     * Test login fails with invalid password
     */
    public function test_user_cannot_login_with_invalid_password(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('correctpassword'),
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(401)
                ->assertJson('email or password is incorrect');
    }

    /**
     * Test login validation for missing email
     */
    public function test_login_requires_email(): void
    {
        $response = $this->postJson('/api/login', [
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test login validation for missing password
     */
    public function test_login_requires_password(): void
    {
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    /**
     * Test login validation for invalid email format
     */
    public function test_login_requires_valid_email_format(): void
    {
        $response = $this->postJson('/api/login', [
            'email' => 'invalid-email',
            'password' => 'password123',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test login validation for password minimum length
     */
    public function test_login_requires_minimum_password_length(): void
    {
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => '1234567', // 7 characters, less than required 8
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    /**
     * Test login with different user roles
     */
    public function test_login_works_for_different_user_roles(): void
    {
        $roles = ['client', 'admin', 'receptionist', 'responsable', 'director'];

        foreach ($roles as $role) {
            $user = User::factory()->create([
                'email' => "test-{$role}@example.com",
                'password' => Hash::make('password123'),
                'role' => $role
            ]);

            $response = $this->postJson('/api/login', [
                'email' => "test-{$role}@example.com",
                'password' => 'password123',
            ]);

            $response->assertStatus(201)
                    ->assertJson([
                        'message' => 'login successfully',
                        'role' => $role,
                        'user' => [
                            'role' => $role
                        ]
                    ]);
        }
    }

    /**
     * Test that login creates a valid Sanctum token
     */
    public function test_login_creates_valid_sanctum_token(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $token = $response->json('Token');

        // Test that the token can be used to access protected routes
        $protectedResponse = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/user');

        $protectedResponse->assertStatus(200);
    }
}
