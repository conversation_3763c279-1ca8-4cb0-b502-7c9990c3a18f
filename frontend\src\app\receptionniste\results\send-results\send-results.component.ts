import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faUpload,
  faFileInvoice,
  faFilePdf,
  faArrowLeft,
  faSpinner,
  faCheckCircle,
  faExclamationTriangle,
  faPaperPlane,
  faVial,
  faEye,
  faDownload,
  faTrash,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';

import { ResultsService } from '../results.service';

@Component({
  selector: 'app-send-results',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, FontAwesomeModule],
  templateUrl: './send-results.component.html',
  styleUrl: './send-results.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // For FontAwesome icons
})
export class SendResultsComponent implements OnInit {
  // Font Awesome icons
  faUpload = faUpload;
  faFileInvoice = faFileInvoice;
  faFilePdf = faFilePdf;
  faArrowLeft = faArrowLeft;
  faSpinner = faSpinner;
  faCheckCircle = faCheckCircle;
  faExclamationTriangle = faExclamationTriangle;
  faPaperPlane = faPaperPlane;
  faVial = faVial;
  faEye = faEye;
  faDownload = faDownload;
  faTrash = faTrash;
  faInfoCircle = faInfoCircle;

  // Component properties
  reportId: number | null = null;
  demandeId: string = '';
  reportDetails: any = null;
  demandeDetails: any = null;

  // File upload properties
  selectedReportFile: File | null = null;
  selectedInvoiceFile: File | null = null;
  reportFileName: string = '';
  invoiceFileName: string = '';

  // UI state properties
  isLoading: boolean = false;
  isUploading: boolean = false;
  isSending: boolean = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  // Upload section visibility flags
  showUploadSection: boolean = false;
  showReportUpload: boolean = false;
  showInvoiceUpload: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resultsService: ResultsService
  ) {}

  ngOnInit(): void {
    // Reset upload section visibility flags
    this.showReportUpload = false;
    this.showInvoiceUpload = false;

    // Get report ID from route params
    this.route.params.subscribe(params => {
      this.reportId = +params['id'];

      // Get demande ID from query params
      this.route.queryParams.subscribe(queryParams => {
        this.demandeId = queryParams['demandeId'] || '';

        if (this.reportId && this.demandeId) {
          this.loadReportDetails();
          this.loadDemandeDetails();
          this.checkExistingFiles();
        } else {
          this.errorMessage = 'Identifiants de rapport ou de demande invalides.';
        }
      });
    });
  }

  // Check if there are already uploaded files for this demande
  checkExistingFiles(): void {
    if (!this.demandeId) return;

    this.resultsService.getResultatClient(this.demandeId).subscribe({
      next: (data) => {
        if (data) {
          // Store file paths in the component
          this.reportDetails = {
            ...this.reportDetails,
            rapport_file: data.rapport_file,
            facture_file: data.facture_file,
            client_status: data.status
          };
        }
      },
      error: (error) => {
        console.error('Error checking existing files:', error);
      }
    });
  }

  loadReportDetails(): void {
    if (!this.reportId) return;

    this.isLoading = true;
    this.resultsService.getReportDetails(this.reportId).subscribe({
      next: (data) => {
        this.reportDetails = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching report details:', error);
        this.errorMessage = 'Échec du chargement des détails du rapport.';
        this.isLoading = false;
      }
    });
  }

  loadDemandeDetails(): void {
    if (!this.demandeId) return;

    this.isLoading = true;
    this.resultsService.getDemandeDetails(this.demandeId).subscribe({
      next: (data) => {
        this.demandeDetails = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching demande details:', error);
        this.errorMessage = 'Échec du chargement des détails de la demande.';
        this.isLoading = false;
      }
    });
  }

  onReportFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check if file is a PDF or image
      const validTypes = [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png'
      ];

      if (validTypes.includes(file.type)) {
        this.selectedReportFile = file;
        this.reportFileName = file.name;
        this.errorMessage = null;
      } else {
        this.errorMessage = 'Veuillez sélectionner un fichier PDF ou image (JPG, JPEG, PNG) pour le rapport.';
        this.selectedReportFile = null;
        this.reportFileName = '';
        event.target.value = '';
      }
    }
  }

  onInvoiceFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check if file is a PDF or image
      const validTypes = [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png'
      ];

      if (validTypes.includes(file.type)) {
        this.selectedInvoiceFile = file;
        this.invoiceFileName = file.name;
        this.errorMessage = null;
      } else {
        this.errorMessage = 'Veuillez sélectionner un fichier PDF ou image (JPG, JPEG, PNG) pour la facture.';
        this.selectedInvoiceFile = null;
        this.invoiceFileName = '';
        event.target.value = '';
      }
    }
  }

  uploadFiles(): void {
    if (!this.selectedReportFile && !this.selectedInvoiceFile) {
      this.errorMessage = 'Veuillez sélectionner au moins un fichier à télécharger.';
      return;
    }

    if (!this.demandeId) {
      this.errorMessage = 'Identifiant de demande non disponible. Veuillez réessayer.';
      return;
    }

    this.isUploading = true;
    this.errorMessage = null;
    this.successMessage = null;

    // Upload report file if selected
    if (this.selectedReportFile) {
      this.uploadReportFile();
    } else if (this.selectedInvoiceFile) {
      // If only invoice file is selected, upload it
      this.uploadInvoiceFile();
    }
  }

  uploadReportFile(): void {
    if (!this.selectedReportFile || !this.demandeId) return;

    this.resultsService.uploadReportFile(this.demandeId, this.selectedReportFile).subscribe({
      next: () => {
        // If invoice file is also selected, upload it
        if (this.selectedInvoiceFile) {
          this.uploadInvoiceFile();
        } else {
          this.finishUpload();
        }
      },
      error: (error) => {
        console.error('Error uploading report file:', error);
        this.handleUploadError(error);
      }
    });
  }

  uploadInvoiceFile(): void {
    if (!this.selectedInvoiceFile || !this.demandeId) return;

    this.resultsService.uploadInvoiceFile(this.demandeId, this.selectedInvoiceFile).subscribe({
      next: () => {
        this.finishUpload();
      },
      error: (error) => {
        console.error('Error uploading invoice file:', error);
        this.handleUploadError(error);
      }
    });
  }

  finishUpload(): void {
    this.isUploading = false;
    this.successMessage = 'Fichiers téléchargés avec succès!';

    // Reset file selection
    this.selectedReportFile = null;
    this.selectedInvoiceFile = null;
    this.reportFileName = '';
    this.invoiceFileName = '';

    // Reset upload section visibility flags
    this.showReportUpload = false;
    this.showInvoiceUpload = false;

    // Reload the file information
    this.checkExistingFiles();
  }

  handleUploadError(error: any): void {
    this.isUploading = false;

    // Display a more specific error message if available
    if (error.error?.message) {
      this.errorMessage = `Échec du téléchargement: ${error.error.message}`;
    } else if (error.message) {
      this.errorMessage = `Échec du téléchargement: ${error.message}`;
    } else {
      this.errorMessage = 'Échec du téléchargement des fichiers. Veuillez réessayer.';
    }
  }

  sendToClient(): void {
    if (!this.demandeId) {
      this.errorMessage = 'Identifiant de demande non disponible. Veuillez réessayer.';
      return;
    }

    this.isSending = true;
    this.errorMessage = null;

    this.resultsService.sendReportToClient(this.demandeId).subscribe({
      next: () => {
        this.isSending = false;
        this.successMessage = 'Rapport envoyé au client avec succès!';

        // Redirect back to results list after 2 seconds
        setTimeout(() => {
          this.router.navigate(['/receptionist/results']);
        }, 2000);
      },
      error: (error) => {
        console.error('Error sending report to client:', error);
        this.isSending = false;

        // Display a more specific error message if available
        if (error.error?.message) {
          this.errorMessage = `Échec de l'envoi: ${error.error.message}`;
        } else if (error.message) {
          this.errorMessage = `Échec de l'envoi: ${error.message}`;
        } else {
          this.errorMessage = 'Échec de l\'envoi du rapport au client. Veuillez réessayer.';
        }
      }
    });
  }

  // File handling methods
  hasUploadedFiles(): boolean {
    return this.hasReportFile() || this.hasInvoiceFile();
  }

  hasReportFile(): boolean {
    return !!(this.reportDetails && this.reportDetails.rapport_file);
  }

  hasInvoiceFile(): boolean {
    return !!(this.reportDetails && this.reportDetails.facture_file);
  }

  getReportFileName(): string {
    if (!this.hasReportFile()) return '';
    const path = this.reportDetails.rapport_file;
    return path.split('/').pop() || 'rapport.pdf';
  }

  getInvoiceFileName(): string {
    if (!this.hasInvoiceFile()) return '';
    const path = this.reportDetails.facture_file;
    return path.split('/').pop() || 'facture.pdf';
  }

  isClientStatusSent(): boolean {
    return this.reportDetails && this.reportDetails.client_status === 'sent';
  }

  viewFile(type: 'report' | 'invoice'): void {
    const filePath = type === 'report' ? this.reportDetails.rapport_file : this.reportDetails.facture_file;
    if (!filePath) return;

    // Open the file in a new tab
    const url = this.resultsService.getFileUrl(filePath);
    window.open(url, '_blank');
  }

  downloadFile(type: 'report' | 'invoice'): void {
    const filePath = type === 'report' ? this.reportDetails.rapport_file : this.reportDetails.facture_file;
    if (!filePath) return;

    // Create a link to download the file
    const url = this.resultsService.getFileUrl(filePath);
    const a = document.createElement('a');
    a.href = url;
    a.download = type === 'report' ? this.getReportFileName() : this.getInvoiceFileName();
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  deleteFile(type: 'report' | 'invoice'): void {
    if (!this.demandeId) return;

    if (!confirm(`Êtes-vous sûr de vouloir supprimer ce fichier ${type === 'report' ? 'rapport' : 'facture'} ?`)) {
      return;
    }

    const deleteMethod = type === 'report'
      ? this.resultsService.deleteReportFile(this.demandeId)
      : this.resultsService.deleteInvoiceFile(this.demandeId);

    deleteMethod.subscribe({
      next: () => {
        this.successMessage = `Fichier ${type === 'report' ? 'rapport' : 'facture'} supprimé avec succès.`;

        // Update the component state
        if (type === 'report') {
          this.reportDetails.rapport_file = null;
          // Show the report upload section
          this.showReportUpload = true;
        } else {
          this.reportDetails.facture_file = null;
          // Show the invoice upload section
          this.showInvoiceUpload = true;
        }

        // Clear success message after a short delay
        setTimeout(() => {
          this.successMessage = null;
        }, 2000);
      },
      error: (error) => {
        console.error(`Error deleting ${type} file:`, error);
        this.errorMessage = `Échec de la suppression du fichier ${type === 'report' ? 'rapport' : 'facture'}.`;
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/receptionist/results']);
  }
}
