@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* General Styles */
.reclamation-container {
  text-align: center;
  padding: 3rem 5%;
  font-family: 'Montserrat', sans-serif;
  background: white;
  color: black;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  max-width:60%;
  margin: 0 auto;
}

/* Title Container */
.reclamation-title-container {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.circle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, #2496d3, #0a6ebd);
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(36, 150, 211, 0.3);
}

.circle-icon i {
  font-size: 1.5rem;
  color: white;
}

/* Reclamation Title */
.reclamation-title {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(1.8rem, calc(1vw + 1.2rem), 2.5rem);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.15rem;
  margin: 0;
  border-bottom: 2px solid #2496d3;
  padding-bottom: 0.4rem;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* View All Link */
.view-all-link {
  margin-bottom: 1.5rem;
  text-align: right;
}

.view-all-link a {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #2496d3;
  font-weight: 600;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: all 0.3s;
  cursor: pointer;
}

.view-all-link a:hover {
  background-color: rgba(36, 150, 211, 0.1);
  color: #0a6ebd;
  transform: translateY(-2px);
}

.view-all-link i {
  font-size: 1rem;
}

/* Form Container */
.reclamation-form-container {
  background: #f9f9f9;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-top: 1.5rem;
  text-align: left;
}

/* Form Groups */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.required {
  color: #dc3545;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-family: 'Montserrat', sans-serif;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 3px rgba(36, 150, 211, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

/* Dropdown specific styles */
.select-wrapper {
  position: relative;
  width: 100%;
}

select.form-control {
  appearance: none; /* Remove default appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  padding-right: 2.5rem; /* Add space for the dropdown arrow */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  height: auto; /* Ensure height is not restricted */
  min-height: 2.75rem; /* Minimum height to ensure text is visible */
  text-overflow: ellipsis; /* Add ellipsis for text overflow */
  white-space: normal; /* Allow text to wrap */
}

select.form-control::-ms-expand {
  display: none; /* Hide the default arrow in IE */
}

select.form-control option {
  padding: 0.75rem;
  font-size: 1rem;
  color: #333;
  background-color: white;
  white-space: normal; /* Allow text to wrap in options */
  min-height: 2rem; /* Minimum height for options */
}

/* Error Messages */
.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* File Upload */
.file-upload-container {
  position: relative;
  border: 2px dashed #ced4da;
  border-radius: 5px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s;
  background-color: #f8f9fa;
}

.file-upload-container:hover {
  border-color: #2496d3;
  background-color: #f0f7fc;
}

.file-upload-input {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.file-upload-label i {
  font-size: 1.5rem;
  color: #2496d3;
}

/* Submit Button */
.form-actions {
  text-align: center;
  margin-top: 2rem;
}

.btn-submit {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(to right, #2496d3, #0a6ebd);
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 10px rgba(36, 150, 211, 0.3);
  min-width: 200px;
}

.btn-submit:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(36, 150, 211, 0.4);
}

.btn-submit:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner {
  font-size: 2rem;
  color: #2496d3;
  margin-bottom: 1rem;
}

/* Success Modal */
.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.success-modal {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 400px;
  padding: 0;
  overflow: hidden;
  animation: slideIn 0.4s ease-out;
}

.success-modal-content {
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.success-icon {
  color: #28a745;
  margin-bottom: 20px;
}

.success-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  margin-bottom: 15px;
  color: #28a745;
}

.success-message {
  font-size: 16px;
  margin-bottom: 25px;
  color: #333;
}

.success-close-btn {
  padding: 10px 25px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.success-close-btn:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .reclamation-container {
    padding: 2rem 3%;
  }

  .reclamation-form-container {
    padding: 1.5rem;
  }

  .reclamation-title {
    font-size: 1.5rem;
  }

  .circle-icon {
    width: 3rem;
    height: 3rem;
  }

  .circle-icon i {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .reclamation-container {
    padding: 1.5rem 2%;
  }

  .reclamation-title-container {
    gap: 0.8rem;
  }

  .reclamation-title {
    font-size: 1.3rem;
  }

  .circle-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .circle-icon i {
    font-size: 1rem;
  }

  .btn-submit {
    width: 100%;
    padding: 0.7rem 1rem;
  }
}