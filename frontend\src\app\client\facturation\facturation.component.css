@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ============================= */
/*       Conteneur Principal    */
/* ============================= */
.facturation-container {
  background: white;
  color: black;
  text-align: center;
  padding: 50px 5%;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInUp 1s ease-in-out;
}

/* ============================= */
/*        Titre Principal       */
/* ============================= */
.list {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 30px;
  display: inline-block;
  padding-bottom: 10px;
  border-bottom: 3px solid #2496d3;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ============================= */
/*       Tableau des Factures   */
/* ============================= */
.styled-table {
  width: 100%;
  /* max-width: 1000px;  <-- Ligne commentée si on ne veut pas limiter la largeur */
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* En-têtes du tableau */
th {
  background-color: #2496d3;
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 12px;
  text-transform: uppercase;
}

/* Cellules */
td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
}

/* Lignes alternées pour un effet visuel */
tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

tbody tr:hover {
  background-color: rgba(36, 150, 211, 0.2);
  transition: background 0.3s ease-in-out;
}

/* Statuts bien alignés */
.status-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

/* ============================= */
/*      Styles des Statuts      */
/* ============================= */
.paid {
  background: #28a745;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-block;
  text-align: center;
  min-width: 100px;
}

.pending {
  background: #ffc107;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-block;
  text-align: center;
  min-width: 100px;
}

.unpaid {
  background: #dc3545;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-block;
  text-align: center;
  min-width: 100px;
}

/* ============================= */
/*      Bouton Voir Détails     */
/* ============================= */
.btn-view {
  background: #007bff;
  color: white;
  padding: 8px 12px;
  border: none;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 5px 15px rgba(0, 123, 255, 0.5);
  display: flex;           /* pour l'icône et le texte */
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.btn-view:hover {
  background: #0056b3;
  transform: scale(1.05);
  box-shadow: 0px 10px 25px rgba(0, 123, 255, 0.7);
}

/* Icône dans le bouton Voir */
.btn-view i {
  font-size: 16px;
}

/* ============================= */
/*       Animation fade-in      */
/* ============================= */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes glowText {
  from { text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); }
  to { text-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5); }
}

/* ============================= */
/*            Responsive        */
/* ============================= */
@media (max-width: 768px) {
  .styled-table {
    max-width: 100%;
  }
  .btn-view {
    font-size: 14px;
    padding: 6px 10px;
  }
  .status-container {
    flex-direction: column;
    gap: 4px;
  }
}
