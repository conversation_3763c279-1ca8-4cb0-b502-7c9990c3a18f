<div class="receptionist-dashboard">
  <!-- Titre avec Icône -->
  <div class="dashboard-title-container">
    <div class="circle-icon"><i class="fas fa-concierge-bell"></i></div>
    <h2 class="dashboard-title">Tableau de bord du Réceptionniste</h2>
  </div>

  <!-- Message de Bienvenue -->
  <div class="welcome-message">
    <p>Bienvenue, <strong>{{ name }} {{nickname}}</strong> </p>
  </div>

  <!-- Section Statistiques -->
  <div class="statistics">
    <div class="statistic-card">
      <span class="statistic-icon"><i class="fas fa-envelope"></i></span>
      <h3 class="statistic-title">Demandes en attente de validation</h3>
      <p class="statistic-value">{{ nonValidDemandesCount }}</p>
    </div>

    <div class="statistic-card">
      <span class="statistic-icon"><i class="fas fa-file-alt"></i></span>
      <h3 class="statistic-title">Rapports en attente d'envoi</h3>
      <p class="statistic-value">{{ nonSentRapports }}</p>
    </div>
  </div>

  <!-- Tabs Navigation -->
  <div class="dashboard-tabs">
    <div class="tab-buttons">
      <button type="button" class="tab-button active" data-tab="demandes">
        <i class="fas fa-envelope"></i> Demandes
      </button>
      <button type="button" class="tab-button" data-tab="analyses">
        <i class="fas fa-flask"></i> Analyses
      </button>
      <button type="button" class="tab-button" data-tab="documents">
        <i class="fas fa-file-alt"></i> Documents
      </button>
      <button type="button" class="tab-button" data-tab="admin">
        <i class="fas fa-cog"></i> Administration
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- Demandes Tab -->
    <div class="tab-pane active" id="demandes-tab">
      <div class="receptionist-actions">
        <button type="button" class="btn-receptionist demandes" (click)="openDemandes()" aria-label="Gérer les demandes">
          <i class="fas fa-envelope"></i> Gérer les demandes
        </button>
        <button type="button" class="btn-receptionist validation" (click)="openValidation()" aria-label="Demandes approuvées">
          <i class="fas fa-check-circle"></i> Demandes approuvées
        </button>
      </div>
    </div>

    <!-- Analyses Tab -->
    <div class="tab-pane" id="analyses-tab">
      <div class="receptionist-actions">
        <button type="button" class="btn-receptionist results" (click)="openResults()" aria-label="Résultats validés">
          <i class="fas fa-vial"></i> Transmettre les Résultats
        </button>
        <button type="button" class="btn-receptionist rapports" (click)="openRapports()" aria-label="Rapports">
          <i class="fas fa-chart-bar"></i> Rapports
        </button>
        <button type="button" class="btn-receptionist facturation" (click)="openFacturation()" aria-label="Facturation">
          <i class="fas fa-money-bill"></i> Facturation
        </button>

      </div>
    </div>

    <!-- Documents Tab -->
    <div class="tab-pane" id="documents-tab">
      <div class="receptionist-actions">
        <button type="button" class="btn-receptionist transmission" (click)="openFicheTransmission()" aria-label="Fiche de transmission">
          <i class="fas fa-file-alt"></i> Fiche de transmission
        </button>
        <button type="button" class="btn-receptionist registre" (click)="openRegistre()" aria-label="Registre de suivi">
          <i class="fas fa-book"></i> Registre de suivi
        </button>
       
      </div>
    </div>

    <!-- Administration Tab -->
    <div class="tab-pane" id="admin-tab">
      <div class="receptionist-actions">
        <button type="button" class="btn-receptionist users" (click)="openUsers()" aria-label="Gestion des utilisateurs">
          <i class="fas fa-users"></i> Gérer les utilisateurs
        </button>
        <button type="button" class="btn-receptionist gestion-analyse" (click)="openGestionAnalyse()" aria-label="Gestion des analyses">
          <i class="fas fa-flask"></i> Gérer les analyses
        </button>
        <button type="button" class="btn-receptionist reclamations" (click)="openReclamations()" aria-label="Réclamations">
          <i class="fas fa-exclamation-triangle"></i> Gérer les réclamations
        </button>
      </div>
    </div>
  </div>
</div>