<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "Checking demandes table structure...\n";
    
    $columns = DB::select('DESCRIBE demandes');
    echo "Demandes table columns:\n";
    echo "======================\n";
    
    $hasRapportCreated = false;
    foreach($columns as $column) {
        echo "- {$column->Field} ({$column->Type}) " . ($column->Null === 'YES' ? 'NULL' : 'NOT NULL');
        if ($column->Default !== null) {
            echo " DEFAULT '{$column->Default}'";
        }
        echo "\n";
        
        if ($column->Field === 'rapport_created') {
            $hasRapportCreated = true;
        }
    }
    
    echo "\nTotal columns: " . count($columns) . "\n";
    
    if ($hasRapportCreated) {
        echo "\n✅ 'rapport_created' column exists in demandes table!\n";
    } else {
        echo "\n❌ 'rapport_created' column does NOT exist in demandes table\n";
    }
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
