@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* Main Container */
.reclamation-details-container {
  font-family: 'Montserrat', sans-serif;
  padding: 1rem;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.2);
  margin: 1rem auto;
  max-width: 1200px;
}

/* Title */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0;
  border-bottom: 2px solid #2496d3;
  display: inline-block;
  padding-bottom: 3px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  flex: 1;
}

@keyframes glowText {
  from { text-shadow: 0 0 5px rgba(36, 150, 211, 0.3); }
  to { text-shadow: 0 0 10px rgba(36, 150, 211, 0.6); }
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(36, 150, 211, 0.3);
  border-radius: 50%;
  border-top-color: #2496d3;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 5px;
  margin: 1rem 0;
  text-align: center;
}

/* Success Message */
.success-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  padding: 2rem;
  text-align: center;
  z-index: 1000;
  max-width: 400px;
  width: 90%;
  animation: fadeIn 0.3s ease-out;
}

.success-icon {
  color: #28a745;
  font-size: 3rem;
  margin-bottom: 1rem;
}

.success-message p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.btn-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Details Content */
.details-content {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

/* Reclamation Header */
.reclamation-header {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 0.6rem 0.8rem;
  border-radius: 8px;
  margin-bottom: 0.6rem;
  font-size: 0.9rem;
}

.reclamation-id {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.9rem;
}

.value {
  font-size: 1.1rem;
}

/* Status Tags */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  font-size: 0.75rem;
  margin-left: 4px;
}

.status-tag.pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-tag.in-progress {
  background-color: #cce5ff;
  color: #004085;
}

.status-tag.resolved {
  background-color: #d4edda;
  color: #155724;
}

/* Cards */
.client-info-card,
.reclamation-content-card,
.status-change-card,
.response-form-card,
.previous-responses-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0.8rem;
  margin-bottom: 0.8rem;
}

h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.6rem;
  color: #2496d3;
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

h3 fa-icon {
  color: #2496d3;
}

/* Client Info */
.client-info, .reclamation-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

/* Client Info Table */
.client-info-table-container {
  width: 100%;
  overflow-x: auto;
}

.client-info-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 5px;
  overflow: hidden;
}

.client-info-table tr {
  border-bottom: 1px solid #f0f0f0;
}

.client-info-table tr:last-child {
  border-bottom: none;
}

.client-info-table td {
  padding: 6px 10px;
}

.client-info-table td.label {
  width: 30%;
  background-color: #f8f9fa;
  font-weight: 600;
  color: #6c757d;
  font-size: 0.85rem;
}

.client-info-table td.value {
  width: 70%;
  font-size: 0.9rem;
}

.description-text {
  background-color: #f8f9fa;
  padding: 0.6rem;
  border-radius: 5px;
  line-height: 1.4;
  font-size: 0.9rem;
  max-height: 100px;
  overflow-y: auto;
}

/* File Card */
.file-card {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 0.6rem;
  gap: 0.6rem;
  max-width: 400px;
}

.file-icon {
  font-size: 1.5rem;
  color: #2496d3;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.file-name {
  font-weight: 600;
  word-break: break-all;
  font-size: 0.9rem;
}

.file-actions {
  display: flex;
  gap: 0.3rem;
}

.btn-file-action {
  background-color: #e9ecef;
  border: none;
  border-radius: 5px;
  padding: 0.4rem;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
}

.btn-file-action:hover {
  background-color: #dee2e6;
}

/* Link Button */
.btn-link {
  background: none;
  border: none;
  color: #2496d3;
  text-decoration: underline;
  cursor: pointer;
  padding: 0;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.btn-link:hover {
  color: #1a7bb9;
}

/* Status Change */
.card-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.6rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-end;
}

.btn-status {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
  opacity: 0.7;
}

.btn-status:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-status.active {
  opacity: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.btn-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.btn-status.in-progress {
  background-color: #cce5ff;
  color: #004085;
}

.btn-status.resolved {
  background-color: #d4edda;
  color: #155724;
}

/* Response Form */
.form-group {
  margin-bottom: 0.8rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 600;
  font-size: 0.9rem;
}

textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 5px;
  resize: vertical;
  min-height: 80px;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.9rem;
}

textarea:focus {
  outline: none;
  border-color: #2496d3;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

textarea.is-invalid {
  border-color: #dc3545;
}

.form-actions {
  display: flex;
  justify-content: center;
}

.btn-submit {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 1.5rem;
  background: linear-gradient(to right, #2496d3, #0a6ebd);
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 180px;
  justify-content: center;
}

.btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(36, 150, 211, 0.3);
}

.btn-submit:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Previous Responses */
.responses-list {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
  max-height: 200px;
  overflow-y: auto;
}

.response-item {
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 0.6rem;
}

.response-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.3rem;
  font-size: 0.85rem;
}

.response-author {
  font-weight: 600;
  color: #2496d3;
}

.response-date {
  color: #6c757d;
  font-size: 0.8rem;
}

.response-content {
  line-height: 1.4;
  font-size: 0.9rem;
}

/* Header with Back Button */
.header-with-back {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.btn-back {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #f0f0f0;
  color: #2496d3;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.btn-back:hover {
  background-color: #e0e0e0;
  transform: translateX(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-header-flex {
    flex-direction: column;
    align-items: flex-start;
  }

  .status-options {
    justify-content: flex-start;
    margin-top: 0.5rem;
    width: 100%;
  }

  .btn-status {
    justify-content: center;
    flex: 1;
  }
}

@media (max-width: 576px) {
  .reclamation-details-container {
    padding: 0.8rem;
  }

  h2 {
    font-size: 1.2rem;
  }

  .client-info-table td {
    padding: 6px 8px;
  }

  .client-info-table td.label {
    width: 40%;
  }

  .client-info-table td.value {
    width: 60%;
    font-size: 0.85rem;
  }

  .file-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .file-actions {
    margin-top: 0.3rem;
  }

  .reclamation-id {
    font-size: 0.85rem;
  }
}