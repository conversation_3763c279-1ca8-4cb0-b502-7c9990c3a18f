import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Demande } from '../demandes/demande.model';

@Injectable({
  providedIn: 'root'
})
export class FicheTransmissionService {
  private apiUrl = 'http://127.0.0.1:8000/api/fiches';

  constructor(private http: HttpClient) {}

  // Fetch all fiches de transmission
  getAllFiches(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}`);
  }
  sendFiche(ficheId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${ficheId}/send`, {});
  }
  // Fetch fiches related to a specific fiche_transmission_id
  getFichesByTransmissionId(ficheTransmissionId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${ficheTransmissionId}`);
  }

  createFicheTransmission(demandeId: String): Observable<{ ficheId: number }> {
    console.log(demandeId);
    return this.http.post<{ ficheId: number }>(`${this.apiUrl}/create/${demandeId}`, {});
  }
  createRapport(demandeId: string): Observable<any> {
    return this.http.post(`http://127.0.0.1:8000/api/create-rapport/${demandeId}`, {});
  }
  getRapportIdByDemande(demandeId: string): Observable<any> {
    return this.http.get(`http://127.0.0.1:8000/api/rapport/id/demande/${demandeId}`);
  }

}
