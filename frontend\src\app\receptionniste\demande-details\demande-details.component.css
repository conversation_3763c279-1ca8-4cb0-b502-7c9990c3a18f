@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* =========================
   1. Global Container
   ========================= */
.demandes-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;

  margin-top: 40px;
  margin-bottom: 40px;
}

/* =========================
   2. Header Actions & Titre principal
   ========================= */
.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  position: relative;
}

.title-print-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
/* Rejection Reason Styling */
.rejection-reason {
  background-color: rgba(220, 53, 69, 0.1); /* Light red background */
  border-left: 4px solid #dc3545; /* Red left border */
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.rejection-content {
  flex: 1;
}

.rejection-content h4 {
  color: #dc3545;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.rejection-content p {
  color: #333;
  font-size: 15px;
  line-height: 1.5;
  margin: 0;
  text-align: left;
}


h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 26px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0; /* Changed from 20px to align with button */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text; /* Standard property for compatibility */
  -webkit-text-fill-color: transparent;
  border-bottom: 4px solid #2496d3;
  text-shadow: none;
}

/* Print Button Styling */
.btn-print {
  /* Position the print button on the right */
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);

  /* Button styling */
  background: #2496d3;
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 4px 10px rgba(36, 150, 211, 0.3);
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-print:hover {
  background: #1e7bbd;
  transform: translateY(-50%) scale(1.05); /* Keep vertical centering while scaling */
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
}

/* =========================
   3. Informations principales (p, strong)
   ========================= */
.demandes-container p {
  margin: 10px 0;
  font-size: 16px;
  color: #333;
}
.demandes-container p strong {
  color: #000;
  font-weight: 600;
}

/* =========================
   4. Status Container & Tags
   ========================= */
.status-container {
  margin: 20px auto;
}
.status-container p {
  font-size: 18px;
}
.status-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  text-transform: uppercase;
  margin-left: 10px;
}
.status-tag.pending {
  background-color: #fff3cd; /* Jaune pâle */
  color: #856404;
}
.status-tag.ongoing {
  background-color: #cce5ff; /* Bleu pâle */
  color: #004085;
}
.status-tag.derogation {
  background-color: #ffe5cc; /* Orange pâle */
  color: #663c00;
}
.status-tag.valid {
  background-color: #d4edda; /* Vert pâle */
  color: #155724;
}
.status-tag.rejected {
  background-color: #f8d7da; /* Rouge pâle */
  color: #721c24;
}

/* =========================
   5. Tableau d'échantillons
   ========================= */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 30px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.2);
}

thead tr th {
  background-color: #2496d3;
  color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 15px;
  text-align: center;
}

tbody tr td {
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  padding: 14px;
  border-bottom: 1px solid #ddd;
  vertical-align: middle;
  /* Par défaut, centré. Ajustez selon la colonne si nécessaire */
  text-align: center;
}

/* Lignes alternées (facultatif) */
tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* Survol */
tbody tr:hover {
  background-color: rgba(36, 150, 211, 0.05);
  transition: background 0.3s ease;
}

/* =========================
   6. Loading & Error & Notification
   ========================= */
.loading {
  font-size: 18px;
  color: #2496d3;
  font-weight: bold;
  margin-top: 20px;
}
.error {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}
.notification {
  background: white;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;

}

/* =========================
   7. Buttons Container
   ========================= */
.buttons-container {
  margin-top: 30px;
}
.buttons-container button {
  margin: 10px;
}

/* =========================
   7.1 Pre-validation
   ========================= */
.btn-prevalider {
  background: #2496d3;
  color: #fff;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 10px rgba(36, 150, 211, 0.3);
}
.btn-prevalider:hover {
  background: #1e7bbd;
  transform: scale(1.03);
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.6);
}

/* =========================
   7.2 Validation Complète & Dérogation
   ========================= */
.validation-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}
.btn-validation {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.btn-validation:hover {
  background-color: #0056b3;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
.btn-derogation {
  background-color: #f7bd0f;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.btn-derogation:hover {
  background-color: #e0a800;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* =========================
   7.3 "La facture a été créée"
   ========================= */
/* Couleur verte + icône coche avant le texte */
.statusDenvoir.status-sent {
  display: inline-block;
  color: #fff;
  background-color: #28a745; /* Vert succès */
  font-family: Arial, sans-serif;
  padding: 8px 12px;
  border-radius: 4px;
  max-width: fit-content;
  position: relative;
  box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}
.statusDenvoir.status-sent::before {
  content: "✓  "; /* Icône coche */
  color: #fff;
}
.statusDenvoir.status-not-sent {
  color: #fff;
  background-color: #dc3545; /* Rouge */
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.15);
  margin: 10px auto;
  display: inline-block;
  position: relative;
}
.statusDenvoir.status-not-sent::before {
  content: "⚠  ";
  color: #fff;
}


/* =========================
   8. Devis Section & Container
   ========================= */
.devis-section {
  margin-top: 20px;
}

.devis-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  margin-top: 20px;
}

.devis-container,
.devis-container2 {
  margin-top: 30px;
  text-align: left;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.1);
}

.devis-label {
  padding: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #2496d3;
  margin-bottom: 5px;

}
.devis-subtitle {
  font-style: 'Poppins';
  color: #666;
  margin-bottom: 15px;
}

/* =========================
   8.1 Table inside Devis
   ========================= */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  text-align: center;
  font-size:20px;
}
.table.table-bordered th,
.table.table-bordered td {
  border: 1px solid #ccc;
  padding: 10px;
}
.table-light {
  background-color: #e9ecef;
  font-weight: 600;
}
.total-row td {
  background-color: #f1f3f5;
}
.total-row td.text-primary {
  color: #007bff;
  font-size: 18px;
  font-weight: bold;
}

/* =========================
   9. Extra Buttons
   ========================= */
.btn {
  padding: 8px 12px;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  margin: 5px;
  transition: all 0.3s ease-in-out;
}
.btn-sm {
  font-size: 14px;
  padding: 6px 10px;
}
.btn-primary {
  background-color: #007bff;
  color: #fff;
}
.btn-primary:hover {
  background-color: #0056b3;
  transform: scale(1.03);
  box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}
.btn-success {
  background-color: #28a745;
  color: #fff;
}
.btn-success:hover {
  background-color: #218838;
  transform: scale(1.03);
  box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* Text utilities */
.text-primary {
  color: #007bff !important;
}
.fw-bold {
  font-weight: 600;
}

/* =========================
   10. Spacing & Layout
   ========================= */
.message-container {
  margin-bottom: 20px;
}
.button-row {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}
.no-devis p {
  margin: 0;
  font-style: italic;
  color: #999;
}

/* =========================
   11. Animations
   ========================= */
@keyframes glowText {
  from {
    text-shadow: 0 0 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0 0 20px rgba(36, 150, 211, 0.8);
  }
}

/* =========================
   12. Responsive
   ========================= */
@media (max-width: 768px) {
  .demandes-container {
    padding: 30px;
  }
  table, .table {
    display: block;
    overflow-x: auto; /* Scroll horizontal si trop large */
  }
  /* Ajustez les polices si nécessaire */
  h2 {
    font-size: 22px;
  }
  .btn-prevalider, .btn-validation, .btn-derogation {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* =========================
   Notification de soumission
   ========================= */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

/* Notification styling is already defined above */

.notification-icon {
  font-size: 50px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.notification-message {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

/* =========================
   Loading Spinner Overlay
   ========================= */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Content hidden when loading */
.content-hidden {
  opacity: 0.3;
  pointer-events: none;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =========================
   9. Payment Section Styles
   ========================= */
.payment-section {
  margin-top: 30px;
  border-top: 2px dashed #e0e0e0;
  padding-top: 20px;
}

.payment-label {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  text-align: left;
}

.payment-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.payment-error {
  color: #dc3545;
  padding: 10px;
  background-color: #f8d7da;
  border-radius: 5px;
  margin-bottom: 15px;
}

.no-payments {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-bottom: 15px;
  color: #6c757d;
}

.payment-table-container {
  margin-top: 15px;
  overflow-x: auto;
}

.payment-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.payment-table th,
.payment-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.payment-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.payment-status {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.actions-cell {
  white-space: nowrap;
}

.btn-action {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px 8px;
  margin-right: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.btn-action.view {
  color: #007bff;
}

.btn-action.view.active {
  background-color: #007bff;
  color: white;
}

.btn-action.download {
  color: #28a745;
}

.btn-action.approve {
  color: #28a745;
}

.btn-action.reject {
  color: #dc3545;
}

.btn-action:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Preview row styles */
.preview-row {
  background-color: #f8f9fa;
}

.preview-cell {
  padding: 0 !important;
}

.preview-container {
  padding: 15px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-message {
  color: #dc3545;
  padding: 10px;
  background-color: #f8d7da;
  border-radius: 5px;
  margin-bottom: 15px;
}

.btn-retry {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.preview-content {
  display: flex;
  flex-direction: column;
}

.file-preview {
  margin-top: 10px;
}

.file-preview h4 {
  font-size: 16px;
  margin-bottom: 10px;
}

.image-container {
  max-width: 100%;
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pdf-container,
.unknown-container {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  text-align: center;
}

.pdf-icon,
.file-icon {
  margin-bottom: 10px;
  color: #dc3545;
}

.file-icon {
  color: #6c757d;
}

.file-name {
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
  text-align: center;
}

.btn-open-pdf,
.btn-download,
.btn-open-direct {
  display: inline-block;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  margin-right: 5px;
  text-decoration: none;
}

.btn-download {
  background-color: #28a745;
}

.btn-open-direct {
  background-color: #6c757d;
}

.btn-retry {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

/* =========================
   10. Responsive Design for Payment Section
   ========================= */
@media (max-width: 768px) {
  .payment-table th,
  .payment-table td {
    padding: 8px;
  }

  .preview-image {
    max-height: 200px;
  }
}

/* PDF Actions */
.pdf-actions {
  margin-top: 10px;
}

.btn-print {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-print:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-print i {
  margin-right: 8px;
}
