import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private apiUrl = 'http://localhost:8000/api';

  constructor(private http: HttpClient) { }

  /**
   * Get payment details for a specific demande
   * @param demandeId The demande ID to get payment for
   * @returns Observable of the payment details
   */
  getPaymentsByDemandeId(demandeId: string): Observable<any> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    return this.http.get(`${this.apiUrl}/payments/demande/${demandeId}`, { headers }).pipe(
      map(response => response),
      catchError(error => {
        console.error('Error fetching payments:', error);
        return throwError(() => new Error('Failed to load payment information'));
      })
    );
  }

  /**
   * Get the URL for a payment proof file
   * @param paymentProofPath The path to the payment proof file
   * @returns The URL for the payment proof file
   */
  getPaymentProofUrl(paymentProofPath: string): string {
    if (!paymentProofPath) return '';

    // Log the original path for debugging
    console.log('Original payment proof path:', paymentProofPath);

    // Check if the URL already contains http:// or https://
    if (paymentProofPath.startsWith('http://') || paymentProofPath.startsWith('https://')) {
      console.log('Using direct URL:', paymentProofPath);
      return paymentProofPath;
    }

    // Extract just the filename if it contains a path
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    } else if (fileName.includes('\\')) {
      fileName = fileName.split('\\').pop() ?? fileName;
    }

    // Log the extracted filename for debugging
    console.log('Extracted filename for URL construction:', fileName);

    // Check if this is likely an image file
    const isImage = this.isImageFile(fileName);

    // For images, use the dedicated image-file endpoint with CORS headers
    if (isImage) {
      const imageFileUrl = `${this.apiUrl}/image-file/${encodeURIComponent(fileName)}`;
      console.log('Using image-file endpoint for image:', imageFileUrl);
      return imageFileUrl;
    }

    // Add the payments/ prefix if it's not already there and doesn't have another path
    if (!fileName.includes('/') && !fileName.startsWith('payments/')) {
      // Check if it's likely a payment proof file (based on naming convention)
      if (fileName.startsWith('payment_') ||
          fileName.includes('_payment') ||
          fileName.includes('proof') ||
          fileName.includes('justificatif')) {
        fileName = `payments/${fileName}`;
        console.log('Added payments/ prefix to filename:', fileName);
      }
    }

    // Try the direct file route for non-image files
    const directFileUrl = `${this.apiUrl}/direct-file/${encodeURIComponent(fileName)}`;
    console.log('Using direct file route:', directFileUrl);
    return directFileUrl;
  }

  /**
   * Check if a file is an image based on its extension
   * @param filePath The file path or name
   * @returns True if the file is an image, false otherwise
   */
  private isImageFile(filePath: string): boolean {
    if (!filePath) return false;

    const lowerPath = filePath.toLowerCase();
    return lowerPath.endsWith('.jpg') ||
           lowerPath.endsWith('.jpeg') ||
           lowerPath.endsWith('.png') ||
           lowerPath.endsWith('.gif') ||
           lowerPath.endsWith('.bmp');
  }

  /**
   * Get the download URL for a payment proof file
   * @param paymentProofPath The relative path to the payment proof file
   * @returns The download URL for the payment proof file
   */
  getPaymentProofDownloadUrl(paymentProofPath: string): string {
    // Use the same URL construction logic as getPaymentProofUrl
    return this.getPaymentProofUrl(paymentProofPath);
  }

  /**
   * View a payment proof file directly
   * @param paymentProofPath The relative path to the payment proof file
   * @returns Observable of the file response
   */
  viewPaymentProof(paymentProofPath: string): Observable<any> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // Extract just the filename
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    }

    // Try multiple approaches for maximum compatibility

    // 1. Try the direct file route first (most reliable)
    return this.http.get(`${this.apiUrl}/direct-file/${encodeURIComponent(fileName)}`, {
      headers,
      responseType: 'blob'
    }).pipe(
      catchError(error => {
        console.error('Error using direct file route:', error);

        // 2. Fall back to the view endpoint
        return this.http.get(`${this.apiUrl}/payments/view/${encodeURIComponent(fileName)}`, {
          headers,
          responseType: 'blob'
        });
      })
    );
  }

  /**
   * Download a payment proof file
   * @param paymentProofPath The path to the payment proof file
   * @returns Observable of the file as a Blob
   */
  downloadPaymentProof(paymentProofPath: string): Observable<Blob> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // Extract just the filename
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    }

    // Try multiple approaches for maximum compatibility

    // 1. Try the direct file route first (most reliable)
    return this.http.get(`${this.apiUrl}/direct-file/${encodeURIComponent(fileName)}`, {
      headers,
      responseType: 'blob'
    }).pipe(
      catchError(error => {
        console.error('Error using direct file route for download:', error);

        // 2. Fall back to the download endpoint
        return this.http.get(`${this.apiUrl}/payments/download/${encodeURIComponent(fileName)}`, {
          headers,
          responseType: 'blob'
        });
      })
    );
  }

  /**
   * Download a payment proof file by payment ID
   * @param paymentId The ID of the payment
   * @returns Observable of the file as a Blob
   */
  downloadPaymentProofById(paymentId: number): Observable<Blob> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    return this.http.get(`${this.apiUrl}/payments/${paymentId}/download`, {
      headers,
      responseType: 'blob'
    }).pipe(
      catchError(error => {
        console.error('Error downloading payment proof by ID:', error);
        return throwError(() => new Error('Failed to download payment proof'));
      })
    );
  }

  /**
   * Delete a payment
   * @param paymentId The ID of the payment to delete
   * @returns Observable of the delete response
   */
  deletePayment(paymentId: number): Observable<any> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    return this.http.delete(`${this.apiUrl}/payments/${paymentId}`, { headers });
  }

  /**
   * Update the status of a payment
   * @param paymentId The ID of the payment to update
   * @param status The new status ('approved' or 'rejected')
   * @param rejectionReason Optional reason for rejection (required when status is 'rejected')
   * @returns Observable of the update response
   */
  updatePaymentStatus(paymentId: number, status: 'approved' | 'rejected', rejectionReason?: string): Observable<any> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });

    const payload: any = { status };

    // Add rejection reason to payload if provided and status is 'rejected'
    if (status === 'rejected' && rejectionReason) {
      payload.rejection_reason = rejectionReason;
    }

    return this.http.put(`${this.apiUrl}/payments/${paymentId}/status`, payload, { headers }).pipe(
      catchError(error => {
        console.error('Error updating payment status:', error);
        return throwError(() => new Error('Failed to update payment status'));
      })
    );
  }
}
