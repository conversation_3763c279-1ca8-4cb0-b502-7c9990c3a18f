<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('samples', function (Blueprint $table) {
            $table->id();
            $table->foreignId('demande_id')->constrained('demandes')->onDelete('cascade');
            $table->string('identification_echantillon');
            $table->string('nature_echantillon');
            $table->string('provenance');
            $table->float('masse_echantillon');
            $table->string('etat');
            $table->json('analyses_demandees'); // Store checklist as JSON
            $table->string('delai_souhaite')->nullable();
            $table->string('analyse_souhaite')->nullable();
            $table->string('lot')->nullable();
            $table->string('reference')->nullable();
            $table->string('mode_reglement');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('samples');
    }
};
