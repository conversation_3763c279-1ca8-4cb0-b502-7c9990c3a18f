<?php

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "Verifying fiches table date_transmission fix...\n";
echo "===============================================\n";

try {
    // Check the specific column
    $columns = DB::select('DESCRIBE fiches');
    foreach($columns as $col) {
        if ($col->Field === 'date_transmission') {
            $nullable = $col->Null === 'YES' ? '✅ NULLABLE' : '❌ NOT NULLABLE';
            echo "date_transmission in fiches table: {$nullable}\n";
            
            if ($col->Null === 'YES') {
                echo "✅ SUCCESS: The fiches table date_transmission field is now nullable!\n";
                echo "✅ Fiche creation should now work without integrity constraint errors.\n";
            } else {
                echo "❌ FAILED: The field is still not nullable.\n";
            }
            break;
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
