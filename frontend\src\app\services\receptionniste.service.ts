import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReceptionnisteService {
  private apiUrl = 'http://localhost:8000/api/demandes';

  constructor(private http: HttpClient) {}

  getDemandesEnAttente(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/en-attente`);
  }

  validerDemande(id: number): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/valider`, {});
  }

  refuserDemande(id: number): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/refuser`, {});
  }

  getDemandesTraitees(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/traitees`);
  }
}
