import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class ScrollService {
  private scrollTarget = new BehaviorSubject<string | null>(null);

  setScrollTarget(target: string | null): void { // Now accepts string or null
    this.scrollTarget.next(target);
  }

  getScrollTarget() {
    return this.scrollTarget.asObservable();
  }
}