/* =======================
   STYLES GLOBAUX
======================= */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}

body {
  background-color: #eef7fc; /* Bleu ciel léger */
  color: #333;
  line-height: 1.6;
  font-size: 16px;
}
/* =======================
   STYLES POUR LES LOGOS
======================= */
.lab-logo {
  position: absolute;
  left: 20px; /* Position à gauche */
  top: 15px; /* Alignement avec le logo de droite */
  width: 120px; /* <PERSON><PERSON> du logo */
  height: auto;
  background: white;
  padding: 5px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Ombre douce */
  transition: transform 0.3s ease-in-out;
}
/* Default input styles (non-edit mode) */
input[type="text"],
input[type="date"] {
  background-color: transparent;
  color: #333;
  border: 1px solid #ddd;
  padding: 8px;
  border-radius: 5px;
  width: 100%;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

/* Disabled input styles */
input[type="text"]:disabled,
input[type="date"]:disabled {
  background-color: #f9f9f9;
  color: #666;
  border: 1px solid #ddd;
  cursor: not-allowed;
}

/* Edit mode input styles */
.editing-row input[type="text"],
.editing-row input[type="date"] {
  background-color: #e6f0fa; /* ✅ Bleu très clair en fond */
  color: #0d47a1; /* ✅ Bleu foncé pour le texte */
  border: 1px solid #2496d3;
}

/* Focus styles for edit mode */
.editing-row input[type="text"]:focus,
.editing-row input[type="date"]:focus {
  outline: none;
  border: 1.5px solid #1a6fb5;
  box-shadow: 0 0 6px rgba(36, 150, 211, 0.4);
}
.lab-logo:hover {
  transform: scale(1.05); /* Effet léger de zoom au survol */
}

/* =======================
   CONTENEUR PRINCIPAL
======================= */
.rapport-container {

  margin: 40px auto;
  padding: 25px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.25); /* Ombre améliorée */
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* =======================
   EN-TÊTE DU RAPPORT
======================= */
.rapport-header {
  text-align: center;
  border-bottom: 4px solid #2496d3;
  padding:30px ;
  position: relative;
}

.rapport-header .header-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.rapport-header h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 26px;
  font-weight: bold;
  color: #2496d3;
  margin-bottom: 10px;
  text-transform: uppercase;
  animation: slideDown 1s ease-in-out;
}

.rapport-header p {
  color: #666;
  font-size: 15px;
  margin-top: 0;
}

/* =======================
   LOGO ACCRÉDITATION
======================= */
.accreditation-logo {
  position: absolute;
  top: 15px;  /* Adjust vertical position */
  right: 20px;  /* Adjust horizontal position */
  width: 100px; /* Set proper size */
  height: auto;
  background: white; /* Ensure contrast */
  padding: 10px;
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Soft shadow */
  transition: transform 0.3s ease-in-out;
}

.accreditation-logo:hover {
  transform: scale(1.05); /* Small zoom effect on hover */
}

/* =======================
   INFORMATIONS (CLIENT / LABO)
======================= */
.rapport-additional-info {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.info-left,
.info-right {
  width: 100%;
  box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15); /* Ombre améliorée */
  padding: 18px;
  border-radius: 10px;
  background-color: #fff;
  transition: transform 0.3s ease-in-out;
}

.info-left:hover,
.info-right:hover {
  transform: translateY(-5px); /* Effet léger de soulèvement */
}

.rapport-additional-info h3 {
  color: #2496d3;
  font-size: 20px;
  margin-bottom: 12px;
  font-weight: bold;
}

.rapport-additional-info table {
  width: 100%;
  border-collapse: collapse;
}

.rapport-additional-info td {
  padding: 10px 0;
  border-bottom: 1px solid #f2f2f2;
}

.rapport-additional-info td.label {
  font-weight: 600;
  width: 35%;
  color: #555;
}

.rapport-additional-info td.input-field,
.rapport-additional-info td.lab-info {
  color: #333;
  font-weight: normal;
}

/* =======================
   CONTENU (DESCRIPTIONS / ANALYSES / RÉSULTATS)
======================= */
.rapport-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Sections générales */
.rapport-description,
.rapport-analyses,
.rapport-resultats {
  box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15);
  padding: 25px;
  border-radius: 10px;
  background-color: #fff;
  transition: transform 0.3s ease-in-out;
}

.rapport-description:hover,
.rapport-analyses:hover,
.rapport-resultats:hover {
  transform: translateY(-5px); /* Effet léger */
}

.rapport-description h2,
.rapport-analyses h2,
.rapport-resultats h2 {
  color: #2496d3;
  font-size: 22px;
  font-weight: 700;
  border-bottom: 3px solid #2496d3;
  padding-bottom: 7px;
  margin-bottom: 18px;
}

/* =======================
   BOUTONS D'ACTION
======================= */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-bottom: 15px;
}

.action-buttons button {
  padding: 12px 24px;
  font-size: 16px;
  background-color: #2496d3;
  color: #fff;
  border-radius: 25px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
}



.action-buttons button:hover {
  background-color: #1e78b5;
  transform: scale(1.05);
}

/* Action buttons in table cells */
.action-buttons-cell {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.edit-btn, .update-btn {
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-transform: uppercase;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.edit-btn {
  background-color: #555;
  color: white;
  box-shadow: 0 3px 10px rgba(85, 85, 85, 0.3);
}

.edit-btn:hover {
  background-color: #444;
  transform: scale(1.05);
}

.update-btn {
  background-color: #4caf50;
  color: white;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.update-btn:hover {
  background-color: #3e9d40;
  transform: scale(1.05);
}

/* Style for the approve button */
.approve-btn {
  background-color: #2496d3;
  color: white;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
}

.approve-btn:hover {
  background-color: #1a6a8e;
  transform: scale(1.05);
}

/* Style for the disabled button */
.disabled-btn {
  background-color: #cccccc !important;
  color: #666666 !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
}

.disabled-btn:hover {
  background-color: #cccccc !important;
  transform: none !important;
}

/* Style for the send button */
.send-btn {
  background-color: #4caf50;
  color: white;
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.send-btn:hover {
  background-color: #3e9d40;
  transform: scale(1.05);
}

/* Style for the print button */
.print-btn {
  background-color: #ff9800;
  color: white;
  box-shadow: 0 5px 15px rgba(255, 152, 0, 0.3);
}

.print-btn:hover {
  background-color: #e68a00;
  transform: scale(1.05);
}

.btn-download {
  background-color: #2496d3;
  font-size: 17px;
  font-weight: bold;
}

.btn-confirm {
  background-color: #4caf50;
}

.btn-confirm:hover {
  background-color: #3e9d40;
}

.btn-cancel {
  background-color: #f44336;
}

.btn-cancel:hover {
  background-color: #e53935;
}

/* =======================
   ANIMATIONS
======================= */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =======================
   RESPONSIVE DESIGN
======================= */
@media (max-width: 768px) {
  .rapport-container {
    width: 90%;
    padding: 15px;
  }

  .accreditation-logo {
    width: 100px;
    top: 10px;
    right: 10px;
  }

  .rapport-additional-info {
    flex-direction: column;
  }

  .modal {
    width: 90%;
    max-width: 400px;
  }
}
/* =======================
   TABLE STYLES - Borders and Styling
======================= */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  background: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08); /* Light shadow for better visibility */
  border-radius: 8px;
  overflow: hidden;
}

/* Borders for headers (th) */
th {
  background-color: #2496d3; /* Blue header */
  color: white;
  font-weight: bold;
  padding: 12px;
  border: 2px solid #ffffff; /* White borders */
  text-align: center;
}

/* Borders for table rows (tr) and table cells (td) */
tr {
  border-bottom: 1px solid #e0e0e0; /* Light grey border between rows */
  transition: background 0.2s ease-in-out;
}

td {
  padding: 12px;
  border: 1px solid #ddd; /* Grey borders for each cell */
  text-align: center;
  color: #333;
}

/* Alternate row colors for better readability */
tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* Hover effect for better interaction */
tr:hover {
  background-color: #eef7fc; /* Light blue on hover */
}

/* Adding extra padding for specific sections */
.rapport-description table,
.rapport-analyses table,
.rapport-resultats table {
  margin-bottom: 20px;
  border-radius: 10px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  th, td {
    padding: 10px;
    font-size: 14px;
  }
}

/* =========================
   Notification de soumission
   ========================= */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.notification-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
  animation: fadeIn 0.5s ease-in-out;
}

.notification-icon {
  font-size: 50px;
  margin-bottom: 20px;
}

.notification-message {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

/* =========================
   Loading Spinner Overlay
   ========================= */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.loading-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-popup h3 {
  margin-top: 20px;
  color: #333;
  font-size: 18px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
