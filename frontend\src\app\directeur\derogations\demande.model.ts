export interface Sample {
    identification_echantillon: string;
    reference: string;
    nature_echantillon: string;
    provenance: string;
    masse_echantillon: number;
    etat: string;
    analyses_demandees: string[];
    delai_souhaite: string;
    analyse_souhaite: string;
    lot: string;
    mode_reglement?: string | null;
  }
  
  export interface Demande {
    demande_id: string;
    demande_date: string;
    user_name: string;
    user_email: string;
    mode_reglement: string;
    samples: Sample[];
    status:string;
  }
  