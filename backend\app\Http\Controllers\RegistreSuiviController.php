<?php

namespace App\Http\Controllers;

use App\Models\Demande;
use App\Models\RegistreSuivi;
use App\Models\Registre;
use App\Models\User;
use Illuminate\Http\Request;

class RegistreSuiviController extends Controller {
    
    public function index() {
        return response()->json(RegistreSuivi::select('id', 'demande_id', 'nom_client', 'date_reception')->get());
    }

    public function show($id) {
        $registreSuivi = RegistreSuivi::with('registres')->findOrFail($id);
        return response()->json($registreSuivi);
    }
    public function store($demande_id) {
        // Find the demande using the string demande_id
        $demande = Demande::where('demande_id', $demande_id)->with('samples')->firstOrFail();
        $client = User::where('id', $demande->user_id)->first();
        $clientName = $client->name;
        $clientNickname = $client->nickname;
        // Create the RegistreSuivi
        $registreSuivi = RegistreSuivi::create([
            'demande_id' => $demande->demande_id, // Keeping demande_id as string
            'nom_client' =>$clientName.' '.$clientNickname,
            'date_reception' => $demande->demande_date,
        ]);
    
        // Generate Registres for each Sample in the Demande
        foreach ($demande->samples as $index => $sample) {
            $dayOfYear = now()->format('z'); // Number of days since start of the year
            $code_client = str_pad($index + 1, 4, '0', STR_PAD_LEFT) . '-' . $dayOfYear;
            $code_labo = $code_client . '-25'; // Append '-25' for the year reference
    
            Registre::create([
                'registre_suivi_id' => $registreSuivi->id,
                'code_client' => $code_client,
                'code_labo' => $code_labo,
                'quantite' => $sample->masse_echantillon, // Assuming one sample per record
                'nature_echantillon' => $sample->nature_echantillon,
                'numero_demande' => $demande->demande_id,
                'parametres_a_analyser' => $sample->analyses_demandees,
                'date_prevue_analyse' => now()->addWeekdays(7)->format('Y-m-d'), // +7 working days
            ]);
        }
        $demande->registre_id = $registreSuivi->id;
        $demande->save();
        return response()->json([
            'message' => 'Registre Suivi created successfully',
            'registre_suivi_id' => $registreSuivi->id,
        ]);
    }
    
    
    public function update(Request $request, $id) {
        $registre = Registre::findOrFail($id);
    
        // Validate incoming data
        $validatedData = $request->validate([
            'echantillon_envoye_par' => 'nullable|string|max:255',
            'echantillon_recu_par' => 'nullable|string|max:255',
            'lieu_conservation' => 'nullable|string|max:255',
            'date_effective_analyse' => 'nullable|date',
            'date_emission_rapport' => 'nullable|date',
        ]);
    
        // Update only the provided fields
        $registre->update(array_filter($validatedData));
    
        return response()->json([
            'message' => 'Registre Updated Successfully',
            'registre' => $registre
        ]);
    }
    
}
