import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUser: any = null;

  constructor() {}

  login(credentials: { email: string; password: string }): boolean {
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      this.currentUser = { id: 1, name: 'Admin', role: 'admin', email: credentials.email };
      localStorage.setItem('user', JSON.stringify(this.currentUser)); // Simule un stockage de session
      return true;
    }
    return false;
  }

  logout(): void {
    this.currentUser = null;
    localStorage.removeItem('user');
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('user');
  }

  getCurrentUser(): any {
    if (!this.currentUser) {
      this.currentUser = JSON.parse(localStorage.getItem('user') || 'null');
    }
    return this.currentUser;
  }

  setCurrentUser(user: any): void {
    this.currentUser = user;
    localStorage.setItem('user', JSON.stringify(user));
  }
}
