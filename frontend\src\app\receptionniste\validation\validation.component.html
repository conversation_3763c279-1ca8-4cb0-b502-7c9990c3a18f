<div class="demandes-container">
  <h2> Les demandes  validées</h2>
  <!-- Barr<PERSON> de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro ou client:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="valid">Valide</option>
        <option value="ongoing_validation">Validation en cours</option>
        <option value="derogation">D<PERSON>roger</option>
        <option value="rejected">Rejeté</option>
      </select>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <button (click)="clearFilters()" class="btn-clear"><fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>Effacer les filtres</button>
  </div>

  <!-- Tableau des demandes -->
  <table>
    <thead>
      <tr>
        <th>Demande Numéro</th>
        <th>Nom Client</th>
        <th>Date Réception</th>
        <th>Statut de Paiement</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="isLoading" class="loading-row">
        <td colspan="5" class="text-center">
          <div class="spinner-container">
            <div class="spinner"></div>
            <span>Chargement...</span>
          </div>
        </td>
      </tr>

      <!-- Message quand aucune demande n'est trouvée -->
      <tr *ngIf="!isLoading && filteredDemandes.length === 0" class="empty-row">
        <td colspan="5" class="text-center">
          Aucune demande trouvée.
        </td>
      </tr>

      <!-- Affichage des demandes -->
      <ng-container *ngIf="!isLoading && filteredDemandes.length > 0">
        <tr
          *ngFor="
            let demande of filteredDemandes
             | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
        >
          <td>{{ demande.demande_id }}</td>
          <td>
            {{ demande.userName || 'Chargement...' }} {{ demande.userNick }}
          </td>
          <td>{{ demande.demande_date }}</td>
          <td>
            <span class="status" [ngClass]="getPaymentStatusClass(demande.demande_id)">{{ getPaymentStatusText(demande.demande_id) }}</span>
          </td>
          <td>
            <div class="button-container">
              <!-- Bouton : Voir les détails -->
              <button class="btn btn-primary" (click)="navigateToDemandeDetails(demande)">
                <span class="icon"><fa-icon [icon]="faEye"></fa-icon></span>
                <span class="text">Voir détails</span>
              </button>

              <!-- Bouton : Voir/Créer la fiche -->
              <button
                class="btn"
                [ngClass]="{
                  'btn-loading': demande.isCreatingFiche,
                  'btn-secondary': demande.fiche_id,
                  'btn-success': !demande.fiche_id
                }"
                (click)="viewFicheTransmission(demande)"
                [title]="demande.isCreatingFiche ? 'Création en cours...' : (!demande.fiche_id ? 'Créer une fiche de transmission' : 'Voir la fiche de transmission')"
                [disabled]="demande.isCreatingFiche"
              >
                <span class="icon">
                  <fa-icon [icon]="faFileAlt" [animation]="demande.isCreatingFiche ? 'spin' : undefined"></fa-icon>
                </span>
                <span class="text">
                  {{ demande.isCreatingFiche ? 'Création en cours...' : (demande.fiche_id ? 'Voir fiche' : 'Créer fiche') }}
                </span>
              </button>

              <!-- Bouton : Voir/Créer le registre -->
              <button
                class="btn"
                [ngClass]="{
                  'btn-loading': demande.isCreatingRegistre,
                  'btn-secondary': demande.registre_id,
                  'btn-success': !demande.registre_id
                }"
                (click)="viewRegistre(demande)"
                [title]="demande.isCreatingRegistre ? 'Création en cours...' : (!demande.registre_id ? 'Créer un registre de suivi' : 'Voir le registre de suivi')"
                [disabled]="demande.isCreatingRegistre"
              >
                <span class="icon">
                  <fa-icon [icon]="faClipboard" [animation]="demande.isCreatingRegistre ? 'spin' : undefined"></fa-icon>
                </span>
                <span class="text">
                  {{ demande.isCreatingRegistre ? 'Création en cours...' : (demande.registre_id ? 'Voir registre' : 'Créer registre') }}
                </span>
              </button>

              <!-- Bouton : Voir/Créer la facture -->
              <button
                class="btn"
                [ngClass]="{
                  'btn-loading': demande.isCreatingFacture,
                  'btn-secondary': demande.facture_id,
                  'btn-success': !demande.facture_id
                }"
                (click)="viewFacture(demande)"
                [title]="demande.isCreatingFacture ? 'Création en cours...' : (!demande.facture_id ? 'Créer une facture' : 'Voir la facture')"
                [disabled]="demande.isCreatingFacture"
              >
                <span class="icon">
                  <fa-icon [icon]="faMoneyBill" [animation]="demande.isCreatingFacture ? 'spin' : undefined"></fa-icon>
                </span>
                <span class="text">
                  {{ demande.isCreatingFacture ? 'Création en cours...' : (demande.facture_id ? 'Voir facture' : 'Créer facture') }}
                </span>
              </button>

              <!-- Bouton : Voir/Créer Rapport -->
              <button
                class="btn"
                [ngClass]="{
                  'btn-loading': rapportStatusMap[demande.demande_id]?.isCreatingRapport,
                  'btn-secondary': rapportStatusMap[demande.demande_id]?.rapport_id && rapportStatusMap[demande.demande_id]?.rapport_id !== 'pending',
                  'btn-success': !rapportStatusMap[demande.demande_id]?.rapport_id,
                  'btn-warning': rapportStatusMap[demande.demande_id]?.rapport_id === 'pending'
                }"
                [disabled]="rapportStatusMap[demande.demande_id]?.isCreatingRapport || rapportStatusMap[demande.demande_id]?.rapport_id === 'pending'"
                (click)="goToRapport(demande)"
                [title]="rapportStatusMap[demande.demande_id]?.isCreatingRapport ? 'Création en cours...' : (!rapportStatusMap[demande.demande_id]?.rapport_id ? 'Créer un rapport' : (rapportStatusMap[demande.demande_id]?.rapport_id === 'pending' ? 'Rapport en cours de traitement' : 'Voir le rapport'))"
              >
                <span class="icon">
                  <fa-icon [icon]="faFilePdf" [animation]="rapportStatusMap[demande.demande_id]?.isCreatingRapport ? 'spin' : undefined"></fa-icon>
                </span>
                <span class="text">
                  {{ rapportStatusMap[demande.demande_id]?.isCreatingRapport ? 'Création en cours...' : (rapportStatusMap[demande.demande_id]?.rapport_id && rapportStatusMap[demande.demande_id]?.rapport_id !== 'pending' ? 'Voir rapport' : 'Créer rapport') }}
                </span>
              </button>


            </div>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>
</div>
