import { Component, OnInit } from '@angular/core';
import { NotificationService } from '../../../notification/notification.service';
import { Notification } from '../../../../models/notification.model';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule],  // Import NgxPaginationModule
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.css']
})
export class NotificationsResponsableComponent implements OnInit {
  notifications: Notification[] = [];
  page: number = 1;  // Current page
  itemsPerPage: number = 5;  // Items per page

  constructor(
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getFicheNotifications();
  }
  getFicheNotifications(): void {
    this.notificationService.getFicheTransmissionNotifications().subscribe(
      (response) => {
        this.notifications = response.notifications;
        this.sortNotifications();
      },
      (error) => console.error('Error fetching notifications:', error)
    );
  }

  private sortNotifications(): void {
    this.notifications.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime() // Sort by most recent first
    );
  }
  // Handle notification click: Mark as read if unread, then navigate
  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe(
        () => {
          notification.is_read = true; // Update UI
          this.navigateToDemandeDetails(notification);
        },
        (error) => console.error('Error marking notification as read:', error)
      );
    } else {
      this.navigateToDemandeDetails(notification);
    }
  }

  // Navigate to demande details page
  navigateToDemandeDetails(notification: Notification): void {
    if (notification.demande) {
      console.log('Navigating to:', `/fiche-transmission/${notification.demande}`);

      this.router.navigate(['/fiche-transmission', notification.fiche_id]);
    } else  {
      console.error('Demande ID is missing in notification:', notification);
    }
  }
}
