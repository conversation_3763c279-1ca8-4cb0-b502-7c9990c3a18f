{"name": "front-labo", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/build": "^18.2.16", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/material": "^18.2.14", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.3", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "ngx-extended-pdf-viewer": "^23.1.1", "ngx-pagination": "^6.0.3", "pagedjs": "^0.4.3", "pdf-lib": "^1.17.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.16", "@angular/cli": "^18.0.3", "@angular/compiler-cli": "^18.0.0", "@types/jasmine": "~5.1.0", "@types/xlsx": "^0.0.35", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}