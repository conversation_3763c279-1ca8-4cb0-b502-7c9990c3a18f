<div class="transmission-container">
    <h3>📑 Fiche de Transmission</h3>
    <!-- ✅ Barre de recherche & filtres -->
    <div class="filter-bar">
        <div class="filter-group">
            <label for="search-demande">N° demande:</label>
            <input
                type="text"
                id="search-demande"
                placeholder="Rechercher..."
                [(ngModel)]="searchDemandeNumber"
                (input)="applyFilter()"
                class="filter-input"
            />
        </div>
        <div class="filter-group">
            <label for="search-client">Client:</label>
            <input
                type="text"
                id="search-client"
                placeholder="Rechercher..."
                [(ngModel)]="searchClientName"
                (input)="applyFilter()"
                class="filter-input"
            />
        </div>
        <div class="filter-group">
            <label for="status">Statut:</label>
            <select id="status" [(ngModel)]="selectedStatus" (change)="applyFilter()" class="filter-select">
                <option value="">Tous les statuts</option>
                <option value="created">Rapport créé</option>
                <option value="not_created">Rapport non créé</option>
            </select>
        </div>
        <button (click)="clearFilters()" class="btn-clear">
            <fa-icon [icon]="faFilter" style="margin-right: 10px;"></fa-icon>Effacer les filtres
        </button>
    </div>
    <!-- Show loading indicator -->
    <div *ngIf="loading" class="loading-message">⏳ Chargement des fiches...</div>

    <!-- Show error message -->
    <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>

    <!-- Display table only when data is available -->
    <table border="1" *ngIf="!loading && !errorMessage && filteredFiches.length > 0">
        <thead>
            <tr>
                <th>ID Fiche</th>
                <th>Demande Numero</th>
                <th>Nom Client</th>
                <th>Date Transmission</th>
                <th>Action</th>
                <th>Statut</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let fiche of filteredFiches">
                <td>{{ fiche.id }}</td>
                <td>{{ fiche.demande.demande_id }}</td>
                <td>{{ fetchUserName(fiche.demande.user_id) }}</td> <!-- ✅ Corrected -->
                <td>{{ fiche.date_transmission ? fiche.date_transmission : 'Pas encore envoyé' }}</td>
                <td>
                    <button class="details-btn" (click)="goToFicheTransmission(fiche.id)">
                      <fa-icon [icon]="faEye" style="margin-right: 10px;"></fa-icon>Voir détails
                    </button>

                    <!-- Show "Créer Rapport" only if statusRapport is 'not_created' -->
                    <button class="create-btn" *ngIf="fiche.statusRapport === 'not_created' && !fiche.isCreatingRapport"
                            (click)="createRapport(fiche.demande.demande_id, fiche)">
                      <fa-icon [icon]="faFileAlt" style="margin-right: 10px;"></fa-icon>Créer Rapport
                    </button>

                    <!-- Show loading indicator while creating rapport -->
                    <button class="create-btn loading-btn" *ngIf="fiche.isCreatingRapport" disabled>
                      <span class="loading-spinner"></span> Création en cours...
                    </button>

                    <!-- Show "Voir Rapport" when `rapport_id` is available and not 'pending' -->
                    <button class="view-btn" *ngIf="fiche.rapport_id && fiche.rapport_id !== 'pending'"
                            (click)="goToRapport(fiche)">
                      <fa-icon [icon]="faFilePdf" style="margin-right: 10px;"></fa-icon>Voir Rapport
                    </button>

                    <!-- Show a disabled button when rapport is created but ID is pending -->
                    <button class="view-btn pending-btn" *ngIf="fiche.rapport_id === 'pending'" disabled>
                      <fa-icon [icon]="faFilePdf" style="margin-right: 10px;"></fa-icon>Rapport en traitement
                    </button>
                  </td>

                  <!-- ✅ Show Status: "Rapport Created" or "Not Created" -->
                  <td>
                    <span *ngIf="fiche.statusRapport === 'created'" class="status-created">✅ Rapport créé</span>
                    <span *ngIf="fiche.statusRapport === 'not_created'" class="status-not-created">❌ Rapport non créé</span>
                  </td>
                </tr>
        </tbody>
    </table>

    <!-- Show message when no fiches exist -->
    <div *ngIf="!loading && !errorMessage && filteredFiches.length === 0" class="no-data">
        ❌ Aucune fiche de transmission trouvée.
    </div>
</div>
