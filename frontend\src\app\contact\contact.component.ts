import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  message: string;
}

@Component({
    selector: 'app-contact',
    standalone: true,
    imports: [CommonModule, FormsModule],
    templateUrl: './contact.component.html',
    styleUrl: './contact.component.css'
})
export class ContactComponent {
    // Form data model
    contactData: ContactFormData = {
      name: '',
      email: '',
      phone: '',
      message: ''
    };

    // UI state
    isSubmitting = false;
    showSuccessMessage = false;
    countdownSeconds = 5;

    // Form submission method
    onSubmit() {
        if (this.isSubmitting) return;

        this.isSubmitting = true;
        console.log('Form data:', this.contactData);

        // Simulate API call with timeout
        setTimeout(() => {
            // Reset form after successful submission
            this.isSubmitting = false;
            this.showSuccessMessage = true;

            // Start countdown timer
            this.countdownSeconds = 5;
            const countdownInterval = setInterval(() => {
                this.countdownSeconds--;
                if (this.countdownSeconds <= 0) {
                    clearInterval(countdownInterval);
                    window.location.reload();
                }
            }, 1000);
        }, 1500);

        // In a real application, you would make an API call here
        // Example:
        // this.http.post('/api/contact', this.contactData).subscribe(
        //   response => {
        //     this.isSubmitting = false;
        //     this.showSuccessMessage = true;
        //     // Reset form
        //   },
        //   error => {
        //     this.isSubmitting = false;
        //     // Show error message
        //   }
        // );
    }
}
