/* ✅ GLOBAL */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}

body {
  background-color: #f7f7f7;
  color: #333;
}
a.router-link {
  display: block;
  width: fit-content;
  margin: 20px auto;

  text-decoration: none;
  background: #0765e0;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 14px;
  transition: 0.3s;
  cursor: pointer;
}

a.router-link:hover {
  background: #555;
}

/* =========================
   Notification de soumission
   ========================= */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.notification-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
  animation: fadeIn 0.5s ease-in-out;
}

.notification-icon {
  font-size: 50px;
  margin-bottom: 20px;
}

.notification-message {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

/* =========================
   Loading Spinner Overlay
   ========================= */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.loading-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-popup h3 {
  margin-top: 20px;
  color: #333;
  font-size: 18px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/* ✅ CONTAINER */
.registre-container {
  width: 90%;
  margin: 50px auto;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 1s ease-in-out;
  overflow: hidden;
}
.table-wrapper {
  overflow-x: auto;
}

/* Prevent text wrapping in table cells */
.registre-table tbody tr td {
  white-space: normal;
}
/* ✅ HEADER */
.registre-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #2496d3;
  margin-bottom: 20px;
  border-bottom: 3px solid #2496d3;
  padding-bottom: 10px;
}
/* Assuming you want to style the selects inside .registre-table cells */
.registre-table tbody tr td select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 17px;
    background-color: #fff;
    cursor: pointer;
    transition: border-color 0.3s ease;
  }

  /* Change border color on hover */
  .registre-table tbody tr td select:hover {
    border-color: #bbb;
  }

  /* Outline on focus */
  .registre-table tbody tr td select:focus {
    outline: none;
    border-color: #2496d3; /* or your primary color */
  }

  /* Disabled appearance */
  .registre-table tbody tr td select:disabled {
    background: #e9ecef;
    cursor: not-allowed;
  }

/* ✅ LOADING & ERROR MESSAGES */
.loading-message {
  font-size: 16px;
  color: #ff9800;
  text-align: center;
  margin-bottom: 15px;
}

.error {
  font-size: 16px;
  color: red;
  text-align: center;
  margin-bottom: 15px;
  font-weight: bold;
}

/* ✅ DETAILS TABLE */
.details-table {

  border-collapse: collapse;
  margin-bottom: 20px;
  background: #fff;
}

.details-table td {
  padding: 12px;
  font-size: 14px;
  border-bottom: 1px solid #f2f2f2;
}

.details-table td:first-child {
  font-weight: bold;
  font-size: 14px;
  color: #2496d3;
}

/* ✅ MAIN TABLE */
.registre-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
}

/* ✅ TABLE HEADERS */
.registre-table thead tr th {
  background-color: #2496d3;
  color: white;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  border-bottom: 2px solid #f2f2f2;
}

/* ✅ TABLE BODY */
.registre-table tbody tr td {
  padding: 12px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f2f2f2;
  transition: all 0.3s ease;
}

/* ✅ HOVER EFFECT */
.registre-table tbody tr:hover td {
  background-color: #f5f5f5;
}

/* ✅ INPUT FIELDS */
.registre-table tbody tr td input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: 0.3s;
}

.registre-table tbody tr td input[readonly] {
  background: #e9ecef;
  cursor: not-allowed;
}

/* ✅ BUTTONS */
button {
  cursor: pointer;
  padding: 8px 12px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  transition: 0.3s ease;
}

button[type="button"] {
  background: #2496d3;
  color: white;
}

button[type="button"]:hover {
  background: #1a75a3;
}

button.router-link {
  display: block;
  width: fit-content;
  margin: 20px auto;
  text-align: center;
  text-decoration: none;
  background: #333;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 14px;
  transition: 0.3s;
}

button.router-link:hover {
  background: #555;
}

/* ✅ NO DATA MESSAGE */
.no-data {
  font-size: 16px;
  color: #d32f2f;
  text-align: center;
  margin-top: 20px;
  font-weight: bold;
}

/* ✅ ANIMATION */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .registre-container {
    padding: 20px 10px;
  }

  .registre-table {
    font-size: 12px;
  }

  .registre-table thead tr th,
  .registre-table tbody tr td {
    padding: 10px;
  }

  button {
    font-size: 12px;
    padding: 6px 10px;
  }
}
