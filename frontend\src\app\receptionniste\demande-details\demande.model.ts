export interface Sample {
  identification_echantillon: string;
  reference: string;
  nature_echantillon: string;
  provenance: string;
  masse_echantillon: number;
  etat: string;
  analyses_demandees: string[];
  delai_souhaite: string;
  analyse_souhaite: string;
  lot: string;
  mode_reglement?: string | null;
}

export interface Payment {
  id: number;
  demande_id: number;
  user_id: number;
  amount: number;
  payment_proof: string;
  payment_proof_url?: string;
  direct_url?: string;
  notes?: string;
  status: 'pending' | 'approved' | 'rejected';
  payment_date: string;
  created_at?: string;
  updated_at?: string;
}

export interface Demande {
  demande_id: string;
  facture_id?:number;
  analyses_accredite?:number;
  devis_sent?:string;
  rapport_created?:string;
  demande_date: string;
  user_name: string;
  user_nickname?: string;
  user_email: string;
  mode_reglement: string;
  samples: Sample[];
  telephone?:string;
  adresse?:string;
  fax?:string;
  status:string;
  payment_id?: number;
  payment?: Payment;
  user_id?: number;
}
