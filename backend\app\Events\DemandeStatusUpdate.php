<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DemandeStatusUpdate implements ShouldBroadcast
{
    use InteractsWithSockets, SerializesModels;

    public $demande;
    public $message;

    public function __construct($demande, $message)
    {
        $this->demande = $demande;
        $this->message = $message;
    }

    public function broadcastOn()
    {
        return ['client-channel-' . $this->demande->client_id];
    }

    public function broadcastAs()
    {
        return 'demande-status-update';
    }
}

