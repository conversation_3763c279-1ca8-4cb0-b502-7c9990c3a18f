export interface User {
  readonly id?: number; // Auto-generated by <PERSON><PERSON>
  name: string;
  nickname: string; // Newly added required field
  email: string;
  password: string;
  role: 'admin' | 'client' | 'receptionist' | 'responsable';
  adress: string; // Newly added required field
  phone: string; // Newly added required field
  fax?: string; // Optional field
  readonly created_at?: Date | string; 
}
