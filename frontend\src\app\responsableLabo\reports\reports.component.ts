import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { RapportsService } from './rapports.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faEye } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.css'],
})
export class RapportsListComponent implements OnInit {
  // Font Awesome icons
  faEye = faEye;

  reports: any[] = [];

  constructor(private rapportService: RapportsService, private router: Router) {}

  ngOnInit() {
    this.loadAllRapports();
  }

  // Fetch all rapports
  loadAllRapports() {
    this.rapportService.getAllRapports().subscribe(
      (data) => {
        this.reports = data.map(report => ({
          ...report,
          status: this.translateStatus(report.status) // ✅ Convert status to French
        }));
        console.log('Rapports:', this.reports);
      },
      (error) => console.error('Error fetching rapports:', error)
    );
  }
  translateStatus(status: string): string {
    switch (status) {
      case 'not_sent': return 'Non envoyé';
      case 'sent': return 'Envoyé';
      default: return status;
    }
  }
  // Send a Rapport
  envoyerRapport(rapportId: number) {
    this.rapportService.sendRapport(rapportId).subscribe(
      (response) => {
        console.log('Rapport envoyé:', response);

        // ✅ Update the UI instantly without reloading the entire list
        const report = this.reports.find(r => r.id === rapportId);
        if (report) {
          report.status = 'Envoyé'; // ✅ Update status in UI
        }
      },
      (error) => console.error('Erreur lors de l\'envoi du rapport:', error)
    );
  }
  // View Report Details
  viewReportDetails(rapportId: number) {
    this.router.navigate(['/responsable/rapports', rapportId]);
  }
}
