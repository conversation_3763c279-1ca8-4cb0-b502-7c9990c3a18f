import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FicheTransmissionService } from '../../responsableLabo/fiche-transmission/fiche.service';

@Component({
  selector: 'app-analyst-dashboard',
  standalone: true,
  imports: [CommonModule, RouterOutlet],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class AnalystDashboardComponent implements OnInit {
  role: string | null = null;
  name: string | null = null;
  nickname: string | null = null;
  user: [] | null = null;
  userEmail: string | null = null;

  // Statistics counters
  ficheWithoutResultsCount: number = 0;

  // Loading states
  loadingFicheStats: boolean = true;

  constructor(
    private router: Router,
    private ficheService: FicheTransmissionService
  ) {}

  ngOnInit() {
    this.userEmail = this.getUserProperty('email');
    this.name = this.getUserProperty('name');
    this.nickname = this.getUserProperty('nickname');

    // Load statistics
    this.loadFicheTransmissionStats();
  }

  /**
   * Loads statistics about fiche transmission without results
   */
  loadFicheTransmissionStats() {
    this.loadingFicheStats = true;

    // Use the dedicated service method for getting fiches without results
    this.ficheService.getFichesWithoutResultsCount().subscribe({
      next: (count) => {
        this.ficheWithoutResultsCount = count;
        this.loadingFicheStats = false;
        console.log('Fiches without results count:', this.ficheWithoutResultsCount);
      },
      error: (error) => {
        console.error('Error fetching fiches stats:', error);
        this.loadingFicheStats = false;
        this.ficheWithoutResultsCount = 0;
      }
    });
  }

  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');

    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null; // Return the value if exists, otherwise null
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }

    return null;
  }

  openFicheTransmission() {
    this.router.navigate(['analysits/fiche-transmission']);
  }

  openResults() {
    this.router.navigate(['analysits/results']);
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user_role');
    localStorage.removeItem('user');
    this.role = null;
    this.user = [];
    this.router.navigate(['/login']);
  }
}
