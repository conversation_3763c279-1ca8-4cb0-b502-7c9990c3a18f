<?php
namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Notifications\Messages\BroadcastMessage;

class DemandeNotification extends Notification
{
    use Queueable, InteractsWithSockets;

    protected $demande;
    protected $message;
    protected $recipientRole;

    public function __construct($demande, $message, $recipientRole)
    {
        $this->demande = $demande;
        $this->message = $message;
        $this->recipientRole = $recipientRole;
    }

    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    public function toArray($notifiable)
    {
        return [
            'demande_id' => $this->demande->id,
            'message' => $this->message,
            'status' => $this->demande->status,
            'recipient_role' => $this->recipientRole,
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'demande_id' => $this->demande->id,
            'message' => $this->message,
            'status' => $this->demande->status,
            'recipient_role' => $this->recipientRole,
        ]);
    }
}
