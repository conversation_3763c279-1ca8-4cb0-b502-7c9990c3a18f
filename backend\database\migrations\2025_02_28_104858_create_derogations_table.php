<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('derogations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('demande_id')->constrained('demandes')->onDelete('cascade');
            $table->string('identification_echantillon');
            $table->string('nature_echantillon');
            $table->string('reference')->nullable();
            $table->string('provenance')->nullable();
            $table->string('demandeur');
            $table->date('date_demande_client');
            $table->decimal('masse_echantillon', 8, 2);
            $table->json('analyses_demandees');
            $table->text('description_ecart');
            $table->text('reponse_du_client');
            $table->text('decision')->nullable();
            $table->date('date_et_visa_du_DL')->nullable();
            $table->date('date_et_visa_du_demander')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('derogations');
    }
};
