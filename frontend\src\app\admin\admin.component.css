@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Section du panneau administrateur */
.admin-panel {
  text-align: center;
  padding: 50px 5%;
  font-family: 'Montserrat', sans-serif;
  background: white;
  color: black;
  animation: fadeInBg 2s ease-in-out;
}

/* ✅ Titre principal */
.admin-panel h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 26px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 10px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Section des actions */
.admin-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

/* ✅ Boutons d'action */
.btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2496d3;
  color: white;
  padding: 12px 25px;
  font-size: 16px;
  border-radius: 50px;
  border: none;
  cursor: pointer;
  transition: all 0.4s ease-in-out;
  font-weight: bold;
  box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5);
  text-transform: uppercase;
  gap: 10px;
}

/* ✅ Effet au survol */
.btn-action:hover {
  transform: scale(1.1);
  box-shadow: 0px 12px 35px rgba(0, 0, 0, 0.8);
}


/* ✅ Animation pour un effet plus fluide */
@keyframes fadeInBg {
  from { background: #f2f2f2; }
  to { background: white; }
}

@keyframes glowText {
  from { text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); }
  to { text-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5); }
}
