@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ HERO SECTION */
.hero-section {
  background: white;
  color: rgb(255, 255, 255);
  display: flex;

  align-items: center;
  justify-content: center;
  text-align: center;
  animation: fadeInBg 2s ease-in-out;
}

.hero-container {
  display: flex;
  padding: 20px;
}

/* ✅ TITRE */
.title {
  font-family: 'Poppins', sans-serif;
  color: orange;
  font-size: 60px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 70px; /* Reduced for better spacing */
  animation: glowText 1.5s infinite alternate;
  background: none;
  -webkit-text-fill-color: orange;
}


.description {
  font-family: 'Montserrat', sans-serif;
  font-size: 25px;
  font-weight: bold;
  color: white; /* Improved contrast */
  opacity: 0;
  animation: fadeIn 1.5s ease-in forwards;
  letter-spacing: 1px;
  margin-bottom: 30px; /* Added spacing between description and button */
  padding: 10px 20px;
}


/* ✅ BOUTON CALL-TO-ACTION */
.cta-button {
  background-color: #2496d3;
  color: white;
  padding: 15px 40px;
  font-size: 18px;
  border-radius: 50px;
  border: none;
  cursor: pointer;
  text-transform: uppercase;
  font-weight: bold;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  animation: fadeIn 1.8s ease-in forwards, pulse 2s infinite alternate;
}

.cta-button:hover {
  background-color: black;
  transform: scale(1.1);
  color: white;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.8);
}

/* ✅ CONTAINER DES IMAGES */
.hero-image {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  position: relative;
  width: 600px;
  height: 350px;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
}

/* ✅ STYLE DES IMAGES */
.hero-image img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

/* ✅ IMAGE ACTIVE */
.hero-image img.active {
  opacity: 1;
}

/* ✅ ANIMATIONS */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInBg {
  from { background: #f2f2f2; }
  to { background: white; }
}

@keyframes glowText {
  from { text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); }
  to { text-shadow: 0px 0px 20px rgba(0, 0, 0, 0.5); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
/* ✅ HERO SECTION */
/* ✅ HERO SECTION */
.hero-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
}

/* ✅ HERO CONTAINER */
/* ✅ HERO SECTION (Adjusted for Navbar) */
.hero-section {
  position: relative;
  /* Adjusted height to account for navbar */
  height: calc(100vh - 75px);

  background: white;
  color: rgb(255, 255, 255);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
}

/* ✅ HERO CONTAINER */
.hero-container {
  z-index: 1;
  text-align: center;
  background: rgba(255, 255, 255, 0.8); /* Slight transparency for better contrast */
  padding: 50px 60px;
  border-radius: 15px;
  max-width: 80%;
}


/* ✅ LARGE CAROUSEL BEHIND TEXT */
.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 75px); /* Match hero-section height */
  overflow: hidden;
  z-index: -1; /* Push it behind text */
}

/* ✅ MAKE IMAGES COVER FULL BACKGROUND */
.hero-image img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

/* ✅ IMAGE ACTIVE */
.hero-image img.active {
  opacity: 1;
}

/* ✅ BOUTONS DE NAVIGATION */
.prev-btn, .next-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  font-size: 50px;
  font-weight: bold;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: 0.3s;
  opacity: 0;
}

.prev-btn:hover, .next-btn:hover {
  background: rgb(255, 255, 255);
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

/* ✅ INDICATEURS (DOTS) */
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  width: 100%;
  text-align: center;
}

.carousel-indicators .dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin: 0 5px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: 0.3s;
}

.carousel-indicators .dot.active {
  background: white;
  transform: scale(1.2);
}
