import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router'; // ✅ Import Router
import { FicheTransmissionService } from '../../fiche-transmission/fiche.service';
import { DemandeService } from '../../../receptionniste/demandes/demande.service';
import { FormsModule } from '@angular/forms'; // ✅ Import FormsModule for ngModel

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faEye, faFileAlt, faFilePdf, faFilter } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-fiche-list',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule], // ✅ Add FormsModule and FontAwesomeModule
  templateUrl: './fiche-list.component.html',
  styleUrl: './fiche-list.component.css'
})
export class FichesListComponent implements OnInit {
  // Font Awesome icons
  faEye = faEye;
  faFileAlt = faFileAlt;
  faFilePdf = faFilePdf;
  faFilter = faFilter;

  fiches: any[] = [];
  filteredFiches: any[] = []; // ✅ Add filteredFiches array
  rapport_id:string='';
  loading: boolean = true;
  errorMessage: string = '';
  userCache: { [key: number]: { name: string; nickname: string } } = {}; // ✅ Cache for user details
  sentStatus: { [key: number]: boolean } = {}; // ✅ Track sent status for each fiche

  // ✅ Filter variables
  searchDemandeNumber: string = '';
  searchClientName: string = '';
  selectedStatus: string = '';

  constructor(
    private ficheService: FicheTransmissionService,
    private demandeService: DemandeService,
    private router: Router, // ✅ Inject Router
    private cdr: ChangeDetectorRef // ✅ Inject ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.loadFiches();
  }

  // ✅ Load all fiches and fetch user details
  loadFiches() {
    this.ficheService.getAllFiches().subscribe({
      next: (data) => {
        // ✅ Filter only fiches where `status` is 'sented'
        this.fiches = data.filter((fiche:any) => fiche.status === 'sented');
        this.filteredFiches = [...this.fiches]; // ✅ Initialize filteredFiches
        this.loading = false;

        this.fiches.forEach(fiche => {
          this.sentStatus[fiche.id] = true; // ✅ Track transmission status

          if (!fiche.statusRapport) {
            fiche.statusRapport = 'not_created'; // ✅ Default if missing
          }

          // ✅ Fetch `rapport_id` for each `demande_id`
          if (fiche.demande?.demande_id) {
            this.fetchRapportId(fiche.demande.demande_id, fiche);
          }
        });

        // ✅ Fetch user details in bulk
        const userIds = [...new Set(
          this.fiches.map(fiche => fiche.demande?.user_id).filter(id => id)
        )];

        if (userIds.length > 0) {
          this.demandeService.getUsersDetails(userIds).subscribe({
            next: (users) => {
              users.forEach(user => {
                this.userCache[user.id] = { name: user.name, nickname: user.nickname || '' };
              });
            },
            error: (error) => {
              console.error('Error fetching user details:', error);
              userIds.forEach(id => {
                this.userCache[id] = { name: 'Inconnu', nickname: '' };
              });
            }
          });
        }

        this.cdr.detectChanges();
      },
      error: (error) => {
        this.errorMessage = 'Échec du chargement des fiches';
        console.error('Erreur lors du chargement des fiches:', error);
        this.loading = false;
      }
    });
  }

  // ✅ Apply filters method
  applyFilter() {
    let filtered = [...this.fiches];

    // Filter by demande number
    if (this.searchDemandeNumber.trim() !== '') {
      const searchTermLower = this.searchDemandeNumber.toLowerCase();
      filtered = filtered.filter(fiche =>
        fiche.demande?.demande_id?.toString().toLowerCase().includes(searchTermLower)
      );
    }

    // Filter by client name
    if (this.searchClientName.trim() !== '') {
      const searchTermLower = this.searchClientName.toLowerCase();
      filtered = filtered.filter(fiche => {
        const userName = this.fetchUserName(fiche.demande?.user_id).toLowerCase();
        return userName.includes(searchTermLower);
      });
    }

    // Filter by status
    if (this.selectedStatus !== '') {
      filtered = filtered.filter(fiche => fiche.statusRapport === this.selectedStatus);
    }

    this.filteredFiches = filtered;
    this.cdr.detectChanges();
  }

  // ✅ Clear filters method
  clearFilters() {
    this.searchDemandeNumber = '';
    this.searchClientName = '';
    this.selectedStatus = '';
    this.filteredFiches = [...this.fiches];
    this.cdr.detectChanges();
  }

  goToRapport(fiche: any) {
    console.log("🔎 Full fiche object:", fiche);
    console.log(`🛠️ Navigating to rapport with ID: ${fiche.rapport_id}`);

    if (fiche.rapport_id && fiche.rapport_id !== 'pending') {
      this.router.navigate(['/responsable/rapports/', fiche.rapport_id]);
    } else if (fiche.rapport_id === 'pending') {
      console.warn("⚠️ Le rapport est en cours de traitement. Veuillez réessayer plus tard.");
      alert("Le rapport est en cours de traitement. Veuillez réessayer plus tard ou rafraîchir la page.");
    } else {
      console.warn("⚠️ Aucun rapport trouvé pour cette demande.");
      alert("Aucun rapport trouvé pour cette demande.");
    }
  }



  fetchUserName(user_id: number): string {
    if (!user_id) return '⏳ Chargement...';
    if (this.userCache[user_id]) {
      return `${this.userCache[user_id].name} ${this.userCache[user_id].nickname || ''}`;
    }
    return '⏳ Chargement...';
  }

  // ✅ Navigate to fiche-transmission/:id on click
  goToFicheTransmission(ficheId: number) {
    this.router.navigate(['/fiche-transmission', ficheId]);
  }

  createRapport(demandeId: number | string, fiche: any) {
    if (!demandeId) {
      alert("Demande ID invalide !");
      return;
    }

    // Immediately update the UI to show loading state
    fiche.isCreatingRapport = true;
    this.cdr.detectChanges();

    this.ficheService.createRapport(demandeId.toString()).subscribe({
      next: (response) => {
        console.log('Rapport créé - Response:', response);
        console.log('Response type:', typeof response);
        console.log('Response structure:', JSON.stringify(response, null, 2));

        // Update the fiche object with the new rapport_id and status
        // Handle case where response or rapport_id might be null
        if (response && response.rapport_id) {
          fiche.rapport_id = response.rapport_id;
        } else {
          console.log('No rapport_id in response, fetching it separately');
          // Fetch the rapport_id separately
          this.fetchRapportId(demandeId, fiche);
        }

        fiche.statusRapport = 'created';
        fiche.isCreatingRapport = false;

        // Show success message
        alert('Rapport créé avec succès !');

        // Force UI update
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur lors de la création du rapport:', error);
        alert("Échec de la création du rapport.");
        fiche.isCreatingRapport = false;
        this.cdr.detectChanges();
      }
    });
  }
  fetchRapportId(demandeId: string | number, fiche: any) {
    this.ficheService.getRapportIdByDemande(demandeId.toString()).subscribe({
      next: (response) => {
        console.log(`✅ Rapport ID found for demande ${demandeId}:`, response);

        if (response && response.rapport_id) {
          fiche.rapport_id = response.rapport_id;
          fiche.statusRapport = 'created';
          console.log('Updated fiche with rapport_id:', fiche.rapport_id);
        } else {
          console.warn('No rapport_id found in response:', response);
          // If we still can't get the rapport_id, we'll set a temporary one
          // This will be updated when the page refreshes
          fiche.rapport_id = 'pending';
          fiche.statusRapport = 'created';
        }

        // Make sure to clear the loading state
        fiche.isCreatingRapport = false;
        this.cdr.detectChanges(); // ✅ Force UI refresh
      },
      error: (error) => {
        console.error('Error fetching rapport_id:', error);
        fiche.rapport_id = null;
        fiche.statusRapport = 'not_created';
        fiche.isCreatingRapport = false;
        this.cdr.detectChanges();
      }
    });
  }
}
