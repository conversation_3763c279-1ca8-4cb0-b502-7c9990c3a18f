import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faUser, faEnvelope, faPhone, faFax, faMapMarkerAlt, faIdCard } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FontAwesomeModule],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  // User data
  userId: number | null = null;
  name: string | null = null;
  nickname: string | null = null;
  email: string | null = null;
  phone: string | null = null;
  fax: string | null = null;
  adress: string | null = null;
  role: string | null = null;
  createdAt: string | null = null;

  // Font Awesome icons
  faUser = faUser;
  faEnvelope = faEnvelope;
  faPhone = faPhone;
  faFax = faFax;
  faMapMarkerAlt = faMapMarkerAlt;
  faIdCard = faIdCard;

  // Notification properties
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';

  constructor(
    private router: Router
  ) {}

  ngOnInit(): void {
    // Load user data from localStorage
    this.loadUserData();
  }

  /**
   * Load user data from localStorage
   */
  loadUserData(): void {
    this.userId = this.getUserProperty('id');
    this.name = this.getUserProperty('name');
    this.nickname = this.getUserProperty('nickname');
    this.email = this.getUserProperty('email');
    this.phone = this.getUserProperty('phone');
    this.fax = this.getUserProperty('fax');
    this.adress = this.getUserProperty('adress');
    this.role = this.getUserProperty('role');
    this.createdAt = this.getUserProperty('created_at');
  }

  /**
   * Get a specific property from the user object in localStorage
   */
  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');
    
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null; // Return the value if exists, otherwise null
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }

    return null;
  }

  /**
   * Navigate back to dashboard
   */
  goToDashboard(): void {
    this.router.navigate(['/receptionist/dashboard']);
  }

  /**
   * Navigate to change password page
   */
  goToChangePassword(): void {
    this.router.navigate(['/change-password']);
  }

  /**
   * Format role for display (capitalize first letter)
   */
  formatRole(role: string | null): string {
    if (!role) return '';
    
    // Translate role to French
    switch(role) {
      case 'receptionist':
        return 'Réceptionniste';
      case 'client':
        return 'Client';
      case 'admin':
        return 'Administrateur';
      case 'responsable':
        return 'Responsable';
      case 'director':
        return 'Directeur';
      default:
        return role.charAt(0).toUpperCase() + role.slice(1);
    }
  }

  /**
   * Format date for display
   */
  formatDate(date: string | null): string {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return date;
    }
  }
}
