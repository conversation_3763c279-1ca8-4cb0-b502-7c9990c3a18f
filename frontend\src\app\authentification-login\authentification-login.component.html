<div class="login-container">
  <h1 class="title">Se connecter</h1>

  <!-- Display a global error message if needed -->
  <div *ngIf="errorMessage" class="error">
    {{ errorMessage }}
  </div>

  <!-- Loading Spinner Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-popup">
      <div class="spinner"></div>
      <h3>Chargement...</h3>
    </div>
  </div>

  <form [formGroup]="loginForm" (ngSubmit)="loginFormSubmit()">
    <div class="input-group">
      <!-- Email Field -->
      <div class="field-group">
        <label for="email" class="input-label-form">
          Email:
          <span
            *ngIf="
              loginForm.get('email')?.touched &&
              loginForm.get('email')?.errors
            "
            class="error"
          >
            <span *ngIf="loginForm.get('email')?.errors?.['required']">
              L'email est requis.
            </span>
            <span *ngIf="loginForm.get('email')?.errors?.['pattern']">
              Format d'email invalide.
            </span>
          </span>
        </label>
        <input
          id="email"
          type="email"
          formControlName="email"
          class="input-field-form"
        >
      </div>

      <!-- Password Field -->
      <div class="field-group">
        <label for="password" class="input-label-form">
          Mot de passe:
          <span
            *ngIf="
              loginForm.get('password')?.touched &&
              loginForm.get('password')?.errors
            "
            class="error"
          >
            <span *ngIf="loginForm.get('password')?.errors?.['required']">
              Le mot de passe est requis.
            </span>
            <span *ngIf="loginForm.get('password')?.errors?.['minlength']">
              Le mot de passe doit contenir au moins 8 caractères.
            </span>
          </span>
        </label>
        <input
          id="password"
          type="password"
          formControlName="password"
          class="input-field-form"
        >
      </div>
    </div>




    <button
      type="submit"
      class="btn-primary"
      aria-label="Se connecter"
    >
      Se connecter
    </button>
    <div class="login-link">
      Vous n’avez pas de compte ?
      <a (click)="navigateTo('/signup')" class="btn-singup">S'inscrire</a>
    </div>
    <div class="rest-password">
      Mot de passe oublié ?
      <a (click)="navigateTo('/resetPassword')" class="btn-reset" >réinitialiser le mot de passe</a>
    </div>
  </form>
</div>
