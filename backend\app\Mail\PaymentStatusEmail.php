<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PaymentStatusEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public $notification;
    public $demande;
    public $user;
    public $payment;
    public $status;
    public $rejectionReason;

    public function __construct($notification, $demande, $user, $payment, $status, $rejectionReason = null)
    {
        $this->notification = $notification;
        $this->demande = $demande;
        $this->user = $user;
        $this->payment = $payment;
        $this->status = $status;
        $this->rejectionReason = $rejectionReason;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->status === 'approved' 
            ? 'Paiement Approuvé - ' . $this->demande->demande_id
            : 'Paiement Rejeté - ' . $this->demande->demande_id;
            
        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->status === 'approved' 
            ? 'emails.payment_approved'
            : 'emails.payment_rejected';
            
        return new Content(
            view: $view,
        );
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $view = $this->status === 'approved' 
            ? 'emails.payment_approved'
            : 'emails.payment_rejected';
            
        return $this->view($view)
                    ->with([
                        'notification' => $this->notification,
                        'demande' => $this->demande,
                        'user' => $this->user,
                        'payment' => $this->payment,
                        'rejectionReason' => $this->rejectionReason
                    ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
