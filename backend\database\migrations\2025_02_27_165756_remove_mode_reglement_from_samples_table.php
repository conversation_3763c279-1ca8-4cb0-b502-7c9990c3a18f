<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('samples', function (Blueprint $table) {
            $table->dropColumn('mode_reglement'); // Remove the column
        });
    }

    public function down()
    {
        Schema::table('samples', function (Blueprint $table) {
            $table->string('mode_reglement')->nullable(); // Add it back if rollback
        });
    }
};
