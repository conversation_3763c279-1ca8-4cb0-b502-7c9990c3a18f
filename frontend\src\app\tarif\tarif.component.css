/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ Section principale */
.pricing-section {
    min-height: 85vh;
    padding: 80px 5%;
    background: white;
    color: black;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

/* ✅ Conteneur principal */
.container {
    width: 90%;
    max-width: 1200px;
    margin: auto;
}
.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background: #2496d3;
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.4s ease-in-out;
    margin-top: 40px;
    text-align: center;
    animation: pulseGlow 2s infinite alternate;
    box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5);
  }
  .cta-button:hover {
    background: rgb(1, 182, 253);
    transform: scale(1.1);
    box-shadow: 0px 12px 35px rgba(252, 252, 252, 0.8);
  }
/* ✅ Titre animé */
.title {
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 30px;
  letter-spacing: 3px;
  text-shadow: 0px 0px 15px #2496d3;
  border-bottom: 4px solid #2496d3;
  padding-bottom: 10px;
  display: inline-block;
  animation: glowText 1.5s infinite alternate;
}

/* ✅ Animation du titre */
@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4); }
    to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8); }
}

/* ✅ Description */
.description {
    font-size: 18px;
    max-width: 80%;
    margin: auto;
    line-height: 1.8;
}

/* ✅ Bouton interactif */
.button-container {
    margin: 30px 0;
}

.btn-discover {
    font-size: 17px;
    background: #2496d3;
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: bold;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.5s ease-in-out;
    animation: pulse 2s infinite ease-in-out;
    box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.4); /* ✅ Ombre bleue */
}

/* ✅ Effet pulsant */
@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.4); }
    50% { transform: scale(1.05); box-shadow: 0px 10px 20px rgba(36, 150, 211, 0.6); }
    100% { transform: scale(1); box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.4); }
}

/* ✅ Effet au survol */
.btn-discover:hover {
    transform: scale(1.1);
    background: #007bb5;
    box-shadow: 0px 10px 25px rgba(36, 150, 211, 0.6);
}

/* ✅ Boîtes d'analyse */
.pricing-box {
    background: white;
    padding: 30px;
    margin: 20px auto;
    border-radius: 15px;
    box-shadow: 0px 10px 25px rgba(36, 150, 211, 0.3); /* ✅ Ombre bleu ciel */
    transition: transform 0.4s ease-in-out, box-shadow 0.4s ease-in-out;
    opacity: 1; /* ✅ Opacité ajustée pour meilleure visibilité */
    animation: fadeInUp 1s ease-in-out forwards;
    max-width: 800px;
    cursor: default;
}

/* ✅ Effet au survol */
.pricing-box:hover {
    transform: scale(1.05);
    box-shadow: 0px 20px 35px rgba(36, 150, 211, 0.4);
}

/* ✅ Titre des boîtes */
.pricing-box h2 {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

/* ✅ Numérotation */
.step {
    color: orange;
    font-weight: bold;
    font-size: 22px;
    margin-right: 8px;
    text-shadow: 0px 0px 10px rgba(255, 165, 0, 0.6); /* ✅ Ombre orange */
}

/* ✅ Texte */
.pricing-box p {
    font-size: 18px;
    line-height: 1.8;
}

/* ✅ Animation d'apparition */
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✅ Responsive */
@media (max-width: 1024px) {
    .container {
        flex-direction: column;
    }

    .title {
        font-size: 22px;
    }

    .description {
        font-size: 17px;
    }

    .btn-discover {
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .title {
        font-size: 18px;
    }

    .description {
        font-size: 16px;
    }

    .btn-discover {
        font-size: 14px;
        padding: 12px 30px;
    }

    .pricing-box p {
        font-size: 16px;
    }
}
