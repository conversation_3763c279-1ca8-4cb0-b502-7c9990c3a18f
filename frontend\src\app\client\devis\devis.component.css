@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* =========================
   ANIMATIONS
   ========================= */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes underlineGlow {
  from {
    box-shadow: 0 0 10px rgba(36, 150, 211, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(36, 150, 211, 0.9);
  }
}

/* =========================
   CONTENEUR PRINCIPAL
   ========================= */
.devis-container {
  width:80%;
  margin: 50px auto;
  padding: 30px;
  background: #fff;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  animation: fadeInUp 1s ease-in-out forwards;
  transition: transform 0.3s ease-in-out;
  margin-bottom: 10px;
}
.devis-container:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

/* =========================
   TITRE PRINCIPAL
   ========================= */
.devis-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 28px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  color: #2496d3;
  
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* ✅ EFFET DE SOULIGNEMENT BLEU */
.devis-container h2::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 130px;
  height: 4px;
  background: #2496d3;
  border-radius: 2px;
  animation: underlineGlow 1.5s infinite alternate;
}

/* =========================
   TABLEAU
   ========================= */
.styled-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.styled-table thead th {
  background: #2496d3;
  color: #fff;
  padding: 14px;
  text-transform: uppercase;
  font-weight: bold;
  border: none;
}

/* ✅ NOUVEAU : vertical-align pour un alignement consistant */
.styled-table td {
  border: 1px solid #ddd;
  padding: 14px;
  text-align: center;
  font-size: 16px;
  vertical-align: middle; /* centre le contenu verticalement */
}

/* =========================
   INTERACTIVITÉ LIGNES
   ========================= */
.clickable-row {
  cursor: pointer;
  transition: background-color 0.3s;
}
.clickable-row:hover {
  background-color: #f7f7f7; /* Gris plus clair */
}

/* =========================
   BADGES DE STATUT
   ========================= */
.status-tag {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

/* Couleurs pour chaque statut */
.status-tag.pending {
  background-color: #fff3cd; /* jaune pâle */
  color: #856404;
}
.status-tag.ongoing {
  background-color: #cce5ff; /* bleu pâle */
  color: #004085;
}
.status-tag.derogation {
  background-color: #f8d7da; /* rouge pâle */
  color: #721c24;
}
.status-tag.valid {
  background-color: #d4edda; /* vert pâle */
  color: #155724;
}
.status-tag.rejected {
  background-color: #f8d7da; /* rouge pâle */
  color: #721c24;
}

/* =========================
   COLONNE "ACTIONS"
   ========================= */
/* ✅ NOUVEAU : vous pouvez forcer une largeur min si nécessaire */
.actions-col {
  width: 140px; /* Ajustez selon vos besoins */
  text-align: center;
}

/* =========================
   BOUTONS D'ACTION
   ========================= */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.btn-details {
  background: #2496d3;
  color: #fff;
  border: none;
  /* ✅ NOUVEAU : padding vertical/horizontal cohérent */
  padding: 10px 18px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  line-height: 1; /* Assure une même hauteur pour tous */
  transition: all 0.3s ease-in-out;
  /* ✅ NOUVEAU : min-width pour uniformiser la largeur */
  min-width: 100px;
}

.btn-details:hover {
  transform: scale(1.05);
  background: #1a7bad;
  box-shadow: 0 5px 15px rgba(26, 123, 173, 0.4);
}

.btn-payment {
  background: #28a745;
  color: #fff;
  border: none;
  padding: 10px 18px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  line-height: 1;
  transition: all 0.3s ease-in-out;
  min-width: 100px;
}

.btn-payment:hover {
  transform: scale(1.05);
  background: #218838;
  box-shadow: 0 5px 15px rgba(33, 136, 56, 0.4);
}

/* Button for viewing payment proof */
.btn-view-payment {
  background: #17a2b8; /* Info blue color */
  color: #fff;
  border: none;
  padding: 10px 18px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  line-height: 1;
  transition: all 0.3s ease-in-out;
  min-width: 100px;
}

.btn-view-payment:hover {
  transform: scale(1.05);
  background: #138496; /* Darker blue on hover */
  box-shadow: 0 5px 15px rgba(19, 132, 150, 0.4);
}

/* =========================
   FILTER BAR
   ========================= */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end; /* Aligns items at the bottom to match input fields */
  justify-content: center;
  gap: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 75%; /* Limits the width as in your original design */
  margin: 0 auto 20px; /* Centers the filter bar within its parent */
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
  align-items: flex-start;
  text-align: left;
}

.filter-group label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
  margin-bottom: 5px;
}

.filter-group input,
.filter-group select {
  width: 180px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 220px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

.btn-clear {
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  height: 38px; /* Match the height of input fields */
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.btn-group {
  margin-bottom: 0;
}

.invisible {
  visibility: hidden;
  height: 0;
  margin: 0;
  padding: 0;
}

/* =========================
   PAGINATION
   ========================= */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* =========================
   EMPTY AND LOADING STATES
   ========================= */
.empty-row td {
  padding: 30px;
  text-align: center;
  color: #6c757d;
  font-size: 16px;
}

.text-center {
  text-align: center;
}

/* =========================
   LOADING / ERROR MESSAGES
   ========================= */
.loading-message {
  font-size: 18px;
  color: #007bff;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-message::before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #007bff;
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

.error-message {
  font-size: 16px;
  color: #dc3545;
  margin-top: 20px;
  text-align: center;
}

/* =========================
   RESPONSIVE
   ========================= */
@media (max-width: 768px) {
  .styled-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .filter-bar {
    max-width: 95%;
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .filter-group input,
  .filter-group select {
    width: 100%;
  }

  .btn-group {
    margin-top: 0.5rem;
  }

  .btn-clear {
    width: 100%;
    justify-content: center;
  }
}
