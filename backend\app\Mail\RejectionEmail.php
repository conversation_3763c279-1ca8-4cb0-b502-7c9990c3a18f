<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RejectionEmail extends Mailable
{
    use Queueable, SerializesModels;
    
    public $notification;
    public $demande;
    public $user;
    public $reason;

    /**
     * Create a new message instance.
     *
     * @param object $notification The notification object
     * @param object $demande The demande object
     * @param object $user The user object
     * @param string $reason The reason for rejection
     */
    public function __construct($notification, $demande, $user, $reason = null)
    {
        $this->notification = $notification;
        $this->demande = $demande;
        $this->user = $user;
        $this->reason = $reason;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Demande Rejetée - ' . $this->demande->demande_id,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.rejection',
            with: [
                'notification' => $this->notification,
                'demande' => $this->demande,
                'user' => $this->user,
                'reason' => $this->reason
            ]
        );
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.rejection')
                    ->with([
                        'notification' => $this->notification,
                        'demande' => $this->demande,
                        'user' => $this->user,
                        'reason' => $this->reason
                    ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
