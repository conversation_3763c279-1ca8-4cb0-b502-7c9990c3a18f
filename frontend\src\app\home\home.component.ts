import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LaboratoireGestionAnalyseComponent } from '../laboratoire-gestion-analyse/laboratoire-gestion-analyse.component';
import { PresentationComponent } from '../presentation/presentation.component';
import { ServicesComponent } from '../services/services.component';
import { ExpertiseComponent } from '../expertise/expertise.component';
import { AcceptComponent } from '../accept/accept.component';
import { AboutComponent } from '../about/about.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    LaboratoireGestionAnalyseComponent,
    PresentationComponent,
    ServicesComponent,
    ExpertiseComponent,
    AcceptComponent,
    AboutComponent
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit {
  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit() {
    // Check for fragment in URL and scroll to that section
    this.route.fragment.subscribe(fragment => {
      if (fragment) {
        // Wait for the DOM to be fully loaded with a longer timeout
        setTimeout(() => {
          console.log('Looking for element with ID:', fragment);
          const element = document.getElementById(fragment);
          if (element) {
            console.log('Element found, scrolling to it');
            element.scrollIntoView({ behavior: 'smooth' });
          } else {
            console.log('Element not found with ID:', fragment);
            // Try to find the element by selector
            const elementBySelector = document.querySelector(`[id="${fragment}"]`);
            if (elementBySelector) {
              console.log('Element found by selector, scrolling to it');
              elementBySelector.scrollIntoView({ behavior: 'smooth' });
            }
          }
        }, 1000); // Increased timeout to ensure DOM is fully loaded
      }
    });
  }
}
