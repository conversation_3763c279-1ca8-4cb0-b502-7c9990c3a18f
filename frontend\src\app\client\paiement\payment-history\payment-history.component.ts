import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';

import { CommonModule } from '@angular/common';
import { PaiementService } from '../paiement.service';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faCheck,
  faTimes,
  faSpinner,
  faDownload,
  faEye,
  faEyeSlash,
  faTrash,
  faSync,
  faFilePdf,
  faFile,
  faExternalLinkAlt,
  faImage
} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-payment-history',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './payment-history.component.html',
  styleUrls: ['./payment-history.component.css']
})
export class PaymentHistoryComponent implements OnInit, OnDestroy {
  @Input() demandeId: string = '';

  payments: any[] = [];
  isLoading: boolean = true;
  errorMessage: string | null = null;

  // Font Awesome icons
  faCheck = faCheck;
  faTimes = faTimes;
  faSpinner = faSpinner;
  faDownload = faDownload;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faTrash = faTrash;
  faSync = faSync;
  faFilePdf = faFilePdf;
  faFile = faFile;
  faExternalLinkAlt = faExternalLinkAlt;
  faImage = faImage;

  // Event emitter to notify parent component when a payment is deleted
  @Output() paymentDeleted = new EventEmitter<void>();

  // Preview properties
  isLoadingPreview = false;
  previewError: string | null = null;
  currentPreviewUrl: string | null = null;
  currentPreviewPath: string | null = null;
  fileType: 'image' | 'pdf' | 'other' = 'other';
  showPreview = false;
  previewPaymentId: number | null = null;
  currentPayment: any = null;

  // Direct image preview properties
  directImagePreview = false;
  directImageUrl: string | null = null;
  currentImageFileName: string | null = null;
  directImageRetryCount = 0;

  // Notification properties
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '';
  notificationColor = '';

  constructor(private readonly paiementService: PaiementService) {}

  ngOnInit(): void {
    if (this.demandeId) {
      this.loadPaymentHistory();
    }
  }

  loadPaymentHistory(): void {
    this.isLoading = true;
    this.errorMessage = null;

    this.paiementService.getPaymentByDemandeId(this.demandeId).subscribe({
      next: (response) => {
        console.log('Payment history response:', response);

        if (response && response.status === 'success') {
          this.payments = response.data || [];

          // Log payment proofs for debugging
          if (this.payments.length > 0) {
            console.log('Payment proofs:');
            this.payments.forEach((payment, index) => {
              console.log(`Payment ${index + 1}:`, {
                id: payment.id,
                amount: payment.amount,
                status: payment.status,
                payment_proof: payment.payment_proof
              });
            });
          }
        } else {
          this.payments = [];
          this.errorMessage = 'Format de réponse inattendu';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading payment history:', error);

        // Check if it's a backend method name error
        if (error.status === 500 && error.error?.message?.includes('undefined method')) {
          this.errorMessage = 'Erreur de configuration du serveur. Veuillez contacter l\'administrateur.';
          console.error('Backend method name mismatch. The method getPaymentsByDemandeId() needs to be defined in PaymentController.');
        } else {
          this.errorMessage = 'Impossible de charger l\'historique des paiements';
        }

        this.payments = [];
        this.isLoading = false;
      }
    });
  }

  getStatusClass(status: string): { [key: string]: boolean } {
    return {
      'status-approved': status === 'approved',
      'status-rejected': status === 'rejected',
      'status-pending': status !== 'approved' && status !== 'rejected'
    };
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'approved':
        return 'Approuvé';
      case 'rejected':
        return 'Rejeté';
      default:
        return 'En attente';
    }
  }

  getStatusIcon(status: string) {
    switch (status) {
      case 'approved':
        return this.faCheck;
      case 'rejected':
        return this.faTimes;
      default:
        return this.faSpinner;
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * View a payment proof file
   * @param paymentProofPath The path to the payment proof file
   * @param paymentId The ID of the payment
   */
  viewPaymentProof(paymentProofPath: string, paymentId?: number): void {
    console.log('View payment proof called with path:', paymentProofPath);

    if (!paymentProofPath) {
      console.error('Payment proof path is empty');
      this.errorMessage = 'Aucun justificatif de paiement disponible';
      return;
    }

    // Find the current payment in the payments array
    if (paymentId) {
      this.currentPayment = this.payments.find(p => p.id === paymentId);
      console.log('Current payment object:', this.currentPayment);
    }

    // Check if we're already showing this payment's preview (either type)
    const isDirectImageActive = this.directImagePreview && this.currentPayment?.id === paymentId;

    // If we're already showing this payment's preview, toggle it off
    if (isDirectImageActive) {
      this.closeDirectImagePreview();
      return;
    } else if (this.showPreview && this.previewPaymentId === paymentId) {
      this.closePreview();
      return;
    }

    // Determine file type based on file extension
    const fileName = this.getFileNameFromPath(paymentProofPath).toLowerCase();
    let isImage = false;

    if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') ||
        fileName.endsWith('.gif') || fileName.endsWith('.bmp')) {
      console.log('File type detected from filename: Image');
      isImage = true;
    }

    // For images, show directly under the payment history
    if (isImage) {
      // Close any existing preview
      this.closePreview();

      // Reset retry counter
      this.directImageRetryCount = 0;

      // Try to use the direct URLs from the payment object first if available
      if (this.currentPayment?.payment_proof_url) {
        console.log('Using payment_proof_url for direct image preview:', this.currentPayment.payment_proof_url);
        this.directImageUrl = this.currentPayment.payment_proof_url;
      } else if (this.currentPayment?.direct_url) {
        console.log('Using direct_url for direct image preview:', this.currentPayment.direct_url);
        this.directImageUrl = this.currentPayment.direct_url;
      } else {
        // Try with different URL constructions
        // First try with the full path
        const fullPathUrl = this.paiementService.getPaymentProofUrl(paymentProofPath);
        console.log('Using full path URL for direct image preview:', fullPathUrl);
        this.directImageUrl = fullPathUrl;
      }

      // Set direct image preview properties
      this.currentImageFileName = this.getFileNameFromPath(paymentProofPath);
      this.directImagePreview = true;

      // Store the current payment for reference
      this.previewPaymentId = paymentId ?? null;

      return;
    }

    // For non-image files, continue with the existing preview logic
    // If we're already showing this payment's preview, toggle it off
    if (this.showPreview && this.previewPaymentId === paymentId) {
      this.showPreview = false;
      this.previewPaymentId = null;
      this.currentPayment = null;
      return;
    }

    // Reset preview state
    this.isLoadingPreview = true;
    this.previewError = null;
    this.currentPreviewPath = paymentProofPath;
    this.currentPreviewUrl = null;
    this.fileType = 'pdf'; // Default to PDF
    this.showPreview = true;
    this.previewPaymentId = paymentId ?? null;

    // Find the current payment in the payments array
    if (paymentId) {
      this.currentPayment = this.payments.find(p => p.id === paymentId);
      console.log('Current payment:', this.currentPayment);
    }

    try {
      // Determine file type based on file extension
      if (fileName.endsWith('.pdf')) {
        console.log('File type detected from filename: PDF');
        this.fileType = 'pdf';
      } else {
        console.log('File type: Other (not recognized)');
        this.fileType = 'other';
      }

      // Get the URL from the service
      const url = this.paiementService.getPaymentProofUrl(paymentProofPath);
      console.log('Preview URL:', url);

      // Set the URL directly for all file types
      this.currentPreviewUrl = url;

      // Mark loading as complete
      this.isLoadingPreview = false;

      // Set a timeout to hide the loading spinner if it's still showing after 3 seconds
      setTimeout(() => {
        if (this.isLoadingPreview) {
          console.log('Loading timeout reached, hiding spinner');
          this.isLoadingPreview = false;
        }
      }, 3000);
    } catch (error) {
      console.error('Error preparing preview:', error);
      this.isLoadingPreview = false;
      this.previewError = 'Impossible de préparer l\'aperçu du justificatif';
    }
  }

  /**
   * Close the preview
   */
  closePreview(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.currentPreviewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.currentPreviewUrl);
    }

    this.showPreview = false;
    this.currentPreviewUrl = null;
    this.currentPreviewPath = null;
    this.previewError = null;
    this.previewPaymentId = null;

    // Also close direct image preview if it's open
    this.closeDirectImagePreview();
  }

  /**
   * Close the direct image preview
   */
  closeDirectImagePreview(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.directImageUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.directImageUrl);
    }

    this.directImagePreview = false;
    this.directImageUrl = null;
    this.currentImageFileName = null;
    this.previewPaymentId = null;
    this.currentPayment = null;
    this.directImageRetryCount = 0; // Reset retry counter
  }

  /**
   * Handle direct image loading errors
   * @param event The error event
   */
  handleDirectImageError(event: Event): void {
    console.error('Direct image failed to load:', event);

    // Log additional information for debugging
    console.log('Current direct image URL:', this.directImageUrl);
    console.log('Current payment object:', this.currentPayment);

    // Log the actual payment proof path from the payment object
    if (this.currentPayment?.payment_proof) {
      console.log('Original payment_proof path:', this.currentPayment.payment_proof);
    }

    // Create a counter to track attempts
    if (!this.directImageRetryCount) {
      this.directImageRetryCount = 1;
    } else {
      this.directImageRetryCount++;
    }

    console.log(`Image load retry attempt: ${this.directImageRetryCount}`);

    // After 3 attempts, try a different approach
    if (this.directImageRetryCount >= 3) {
      console.log('Multiple retries failed, trying alternative approach');

      // Try to open the file in a new tab instead
      if (this.currentPayment?.payment_proof) {
        // Try to download the file instead of viewing it
        this.downloadPaymentProof(this.currentPayment.payment_proof);
        this.showErrorNotification('Impossible de charger l\'image. Le téléchargement a été lancé à la place.');
        this.closeDirectImagePreview();
        return;
      }

      // Show error and close preview
      this.showErrorNotification('Impossible de charger l\'image. Essayez de télécharger le fichier.');
      this.closeDirectImagePreview();
      return;
    }

    // Try alternative URLs if available
    if (this.currentPayment?.payment_proof_url && this.directImageUrl !== this.currentPayment.payment_proof_url) {
      console.log('Trying payment_proof_url after image error:', this.currentPayment.payment_proof_url);
      this.directImageUrl = this.currentPayment.payment_proof_url;
      return; // Let the image try to load with the new URL
    }

    if (this.currentPayment?.direct_url && this.directImageUrl !== this.currentPayment.direct_url) {
      console.log('Trying direct_url after image error:', this.currentPayment.direct_url);
      this.directImageUrl = this.currentPayment.direct_url;
      return; // Let the image try to load with the new URL
    }

    // Try constructing a URL directly from the payment_proof field if available
    if (this.currentPayment?.payment_proof) {
      // Try with payments/ prefix
      if (!this.directImageUrl?.includes('payments/')) {
        const prefixedUrl = this.paiementService.getPaymentProofUrl(`payments/${this.getFileNameFromPath(this.currentPayment.payment_proof)}`);
        console.log('Trying with payments/ prefix:', prefixedUrl);
        this.directImageUrl = prefixedUrl;
        return;
      }

      // Try without any prefix
      const rawFileName = this.getFileNameFromPath(this.currentPayment.payment_proof);
      const directUrl = this.paiementService.getPaymentProofUrl(rawFileName);
      console.log('Trying with raw filename:', directUrl);
      this.directImageUrl = directUrl;
      return;
    }

    // If we get here, all immediate options failed
    // We'll let the retry counter handle further attempts
    console.error('All immediate URL options failed, will retry with different approach next time');
  }

  /**
   * Handle keyboard events for the preview
   * @param event The keyboard event
   */
  handleKeydown(event: KeyboardEvent): void {
    // Close the preview when Escape key is pressed
    if (event.key === 'Escape') {
      this.closePreview();
    }
  }

  /**
   * Clean up resources when the component is destroyed
   */
  ngOnDestroy(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.currentPreviewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.currentPreviewUrl);
    }

    // Close any open previews
    this.closePreview();
    this.closeDirectImagePreview();
  }

  /**
   * Retry loading the preview
   */
  retryLoadPreview(): void {
    if (this.currentPreviewPath) {
      this.viewPaymentProof(this.currentPreviewPath);
    }
  }

  /**
   * Open PDF in a new tab
   */
  openPdfInNewTab(): void {
    if (this.currentPayment?.payment_proof_url) {
      window.open(this.currentPayment.payment_proof_url, '_blank');
    } else if (this.currentPayment?.direct_url) {
      window.open(this.currentPayment.direct_url, '_blank');
    } else if (this.currentPreviewUrl) {
      window.open(this.currentPreviewUrl, '_blank');
    }
  }

  /**
   * Open the file directly in a new tab
   */
  openDirectUrl(): void {
    // Try to use the current payment's URLs if available
    if (this.currentPayment?.payment_proof_url) {
      console.log('Opening signed URL:', this.currentPayment.payment_proof_url);
      window.open(this.currentPayment.payment_proof_url, '_blank');
      return;
    }

    if (this.currentPayment?.direct_url) {
      console.log('Opening direct URL from payment:', this.currentPayment.direct_url);
      window.open(this.currentPayment.direct_url, '_blank');
      return;
    }

    if (this.currentPreviewUrl) {
      console.log('Opening preview URL:', this.currentPreviewUrl);
      window.open(this.currentPreviewUrl, '_blank');
    } else if (this.currentPreviewPath) {
      // Try to get a direct URL from the service
      const fileName = this.getFileNameFromPath(this.currentPreviewPath);
      const directUrl = this.paiementService.getPaymentProofUrl(fileName);
      console.log('Opening constructed direct URL:', directUrl);
      window.open(directUrl, '_blank');
    }
  }

  /**
   * Download the current file being previewed
   */
  downloadCurrentFile(): void {
    if (this.currentPreviewPath) {
      this.downloadPaymentProof(this.currentPreviewPath);
    }
  }

  /**
   * Handle image loading errors
   * @param event The error event
   */
  handleImageError(event: Event): void {
    console.error('Image failed to load in the DOM:', event);

    // Log the URL that failed
    console.log('Failed URL:', this.currentPreviewUrl);
    console.log('Current file type:', this.fileType);
    console.log('Current preview path:', this.currentPreviewPath);

    // Track if we've tried all available URLs
    let allUrlsFailed = true;

    // Try using the signed URL if available
    if (this.currentPayment?.payment_proof_url && this.currentPreviewUrl !== this.currentPayment.payment_proof_url) {
      console.log('Trying signed URL after image error:', this.currentPayment.payment_proof_url);
      this.currentPreviewUrl = this.currentPayment.payment_proof_url;
      allUrlsFailed = false;
      return; // Let the image try to load with the new URL
    }

    // Try using the direct URL if available
    if (this.currentPayment?.direct_url && this.currentPreviewUrl !== this.currentPayment.direct_url) {
      console.log('Trying direct URL after image error:', this.currentPayment.direct_url);
      this.currentPreviewUrl = this.currentPayment.direct_url;
      allUrlsFailed = false;
      return; // Let the image try to load with the new URL
    }

    // Try constructing a URL directly from the payment_proof field if available
    if (this.currentPayment?.payment_proof &&
        this.currentPreviewUrl !== this.paiementService.getPaymentProofUrl(this.currentPayment.payment_proof)) {
      const directUrl = this.paiementService.getPaymentProofUrl(this.currentPayment.payment_proof);
      console.log('Trying constructed URL after image error:', directUrl);
      this.currentPreviewUrl = directUrl;
      allUrlsFailed = false;
      return; // Let the image try to load with the new URL
    }

    // If all URLs fail, change to PDF view as fallback
    if (allUrlsFailed) {
      console.log('All image URLs failed, switching to PDF view');
      this.fileType = 'pdf';
    }
  }

  /**
   * Check if a file is an image based on its URL or extension
   */
  isImageFile(url: string): boolean {
    if (!url) return false;

    const lowerUrl = url.toLowerCase();
    return lowerUrl.endsWith('.jpg') ||
           lowerUrl.endsWith('.jpeg') ||
           lowerUrl.endsWith('.png') ||
           lowerUrl.endsWith('.gif') ||
           lowerUrl.endsWith('.bmp');
  }

  /**
   * Check if a file is a PDF based on its URL or extension
   */
  isPdfFile(url: string): boolean {
    if (!url) return false;

    const lowerUrl = url.toLowerCase();
    return lowerUrl.endsWith('.pdf');
  }

  /**
   * Check if there are any rejected payments
   */
  hasRejectedPayments(): boolean {
    return this.payments.some(payment => payment.status === 'rejected');
  }

  /**
   * Get all rejected payments
   */
  getRejectedPayments(): any[] {
    return this.payments.filter(payment => payment.status === 'rejected');
  }

  /**
   * Check if a payment can be deleted (only pending or rejected payments can be deleted)
   * @param status The payment status
   */
  canDeletePayment(status: string): boolean {
    return status === 'pending' || status === 'rejected';
  }

  /**
   * Download a payment proof file
   * @param paymentProofPath The path to the payment proof file
   */
  downloadPaymentProof(paymentProofPath: string): void {
    console.log('Download payment proof called with path:', paymentProofPath);

    if (!paymentProofPath) {
      console.error('Payment proof path is empty');
      this.errorMessage = 'Aucun justificatif de paiement disponible';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    // Try to use the current payment's direct URL if available
    if (this.currentPayment?.id && this.previewPaymentId === this.currentPayment.id) {
      console.log('Using current payment for download');
      // Try signed URL first
      if (this.currentPayment.payment_proof_url) {
        console.log('Using signed URL for download:', this.currentPayment.payment_proof_url);
        const link = document.createElement('a');
        link.href = this.currentPayment.payment_proof_url;
        link.download = this.getFileNameFromPath(paymentProofPath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.isLoading = false;
        return;
      }

      // Try direct URL next
      if (this.currentPayment.direct_url) {
        console.log('Using direct URL for download:', this.currentPayment.direct_url);
        const link = document.createElement('a');
        link.href = this.currentPayment.direct_url;
        link.download = this.getFileNameFromPath(paymentProofPath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.isLoading = false;
        return;
      }
    }

    // Use the service method that handles authentication
    this.paiementService.downloadPaymentProof(paymentProofPath).subscribe({
      next: (blob) => {
        this.isLoading = false;

        // Create a blob URL and trigger download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Extract filename from the path or use default
        const fileName = this.getFileNameFromPath(paymentProofPath);
        console.log('Using filename:', fileName);
        link.download = fileName;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error downloading payment proof:', error);
        this.errorMessage = 'Impossible de télécharger le justificatif de paiement';

        // Try direct URL as fallback
        try {
          const url = this.paiementService.getPaymentProofUrl(paymentProofPath);
          console.log('Trying fallback URL for download:', url);
          const link = document.createElement('a');
          link.href = url;
          link.download = this.getFileNameFromPath(paymentProofPath);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } catch (e) {
          console.error('Fallback download also failed:', e);
        }
      }
    });
  }

  /**
   * Get the file name from a path
   * @param path The file path
   * @returns The file name
   */
  getFileNameFromPath(path: string): string {
    if (!path) return 'payment_proof';

    // Extract the file name from the path
    const fileName = path.split('/').pop() ?? path.split('\\').pop() ?? 'payment_proof';
    return fileName;
  }

  // This method is not needed anymore since we're using direct file access

  /**
   * Delete a payment
   * @param paymentId The ID of the payment to delete
   */
  deletePayment(paymentId: number): void {
    if (!paymentId) return;

    // Find the payment in the array
    const payment = this.payments.find(p => p.id === paymentId);

    // Check if the payment exists and can be deleted
    if (!payment) {
      this.showErrorNotification('Paiement introuvable');
      return;
    }

    // Check if the payment status allows deletion
    if (!this.canDeletePayment(payment.status)) {
      this.showErrorNotification('Impossible de supprimer un paiement approuvé');
      return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer ce justificatif de paiement ?')) {
      this.isLoading = true;

      this.paiementService.deletePayment(paymentId).subscribe({
        next: () => {
          // Show success notification
          this.showSuccessNotification('Justificatif de paiement supprimé avec succès');

          // Reload the payment history
          this.loadPaymentHistory();

          // Notify parent component
          this.paymentDeleted.emit();
        },
        error: (error) => {
          console.error('Error deleting payment:', error);
          this.errorMessage = 'Impossible de supprimer le paiement';
          this.isLoading = false;

          // Show error notification
          this.showErrorNotification('Impossible de supprimer le justificatif de paiement');
        }
      });
    }
  }

  /**
   * Show a success notification
   * @param message The message to display
   */
  showSuccessNotification(message: string): void {
    this.notificationMessage = message;
    this.notificationIcon = '✅';
    this.notificationColor = '#155724';
    this.showNotification = true;

    // Hide the notification after 3 seconds
    setTimeout(() => {
      this.showNotification = false;
    }, 3000);
  }

  /**
   * Show an error notification
   * @param message The message to display
   */
  showErrorNotification(message: string): void {
    this.notificationMessage = message;
    this.notificationIcon = '❌';
    this.notificationColor = '#721c24';
    this.showNotification = true;

    // Hide the notification after 3 seconds
    setTimeout(() => {
      this.showNotification = false;
    }, 3000);
  }
}
