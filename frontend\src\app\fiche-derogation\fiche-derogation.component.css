/* ✅ IMPORTATION DES FONTS */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ CONTAINER PRINCIPAL */
.fiche-container {
    background: radial-gradient(circle at top left, #0c0032, #190061);
    color: white;
    padding: 70px 5%;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(255, 0, 128, 0.3);
    animation: fadeInUp 1s ease-in-out;
    margin: auto;
    width:auto;
}

/* ✅ HEADER */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

/* ✅ LOGO */
.header-logo img {
    width: 100px;
}

/* ✅ TITRE PRINCIPAL */
.header-title {
    text-align: center;
    flex: 1;
}

.header-title h2 {
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
}

.header-title h3 {
    font-size: 16px;
    font-weight: bold;
    color: #2496d3;
}

/* ✅ INFOS HEADER */
.header-info {
    text-align: right;
}

.header-info p {
    font-size: 14px;
    font-weight: bold;
}

/* ✅ FORMULAIRE */
form {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 900px;
    animation: fadeIn 1s ease-in-out;
}

/* ✅ ORGANISATION DES CHAMPS EN GRILLE */
.form-group {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;
}

.form-group label {
    font-size: 16px;
    font-weight: bold;
    flex: 1;
}

.form-group input {
    flex: 2;
}

/* ✅ CHAMPS DE TEXTE */
input, textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    outline: none;
    transition: all 0.3s ease-in-out;
}

/* ✅ PLACEHOLDER */
input::placeholder, textarea::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* ✅ EFFET INTERACTIF DES CHAMPS */
input:focus, textarea:focus {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.5);
    border: 2px solid #ff007f;
}

/* ✅ TEXTAREA */
textarea {
    height: 80px;
}

/* ✅ BOUTON DE SOUMISSION */
.btn-submit {
    background: linear-gradient(90deg, #ff007f, #2496d3);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    box-shadow: 0px 5px 15px rgba(230, 0, 126, 0.5);
    text-transform: uppercase;
    font-weight: bold;
    animation: pulse 2s infinite;
    width: 100%;
    margin-top: 20px;
}

/* ✅ HOVER DU BOUTON */
.btn-submit:hover {
    transform: scale(1.05);
    box-shadow: 0px 12px 30px rgba(255, 0, 128, 0.6);
}

/* ✅ ANIMATION DU BOUTON */
@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0px 5px 15px rgba(255, 0, 128, 0.5); }
    50% { transform: scale(1.05); box-shadow: 0px 10px 25px rgba(255, 0, 128, 0.8); }
    100% { transform: scale(1); box-shadow: 0px 5px 15px rgba(255, 0, 128, 0.5); }
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 1024px) {
    .form-group {
        flex-direction: column;
    }

    input, textarea {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .header-title h2 {
        font-size: 16px;
    }

    .header-title h3 {
        font-size: 14px;
    }

    .header-info p {
        font-size: 12px;
    }

    .btn-submit {
        font-size: 14px;
        padding: 10px 20px;
    }
}

/* ✅ ANIMATIONS GLOBALES */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(255, 0, 128, 0.6); }
    to { text-shadow: 0px 0px 20px rgba(255, 0, 128, 0.9); }
}
