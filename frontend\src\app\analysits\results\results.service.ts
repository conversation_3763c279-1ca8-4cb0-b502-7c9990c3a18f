import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { Demande } from '../../receptionniste/demandes/demande.model';

// Make sure the environment is available
const API_URL = environment?.apiUrl || 'http://127.0.0.1:8000/api';

@Injectable({
  providedIn: 'root'
})
export class ResultsService {
  private readonly apiUrl = API_URL;
  private readonly baseUrl = 'http://127.0.0.1:8000';

  constructor(private readonly http: HttpClient) { }

  // Get headers with auth token
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json'
    });
  }

  // Get all results
  getAllResults(): Observable<any> {
    return this.http.get(`${this.apiUrl}/results`, { headers: this.getHeaders() });
  }

  // Get results by demande ID
  getResultsByDemandeId(demandeId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/results/demande/${demandeId}`, { headers: this.getHeaders() });
  }

  // Upload results file
  uploadResults(demandeId: string | number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('results_file', file);

    // Try the direct demande endpoint for uploading (submitResults)
    return this.http.post(`${this.apiUrl}/demande/${demandeId}/results`, formData, {
      headers: new HttpHeaders({
        'Authorization': `Bearer ${localStorage.getItem('token') ?? ''}`,
        'Accept': 'application/json'
      })
    }).pipe(
      catchError(error => {
        // If that fails, try the analyst-specific endpoint (also submitResults)
        if (error.status === 404) {
          console.log('Trying analyst-specific endpoint...');
          return this.uploadResultsAnalyst(demandeId, file);
        }
        console.error('Error uploading results:', error);
        return throwError(() => error);
      })
    );
  }

  // Upload results using analyst-specific endpoint
  private uploadResultsAnalyst(demandeId: string | number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('results_file', file);

    return this.http.post(`${this.apiUrl}/analyst/results/submit/${demandeId}`, formData, {
      headers: new HttpHeaders({
        'Authorization': `Bearer ${localStorage.getItem('token') ?? ''}`,
        'Accept': 'application/json'
      })
    }).pipe(
      catchError(error => {
        // If that fails too, try the store endpoint as a last resort
        // This will still use submitResults internally due to our backend changes
        if (error.status === 404) {
          console.log('Trying store endpoint as a last resort...');
          return this.uploadResultsOriginal(demandeId, file);
        }
        console.error('Error uploading results with analyst endpoint:', error);
        return throwError(() => error);
      })
    );
  }

  // Upload results using store endpoint with demande_id parameter
  // This is kept for backward compatibility but will use submitResults internally
  private uploadResultsOriginal(demandeId: string | number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('demande_id', demandeId.toString());
    formData.append('results_file', file);

    return this.http.post(`${this.apiUrl}/results`, formData, {
      headers: new HttpHeaders({
        'Authorization': `Bearer ${localStorage.getItem('token') ?? ''}`,
        'Accept': 'application/json'
      })
    }).pipe(
      catchError(error => {
        console.error('Error uploading results with store endpoint:', error);
        return throwError(() => error);
      })
    );
  }

  // Download results file
  downloadResults(resultId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/results/download/${resultId}`, {
      headers: this.getHeaders(),
      responseType: 'blob'
    });
  }

  // Delete a result
  deleteResult(resultId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/results/${resultId}`, {
      headers: this.getHeaders()
    }).pipe(
      catchError(error => {
        console.error('Error deleting result:', error);
        return throwError(() => new Error('Failed to delete result. Please try again.'));
      })
    );
  }

  // Get base URL for constructing absolute URLs
  getBaseUrl(): string {
    return this.baseUrl;
  }

  // Get all valid demandes
  getValidDemandes(): Observable<Demande[]> {
    return this.http.get<{ data: Demande[] }>(`${this.apiUrl}/demandes/valid`).pipe(
      map((response: any) => {
        if (response.data) {
          return response.data;
        } else {
          throw new Error('Invalid API response format.');
        }
      }),
      catchError(error => {
        console.error('Error fetching valid demandes:', error);
        return throwError(() => new Error('Failed to load valid demandes. Please try again.'));
      })
    );
  }
}
