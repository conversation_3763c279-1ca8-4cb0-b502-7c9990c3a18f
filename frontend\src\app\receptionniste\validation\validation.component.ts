import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DemandeValidService } from './demande-valid.service';
import { Router, RouterModule } from '@angular/router';
import { Demande } from '../demandes/demande.model';
import { NgxPaginationModule } from 'ngx-pagination';
import { FicheTransmissionService } from '../fiche-transmission/fiche.service';
import { RegistreSuiviService } from '../registres-list/registre.service';
import { FormsModule } from '@angular/forms';
import { faSearch, faEraser, faEye, faSpinner, faFileAlt, faClipboard, faMoneyBill, faCheckCircle, faThumbtack, faCreditCard, faTimesCircle, faFilePdf } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { DemandeService } from '../../client/demande-details/demande.service';
@Component({
  selector: 'app-validation',
  standalone: true,
  templateUrl: './validation.component.html',
  styleUrls: ['./validation.component.css'],
  imports: [CommonModule, NgxPaginationModule, RouterModule, FormsModule, FontAwesomeModule]
})
export class ValidationComponent implements OnInit {
  demandes: Demande[] = [];
  filteredDemandes: Demande[] = []; // For filtered results
  isLoading = true;
  errorMessage: string | null = null;
  // Font Awesome icons
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faSpinner = faSpinner;
  faFileAlt = faFileAlt;
  faClipboard = faClipboard;
  faMoneyBill = faMoneyBill;
  faCheckCircle = faCheckCircle;
  faThumbtack = faThumbtack;
  faCreditCard = faCreditCard;
  faTimesCircle = faTimesCircle;
  faFilePdf = faFilePdf;

  // Payment status tracking
  paymentStatusMap: { [demandeId: string]: { status: string, hasPayment: boolean } } = {};
  // Rapport status tracking
  rapportStatusMap: { [demandeId: string]: { statusRapport: string, rapport_id: string | null, isCreatingRapport: boolean } } = {};
  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Filter properties
  searchTerm: string = '';
  selectedStatus: string = '';
  selectedDate: string = '';

  // Status translations
  statusTranslations: { [key: string]: string } = {
    pending: 'En attente',
    ongoing_validation: 'Validation en cours',
    valid: 'Valide',
    derogation: 'Déroger',
    rejected: 'Rejeté'
  };

  constructor(
    private demandeService: DemandeValidService,
    private registreService: RegistreSuiviService,
    private ficheService: FicheTransmissionService,
    private router: Router,
    private clientDemandeService: DemandeService
  ) {}

  ngOnInit(): void {
    this.fetchAllDemandes();
  }

  fetchAllDemandes(): void {
    this.isLoading = true;
    this.demandeService.getValidDemandes().subscribe({
      next: (data: any) => {
        if (data && data.length > 0) {
          this.demandes = data;
          this.filteredDemandes = [...this.demandes]; // Initialize filtered demandes
          this.demandes.forEach((demande: Demande) => {
            this.fetchUserName(demande);
            this.fetchPaymentStatus(demande);

            // Initialize rapport status for each demande
            this.rapportStatusMap[demande.demande_id] = {
              statusRapport: 'not_created',
              rapport_id: null,
              isCreatingRapport: false
            };

            // Check if rapport exists for this demande
            if (demande.fiche_id && demande.demande_id) {
              this.fetchRapportId(demande.demande_id);
            }
          });
        } else {
          this.errorMessage = 'Aucune demande trouvée.';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching demandes:', error);
        this.errorMessage = 'Échec du chargement des demandes.';
        this.isLoading = false;
      }
    });
  }

  // Filter methods
  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page when filter changes
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.demandes];

    // Filter by search term (demande number or client name)
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      const searchTermLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(demande => {
        const demandeIdMatch = demande.demande_id?.toString().toLowerCase().includes(searchTermLower);
        const clientNameMatch = (demande.userName || '').toLowerCase().includes(searchTermLower);
        return demandeIdMatch || clientNameMatch;
      });
    }

    // Filter by status
    if (this.selectedStatus && this.selectedStatus !== '') {
      filtered = filtered.filter(demande => demande.status === this.selectedStatus);
    }

    // Filter by reception date
    if (this.selectedDate && this.selectedDate !== '') {
      filtered = filtered.filter(demande => {
        if (!demande.demande_date) return false;

        // Convert both dates to YYYY-MM-DD format for comparison
        const filterDate = new Date(this.selectedDate).toISOString().split('T')[0];
        const demandeDate = new Date(demande.demande_date).toISOString().split('T')[0];

        return demandeDate === filterDate;
      });
    }

    this.filteredDemandes = filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedDate = '';
    this.currentPage = 1;
    this.filteredDemandes = [...this.demandes];
  }

  fetchUserName(demande: Demande): void {
    this.demandeService.getUserDetails(demande.user_id).subscribe({
      next: (user) => {
        demande.userName = user.name;
        demande.userNick = user.nickname;
      },
      error: (error) => {
        console.error(`Error fetching user for demande ${demande.demande_id}:`, error);
        demande.userName = 'Inconnu';
      }
    });
  }

  /**
   * Fetch payment status for a demande
   * @param demande The demande to fetch payment status for
   */
  fetchPaymentStatus(demande: Demande): void {
    if (!demande.demande_id) return;

    this.demandeService.getPaymentsByDemandeId(demande.demande_id).subscribe({
      next: (response) => {
        // Check if there are any payments
        const hasPayments = response?.data?.length > 0;

        // Get the status of the most recent payment (first in the array)
        const paymentStatus = hasPayments ? response.data[0].status : 'none';

        // Store the payment status in the map
        this.paymentStatusMap[demande.demande_id] = {
          status: paymentStatus,
          hasPayment: hasPayments
        };
      },
      error: (error) => {
        console.error(`Error fetching payment status for demande ${demande.demande_id}:`, error);
        // Set default status in case of error
        this.paymentStatusMap[demande.demande_id] = {
          status: 'error',
          hasPayment: false
        };
      }
    });
  }

  /**
   * Get payment status text for display
   * @param demandeId The demande ID to get payment status for
   * @returns The payment status text
   */
  getPaymentStatusText(demandeId: string): string {
    if (!demandeId || !this.paymentStatusMap[demandeId]) {
      return 'En attente de paiement';
    }

    const paymentInfo = this.paymentStatusMap[demandeId];

    if (!paymentInfo.hasPayment) {
      return 'En attente de paiement';
    }

    switch (paymentInfo.status) {
      case 'approved':
        return 'Payé';
      case 'rejected':
        return 'Paiement refusé';
      case 'pending':
        return 'En attente de validation';
      default:
        return 'En attente de paiement';
    }
  }

  /**
   * Get payment status class for styling
   * @param demandeId The demande ID to get payment status class for
   * @returns The CSS class for the payment status
   */
  getPaymentStatusClass(demandeId: string): string {
    if (!demandeId || !this.paymentStatusMap[demandeId]) {
      return 'status-pending';
    }

    const paymentInfo = this.paymentStatusMap[demandeId];

    if (!paymentInfo.hasPayment) {
      return 'status-pending';
    }

    switch (paymentInfo.status) {
      case 'approved':
        return 'status-approved';
      case 'rejected':
        return 'status-rejected';
      case 'pending':
        return 'status-pending';
      default:
        return 'status-pending';
    }
  }

  navigateToDemandeDetails(demande: Demande): void {
    if (demande.demande_id) {
      this.router.navigate(['/demande', demande.demande_id]);
    } else {
      console.error('Demande ID is missing:', demande);
    }
  }

  getStatusTranslation(status: string): string {
    return this.statusTranslations[status] || status;
  }

  /**
   * View or create fiche de transmission
   * @param demande The demande object
   */
  viewFicheTransmission(demande: Demande): void {
    if (!demande.fiche_id) {
      // Create a new fiche
      this.createFicheTransmission(demande);
    } else {
      // Navigate to the fiche details page
      this.router.navigate(['/receptionist/fichedetails', demande.fiche_id]);
    }
  }

  /**
   * Create a new fiche de transmission
   * @param demande The demande object
   */
  createFicheTransmission(demande: Demande): void {
    if (!demande.demande_id) {
      console.error('Demande ID is missing:', demande);
      return;
    }

    // Show loading state
    demande.isCreatingFiche = true;

    this.ficheService.createFicheTransmission(demande.demande_id).subscribe({
      next: (response) => {
        console.log('Fiche créée avec succès:', response);

        // Update the demande with the new fiche_id
        demande.fiche_id = response.ficheId;

        // Show success message
        setTimeout(() => {
          demande.isCreatingFiche = false;
          // Don't navigate automatically, just update the UI to show the "Voir" button
        }, 1000); // Short delay to show the loading state
      },
      error: (error) => {
        console.error('Erreur lors de la création de la fiche:', error);
        demande.isCreatingFiche = false;
        alert('Erreur lors de la création de la fiche. Veuillez réessayer.');
      }
    });
  }

  /**
   * View or create registre details
   * @param demande The demande object
   */
  viewRegistre(demande: Demande): void {
    if (!demande.registre_id) {
      // Create a new registre
      this.createRegistre(demande);
    } else {
      // Navigate to the registre details page
      this.router.navigate(['/receptionist/registres', demande.registre_id]);
    }
  }

  /**
   * Create a new registre
   * @param demande The demande object
   */
  createRegistre(demande: Demande): void {
    if (!demande.demande_id) {
      console.error('Demande ID is missing:', demande);
      return;
    }

    // Show loading state
    demande.isCreatingRegistre = true;

    this.registreService.create(demande.demande_id).subscribe({
      next: (response) => {
        console.log('Registre créé avec succès:', response);

        // Update the demande with the new registre_id
        demande.registre_id = response.registre_suivi_id;

        // Show success message
        setTimeout(() => {
          demande.isCreatingRegistre = false;
          // Don't navigate automatically, just update the UI to show the "Voir" button
        }, 1000); // Short delay to show the loading state
      },
      error: (error) => {
        console.error('Erreur lors de la création du registre:', error);
        demande.isCreatingRegistre = false;
        alert('Erreur lors de la création du registre. Veuillez réessayer.');
      }
    });
  }

  /**
   * View or create facture details
   * @param demande The demande object
   */
  viewFacture(demande: Demande): void {
    if (!demande.facture_id) {
      // Create a new facture
      this.createFacture(demande);
    } else {
      // Navigate to the facture details page
      this.router.navigate(['/facture', demande.facture_id]);
    }
  }

  /**
   * Create a new facture
   * @param demande The demande object
   */
  createFacture(demande: Demande): void {
    if (!demande.demande_id) {
      console.error('Demande ID is missing:', demande);
      return;
    }

    // Show loading state
    demande.isCreatingFacture = true;

    this.clientDemandeService.createFacture(demande.demande_id).subscribe({
      next: (response) => {
        console.log('Facture créée avec succès:', response);

        // Update the facture_id if available in the response
        if (response?.facture?.id) {
          demande.facture_id = response.facture.id;
        } else {
          // If facture_id is not in the response, we'll need to refresh to get it
          this.fetchAllDemandes();
        }

        // Show success message and update UI after a short delay
        setTimeout(() => {
          demande.isCreatingFacture = false;
          // Don't navigate automatically, just update the UI to show the "Voir" button
        }, 1000); // Short delay to show the loading state
      },
      error: (error) => {
        console.error('Erreur lors de la création de la facture:', error);
        demande.isCreatingFacture = false;
        alert('Erreur lors de la création de la facture. Veuillez réessayer.');
      }
    });
  }

  /**
   * Create a rapport for a demande
   * @param demandeId The demande ID to create a rapport for
   */
  createRapport(demandeId: string): void {
    if (!demandeId) {
      alert("Demande ID invalide !");
      return;
    }

    // Update UI to show loading state
    this.rapportStatusMap[demandeId].isCreatingRapport = true;

    this.ficheService.createRapport(demandeId).subscribe({
      next: (response) => {
        console.log('Rapport créé - Response:', response);

        // Update the rapport status
        if (response?.rapport_id) {
          this.rapportStatusMap[demandeId].rapport_id = response.rapport_id;
        } else {
          console.log('No rapport_id in response, fetching it separately');
          // Fetch the rapport_id separately
          this.fetchRapportId(demandeId);
        }

        this.rapportStatusMap[demandeId].statusRapport = 'created';

        // Show success message and update UI after a short delay
        setTimeout(() => {
          this.rapportStatusMap[demandeId].isCreatingRapport = false;
          // Don't navigate automatically, just update the UI to show the "Voir" button
        }, 1000); // Short delay to show the loading state
      },
      error: (error) => {
        console.error('Erreur lors de la création du rapport:', error);
        alert("Échec de la création du rapport.");
        this.rapportStatusMap[demandeId].isCreatingRapport = false;
      }
    });
  }

  /**
   * Fetch rapport ID for a demande
   * @param demandeId The demande ID to fetch rapport ID for
   */
  fetchRapportId(demandeId: string): void {
    this.ficheService.getRapportIdByDemande(demandeId).subscribe({
      next: (response) => {
        console.log(`✅ Rapport ID found for demande ${demandeId}:`, response);

        if (response?.rapport_id) {
          this.rapportStatusMap[demandeId].rapport_id = response.rapport_id;
          this.rapportStatusMap[demandeId].statusRapport = 'created';
        } else {
          console.warn('No rapport_id found in response:', response);
          // If we still can't get the rapport_id, we'll set a temporary one
          this.rapportStatusMap[demandeId].rapport_id = 'pending';
          this.rapportStatusMap[demandeId].statusRapport = 'created';
        }

        // Make sure to clear the loading state
        this.rapportStatusMap[demandeId].isCreatingRapport = false;
      },
      error: (error) => {
        console.error('Error fetching rapport_id:', error);
        this.rapportStatusMap[demandeId].rapport_id = null;
        this.rapportStatusMap[demandeId].statusRapport = 'not_created';
        this.rapportStatusMap[demandeId].isCreatingRapport = false;
      }
    });
  }

  /**
   * Navigate to rapport details or create a new rapport
   * @param demande The demande object
   */
  goToRapport(demande: Demande): void {
    const demandeId = demande.demande_id;
    if (!demandeId || !this.rapportStatusMap[demandeId]) {
      console.error('Demande ID is missing or rapport status not found:', demande);
      return;
    }

    const rapportId = this.rapportStatusMap[demandeId].rapport_id;

    if (rapportId && rapportId !== 'pending') {
      // Navigate to existing rapport
      this.router.navigate(['/receptionist/rapports', rapportId]);
    } else if (rapportId === 'pending') {
      console.warn("⚠️ Le rapport est en cours de traitement. Veuillez réessayer plus tard.");
      alert("Le rapport est en cours de traitement. Veuillez réessayer plus tard ou rafraîchir la page.");
    } else {
      // Create a new rapport
      this.createRapport(demandeId);
    }
  }
}
