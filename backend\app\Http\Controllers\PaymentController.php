<?php

namespace App\Http\Controllers;

use App\Models\Demande;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\PaymentNotificationEmail;
use App\Mail\PaymentStatusEmail;
use App\Models\Notification;
class PaymentController extends Controller
{
    /**
     * Store a new payment proof
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function store(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'demande_id' => 'required|string|exists:demandes,demande_id',
                'payment_proof' => 'required|file|mimes:jpeg,jpg,png,pdf|max:25600', // 25MB max
                'amount' => 'required|numeric|min:0',
                'notes' => 'nullable|string|max:500',
            ], [
                'demande_id.required' => 'L\'ID de la demande est requis',
                'demande_id.exists' => 'La demande spécifiée n\'existe pas',
                'payment_proof.required' => 'Le justificatif de paiement est requis',
                'payment_proof.file' => 'Le justificatif de paiement doit être un fichier',
                'payment_proof.mimes' => 'Le justificatif de paiement doit être au format JPEG, JPG, PNG ou PDF',
                'payment_proof.max' => 'Le justificatif de paiement ne doit pas dépasser 25 Mo',
                'amount.required' => 'Le montant est requis',
                'amount.numeric' => 'Le montant doit être un nombre',
                'amount.min' => 'Le montant doit être supérieur ou égal à 0',
                'notes.max' => 'Les notes ne doivent pas dépasser 500 caractères',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get the demande
            $demande = Demande::where('demande_id', $request->demande_id)->first();

            if (!$demande) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Demande not found',
                    'errors' => ['demande_id' => ['La demande spécifiée n\'existe pas']]
                ], 404);
            }

            // Check if the file is present in the request
            if (!$request->hasFile('payment_proof') || !$request->file('payment_proof')->isValid()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid file',
                    'errors' => ['payment_proof' => ['Le fichier est invalide ou corrompu']]
                ], 422);
            }

            // Store the payment proof file
            $path = $request->file('payment_proof')->store('payments', 'public');

            // Get the token from the request header
            $token = $request->bearerToken();

            // If no token is provided, try to get the user ID from the request
            if (!$token) {
                // If user_id is provided in the request, use it
                if ($request->has('user_id')) {
                    $userId = $request->user_id;
                } else {
                    // Return error if no token and no user_id
                    return response()->json([
                        'status' => 'error',
                        'message' => 'User not authenticated',
                        'errors' => ['auth' => ['Vous devez être connecté pour effectuer cette action']]
                    ], 401);
                }
            } else {
                // Try to get the user from the token
                try {
                    $userId = \Laravel\Sanctum\PersonalAccessToken::findToken($token)->tokenable->id;
                } catch (\Exception $e) {
                    // If token is invalid, return error
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Invalid token',
                        'errors' => ['auth' => ['Token invalide ou expiré']]
                    ], 401);
                }
            }

            // Create a new payment record
            $payment = new Payment();
            $payment->demande_id = $demande->id; // Use the numeric ID for the relationship
            $payment->user_id = $userId;
            $payment->amount = $request->amount;
            $payment->payment_proof = $path;
            $payment->notes = $request->notes;
            $payment->status = 'pending'; // Default status is pending
            $payment->payment_date = now();
            $payment->save();

            // Update the demande payment_id
            $demande->payment_id = $payment->id;
            $demande->save();

            // Create notification for payment
            $notification = Notification::create([
                'title' => 'Devis Payé',
                'message' => 'Le devis de la demande '.$demande->demande_id.' a été payé.',
                'demande' => $demande->demande_id,
                'type' => 'devis',
                'rapport_id' => null,
                'user_id' => $userId
            ]);

            // Get user information for the email
            $user = User::find($userId);

            // Find receptionist to send email notification
            $receptionists = User::where('role', 'receptionist')->first();

            // Send email notification
            try {
                if ($receptionists) {
                    Mail::to($receptionists->email)->send(new PaymentNotificationEmail($notification, $demande, $user));
                    Log::info('Payment notification email sent successfully', [
                        'receptionist_email' => $receptionists->email,
                        'demande_id' => $demande->demande_id
                    ]);
                } else {
                    Log::warning('No receptionist found to send payment notification email');
                }
            } catch (\Exception $e) {
                Log::error('Failed to send payment notification email', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'demande_id' => $demande->demande_id
                ]);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Payment proof submitted successfully',
                'data' => $payment
            ], 201);

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Payment upload error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while processing your payment',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get payments by demande ID
     *
     * @param  string  $demandeId
     * @return \Illuminate\Http\Response
     */
    public function getByDemandeId($demandeId)
    {
        // Get the demande
        $demande = Demande::where('demande_id', $demandeId)->first();

        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Check if the demande belongs to the authenticated user


        // Get all payments for this demande with signed URLs
        $payments = Payment::where('demande_id', $demande->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($payment) {
                if ($payment->payment_proof) {
                    // Generate a signed URL for secure access
                    $payment->payment_proof_url = URL::temporarySignedRoute(
                        'serve.payment.proof',
                        now()->addMinutes(30), // URL valid for 30 minutes
                        ['paymentId' => $payment->id]
                    );

                    // Also provide a direct URL as fallback (less secure but might be more reliable)
                    $payment->direct_url = asset('storage/' . $payment->payment_proof);
                } else {
                    $payment->payment_proof_url = null;
                    $payment->direct_url = null;
                }
                return $payment;
            });

        return response()->json([
            'status' => 'success',
            'data' => $payments
        ]);
    }

    /**
     * Delete a payment
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Find the payment
        $payment = Payment::find($id);

        if (!$payment) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment not found'
            ], 404);
        }

        // Check if the payment belongs to the authenticated user


        // Check if the payment is in a deletable state (only pending payments can be deleted)
        if ($payment->status === 'approved' ) {
            return response()->json([
                'status' => 'error',
                'message' => 'Only pending payments can be deleted'
            ], 400);
        }

        // Delete the payment proof file
        if ($payment->payment_proof && Storage::disk('public')->exists($payment->payment_proof)) {
            Storage::disk('public')->delete($payment->payment_proof);
        }

        // Get the demande
        $demande = Demande::find($payment->demande_id);
        if ($demande && $demande->payment_id === $payment->id) {
            $demande->payment_id = null;
            $demande->save();
        }

        // Delete the payment
        $payment->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Payment deleted successfully'
        ]);
    }

    /**
     * View a payment proof file
     *
     * @param  Request  $request
     * @param  string  $filePath
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function viewPaymentProof(Request $request, $filePath)
    {
        // Decode the file path
        $filePath = urldecode($filePath);

        // Prepend the payments directory if not already included
        if (!str_starts_with($filePath, 'payments/')) {
            $filePath = 'payments/' . $filePath;
        }

        // Check if the file exists
        if (!Storage::disk('public')->exists($filePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'File not found'
            ], 404);
        }

        // Check if the file exists in storage
        $fullFilePath = storage_path('app/public/' . $filePath);

        if (!file_exists($fullFilePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment proof file not found'
            ], 404);
        }

        // For direct file download
        if ($request->has('format') && $request->format === 'json') {
            // Generate a URL to the file
            $url = asset('storage/' . $filePath);

            return response()->json([
                'status' => 'success',
                'file_url' => $url,
                'file_path' => $filePath
            ]);
        }

        // Default: serve the file directly
        return response()->file($fullFilePath);
    }


    /**
     * Download a payment proof file
     *
     * @param  Request  $request
     * @param  string  $filePath
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function getPaymentsByDemandeId(Request $request, $demandeId)
    {
        // Get the demande
        $demande = Demande::where('demande_id', $demandeId)->first();

        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // No authentication check - allow direct access to payment information

        // Get all payments for this demande
        $payments = Payment::where('demande_id', $demande->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($payment) {
                if ($payment->payment_proof) {
                    // Add direct URL for the payment proof
                    $payment->direct_url = asset('storage/' . $payment->payment_proof);
                }
                return $payment;
            });

        return response()->json([
            'status' => 'success',
            'data' => $payments
        ]);
    }
    public function downloadPaymentProof(Request $request, $filePath)
    {
        // Decode the file path
        $filePath = urldecode($filePath);

        // Prepend the payments directory if not already included
        if (!str_starts_with($filePath, 'payments/')) {
            $filePath = 'payments/' . $filePath;
        }

        // Check if the file exists
        if (!Storage::disk('public')->exists($filePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'File not found'
            ], 404);
        }

        // Check if the file exists in storage
        $fullFilePath = storage_path('app/public/' . $filePath);

        if (!file_exists($fullFilePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment proof file not found'
            ], 404);
        }

        // For direct file download
        if ($request->has('format') && $request->format === 'json') {
            // Generate a URL to the file
            $url = asset('storage/' . $filePath);

            return response()->json([
                'status' => 'success',
                'file_url' => $url,
                'file_path' => $filePath
            ]);
        }

        // Default: serve the file directly
        return response()->file($fullFilePath);
    }

    /**
     * Download a payment proof file by payment ID
     *
     * @param  Request  $request
     * @param  int  $paymentId
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function downloadPaymentProofById(Request $request, $paymentId)
    {
        // Find the payment
        $payment = Payment::find($paymentId);

        if (!$payment) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment not found'
            ], 404);
        }

        // Check if the file exists in storage
        $filePath = $payment->payment_proof;
        $fullFilePath = storage_path('app/public/' . $filePath);

        if (!file_exists($fullFilePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment proof file not found'
            ], 404);
        }

        // For direct file download
        if ($request->has('format') && $request->format === 'json') {
            // Generate a URL to the file
            $url = asset('storage/' . $filePath);

            return response()->json([
                'status' => 'success',
                'file_url' => $url,
                'file_path' => $filePath,
                'payment_id' => $paymentId
            ]);
        }

        // Default: serve the file directly
        return response()->file($fullFilePath);
    }

    /**
     * Serve a payment proof file with signature verification
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $paymentId
     * @return \Illuminate\Http\Response
     */
    public function servePaymentProof(Request $request, $paymentId)
    {
        // Find the payment
        $payment = Payment::find($paymentId);
        if (!$payment) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment not found'
            ], 404);
        }

        // No authorization check - allow direct access to payment proof

        // Get the file path
        $filePath = $payment->payment_proof;
        $fullFilePath = storage_path('app/public/' . $filePath);

        // Check if the file exists
        if (!file_exists($fullFilePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment proof file not found'
            ], 404);
        }

        // Serve the file
        return response()->file($fullFilePath);
    }

    /**
     * Update payment status (admin only)
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request, $id)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:pending,approved,rejected',
            'rejection_reason' => 'required_if:status,rejected|string|max:500',
        ], [
            'rejection_reason.required_if' => 'La raison du rejet est requise lorsque le statut est "rejected"',
            'rejection_reason.max' => 'La raison du rejet ne doit pas dépasser 500 caractères',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the payment
        $payment = Payment::find($id);

        if (!$payment) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment not found'
            ], 404);
        }

        // Get the demande associated with this payment
        $demande = Demande::find($payment->demande_id);
        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Associated demande not found'
            ], 404);
        }

        // Update the payment status
        $payment->status = $request->status;

        // If the payment is rejected, store the rejection reason in the notes field
        if ($request->status === 'rejected' && $request->has('rejection_reason')) {
            $payment->notes = $request->rejection_reason;
        }

        $payment->save();

        // Create notification based on the status
        $notification = null;
        $rejectionReason = null;

        if ($request->status === 'approved') {
            $notification = Notification::create([
                'title' => 'Paiement Approuvé',
                'message' => 'Le paiement pour la demande ' . $demande->demande_id . ' a été approuvé.',
                'demande_id' => $demande->id,
                'demande' => $demande->demande_id,
                'type' => 'devis',
                'rapport_id' => null,
                'user_id'=>$demande->user_id
            ]);
        } elseif ($request->status === 'rejected') {
            $rejectionReason = $request->rejection_reason;
            $notification = Notification::create([
                'title' => 'Paiement Rejeté',
                'message' => 'Le paiement pour la demande ' . $demande->demande_id . ' a été rejeté. Raison: ' . $rejectionReason,
                'demande_id' => $demande->id,
                'demande' => $demande->demande_id,
                'type' => 'devis',
                'rapport_id' => null,
                'user_id'=>$demande->user_id
            ]);
        }

        // Send email notification if status is approved or rejected
        if ($notification && in_array($request->status, ['approved', 'rejected'])) {
            // Get user information
            $user = User::find($demande->user_id);



            // Send email notification
            try {
                // Also send email to the client
                if ($user) {
                    Mail::to($user->email)
                        ->send(new PaymentStatusEmail(
                            $notification,
                            $demande,
                            $user,
                            $payment,
                            $request->status,
                            $rejectionReason
                        ));

                    Log::info('Payment status email sent to client', [
                        'client_email' => $user->email,
                        'demande_id' => $demande->demande_id,
                        'status' => $request->status
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to send payment status email', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'demande_id' => $demande->demande_id,
                    'status' => $request->status
                ]);
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Payment status updated successfully',
            'data' => $payment
        ]);
    }

    /**
     * Get payments for the authenticated user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getUserPayments(Request $request)
    {
        // Get user ID from request parameter
        if ($request->has('user_id')) {
            $userId = $request->user_id;
        } else {
            // Default to a user ID if none provided
            // This is not secure but meets the requirement to remove authentication
            $userId = 1; // Default user ID
        }

        // Get all payments for this user
        $payments = Payment::whereHas('demande', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })
        ->with('demande')
        ->orderBy('created_at', 'desc')
        ->get()
        ->map(function ($payment) {
            if ($payment->payment_proof) {
                // Add direct URL for the payment proof
                $payment->direct_url = asset('storage/' . $payment->payment_proof);
            }
            return $payment;
        });

        return response()->json([
            'status' => 'success',
            'data' => $payments
        ]);
    }
    /**
     * Get all payments with related information
     *
     * @return \Illuminate\Http\Response
     */
    public function getAllPayments()
    {
        try {
            // Get all payments with related demande and user information
            $payments = Payment::with(['demande', 'user'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($payment) {
                    // Add direct URL for the payment proof
                    if ($payment->payment_proof) {
                        $payment->direct_url = asset('storage/' . $payment->payment_proof);
                    }

                    // Format the payment data
                    return [
                        'id' => $payment->id,
                        'demande_id' => $payment->demande ? $payment->demande->demande_id : null,
                        'user_id' => $payment->user_id,
                        'user_name' => $payment->user ? $payment->user->name : null,
                        'user_email' => $payment->user ? $payment->user->email : null,
                        'amount' => $payment->amount,
                        'payment_proof' => $payment->payment_proof,
                        'direct_url' => $payment->direct_url ?? null,
                        'status' => $payment->status,
                        'payment_date' => $payment->payment_date,
                        'notes' => $payment->notes,
                        'created_at' => $payment->created_at,
                        'updated_at' => $payment->updated_at
                    ];
                });

            return response()->json([
                'status' => 'success',
                'data' => $payments
            ]);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Error fetching all payments: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while fetching payments',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}