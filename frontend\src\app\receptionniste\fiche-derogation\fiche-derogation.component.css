@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

/* Conteneur principal */
.demandes-container {
  text-align: center;
  padding: 70px 5%;
  font-family: 'Poppins', sans-serif;
  background: white;
  color: black;
  margin: auto;
  border-radius: 12px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  animation: fadeIn 0.3s ease-in-out;
}
.notification {
  background: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
  text-align: center;
  width: 80%;
}

/* =========================
   Notification de soumission
   ========================= */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.notification-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
  animation: fadeIn 0.5s ease-in-out;
}

.notification-icon {
  font-size: 50px;
  margin-bottom: 20px;
}

.notification-message {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

/* =========================
   Loading Spinner Overlay
   ========================= */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.loading-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-popup h3 {
  margin-top: 20px;
  color: #333;
  font-size: 18px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/* TITRE PRINCIPAL */
.demandes-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 10px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;

}
.sample-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Ensure alignment to the left */
  gap: 5px; /* Optional: space between elements */
  width: 100%; /* Make it take the full width of the container */
}

/* Échantillon Cards */
.echantillon-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  margin-bottom: 20px;
  transition: transform 0.4s ease-in-out, box-shadow 0.4s ease-in-out;
  margin: auto;
}


/* Grille des inputs et labels */
.input-group {
  display: grid;
  grid-template-columns: 250px 1fr;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  width: 100%;
}
.input-group {
  display: grid;
  grid-template-columns: 250px 1fr;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  width: 100%;
}

/* Style des labels */
.input-group label {
  font-weight: bold;
  font-size: 16px;
  color: black;
  text-align: left;
}

/* Inputs, Selects, et Textareas */
input[type="text"],
input[type="number"],
select,
textarea {
  padding: 12px;
  border: 2px solid #2496d3;
  border-radius: 8px;
  font-size: 16px;
  box-shadow: 0px 0px 8px rgba(36, 150, 211, 0.3);
  transition: all 0.3s ease-in-out;
  width: 100%;
}

input:focus,
textarea:focus,
select:focus {
  border-color: #1a73e8;
  box-shadow: 0px 0px 12px rgba(36, 150, 211, 0.6);
  animation: glowInput 0.5s infinite alternate;
}

/* Animation pour l'effet de glow */


/* Dropdown personnalisé */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-toggle {
  padding: 12px;

  border-radius: 8px;
  font-size: 16px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 0px 8px rgba(36, 150, 211, 0.3);
}

.dropdown-list {
  display: none;
  position: absolute;
  width: 100%;
  background: white;
  border: 2px solid #2496d3;
  border-radius: 8px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  max-height: 250px;
  overflow-y: auto;
  padding: 10px;
  z-index: 10;
}

.dropdown-list.open {
  display: block;
}

.dropdown-list label {
  display: flex;
  align-items: center;
  padding: 5px;
  cursor: pointer;
}

/* Conteneur des boutons */
.btns {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;
}select, option {
  color: black !important; /* Ensure the text is visible */
}


/* Styles généraux des boutons */
button {
  padding: 14px 25px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  border: none;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-add {
  background: #2496d3;
  color: white;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.5);
}

.btn-add:hover {
  transform: scale(1.05);
  box-shadow: 0px 12px 35px rgba(36, 150, 211, 0.8);
}

.btn-delete {
  background: #dc3545;
  color: white;
  display: block;
  margin: 0 auto;
}

.btn-delete:hover {
  transform: scale(1.05);
  background: #b22234;
}

.btn-submit {
  background: #28a745;
  color: white;
  box-shadow: 0px 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-submit:hover {
  transform: scale(1.05);
  background: #218838;
  box-shadow: 0px 8px 20px rgba(40, 167, 69, 0.6);
}

/* Adaptation Responsive */
@media (max-width: 768px) {
  .input-group {
    grid-template-columns: 1fr;
  }

  .echantillon-card {
    width: 100%;
    padding: 15px;
  }

  .btns {
    flex-direction: column;
    width: 100%;
  }

  button {
    width: 100%;
  }
}
