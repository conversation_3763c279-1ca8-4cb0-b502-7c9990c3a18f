/* ✅ Styled Table */
.styled-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: white;
    text-align: left;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
    animation: fadeInBg 2s ease-in-out;
    
  }
  .container{
      text-align: center;
      padding: 50px 5%;
      font-family: 'Montserrat', sans-serif;
      background: white;
      color: black;
      animation: fadeInBg 2s ease-in-out;
      
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
  
  .edit-btn, .delete-btn {
    padding: 10px;
    border: none;
    cursor: pointer;
    font-size: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
  }
  
  /* ✅ MODIFIER */
  .edit-btn {
    background: #f0ad4e;
    color: white;
  }
  
  .edit-btn:hover {
    background: #d6963c;
    transform: scale(1.1);
  }
  
  /* ✅ SUPPRIMER */
  .delete-btn {
    background: #dc3545;
    color: white;
  }
  
  .delete-btn:hover {
    background: #b22234;
    transform: scale(1.1);
  }
  .action-btns{
    display: flex;
    justify-content: center;
    gap: 10px;

  }
  .user-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
    justify-content: center;
    align-items: center;
  }
  
  .user-table th {
    background: #2496d3;
    color: white;
    padding: 14px;
    text-transform: uppercase;
    font-weight: bold;
  }
  
  .user-table td {
    border: 1px solid #ddd;
    padding: 14px;
    text-align: center;
    font-size: 16px;
  }
  th, td {
    padding: 12px;
    border-bottom: 1px solid #ddd;
  }
  
  th {
    background: #2496d3;
    color: white;
    text-transform: uppercase;
  }
  
  td {
    color: black;
  }
  
  .styled-table tbody tr:hover {
    background-color: rgba(36, 150, 211, 0.1);
  }
  
  /* ✅ Form Styles */
  .form-group {
    margin-bottom: 15px;
  }
  
  input {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
  }
  
  /* ✅ Button Styles */
  .edit-btn {
    background: #ffcc00;
  }
  
  .delete-btn {
    background: #e74c3c;
  }
  
  .save-btn {
    background: #28a745;
  }
  
  .cancel-btn {
    background: grey;
  }
  
  /* ✅ Responsive Design */
  @media screen and (max-width: 768px) {
    .styled-table {
      font-size: 14px;
    }
  
    .btn-action {
      font-size: 14px;
      padding: 10px 20px;
    }
  }
  .modal {
    display: none;
    margin-top: 80px;
    margin-bottom: 80px;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    overflow-y: auto;
  padding: 20px;
  animation: fadeIn 0.3s ease-in-out;
    background-color: rgba(0,0,0,0.4);
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
  background: white;
  padding: 35px;
  border-radius: 12px;
  width: 520px;
  max-height: 90vh;
  text-align: center;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  animation: fadeInUp 0.4s ease-in-out;
}
.modal-content h3 {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
  padding: 50px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* ✅ CHAMPS DU FORMULAIRE */
.modal-content label {
  display: block;
  font-weight: bold;
  margin-top: 12px;
  font-size: 16px;
}

.modal-content input, .modal-content select {
  width: 100%;
  padding: 12px;
  margin-top: 6px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 16px;
  background: #f8f8f8;
}

/* ✅ ESPACEMENT ET LISIBILITÉ */
.modal-content input {
  height: 50px;
}

/* ✅ BOUTONS DE LA MODALE */
.modal-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.close {
    float: right;
    font-size: 24px;
    cursor: pointer;
}
.add-sample-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-weight: bold;
  background: #2496d3;
  color: white;
  padding: 14px 25px;
  font-size: 18px;
  border-radius: 30px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5);
}

.add-sample-btn:hover {
  background: rgb(0, 174, 255);
  transform: scale(1.02);
  box-shadow: 0px 12px 35px rgba(4, 176, 255, 0.8);
}
.add-sample{
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 120px;
  font-weight: bold;
 
  padding: 14px 25px;
  font-size: 18px;
  border-radius: 30px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 28px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 10px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.save-btn, .cancel-btn {
  padding: 14px 20px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: inline-block;
  justify-content: center;
  border-radius: 8px;
  transition: background 0.3s ease, transform 0.2s;
  width: 48%;
}

.save-btn {
  background: #2496d3;
  color: white;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.save-btn:hover, .cancel-btn:hover {
  transform: scale(1.05);
}
.action-btns-modal{

  display: flex;
  justify-content: center;
  gap: 10px;
  padding-top: 20px;
}