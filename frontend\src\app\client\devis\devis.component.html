<div class="devis-container">
  <h2>
    Liste des devis
  </h2>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro:</label>
      <input
        type="text"
        id="search"
        placeholder="Rechercher..."
        [(ngModel)]="searchTerm"
        (input)="onFilterChange()"
        class="filter-input"
      />
    </div>
    <div class="filter-group">
      <label for="status">Statut paiement:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="pending">En attente</option>
        <option value="approved">Approuvé</option>
        <option value="rejected">Rejeté</option>
      </select>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <div class="filter-group btn-group">
      <label class="invisible">Action:</label>
      <button (click)="clearFilters()" class="btn-clear">
        <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>
        Effacer les filtres
      </button>
    </div>
  </div>

  <!-- Affichage conditionnel du tableau -->
  <table class="styled-table" *ngIf="!isLoading && !hasError">
    <thead>
      <tr>
        <th>Demande Numéro</th>
        <th>Date de Soumission</th>
        <th>Prix Total</th>
        <th>Statut Paiement</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <!-- Message quand aucune demande n'est trouvée -->
      <tr *ngIf="!isLoading && filteredDemandes.length === 0" class="empty-row">
        <td colspan="5" class="text-center">
          Aucun devis trouvé.
        </td>
      </tr>

      <!-- Affichage des demandes -->
      <ng-container *ngIf="!isLoading && filteredDemandes.length > 0">
        <tr
          *ngFor="
            let demande of filteredDemandes
            | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
          class="clickable-row"
          (mouseover)="hoveredDemande = demande.demande_id"
          (mouseleave)="hoveredDemande = null"
        >
        <td>{{ demande.demande_id }}</td>
        <td>{{ demande.demande_date }}</td>
        <td>
          <ng-container
            *ngIf="demande?.status === 'valid' && demande?.devis_sent !== null; else notAvailable"
          >
            {{ getTotalPrice(demande) }} DT
          </ng-container>
          <ng-template #notAvailable>
            Prix non disponible
          </ng-template>
        </td>
        <td>
          <!-- Badges de statut de paiement -->
          <span class="status-tag pending" *ngIf="demande?.status === 'valid' && !hasPayment(demande)">
            🟡 En attente de paiement
          </span>
          <span class="status-tag pending" *ngIf="hasPayment(demande) && getPaymentStatus(demande) === 'pending'">
            🟠 Paiement en cours de vérification
          </span>
          <span class="status-tag valid" *ngIf="hasPayment(demande) && getPaymentStatus(demande) === 'approved'">
            ✅ Paiement approuvé
          </span>
          <span class="status-tag rejected" *ngIf="hasPayment(demande) && getPaymentStatus(demande) === 'rejected'">
            ❌ Paiement rejeté
          </span>
        </td>
        <!-- Colonne d'action -->
        <td class="action-buttons">
          <button
            class="btn-details"
            (click)="navigateToDetails(demande.demande_id)"
          >
            <fa-icon [icon]="faEye" style="margin-right: 8px; font-size: 16px;"></fa-icon>Voir détails
          </button>
          <!-- Button for payment when not paid yet -->
          <button
            *ngIf="demande?.status === 'valid' && !hasPayment(demande)"
            class="btn-payment"
            (click)="navigateToPayment(demande.demande_id)"
          >
            <fa-icon [icon]="faMoneyBill" style="margin-right: 8px; font-size: 16px;"></fa-icon>Justification de paiement
          </button>

          <!-- Button to view payment proof when already paid -->
          <button
            *ngIf="hasPayment(demande)"
            class="btn-view-payment"
            (click)="navigateToPayment(demande.demande_id)"
          >
            <fa-icon [icon]="faMoneyBill" style="margin-right: 8px; font-size: 16px;"></fa-icon>Voir justification
          </button>
        </td>
      </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    *ngIf="!isLoading && filteredDemandes.length > 0"
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>

  <!-- Message de chargement -->
  <div class="loading-message" *ngIf="isLoading">Chargement...</div>

  <!-- Message d'erreur -->
  <div class="error-message" *ngIf="hasError">
    Une erreur s’est produite lors de la récupération des données.
  </div>
</div>
