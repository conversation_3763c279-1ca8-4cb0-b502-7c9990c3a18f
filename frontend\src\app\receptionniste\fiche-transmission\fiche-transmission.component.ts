import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FicheTransmissionService } from './fiche.service';
import { DemandeService } from '../demandes/demande.service';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Component({
  selector: 'app-fiche-transmission',
  standalone: true,
  templateUrl: './fiche-transmission.component.html',
  styleUrls: ['./fiche-transmission.component.css'],
  imports: [CommonModule],
})
export class FicheTransmissionComponent implements OnInit {
  // Tableau des fiches chargées
  fiches: any[] = [];

  // État de chargement / message d'erreur
  loading = true;
  errorMessage = '';

  // ID de la ficheTransmission, récupéré depuis l'URL
  ficheTransmissionId!: number;

  // Cache local pour éviter de refetch à chaque fois
  userCache: { [userId: number]: { name: string; nickname: string } } = {};

  // Champs bonus pour l'affichage du résumé ou autre
  clientName = '';             // ex. "Mahdi Naifar"
  dateTransmissionGlobal = ''; // ex. "2025-03-25"

  // Variables for editable PDF
  isEditingPdf: boolean = false;
  editablePrintWindow: Window | null = null;

  constructor(
    private ficheService: FicheTransmissionService,
    private demandeService: DemandeService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    // Récupération de l'ID depuis l'URL
    this.route.params.subscribe((params) => {
      this.ficheTransmissionId = +params['id']; // Convertit en nombre
      if (this.ficheTransmissionId) {
        this.loadFiches(this.ficheTransmissionId);
      }
    });

  }

  /**
   * ➜ Retour à la liste de fiches
   */
  goToFicheList() {
    this.router.navigate(['/receptionist/fiche-transmission']);
  }

  /**
   * ➜ Récupérer le nom de l'utilisateur (client)
   *   - Soit on l'a déjà en cache
   *   - Soit on fetch depuis l'API
   */
  fetchClientName(userId: number): void {
    if (!userId) return;

    // Check cache first
    if (this.userCache[userId]) {
      this.clientName = `${this.userCache[userId].name} ${this.userCache[userId].nickname || ''}`;
      return;
    }

    // Fetch from API if not in cache
    this.demandeService.getUserDetails(userId).subscribe({
      next: (user) => {
        this.userCache[userId] = { name: user.name, nickname: user.nickname || '' };
        this.clientName = `${user.name} ${user.nickname || ''}`;
      },
      error: (error) => {
        console.error(`Error fetching user details for ID ${userId}:`, error);
        this.clientName = 'Inconnu';
      }
    });
  }

  /**
   * ➜ Méthode pour imprimer la fiche avec des champs éditables
   */
  printFiche(): void {
    // Calculate total pages based on fiches
    const fichesPerPage = 10; // Assuming 10 fiches can fit on a page
    const totalFiches = this.fiches.length;
    const fichePages = Math.ceil(totalFiches / fichesPerPage);
    const totalPages = Math.max(1, fichePages); // At least 1 page

    // Generate table rows dynamically from fiches data with editable fields
    const tableRows = this.fiches.map(fiche => {
      // Convert analyses_demandees array to an unordered list
      const analysesList = fiche.analyses_demandees?.length > 0
        ? `<ul style="margin: 0; padding-left: 20px;">${fiche.analyses_demandees.map((analysis:any) => `<li contenteditable="true" class="editable-field">${analysis}</li>`).join('')}</ul>`
        : '—';

      return `
        <tr>
          <td><div contenteditable="true" class="editable-field">${fiche.code_laboratoire || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${fiche.nature_echantillon || '—'} / ${fiche.masse_echantillon || '—'}g</div></td>
          <td><div contenteditable="true" class="editable-field">${fiche.date_transmission || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${fiche.date_remise_resultats || '—'}</div></td>
          <td>${analysesList}</td>
          <td><div contenteditable="true" class="editable-field">${fiche.observations || '—'}</div></td>
        </tr>
      `;
    }).join('');

    const printHtml = `
<!DOCTYPE html>
<html lang="fr">
<head>
<meta charset="UTF-8">
<title>Formulaire d'enregistrement et transmission #${this.ficheTransmissionId}</title>
<style>
  /* Print Settings */
  @media print {
    @page {
      size: A4;
      margin: 5mm 5mm;
      counter-increment: page;
    }
    body {
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
      counter-reset: page 1;
    }
    /* Hide editable field styling when printing */
    .editable-field {
      border: none !important;
      background-color: transparent !important;
    }
    /* Hide print controls when printing */
    .print-controls {
      display: none !important;
    }
    /* Make header repeat on each page */
    thead {
      display: table-header-group;
    }
    tfoot {
      display: table-footer-group;
    }
    /* Ensure proper page breaks */
    tr {
      page-break-inside: avoid;
    }
    .header-table {
      page-break-after: avoid;
    }
    /* Page number styling */
    .page-number::after {
      content: counter(page) " / " attr(data-total-pages);
    }
    .page-number {
      position: relative;
    }
    .page-number span {
      display: none;
    }
  }

  /* Global Styles */
  body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
  }

  /* Editable Fields Styles */
  .editable-field {
    min-height: 20px;
    border: 1px dashed #ccc;
    padding: 2px 5px;
    background-color: #f9f9f9;
  }
  .editable-field:focus {
    outline: none;
    border: 1px dashed #2496d3;
    background-color: #e9f7fe;
  }

  /* Label styles */
  .label {
    font-weight: bold;
  }

  @media print {
    .editable-field {
      border: none;
      background-color: transparent;
    }
  }

  /* Print Controls */
  .print-controls {
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: #2496d3;
    color: white;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1000;
    cursor: move;
    user-select: none;
  }
  .print-controls .drag-handle {
    padding: 5px 0;
    margin-bottom: 10px;
    text-align: center;
    font-weight: bold;
    border-bottom: 1px solid rgba(255,255,255,0.3);
    cursor: move;
  }
  .print-controls button {
    background-color: white;
    color: #2496d3;
    border: none;
    padding: 8px 15px;
    margin: 5px;
    border-radius: 3px;
    cursor: pointer;
    font-weight: bold;
    display: block;
    width: 100%;
  }
  .print-controls button:hover {
    background-color: #f0f0f0;
  }

  /* Main table for page layout */
  body > table {
    width: 100%;
    border-collapse: collapse;
    border: none;
  }
  body > table > thead > tr > td,
  body > table > tbody > tr > td,
  body > table > tfoot > tr > td {
    padding: 0;
    border: none;
  }

  /* Content wrapper to handle page breaks properly */
  .content-wrapper {
    page-break-before: always;
  }
  .first-page {
    page-break-before: avoid;
  }

  .footer-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 40px;
    box-sizing: border-box;
    padding-top: 50px;
  }

  .footer-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .left-text {
    font-size: 18px;
    font-weight: bold;
  }

  .right-text {
    font-size: 18px;
    font-weight: bold;
    padding: 5px 10px;
  }

  .bottom-text {
    border-top: 1px solid #000;
    padding-top: 20px;
    font-size: 14px;
    text-align: center;
    font-style: italic;
    margin-top: 30px;
  }

  /* Header Table */
  .header-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px auto;
    margin-bottom: 40px;
    page-break-inside: avoid;
    page-break-after: avoid;
  }

  /* Data tables */
  .data-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px auto;
  }

  th, td {
    border: 1px solid black;
    padding: 8px;
    text-align: left;
    vertical-align: top; /* Ensures list aligns properly */
  }

  .logo-cell {
    vertical-align: middle;
    text-align: center;
  }

  .second-column {
    background-color: #e0e0e0;
    text-align: center;
  }

  .third-column {
    text-align: right;
  }

  .date-header {
    text-align: center;
  }

  .sub-header {
    text-align: center;
  }
</style>
</head>
<body>
<table>
  <thead>
    <!-- Document Header that will repeat on each page -->
    <tr>
      <td colspan="100%">
        <table class="header-table">
          <tr>
            <td rowspan="4" class="logo-cell">
              <div>
                <img src="${window.location.origin}/kls.png" alt="Logo Left" class="logo-left" style="width: 120px; height: auto;">
              </div>
            </td>
            <td rowspan="2" class="second-column"><div contenteditable="true" class="editable-field">FORMULAIRE D'ENREGISTREMENT</div></td>
            <td class="third-column">CODE: <span contenteditable="true" class="editable-field">FE/03-PRT/08</span></td>
          </tr>
          <tr>
            <td class="third-column">VERSION : <span contenteditable="true" class="editable-field">01</span></td>
          </tr>
          <tr>
            <td rowspan="2" class="second-column"><div contenteditable="true" class="editable-field">Fiche de transmission des échantillons au laboratoire</div></td>
            <td class="third-column">DATE : <span contenteditable="true" class="editable-field">${this.dateTransmissionGlobal || '23/02/2021'}</span></td>
          </tr>
          <tr>
            <td class="third-column">PAGE : <span contenteditable="true" class="editable-field page-number" data-total-pages="${totalPages}"><span>1 / ${totalPages}</span></span></td>
          </tr>
        </table>
      </td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td colspan="100%">
        <div class="content-wrapper first-page">
          <!-- Fiche de transmission -->
          <table class="data-table">
            <tr>
              <th rowspan="2">Code échantillon (laboratoire)</th>
              <th rowspan="2">Nature/ Masse de l'échantillon</th>
              <th colspan="2" class="date-header">Date</th>
              <th rowspan="2">Analyses demandées*</th>
              <th rowspan="2">Observations</th>
            </tr>
            <tr>
              <th class="sub-header">Transmission</th>
              <th class="sub-header">Remise des résultats</th>
            </tr>
            ${tableRows}
          </table>

          <div class="footer-section">
            <div class="left-text"><div contenteditable="true" class="editable-field">Réceptionnaire</div></div>
            <div class="right-text"><div contenteditable="true" class="editable-field">Responsables des analyses</div></div>
          </div>

          <div class="footer-container">
            <div class="bottom-text">
              <div contenteditable="true" class="editable-field">Ce document est la propriété du Laboratoire B³Aqua-Toute, utilisation, reproduction modification est soumise à un accord du propriétaire</div>
            </div>
          </div>
        </div>
      </td>
    </tr>
  </tbody>
  <tfoot>
    <tr>
      <td colspan="100%">
        <!-- Footer content if needed -->
      </td>
    </tr>
  </tfoot>
</table>

<!-- Print Controls (Draggable) -->
<div class="print-controls" id="draggable-print-controls">
  <div class="drag-handle">⋮⋮ Déplacer</div>
  <button id="print-button">Imprimer</button>
  <button id="cancel-button">Annuler</button>
</div>

<script>
  // Make print controls draggable
  (function() {
    const dragElement = document.getElementById('draggable-print-controls');
    if (!dragElement) return;

    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    const dragHandle = document.querySelector('.drag-handle');

    if (dragHandle) {
      // If drag handle exists, attach mousedown to it
      dragHandle.onmousedown = dragMouseDown;
    } else {
      // Otherwise, attach mousedown to the whole element
      dragElement.onmousedown = dragMouseDown;
    }

    function dragMouseDown(e) {
      e.preventDefault();
      // Get the mouse cursor position at startup
      pos3 = e.clientX;
      pos4 = e.clientY;
      document.onmouseup = closeDragElement;
      // Call a function whenever the cursor moves
      document.onmousemove = elementDrag;
    }

    function elementDrag(e) {
      e.preventDefault();
      // Calculate the new cursor position
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;
      // Set the element's new position
      dragElement.style.top = (dragElement.offsetTop - pos2) + 'px';
      dragElement.style.left = (dragElement.offsetLeft - pos1) + 'px';
      dragElement.style.right = 'auto';
      dragElement.style.bottom = 'auto';
    }

    function closeDragElement() {
      // Stop moving when mouse button is released
      document.onmouseup = null;
      document.onmousemove = null;
    }
  })();

  // Function to update page numbers - only needed for screen preview
  function updatePageNumbers() {
    const pageNumberElement = document.querySelector('.page-number');
    if (pageNumberElement) {
      const totalPages = pageNumberElement.getAttribute('data-total-pages') || '1';
      const pageNumberSpan = pageNumberElement.querySelector('span');
      if (pageNumberSpan) {
        pageNumberSpan.textContent = '1 / ' + totalPages;
      }
    }
  }

  // Add event listeners to buttons
  document.getElementById('print-button').addEventListener('click', function() {
    // Hide controls before printing
    document.querySelector('.print-controls').style.display = 'none';
    // Update page numbers before printing
    updatePageNumbers();
    // Print the document
    setTimeout(function() {
      window.print();
      // Show controls again after printing
      document.querySelector('.print-controls').style.display = 'block';
    }, 100);
  });

  document.getElementById('cancel-button').addEventListener('click', function() {
    window.close();
  });
</script>

</body>
</html>
    `;

    // Open a new window for editing and printing
    this.editablePrintWindow = window.open('', '_blank', 'width=800,height=600');
    if (this.editablePrintWindow) {
      this.editablePrintWindow.document.write(printHtml);
      this.editablePrintWindow.document.close();
      this.isEditingPdf = true;
    } else {
      console.error('Failed to open print window. Please allow pop-ups.');
    }
  }

  loadFiches(ficheTransmissionId: number) {
    this.ficheService.getFichesByTransmissionId(ficheTransmissionId).subscribe({
      next: (response) => {
        console.log('Fiches response:', response);

        // Handle new response structure with fiches and notes
        if (response && response.fiches) {
          this.fiches = response.fiches;
        } else {
          // Fallback for old response structure (just array of fiches)
          this.fiches = Array.isArray(response) ? response : [];
        }

        this.loading = false;

        // Fetch client name if data is available
        if (this.fiches && this.fiches.length > 0 && this.fiches[0].demande?.user_id) {
          this.fetchClientName(this.fiches[0].demande.user_id);
        }

        // Set date if available
        if (this.fiches && this.fiches.length > 0 && this.fiches[0].date_transmission) {
          this.dateTransmissionGlobal = this.fiches[0].date_transmission;
        }
      },
      error: (error) => {
        this.errorMessage = 'Échec du chargement des fiches';
        console.error('❌ Error fetching fiches:', error);
        this.loading = false;
      },
    });
  }
}
