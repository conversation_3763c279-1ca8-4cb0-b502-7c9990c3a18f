/* Modern Button System - 2024
   A comprehensive button styling system using CSS variables
   for consistent, accessible, and modern UI design
*/

:root {
  /* Primary Color Palette */
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-200: #90caf9;
  --primary-300: #64b5f6;
  --primary-400: #42a5f5;
  --primary-500: #2196f3;  /* Main primary color */
  --primary-600: #1e88e5;
  --primary-700: #1976d2;
  --primary-800: #1565c0;
  --primary-900: #0d47a1;

  /* Success Color Palette */
  --success-50: #e8f5e9;
  --success-100: #c8e6c9;
  --success-200: #a5d6a7;
  --success-300: #81c784;
  --success-400: #66bb6a;
  --success-500: #4caf50;  /* Main success color */
  --success-600: #43a047;
  --success-700: #388e3c;
  --success-800: #2e7d32;
  --success-900: #1b5e20;

  /* Warning Color Palette */
  --warning-50: #fff8e1;
  --warning-100: #ffecb3;
  --warning-200: #ffe082;
  --warning-300: #ffd54f;
  --warning-400: #ffca28;
  --warning-500: #ffc107;  /* Main warning color */
  --warning-600: #ffb300;
  --warning-700: #ffa000;
  --warning-800: #ff8f00;
  --warning-900: #ff6f00;

  /* Danger Color Palette */
  --danger-50: #ffebee;
  --danger-100: #ffcdd2;
  --danger-200: #ef9a9a;
  --danger-300: #e57373;
  --danger-400: #ef5350;
  --danger-500: #f44336;  /* Main danger color */
  --danger-600: #e53935;
  --danger-700: #d32f2f;
  --danger-800: #c62828;
  --danger-900: #b71c1c;

  /* Neutral Color Palette */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* Button Sizing */
  --btn-padding-y: 0.625rem;      /* 10px */
  --btn-padding-x: 1.25rem;       /* 20px */
  --btn-font-size: 0.875rem;      /* 14px */
  --btn-line-height: 1.5;
  --btn-font-weight: 500;
  --btn-border-radius: 0.375rem;  /* 6px */
  --btn-transition: all 0.2s ease-in-out;

  /* Button Shadow */
  --btn-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --btn-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.12);
  --btn-shadow-active: 0 1px 2px rgba(0, 0, 0, 0.15);

  /* Button Icon */
  --btn-icon-spacing: 0.5rem;     /* 8px */
  --btn-icon-size: 1rem;          /* 16px */
}

/* Base Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--btn-icon-spacing);
  padding: var(--btn-padding-y) var(--btn-padding-x);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  line-height: var(--btn-line-height);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: var(--btn-border-radius);
  transition: var(--btn-transition);
  box-shadow: var(--btn-shadow);
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--btn-shadow-hover);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--btn-shadow-active);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.65;
  pointer-events: none;
  box-shadow: none;
}

.btn .icon {
  font-size: var(--btn-icon-size);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Button Variants */

/* Primary Button */
.btn-primary {
  background-color: var(--primary-500);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-600);
}

.btn-primary:active {
  background-color: var(--primary-700);
}

/* Success Button */
.btn-success {
  background-color: var(--success-500);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-600);
}

.btn-success:active {
  background-color: var(--success-700);
}

/* Warning Button */
.btn-warning {
  background-color: var(--warning-500);
  color: var(--neutral-900);
}

.btn-warning:hover {
  background-color: var(--warning-600);
}

.btn-warning:active {
  background-color: var(--warning-700);
}

/* Danger Button */
.btn-danger {
  background-color: var(--danger-500);
  color: white;
}

.btn-danger:hover {
  background-color: var(--danger-600);
}

.btn-danger:active {
  background-color: var(--danger-700);
}

/* Secondary Button */
.btn-secondary {
  background-color: var(--neutral-200);
  color: var(--neutral-800);
}

.btn-secondary:hover {
  background-color: var(--neutral-300);
}

.btn-secondary:active {
  background-color: var(--neutral-400);
}

/* Outline Button Variants */
.btn-outline {
  background-color: transparent;
  border: 1px solid currentColor;
}

.btn-outline-primary {
  color: var(--primary-500);
}

.btn-outline-primary:hover {
  background-color: var(--primary-50);
}

.btn-outline-success {
  color: var(--success-500);
}

.btn-outline-success:hover {
  background-color: var(--success-50);
}

.btn-outline-warning {
  color: var(--warning-700);
}

.btn-outline-warning:hover {
  background-color: var(--warning-50);
}

.btn-outline-danger {
  color: var(--danger-500);
}

.btn-outline-danger:hover {
  background-color: var(--danger-50);
}

.btn-outline-secondary {
  color: var(--neutral-600);
}

.btn-outline-secondary:hover {
  background-color: var(--neutral-100);
}

/* Ghost Button Variants */
.btn-ghost {
  background-color: transparent;
  box-shadow: none;
}

.btn-ghost-primary {
  color: var(--primary-500);
}

.btn-ghost-primary:hover {
  background-color: var(--primary-50);
}

.btn-ghost-success {
  color: var(--success-500);
}

.btn-ghost-success:hover {
  background-color: var(--success-50);
}

.btn-ghost-warning {
  color: var(--warning-700);
}

.btn-ghost-warning:hover {
  background-color: var(--warning-50);
}

.btn-ghost-danger {
  color: var(--danger-500);
}

.btn-ghost-danger:hover {
  background-color: var(--danger-50);
}

.btn-ghost-secondary {
  color: var(--neutral-600);
}

.btn-ghost-secondary:hover {
  background-color: var(--neutral-100);
}

/* Button Sizes */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Button with loading state */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 1rem;
  height: 1rem;
  top: calc(50% - 0.5rem);
  left: calc(50% - 0.5rem);
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-top-color: white;
  border-radius: 50%;
  animation: button-loading-spinner 0.6s linear infinite;
}

@keyframes button-loading-spinner {
  from {
    transform: rotate(0turn);
  }
  to {
    transform: rotate(1turn);
  }
}

/* Glass effect buttons (Glassmorphism) */
.btn-glass {
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-glass:hover {
  background-color: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Soft UI effect (Neumorphism) - use on light backgrounds */
.btn-soft {
  background-color: var(--neutral-100);
  box-shadow: 
    5px 5px 10px rgba(0, 0, 0, 0.1),
    -5px -5px 10px rgba(255, 255, 255, 0.8);
  border: none;
  color: var(--neutral-700);
}

.btn-soft:hover {
  box-shadow: 
    3px 3px 6px rgba(0, 0, 0, 0.1),
    -3px -3px 6px rgba(255, 255, 255, 0.8);
}

.btn-soft:active {
  box-shadow: 
    inset 2px 2px 5px rgba(0, 0, 0, 0.1),
    inset -2px -2px 5px rgba(255, 255, 255, 0.8);
}
