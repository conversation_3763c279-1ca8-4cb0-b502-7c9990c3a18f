<div class="fiches-container">
  <div class="fiches-header">
    <h2 class="fiches-title"> <fa-icon [icon]="faFileAlt"></fa-icon> Fiches de Transmission sans résultats </h2>
    <button class="back-btn" (click)="goToDashboard()"><fa-icon [icon]="faArrowLeft"></fa-icon>  Retour au Tableau de Bord</button>
  </div>

  <!-- Filters Section -->
  <div class="filter-bar">
    <div class="filter-inputs-group">
      <div class="filter-group">
        <label for="combinedSearch">
          Rechercher par numéro ou client :
        </label>
        <div class="input-with-icon">
          <fa-icon class="input-icon" [icon]="faSearch"></fa-icon>
          <input
            type="text"
            id="combinedSearch"
            class="filter-input"
            [(ngModel)]="combinedSearch"
            placeholder="Rechercher par numéro ou nom..."
          >
        </div>
      </div>

      <div class="filter-group">
        <label for="transmissionDate">
          Date:
        </label>
        <input
          type="date"
          id="transmissionDate"
          class="filter-input"
          [(ngModel)]="searchDate"
        >
      </div>
    </div>

    <div class="filter-buttons">
      <button class="btn-filter-apply" (click)="onFilterChange()">
        <fa-icon [icon]="faFilter"></fa-icon> Appliquer les filtres
      </button>

      <button class="btn-clear" (click)="resetFilters()">
        <fa-icon [icon]="faEraser"></fa-icon> Effacer les filtres
      </button>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading-message">
    <i class="fas fa-spinner fa-spin"></i> Chargement des fiches...
  </div>

  <!-- Error Message -->
  <div *ngIf="errorMessage" class="error-message">
    <i class="fas fa-exclamation-triangle"></i> {{ errorMessage }}
  </div>

  <!-- Fiches Table -->
  <table *ngIf="!loading && !errorMessage && filteredFiches.length > 0">
    <thead>
      <tr>
        <th>ID Fiche</th>
        <th>Demande Numéro</th>
        <th>Nom Client</th>
       <th>Date Réception</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let fiche of filteredFiches">
        <td>{{ fiche.id }}</td>
        <td>{{ fiche.demande?.demande_id || 'N/A' }}</td>
        <td>{{ fetchUserName(fiche.demande?.user_id) }}</td>

        <td>{{ fiche.date_transmission || 'Pas encore envoyé' }}</td>
        <td>
          <button class="details-btn" (click)="goToFicheTransmission(fiche.id)">
            <fa-icon [icon]="faEye"></fa-icon> Voir détails
          </button>
        </td>
      </tr>
    </tbody>
  </table>

  <!-- No Data Message -->
  <div *ngIf="!loading && !errorMessage && filteredFiches.length === 0" class="no-data-message">
    <i class="fas fa-info-circle"></i> Aucune fiche de transmission sans résultats trouvée.
  </div>
</div>
