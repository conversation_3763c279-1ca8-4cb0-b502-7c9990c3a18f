<!-- registre-details.component.html -->
<div class="registre-container">
  <h2>Détails du Registre de Suivi #{{registre.id}}</h2>

  <!-- Loading & Error messages -->

  <div *ngIf="error" class="error">{{ error }}</div>

  <!-- If 'registre' exists, show content -->
  <div *ngIf="registre">
    <h3 class="sub-header">Informations générales</h3>
    <table class="details-table">

      <tr>
        <td>Numéro de demande:</td>
        <td>{{ registre.demande_id || '-' }}</td>
      </tr>
      <tr>
        <td>Nom du Client:</td>
        <td>{{ registre.nom_client || '-' }}</td>
      </tr>
      <tr>
        <td>Date de Réception:</td>
        <td>{{ registre.date_reception || '-' }}</td>
      </tr>
    </table>

    <h3 class="sub-header">Registres associés</h3>
    <form [formGroup]="registreForm">

      <table class="registre-table">
        <thead>
          <tr>
            <th>Code Client</th>
            <th>Code échantillon(labo)</th>
            <th>Quantité</th>
            <th>Nature Échantillon</th>
            <th>Échantillon Envré Par</th>
            <th>Échantillon Reçu Par</th>
            <th>Lieu de Conservation</th>
            <th>Numéro Demande</th>
            <th>Paramètres Analyser</th>
            <th>Date Prévue Analyse</th>
            <th>Date Effective Analyse</th>
            <th>Date d'Émission Rapport</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let reg of registre.registres">
            <td>{{ reg.code_client || '-' }}</td>
            <td>{{ reg.code_labo || '-' }}</td>
            <td>{{ reg.quantite || '-' }}</td>
            <td>{{ reg.nature_echantillon || '-' }}</td>

            <!-- Editable Fields -->
            <td>
              <input
                [formControlName]="'echantillon_envoye_par_' + reg.id"
                [readonly]="!isEditing[reg.id]"
                placeholder="-"
              />
            </td>
            <td>
              <input
                [formControlName]="'echantillon_recu_par_' + reg.id"
                [readonly]="!isEditing[reg.id]"
                placeholder="-"
              />
            </td>
             <!-- Lieu de Conservation changed to a dropdown menu -->
             <td>
                <select [formControlName]="'lieu_conservation_' + reg.id">
                  <option value="">- Sélectionner -</option>
                  <option value="Refrigerateur">Réfrigérateur</option>
                  <option value="TempAmbiante">Température ambiante</option>
                  <option value="Congelateur">Congélateur</option>
                  <option value="Autre">Autre</option>
                </select>
              </td>
            <td>{{ reg.numero_demande || '-' }}</td>
            <td>{{ reg.parametres_a_analyser?.join(', ') || '-' }}</td>
            <td>{{ reg.date_prevue_analyse || '-' }}</td>
            <td>
              <input
                [formControlName]="'date_effective_analyse_' + reg.id"
                type="date"
                [readonly]="!isEditing[reg.id]"
              />
            </td>
            <td>
              <input
                [formControlName]="'date_emission_rapport_' + reg.id"
                type="date"
                [readonly]="!isEditing[reg.id]"
              />
            </td>

            <td class="actions-cell">
              <button
                *ngIf="!isEditing[reg.id]"
                type="button"
                (click)="toggleEdit(reg.id)"
              >
                <fa-icon [icon]="faPencilAlt" style="margin-right: 8px;"></fa-icon>
                Editer
              </button>
              <button
                *ngIf="isEditing[reg.id]"
                type="button"
                (click)="updateRegistre(reg)"
              >
                <fa-icon [icon]="faCheckCircle" style="margin-right: 8px;"></fa-icon>
                Mettre à Jour
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </form>
  </div>


</div>

