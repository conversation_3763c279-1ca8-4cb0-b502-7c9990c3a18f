// src/app/models/derogation.ts
export interface Derogation {
    id: number;
    demande_id: number;
    identification_echantillon: string;
    nature_echantillon: string;
    reference: string;
    provenance: string;
    demandeur: string;
    date_demande_client: string;
    masse_echantillon: number;
    analyses_demandees: string;
    description_ecart: string;
    reponse_du_client: string;
    decision: string | null;
    date_et_visa_du_DL: string | null;
    date_et_visa_du_demander: string | null;
    created_at: string;
    updated_at: string;
  }
  
  export interface DerogationResponse {
    message: string;
    demande_id: string;
    derogations: Derogation[];
  }