import { Component, OnInit, ElementRef, HostListener, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { NotificationService } from '../notification/notification.service';
import { Notification } from '../../models/notification.model';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'notifications',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule],
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.css']
})
export class NotificationsComponent implements OnInit {
  notifications: Notification[] = [];
  showNotifications = false;
  page: number = 1;
  itemsPerPage: number = 5;
  user: [] | null = null;
  role: string | null = null;
  id: string | null = null;

  constructor(
    private notificationService: NotificationService,
    private router: Router,
    private elementRef: ElementRef,private cdr: ChangeDetectorRef// Added for click outside detection
  ) {}

  ngOnInit(): void {
    this.role = this.getUserProperty('role');
    this.id = this.getUserProperty('id');

    if (this.role === 'receptionist') {
      this.fetchAllNotifications();
    } else if (this.role === 'client') {
      this.fetchClientNotifications();
    } else if (this.role === 'director') {
      this.fetchDirectorNotifications();
    }else if (this.role === 'responsable') {
      this.getFicheNotifications();
    }
  }

  // Listen to clicks on the document
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const clickedInside = this.elementRef.nativeElement.contains(event.target);
    if (!clickedInside && this.showNotifications) {
      this.showNotifications = false;
    }
  }

  toggleNotifications(event: Event): void {
    event.stopPropagation(); // Prevent the click from bubbling up to document
    this.showNotifications = !this.showNotifications;
  }
  fetchClientNotifications(): void {
    forkJoin({
      validated: this.notificationService.getValiatedDemandesNotifications(),
      bringSample: this.notificationService.getBringSampleNotifications(),
      rejected: this.notificationService.getRejectedNotification(),
      validatedClient: this.notificationService.getValidatedNotificationClient(),
      paymentApproved: this.notificationService.getClientPaymentApprovedNotifications(),
      paymentRejected: this.notificationService.getClientPaymentRejectedNotifications(),
      rapportClient: this.notificationService.getClientRapportNotifications()
    }).subscribe({
      next: ({ validated, bringSample, rejected, validatedClient, paymentApproved, paymentRejected, rapportClient }) => {
        this.notifications = [
          ...validated.notifications,
          ...bringSample.notifications,
          ...rejected.notifications,
          ...validatedClient.notifications,
          ...(paymentApproved?.notifications || []),
          ...(paymentRejected?.notifications || []),
          ...(rapportClient?.notifications || [])
        ];
        this.sortNotifications();
        console.log('Client notifications loaded:', this.notifications);
      },
      error: (error) => console.error('Error fetching notifications:', error)
    });
  }


  fetchAllNotifications(): void {
    forkJoin({
      newDemandes: this.notificationService.getNewDemandesNotifications(),
      rejectedReceptionist: this.notificationService.getRejectedNotificationReceptionist(),
      validatedReceptionist: this.notificationService.getValidatedNotificationReceptionist(),
      rapportReceptionist:this.notificationService.getRapportsNotifications(),
      checkRapports:this.notificationService.getCheckedRapportsNotifications(),
      devisNotifications: this.notificationService.getDevisNotifications(),
      resultsNotifications: this.notificationService.getResultsNotifications(),
    }).subscribe({
      next: (response) => {
        console.log('✅ API responses:', response);

        // Convert single objects to arrays if necessary
        const newDemandesData = response.newDemandes?.notifications
          ? (Array.isArray(response.newDemandes.notifications)
              ? response.newDemandes.notifications
              : [response.newDemandes.notifications])
          : [];
        const rapportReceptionist = response.rapportReceptionist?.notifications
          ? (Array.isArray(response.rapportReceptionist.notifications)
              ? response.rapportReceptionist.notifications
              : [response.rapportReceptionist.notifications])
          : [];

        const checkRapports = response.checkRapports?.notifications
          ? (Array.isArray(response.checkRapports.notifications)
              ? response.checkRapports.notifications
              : [response.checkRapports.notifications])
          : [];
        const rejectedData = response.rejectedReceptionist?.notifications
          ? (Array.isArray(response.rejectedReceptionist.notifications)
              ? response.rejectedReceptionist.notifications
              : [response.rejectedReceptionist.notifications])
          : [];

        const validatedData = response.validatedReceptionist?.notifications
          ? (Array.isArray(response.validatedReceptionist.notifications)
              ? response.validatedReceptionist.notifications
              : [response.validatedReceptionist.notifications])
          : [];

        const devisData = response.devisNotifications?.notifications
          ? (Array.isArray(response.devisNotifications.notifications)
              ? response.devisNotifications.notifications
              : [response.devisNotifications.notifications])
          : [];


        const resultsData = response.resultsNotifications?.notifications
          ? (Array.isArray(response.resultsNotifications.notifications)
              ? response.resultsNotifications.notifications
              : [response.resultsNotifications.notifications])
          : [];

        this.notifications = [
          ...newDemandesData,
          ...rejectedData,
          ...validatedData,
          ...rapportReceptionist,
          ...checkRapports,
          ...devisData,

          ...resultsData
        ];

        // Sort notifications before displaying
        this.sortNotifications();

        console.log('📢 Sorted notifications:', this.notifications);
        this.cdr.detectChanges();
      },
      error: (error) => console.error('❌ Error fetching notifications:', error)
    });
  }


  private sortNotifications(): void {
    this.notifications.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime() // Sort by most recent first
    );
  }


  fetchDirectorNotifications(): void {
    // ✅ Use forkJoin to fetch both API calls together
    forkJoin({
      derogated: this.notificationService.getDerogatedNotifications(),
      rapport: this.notificationService.getRapportNotificationsDirector()
    }).subscribe({
      next: (response) => {
        // ✅ Merge notifications from both responses
        this.notifications = [
          ...(response.derogated.notifications || []),
          ...(response.rapport.notifications || [])
        ];

        // ✅ Sort notifications if needed
        this.sortNotifications();

        console.log('All Notifications:', this.notifications);
      },
      error: (error) => console.error('Error fetching notifications:', error)
    });
  }
  getDerogatedNotifications(): void {
    this.notificationService.getDerogatedNotifications().subscribe({
      next: (response) => {
        this.notifications = response.notifications;
        this.sortNotifications();
      },
      error: (error) => console.error('Error fetching notifications:', error)
    });
  }
  getFicheNotifications(): void {
    this.notificationService.getFicheTransmissionNotifications().subscribe({
      next: (response) => {
        this.notifications = response.notifications;
        this.sortNotifications();
      },
      error: (error) => console.error('Error fetching notifications:', error)
    });
  }



  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null;
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }
    return null;
  }

  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe({
        next: () => {
          notification.is_read = true;
          this.navigateToDetails(notification);
        },
        error: (error) => console.error('Error marking notification as read:', error)
      });
    } else {
      this.navigateToDetails(notification);
    }
  }

  navigateToDetails(notification: Notification): void {
    if (this.role === 'receptionist' ) {
      this.navigateToDetailReceptionist(notification);
    } else if (this.role === 'client') {
      this.navigateToDetailClient(notification);
    } else if(this.role ==='director' ) {
     this.navigateToDetailDirector(notification);
    } else if (this.role === 'responsable' && notification.fiche_id){
      console.log('Navigating to demande with ID:', notification.fiche_id);
      this.router.navigate(['/fiche-transmission', notification.fiche_id]);
    }
      else {
      console.error('Navigation not allowed: Missing demande_id or invalid role.');
    }
  }

  navigateToDetailClient(notification: Notification): void {
    console.log('Navigating client notification:', notification);

    // Handle payment notifications
    if (notification.title === 'Paiement Approuvé' && notification.demande) {
      console.log('Navigating to approved payment for demande:', notification.demande);
      this.router.navigate(['/paiement', notification.demande]);
    } else if (notification.title === 'Paiement Rejeté' && notification.demande) {
      console.log('Navigating to rejected payment for demande:', notification.demande);
      this.router.navigate(['/paiement', notification.demande]);
    } else if (notification.title === 'Rapport Prêt' && notification.demande) {
      console.log('Navigating to rapport results:', notification.demande);
      this.router.navigate(['/resultats', notification.demande]);
    } else if (notification.demande) {
      // Default navigation for other notifications
      this.router.navigate(['/demandeClient', notification.demande]);
    } else {
      console.error('Invalid notification or missing demande ID:', notification);
    }
  }
  navigateToDetailReceptionist(notification: Notification): void {
    console.log('Navigating receptionist notification:', notification);

    if (notification.type === 'demande' && notification.demande) {
      console.log('Navigating to demande:', notification.demande);
      this.router.navigate(['/demande', notification.demande]);
    } else if ((notification.type === 'rapport receptionist' || notification.type === 'rapport validation') && notification.rapport_id) {
      console.log('Navigating to rapport:', notification.rapport_id);
      this.router.navigate(['/receptionist/rapports', notification.rapport_id]);
    } else if (notification.type === 'demande validée receptionniste' && notification.demande){
      this.router.navigate(['/demande', notification.demande]);
    } else if (notification.type === 'devis' && notification.demande) {
      console.log('Navigating to devis for demande:', notification.demande);
      this.router.navigate(['/demande', notification.demande]);
    } else if (notification.title === 'Paiement Approuvé' && notification.demande) {
      console.log('Navigating to approved payment for demande:', notification.demande);
      this.router.navigate(['/demande', notification.demande]);
    } else if (notification.title === 'Paiement Rejeté' && notification.demande) {
      console.log('Navigating to rejected payment for demande:', notification.demande);
      this.router.navigate(['/demande', notification.demande]);
    } else if (notification.type === 'results' && notification.rapport_id) {
      console.log('Navigating to results for rapport:', notification.rapport_id);
      this.router.navigate(['/receptionist/rapports/', notification.rapport_id]);
    } else {
      console.error('Invalid notification or missing ID:', notification);
    }
  }
  navigateToDetailDirector(notification: Notification): void {
    console.log('Received notification:', JSON.stringify(notification));

    if (notification.title === 'Demande de validation avec derogation' && notification.demande_id) {
      console.log('Navigating to demande:', notification.demande);
      this.router.navigate(['/derogation', notification.demande]);
    } else if (notification.type === 'rapport' && notification.rapport_id) {
      console.log('Navigating to rapport:', notification.rapport_id);
      this.router.navigate(['/director/rapportsDetails/', notification.rapport_id]);
    } else {
      console.error('Invalid notification or missing ID:', notification);
    }
  }
  getUnreadCount(): number {
    return this.notifications.filter(n => !n.is_read).length;
  }

  navigateToNotifications(): void {
    if (this.role === 'receptionist') {
      this.router.navigate(['/notifications']);
    } else if (this.role === 'client') {
      this.router.navigate(['client/BringSample/notifications/']);
    } else if (this.role === 'director') {
      this.router.navigate(['director/Derogated/notifications/']);
    } else if(this.role === 'responsable'){
      this.router.navigate(['responsable/notifications/']);
    }
  else{
      console.log('Invalid role');
    }
  }
}