<!-- Success Modal Overlay -->
<div *ngIf="isVisible" class="modal-overlay" (click)="onBackdropClick($event)">
  <div class="modal-container" role="dialog" aria-modal="true" [attr.aria-labelledby]="'modal-title'" [attr.aria-describedby]="'modal-description'">
    
    <!-- Success Icon -->
    <div class="success-icon-container">
      <div class="success-icon">
        <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" fill="#4CAF50"/>
          <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="modal-content">
      <h2 id="modal-title" class="modal-title">{{ title }}</h2>
      <p id="modal-description" class="modal-message">{{ message }}</p>
      
      <div class="modal-actions">
        <button 
          type="button" 
          class="btn-primary" 
          (click)="confirmAction()"
          aria-label="Aller à la page de connexion">
          {{ buttonText }}
        </button>
        
        <button 
          type="button" 
          class="btn-secondary" 
          (click)="closeModal()"
          aria-label="Fermer la modal">
          Fermer
        </button>
      </div>
    </div>
  </div>
</div>
