@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.container {
  margin: auto;
  text-align: center;
  font-family: 'Poppins', sans-serif;
  background: white;
  padding: 70px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  display: block;
  justify-content: center;
  max-width: 1400px; /* Increased container width */
  width: 95%; /* Ensures it's responsive */
}

/* ✅ Titre */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

h3 {
  color: #333;
  margin: 20px 0;
  font-weight: 600;
  font-size: 18px;
}

.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: center; /* Vertically centers all items */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 80%; /* Increased width to match the container */
  margin: 0 auto; /* Centers the filter bar within its parent */
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}

.filter-group label {
  font-weight: 600;
  font-size: 14px;
  color: #444;
}

.filter-group input,
.filter-group select {
  width: 250px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear, .btn-refresh {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-refresh {
  background-color: #2496d3;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.btn-refresh:hover {
  background-color: #1e78b5;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 10px;
  width: 100%;
}

/* Icon margin utility class */
.icon-margin-right {
  margin-right: 10px;
}

/* Pagination styles */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* Items per page selector */
.items-per-page {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 10px;
  gap: 10px;
  font-size: 14px;
  color: #555;
}

.items-per-page-select {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.notification, .success-message {
  background: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
  text-align: center;
  width:100%;
  font-weight: bold;
}

.error-notification, .error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
  text-align: center;
  width:100%;
  font-weight: bold;
}

/* Add Analysis Button */
.add-analyse {
  margin: 20px 0;
  text-align: right;
}

.add-analyse-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.add-analyse-btn:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

/* Styles pour l'indicateur de chargement et les messages vides */
.loading-row, .empty-row {
  height: 100px;
}

.loading-row td, .empty-row td {
  text-align: center;
  vertical-align: middle;
  font-size: 18px;
  color: #6c757d;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.text-center {
  text-align: center;
}

/* ✅ Animation Glow */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* Accreditation status */
.accredited, .not-accredited {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  text-transform: uppercase;
}

.accredited {
  background-color: #28a745;
  color: white;
}

.not-accredited {
  background-color: #6c757d;
  color: white;
}

/* Checkbox styling */
.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-weight: bold;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 10px;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

/* ✅ Table */
.analyses-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ En-têtes */
.analyses-table th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
  text-align: center;
}

/* ✅ Lignes */
.analyses-table td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
  vertical-align: middle;
}

/* Actions column */
.actions-col {
  text-align: center;
  min-width: 150px;
}

/* ✅ Effets interactifs */
.analyses-table tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

/* ✅ Boutons */
button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px;
  transition: all 0.3s ease-in-out;
}

/* Action Buttons */
.action-btns {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* Bouton Détails */
.btn-details, .edit-btn, .delete-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-transform: uppercase;
  outline: none;
  box-shadow: 0 5px 15px rgba(85, 85, 85, 0.3);
}

.edit-btn {
  background-color: #ffcc00;
  color: #333;
}

.edit-btn:hover {
  background-color: #e6b800;
  transform: scale(1.05);
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background-color: #c0392b;
  transform: scale(1.05);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
  display: block;
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 60%;
  max-width: 600px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
}

.close {
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input:focus, .form-group textarea:focus {
  outline: none;
  border-color: #2496d3;
  box-shadow: 0 0 5px rgba(36, 150, 211, 0.3);
}

.invalid {
  border-color: #dc3545 !important;
}

.error-text {
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
}

.required {
  color: #dc3545;
}

/* Modal Action Buttons */
.action-btns-modal {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.save-btn, .cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

.save-btn:hover {
  background-color: #218838;
}

.save-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background-color: #5a6268;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .modal-content {
    width: 90%;
    margin: 20% auto;
  }

  .analyses-table {
    font-size: 14px;
  }

  .analyses-table th, .analyses-table td {
    padding: 8px 10px;
  }

  .action-btns {
    flex-direction: column;
    gap: 5px;
  }
}
