<?php

namespace App\Http\Controllers;

use App\Models\Demande;
use App\Models\Result;
use App\Models\ResultatClient;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\ResultsNotificationEmail;

class ResultController extends Controller
{
    /**
     * Get all results
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $results = Result::with('demande')->get();

        return response()->json([
            'status' => 'success',
            'data' => $results
        ]);
    }

    /**
     * Store a new result
     *
     * This method now redirects to submitResults for consistency
     * It is kept for backward compatibility but may be deprecated in the future
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Log the usage of this deprecated method for future refactoring
        \Illuminate\Support\Facades\Log::info('ResultController::store method called - This method is redirecting to submitResults', [
            'demande_id' => $request->input('demande_id'),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // Get demande_id from request body
        $demandeId = $request->input('demande_id');

        // If demande_id is not provided, return an error
        if (!$demandeId) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande ID is required'
            ], 422);
        }

        // Forward to submitResults method for consistent handling
        return $this->submitResults($request, $demandeId);
    }

    /**
     * Get results for a specific demande
     *
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getResultsByDemandeId($demandeId)
    {
        try {
            // Try to find the demande by numeric ID first
            $demande = null;
            if (is_numeric($demandeId)) {
                $demande = Demande::find($demandeId);
            }

            // If not found, try to find by string identifier (demande_id)
            if (!$demande) {
                $demande = Demande::where('demande_id', $demandeId)->first();
            }

            if (!$demande) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Demande not found'
                ], 404);
            }

            // Get the result associated with this demande
            $result = ResultatClient::where('demande_id', $demande->demande_id)->first();
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $result
        ]);
    }
    public function getResultsAnalyse($demandeId)
    {
        try {
            // Try to find the demande by numeric ID first
            $demande = null;
            if (is_numeric($demandeId)) {
                $demande = Demande::find($demandeId);
            }

            // If not found, try to find by string identifier (demande_id)
            if (!$demande) {
                $demande = Demande::where('demande_id', $demandeId)->first();
            }

            if (!$demande) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Demande not found'
                ], 404);
            }

            // Get the result associated with this demande
            $result = Result::where('demande_id', $demande->id)->first();
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $result
        ]);
    }
    /**
     * Submit results for a specific demande
     *
     * @param Request $request
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitResults(Request $request, $demandeId)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'results_file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the demande by demande_id (string identifier)
        $demande = Demande::where('demande_id', $demandeId)->first();

        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Handle file upload
        if ($request->hasFile('results_file')) {
            $file = $request->file('results_file');
            $fileName = 'results_' . $demandeId . '_' . time() . '.' . $file->getClientOriginalExtension();

            // Store the file in the 'results' directory within the storage
            $filePath = $file->storeAs('results', $fileName, 'public');

            // Check if a result already exists for this demande
            $existingResult = Result::where('demande_id', $demande->id)->first();

            if ($existingResult) {
                // Delete the old file if it exists
                $oldFilePath = storage_path('app/public/' . $existingResult->results_file);
                if (file_exists($oldFilePath)) {
                    unlink($oldFilePath);
                }

                // Update the existing result
                $existingResult->results_file = $filePath;
                $existingResult->save();

                $result = $existingResult;
            } else {
                // Create a new result
                $result = new Result();
                $result->demande_id = $demande->id;
                $result->results_file = $filePath;
                $result->save();

                // Update the demande with the result_id
                $demande->result_id = $result->id;
                $demande->save();
            }

            // Create notification for results availability
            $notification = Notification::create([
                'title' => 'Résultats disponibles',
                'message' => 'Les résultats de la demande '.$demande->demande_id.' sont disponibles.',
                'demande_id' => $demande->id,
                'demande' => $demande->demande_id,
                'type' => 'results',
                'rapport_id' =>  $demande->rapport_created,
                'user_id' => $demande->user_id
            ]);

            // Get all analysts with role 'responsable'

$receptionnist=User::where('role', 'receptionist')->first();
            // Get client information for the email
            $client = User::find($demande->user_id);
                    Mail::to($receptionnist->email)->send(new ResultsNotificationEmail($notification, $demande, $client));



            return response()->json([
                'status' => 'success',
                'message' => 'Results uploaded successfully',
                'data' => $result
            ]);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'No file was uploaded'
        ], 400);
    }

    /**
     * Download results file
     *
     * @param Request $request
     * @param int $resultId
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function downloadResults(Request $request, $resultId)
    {
        // First try to find in Result model
        $result = Result::find($resultId);
        $fileField = 'results_file';

        // If not found in Result model, try ResultatClient model
        if (!$result) {
            $result = ResultatClient::find($resultId);
            $fileField = 'rapport_file'; // ResultatClient uses rapport_file instead of results_file
        }

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => "Result not found with ID: {$resultId}"
            ], 404);
        }

        // Get the appropriate file path based on which model we found
        $filePath = storage_path("app/public/{$result->$fileField}");

        if (!file_exists($filePath)) {
            return response()->json([
                'status' => 'error',
                'message' => "Results file not found at path: {$result->$fileField}",
                'debug_info' => [
                    'model_type' => $result instanceof Result ? 'Result' : 'ResultatClient',
                    'file_field' => $fileField,
                    'file_value' => $result->$fileField,
                    'full_path' => $filePath
                ]
            ], 404);
        }

        // For direct file download
        if ($request->has('format') && $request->format === 'json') {
            // Generate a URL to the file
            $url = asset("storage/{$result->$fileField}");

            return response()->json([
                'status' => 'success',
                'file_url' => $url,
                'file_path' => $result->$fileField
            ]);
        }

        // Default: serve the file directly
        return response()->file($filePath);
    }

    /**
     * Get results for the authenticated client
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientResults()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get all demandes for this user
        $demandes = Demande::where('user_id', $user->id)->pluck('id');

        // Get all results for these demandes
        $results = Result::whereIn('demande_id', $demandes)
            ->with('demande')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $results
        ]);
    }

    /**
     * Remove a result
     *
     * @param int $resultId
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($resultId)
    {
        // First try to find in Result model
        $result = Result::find($resultId);
        $fileField = 'results_file';
        $isResultModel = true;

        // If not found in Result model, try ResultatClient model
        if (!$result) {
            $result = ResultatClient::find($resultId);
            $fileField = 'rapport_file'; // ResultatClient uses rapport_file instead of results_file
            $isResultModel = false;
        }

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => "Result not found with ID: {$resultId}"
            ], 404);
        }

        // Handle differently based on model type
        if ($isResultModel) {
            // Get the associated demande to update its result_id
            $demande = Demande::where('result_id', $result->id)->first();

            // Delete the file from storage if it exists
            $filePath = storage_path("app/public/{$result->$fileField}");
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Update the demande to remove the result_id reference
            if ($demande) {
                $demande->result_id = null;
                $demande->save();
            }
        } else {
            // For ResultatClient model
            // Delete the file from storage if it exists
            if ($result->rapport_file) {
                $filePath = storage_path("app/public/{$result->rapport_file}");
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            // Also delete invoice file if it exists
            if ($result->facture_file) {
                $filePath = storage_path("app/public/{$result->facture_file}");
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        }

        // Delete the result record
        $result->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Result deleted successfully'
        ]);
    }


}
