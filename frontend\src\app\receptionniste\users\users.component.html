<div class="user-table-container">
  <div style="text-align: center;">
    <h2>Gestion des Utilisateurs</h2>
  </div>

  <!-- <PERSON><PERSON> de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="nameFilter">Rechercher par nom:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="nameFilter"
          placeholder="Rechercher..."
          [(ngModel)]="nameFilter"
          (input)="applyFilters()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="roleFilter">Rôle:</label>
      <select id="roleFilter" [(ngModel)]="roleFilter" (change)="applyFilters()" class="filter-select">
        <option value="">Tous les rôles</option>
        <option value="responsable">Analyste</option>
        <option value="director">Directeur</option>
        <option value="client">Client</option>
      </select>
    </div>
    <button class="btn-clear" (click)="clearFilters()">
      <fa-icon [icon]="faEraser"></fa-icon> Effacer les filtres
    </button>
  </div>

  <!-- Bouton Ajouter -->
  <button class="add-user-btn" (click)="openUserModal()">
    <fa-icon [icon]="faPlus"></fa-icon> Ajouter un Utilisateur
  </button>

  <!-- Indicateur de chargement -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des utilisateurs...</p>
  </div>

  <!-- Tableau des utilisateurs -->
  <table class="user-table" *ngIf="!isLoading && filteredUsers.length > 0">
    <thead>
      <tr>
        <th style="width: 18%;">Nom</th>
        <th style="width: 25%;">Email</th>
        <th style="width: 15%;">Rôle</th>
        <th style="width: 15%;">Date d'Ajout</th>
        <th style="width: 27%;">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of filteredUsers | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }">
        <td class="centered">{{ user.name }} {{ user.nickname }}</td>
        <td class="centered">{{ user.email }}</td>
        <td class="centered">{{ translateRole(user.role) }}</td>
        <td class="centered">{{ user.created_at ? (user.created_at | date:'dd/MM/yyyy') : 'N/A' }}</td>
        <td class="centered">
          <button (click)="openUserModal(user)" class="btn-edit" title="Modifier">
            <fa-icon [icon]="faEdit" style="margin-right: 5px;"></fa-icon>Modifier
          </button>
          <button (click)="deleteUser(user.id ?? 0)" class="btn-delete" title="Supprimer">
            <fa-icon [icon]="faTrash" style="margin-right: 5px;"></fa-icon>Supprimer
          </button>
        </td>
      </tr>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    *ngIf="filteredUsers.length > 0"
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>

  <!-- Message si aucun utilisateur n'est disponible -->
  <div class="no-users" *ngIf="!isLoading && filteredUsers.length === 0">
    Aucun utilisateur disponible.
  </div>

  <div class="modal" *ngIf="showUserModal">
    <div class="modal-content">
        <h3>{{ editMode ? 'Modifier l\'utilisateur' : 'Ajouter un utilisateur' }}</h3>

        <form [formGroup]="userForm" (ngSubmit)="submitUserForm()">
            <!-- Error Message -->
            <div *ngIf="errorMessage" class="error-message">
                {{ errorMessage }}
            </div>

            <!-- Name -->
            <label>Nom :
                <span
                    *ngIf="userForm.get('name')?.touched && userForm.get('name')?.errors"
                    class="error"
                >
                    <span *ngIf="userForm.get('name')?.errors?.['required']">
                        Le nom est requis.
                    </span>
                </span>
            </label>
            <input type="text" formControlName="name">

            <!-- Email (Readonly in edit mode) -->
            <label>Email :
                <span
                    *ngIf="userForm.get('email')?.touched && userForm.get('email')?.errors"
                    class="error"
                >
                    <span *ngIf="userForm.get('email')?.errors?.['required']">
                        L'email est requis.
                    </span>
                    <span *ngIf="userForm.get('email')?.errors?.['email']">
                        Format d'email invalide.
                    </span>
                    <span *ngIf="userForm.get('email')?.errors?.['emailTaken']">
                        Cet email est déjà utilisé.
                    </span>
                </span>
            </label>
            <input type="email" formControlName="email" [readonly]="editMode">

            <!-- Password (Only in add mode) -->
            <ng-container *ngIf="!editMode">
                <label>Mot de passe :
                    <span
                        *ngIf="userForm.get('password')?.touched && userForm.get('password')?.errors"
                        class="error"
                    >
                        <span *ngIf="userForm.get('password')?.errors?.['required']">
                            Le mot de passe est requis.
                        </span>
                        <span *ngIf="userForm.get('password')?.errors?.['minlength']">
                            Le mot de passe doit contenir au moins 8 caractères.
                        </span>
                    </span>
                </label>
                <input type="password" formControlName="password">

                <!-- Password Confirmation (Only in add mode) -->
                <label>Confirmer le mot de passe :
                    <span
                        *ngIf="userForm.get('password_confirmation')?.touched && userForm.get('password_confirmation')?.errors"
                        class="error"
                    >
                        <span *ngIf="userForm.get('password_confirmation')?.errors?.['required']">
                            La confirmation du mot de passe est requise.
                        </span>
                        <span *ngIf="userForm.get('password_confirmation')?.errors?.['passwordMismatch']">
                            Les mots de passe ne correspondent pas.
                        </span>
                    </span>
                </label>
                <input type="password" formControlName="password_confirmation">
            </ng-container>

            <!-- Role (Editable in both modes) -->
            <label>Rôle :
                <span
                    *ngIf="userForm.get('role')?.touched && userForm.get('role')?.errors"
                    class="error"
                >
                    <span *ngIf="userForm.get('role')?.errors?.['required']">
                        Choisir un rôle.
                    </span>
                </span>
            </label>
            <select formControlName="role">
                <option value="admin">Administrateur</option>
                <option value="receptionist">Réceptionniste</option>
                <option value="responsable">Analyste</option>
                <option value="director">Directeur</option>
                <option value="client">Client</option>
            </select>

            <!-- Buttons -->
            <div class="modal-actions">
                <button type="button" (click)="closeUserModal()" class="cancel-btn">
                  <fa-icon [icon]="faTimes"></fa-icon> Annuler
                </button>
                <button type="submit" class="save-btn">
                  <fa-icon [icon]="faSave"></fa-icon> Sauvegarder
                </button>
            </div>
        </form>
    </div>
</div>
</div>
