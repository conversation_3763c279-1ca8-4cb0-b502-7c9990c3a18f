import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute,Router } from '@angular/router';
import { DemandeService } from './demande.service';
import { Demande } from './demande.model';
import { CommonModule } from '@angular/common';
import { DerogationResponse } from './derogation.model';
import { DerogationService } from './derogation.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-demande-details',
  standalone: true,
  templateUrl: './derogation.component.html',
  imports:[CommonModule, FontAwesomeModule],
  styleUrls: ['./derogation.component.css']
})
export class DerogationComponent implements OnInit {
  // Font Awesome icons
  faCheck = faCheck;
  faTimes = faTimes;

  demande: Demande | null = null;
  demandeid:string='';
  isLoading = true;
  errorMessage: string | null = null;
  derogationDetails: DerogationResponse | null = null;

  // Loading and notification properties
  showLoadingModal = false;
  loadingMessage = 'Traitement en cours...';
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';
  constructor(
    private route: ActivatedRoute,
    private demandeService: DemandeService,
    private router: Router,
    private derogationService: DerogationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.fetchDemandeDetails();
  }

  fetchDemandeDetails(): void {
    const demandeId = this.route.snapshot.paramMap.get('demande_id');
    console.log('demande_id:', demandeId);
    if (!demandeId) {
      this.errorMessage = 'No demande ID provided!';
      this.isLoading = false;
      return;
    }

    this.demandeService.getDemandeBy(demandeId).subscribe({
        next: (response) => {
          this.demande = { ...response, samples: response.samples || [] };
          this.isLoading = false;
         this.demandeid=this.demande?.demande_id || '';
          // Fetch derogation details if status is 'derogation'

            this.fetchDerogationDetails(this.demandeid);

        },
      error: (error) => {
        console.error('Error fetching demande:', error);
        this.errorMessage = 'Failed to load demande details.';
        this.isLoading = false;
      }
    });
  }
  fetchDerogationDetails(demandeId: string): void {
    this.isLoading = true; // Optional: Show loading for derogations
    this.derogationService.getDerogationDetails(demandeId).subscribe({
      next: (response: DerogationResponse) => {
        this.derogationDetails = response;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la récupération des dérogations:', error);
        this.errorMessage = 'Échec du chargement des détails des dérogations.';
        this.isLoading = false;
      }
    });
  }
  preValidateDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Pré-validation de la demande...';

    this.demandeService.updateStatusToOngoing(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Demande pré-validée!", response);

        // Show success notification
        this.notificationMessage = "La demande a été pré-validée. Le client est notifié d'apporter les échantillons.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);

        this.fetchDemandeDetails(); // Refresh demande details
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de la pré-validation de la demande:", error);

        // Show error notification
        this.notificationMessage = "Échec de la pré-validation de la demande. Veuillez réessayer.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);
      }
    });
  }
  validateDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Validation de la demande...';

    this.demandeService.updateStatusToValidated(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Demande validée!", response);

        // Show success notification
        this.notificationMessage = "La demande a été complètement validée.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);

        // ✅ Update status locally
        if (this.demande) {
          this.demande.status = 'validated';
        }
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de la validation de la demande:", error);

        // Show error notification
        this.notificationMessage = "Échec de la validation de la demande.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);
      }
    });
  }

  // ✅ Function for validation with derogation
  validateWithDerogation(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Validation avec dérogation...';

    this.demandeService.updateStatusToDerogation(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Demande validée avec dérogation!", response);

        // Show success notification
        this.notificationMessage = "La demande a été validée avec dérogation.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);

        // ✅ Update status locally
        if (this.demande) {
          this.demande.status = 'valid';
        }
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de la validation avec dérogation:", error);

        // Show error notification
        this.notificationMessage = "Échec de la validation avec dérogation.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);
      }
    });
  }
  rejectDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Rejet de la demande...';

    this.demandeService.updateStatusToRejected(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Demande rejected!", response);

        // Show success notification
        this.notificationMessage = "La demande a été rejetée.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);

        // ✅ Update status locally
        if (this.demande) {
          this.demande.status = 'rejected';
        }
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors du rejet de la demande:", error);

        // Show error notification
        this.notificationMessage = "Échec du rejet de la demande.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 4 seconds
        setTimeout(() => {
          this.showNotification = false;
          this.cdr.detectChanges();
        }, 4000);
      }
    });
  }

}
