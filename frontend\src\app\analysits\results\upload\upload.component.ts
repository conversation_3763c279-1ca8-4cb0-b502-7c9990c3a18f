import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faUpload, faFileExcel, faArrowLeft, faSpinner, faCheckCircle, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';

import { ResultsService } from '../results.service';
import { DemandeService } from '../../../receptionniste/demandes/demande.service';

@Component({
  selector: 'app-upload-results',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // For [spin] attribute on fa-icon
})
export class UploadResultsComponent implements OnInit {
  // Font Awesome icons
  faUpload = faUpload;
  faFileExcel = faFileExcel;
  faArrowLeft = faArrowLeft;
  faSpinner = faSpinner;
  faCheckCircle = faCheckCircle;
  faExclamationTriangle = faExclamationTriangle;

  demandeId: number | null = null;
  demandeDetails: any = null;
  demandeStringId: string = '';
  selectedFile: File | null = null;
  fileName: string = '';
  isLoading: boolean = false;
  isUploading: boolean = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly resultsService: ResultsService,
    private readonly demandeService: DemandeService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.demandeId = +params['id'];
      if (this.demandeId) {
        this.loadDemandeDetails();
      } else {
        this.errorMessage = 'ID de demande invalide.';
      }
    });
  }

  loadDemandeDetails(): void {
    if (!this.demandeId) return;

    this.isLoading = true;
    this.demandeService.getDemandeById(this.demandeId).subscribe({
      next: (data) => {
        this.demandeDetails = data;
        // Store the string demande_id for use with the results API
        if (data?.demande_id) {
          this.demandeStringId = data.demande_id;
          console.log('Demande string ID:', this.demandeStringId);
        } else {
          console.error('Demande data does not contain demande_id:', data);
        }

        // Calculate total_enregistrements if it's not available
        if (!this.demandeDetails.total_enregistrements && this.demandeDetails.samples) {
          this.demandeDetails.total_enregistrements = this.demandeDetails.samples.length;
          console.log('Calculated total_enregistrements:', this.demandeDetails.total_enregistrements);
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching demande details:', error);
        this.errorMessage = 'Échec du chargement des détails de la demande.';
        this.isLoading = false;
      }
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check if file is an Excel file
      const validTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel.sheet.macroEnabled.12'
      ];

      if (validTypes.includes(file.type)) {
        this.selectedFile = file;
        this.fileName = file.name;
        this.errorMessage = null;
      } else {
        this.errorMessage = 'Veuillez sélectionner un fichier Excel (.xls, .xlsx).';
        this.selectedFile = null;
        this.fileName = '';
        event.target.value = '';
      }
    }
  }

  uploadFile(): void {
    if (!this.selectedFile) {
      this.errorMessage = 'Veuillez sélectionner un fichier Excel.';
      return;
    }

    if (!this.demandeStringId) {
      this.errorMessage = 'Identifiant de demande non disponible. Veuillez réessayer.';
      console.error('Missing demandeStringId for upload', {
        demandeId: this.demandeId,
        demandeDetails: this.demandeDetails
      });
      return;
    }

    this.isUploading = true;
    this.errorMessage = null;
    this.successMessage = null;

    // Use the string demande_id instead of the numeric ID
    console.log('Uploading results for demande_id:', this.demandeStringId);
    this.resultsService.uploadResults(this.demandeStringId, this.selectedFile).subscribe({
      next: () => {
        this.isUploading = false;
        this.successMessage = 'Résultats téléchargés avec succès!';

        // Reset file selection
        this.selectedFile = null;
        this.fileName = '';

        // Redirect back to results list after 2 seconds
        setTimeout(() => {
          this.router.navigate(['/analysits/results']);
        }, 2000);
      },
      error: (error) => {
        console.error('Error uploading results:', error);
        this.isUploading = false;

        // Display a more specific error message if available
        if (error.error?.message) {
          this.errorMessage = `Échec du téléchargement: ${error.error.message}`;
        } else if (error.message) {
          this.errorMessage = `Échec du téléchargement: ${error.message}`;
        } else {
          this.errorMessage = 'Échec du téléchargement des résultats. Veuillez réessayer.';
        }

        // Add debugging information to the console
        console.log('Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          error: error.error
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/analysits/results']);
  }
}
