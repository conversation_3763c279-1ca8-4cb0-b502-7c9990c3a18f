import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { NotificationService } from '../../../notification/notification.service';
import { Notification } from '../../../../models/notification.model';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faTrash, faTrashAlt, faEye } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule, FontAwesomeModule],  // Import NgxPaginationModule
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.css']
})
export class NotificationsDirectorComponent implements OnInit {
  notifications: Notification[] = [];
  page: number = 1;  // Current page
  itemsPerPage: number = 5;  // Items per page

  // FontAwesome icons
  faTrash = faTrash;
  faTrashAlt = faTrashAlt;
  faEye = faEye;

  // Delete loading states
  isDeletingNotification: boolean = false;
  isDeletingAll: boolean = false;

  // Success/Error messages
  successMessage: string = '';
  errorMessage: string = '';

  constructor(
    private notificationService: NotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.fetchAllNotifications(); // ✅ Fetch both types of notifications
  }

  // ✅ Fetch both derogations and rapports together
  fetchAllNotifications(): void {
    this.notificationService.getDerogatedNotifications().subscribe(
      (derogatedResponse) => {
        this.notificationService.getRapportNotificationsDirector().subscribe(
          (rapportResponse) => {
            this.notifications = [
              ...(derogatedResponse.notifications || []),
              ...(rapportResponse.notifications || [])
            ];

            // ✅ Sorting: Unread first, then by created_at (latest first)
            this.notifications.sort(
              (a, b) =>
                Number(a.is_read) - Number(b.is_read) ||
                new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            );
          },
          (error) => console.error('Error fetching rapport notifications:', error)
        );
      },
      (error) => console.error('Error fetching derogated notifications:', error)
    );
  }

  // ✅ Handle notification click: Mark as read, then navigate
  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe(
        () => {
          notification.is_read = true; // Update UI
          this.navigateToDetailDirector(notification);
        },
        (error) => console.error('Error marking notification as read:', error)
      );
    } else {
      this.navigateToDetailDirector(notification);
    }
  }

  // ✅ Navigate based on the type of notification
  navigateToDetailDirector(notification: Notification): void {
    if (notification.title === 'Demande de validation avec derogation' && notification.demande) {
      console.log('Navigating to demande:', notification.demande);
      this.router.navigate(['/derogation', notification.demande]);
    } else if (notification.type === 'rapport' && notification.rapport_id) {
      console.log('Navigating to rapport:', notification.rapport_id);
      this.router.navigate(['/director/rapportsDetails/', notification.rapport_id]);
    } else {
      // Handle invalid or unexpected notification cases
      console.error('Invalid notification or missing ID:', notification);
    }
  }

  // Remove a specific notification with confirmation
  removeNotification(notification: Notification): void {
    // Show confirmation dialog
    const confirmDelete = confirm(`Êtes-vous sûr de vouloir supprimer cette notification ?\n\n"${notification.title}"`);

    if (!confirmDelete) {
      return;
    }

    // Set loading state
    this.isDeletingNotification = true;
    this.clearMessages();

    // Call API to delete notification
    this.notificationService.deleteNotification(notification.id).subscribe({
      next: (response) => {
        if (response.success) {
          // Remove from local array
          this.notifications = this.notifications.filter(n => n.id !== notification.id);

          // Show success message
          this.successMessage = response.message || 'Notification supprimée avec succès';
          this.autoHideMessage();

          console.log('✅ Notification deleted successfully:', notification.id);
        } else {
          this.errorMessage = response.message || 'Erreur lors de la suppression de la notification';
          this.autoHideMessage();
        }
      },
      error: (error) => {
        console.error('❌ Error deleting notification:', error);
        this.errorMessage = 'Erreur lors de la suppression de la notification. Veuillez réessayer.';
        this.autoHideMessage();
      },
      complete: () => {
        this.isDeletingNotification = false;
        this.cdr.detectChanges();
      }
    });
  }

  // Remove all notifications with confirmation
  removeAllNotifications(): void {
    // Check if there are notifications to delete
    if (this.notifications.length === 0) {
      this.errorMessage = 'Aucune notification à supprimer';
      this.autoHideMessage();
      return;
    }

    // Show confirmation dialog
    const confirmDelete = confirm(`Êtes-vous sûr de vouloir supprimer TOUTES vos notifications ?\n\nCette action supprimera ${this.notifications.length} de vos notification(s) et ne peut pas être annulée.`);

    if (!confirmDelete) {
      return;
    }

    // Set loading state
    this.isDeletingAll = true;
    this.clearMessages();

    // Call API to delete all notifications
    this.notificationService.deleteAllNotifications().subscribe({
      next: (response) => {
        if (response.success) {
          // Clear local array
          this.notifications = [];

          // Show success message with count
          this.successMessage = response.message || `${response.deleted_count} notification(s) supprimée(s) avec succès`;
          this.autoHideMessage();

          console.log('✅ All notifications deleted successfully:', response.deleted_count);
        } else {
          this.errorMessage = response.message || 'Erreur lors de la suppression des notifications';
          this.autoHideMessage();
        }
      },
      error: (error) => {
        console.error('❌ Error deleting all notifications:', error);
        this.errorMessage = 'Erreur lors de la suppression des notifications. Veuillez réessayer.';
        this.autoHideMessage();
      },
      complete: () => {
        this.isDeletingAll = false;
        this.cdr.detectChanges();
      }
    });
  }

  // Helper method to clear success/error messages
  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = '';
  }

  // Helper method to auto-hide messages after 5 seconds
  autoHideMessage(): void {
    setTimeout(() => {
      this.clearMessages();
      this.cdr.detectChanges();
    }, 5000);
  }
}
