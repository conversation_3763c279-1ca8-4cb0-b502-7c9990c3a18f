<div class="auth-section">
  <div class="container" [ngClass]="{ 'active': isRegisterMode }">

    <!-- ✅ Formulaire de Connexion -->
    <div class="form-box login" *ngIf="!isRegisterMode">
        <h1 class="login-title">Connexion</h1>
        <form [formGroup]="loginForm" (ngSubmit)="loginFormSubmit()">
          <p *ngIf="errorMessage" class="error">{{ errorMessage }}</p>

          <div class="input-box">
              <input type="email" formControlName="email" placeholder="Email" required>
              <i class="bx bxs-envelope"></i>
          </div>
          <div class="input-box">
              <input type="password" formControlName="password" placeholder="Mot de passe" required>
              <i class="bx bxs-lock-alt"></i>
          </div>
          <button type="submit" class="btn-primary">Se connecter</button>
          <p>Pas encore inscrit ? <a (click)="toggleMode()">C<PERSON>er un compte</a></p>
        </form>
    </div>

    <!-- ✅ Formulaire d'Inscription -->
    <div class="form-box register" *ngIf="isRegisterMode">
        <h1>Inscription</h1>
        <form [formGroup]="registerForm" (ngSubmit)="registerFormSubmit()">
          <div class="input-box">
              <input type="text" formControlName="nom" placeholder="Nom" required>
              <i class="bx bxs-user"></i>
          </div>
          <div class="input-box">
              <input type="email" formControlName="email" placeholder="Email" required>
              <i class="bx bxs-envelope"></i>
          </div>
          <div class="input-box">
              <input type="password" formControlName="password" placeholder="Mot de passe" required>
              <i class="bx bxs-lock-alt"></i>
          </div>
          <button type="submit" class="btn-primary">S'inscrire</button>
          <p>Déjà inscrit ? <a (click)="toggleMode()">Se connecter</a></p>
        </form>
    </div>
  </div>
</div>
