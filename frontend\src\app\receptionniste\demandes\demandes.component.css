@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.demandes-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 70px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  display: block;
  justify-content: center;

}
.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: center; /* Vertically centers all items */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 60%; /* Limits the width as in your original design */
  margin: 0 auto; /* Centers the filter bar within its parent */
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}
.filter-group label {
  font-weight: 600;
  font-size: 14px;
  color: #444;
}
.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 250px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.notification {

  background: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
  text-align: center;
  width:100%;
  font-weight: bold;

}
/* ✅ Titre */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ En-têtes */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Lignes */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Colonne d'actions */
.actions-col {
  text-align: center;
  padding: 10px;
  width: 20%;
}

/* ✅ Badges de statut */
.status-tag {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

/* Couleurs pour chaque statut */
.status-tag.pending {
  background-color: #fff3cd; /* jaune pâle */
  color: #856404;
}
.status-tag.ongoing {
  background-color: #cce5ff; /* bleu pâle */
  color: #004085;
}
.status-tag.derogation {
  background-color: #f8d7da; /* rouge pâle */
  color: #721c24;
}
.status-tag.valid {
  background-color: #d4edda; /* vert pâle */
  color: #155724;
}
.status-tag.rejected {
  background-color: #f8d7da; /* rouge pâle */
  color: #721c24;
}

/* ✅ Boutons */
button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px;
  transition: all 0.3s ease-in-out;
}

/* ✅ Bouton Voir détails */
.btn-details {
  background: #2496d3;
  color: #fff;
  border: none;
  padding: 8px 14px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  line-height: 1; /* Assure une même hauteur pour tous */
  transition: all 0.3s ease-in-out;
  min-width: 120px;
  display: inline-flex; /* Changed to inline-flex to better match text flow */
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  margin: 0 auto; /* Center the button horizontally */
}

.btn-details:hover {
  transform: scale(1.05);
  background: #1a7bad;
  box-shadow: 0 5px 15px rgba(26, 123, 173, 0.4);
  color: white;
}

/* ✅ Bouton Valider */
.btn-valider {
  background: #28a745;
  color: white;
  box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.3);
}

.btn-valider:hover {
  background: #218838;
  transform: scale(1.1);
  box-shadow: 0px 8px 20px rgba(40, 167, 69, 0.6);
}

/* ✅ Bouton Refuser */
.btn-refuser {
  background: #dc3545;
  color: white;
  box-shadow: 0px 4px 10px rgba(220, 53, 69, 0.3);
}

.btn-refuser:hover {
  background: #b22234;
  transform: scale(1.1);
  box-shadow: 0px 8px 20px rgba(220, 53, 69, 0.6);
}

/* ✅ Effets interactifs */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* ✅ Animation Glow */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* Styles pour l'indicateur de chargement et les messages vides */
.loading-row, .empty-row {
  height: 100px;
}

.loading-row td, .empty-row td {
  text-align: center;
  vertical-align: middle;
  font-size: 18px;
  color: #6c757d;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.text-center {
  text-align: center;
}
