printRapport(): void {
  if (!this.reportData) {
    console.error('No report data available to print.');
    return;
  }

  const formattedDate = this.formatDate(this.reportData.creation_date);
  const rapportId = this.reportData.rapport_id;

  const printHtml = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <title>Rapport d'Analyse n° ${rapportId}</title>
      <style>
        /* Existing Styles */
        @media print {
          @page { size: A4; margin: 5mm; }
          body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
          .editable-field {
            border: none !important;
            background-color: transparent !important;
          }
          .print-controls {
            display: none !important;
          }
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: #000;
        }
        .container {
          padding: 10px;
          width: 100%;
        }
        .government-header {
          text-align: center;
          font-size: 11pt;
          margin-top: 10px;
          line-height: 1.4;
        }
        .government-header p {
          margin: 0;
        }
        .header-table {
          width: 100%;
          border-collapse: collapse;
          border-top: 4px double black;
          border-bottom: 4px double black;
          margin: 15px 0;
        }
        .header-table td {
          vertical-align: middle;
          text-align: center;
          border: none;
          padding: 5px;
        }
        .logo-left img {
          height: 90px;
          width: auto;
        }
        .center-title {
          font-weight: bold;
          font-size: 16pt;
          text-transform: uppercase;
        }
        .cartouche {
          text-align: left;
          font-size: 10pt;
          line-height: 1.6;
          flex: 1;
          white-space: nowrap;
          min-width: 180px;
        }
        .cartouche strong {
          font-weight: bold;
        }
        .validation {
          display: inline-block;
          margin-top: 5px;
          padding: 3px 10px;
          font-size: 11pt;
          border-radius: 8px;
          text-transform: uppercase;
        }
        .approved {
          color: #4caf50;
          border: 1px solid #4caf50;
          background-color: rgba(76,175,80,0.1);
        }
        .rejected {
          color: #f44336;
          border: 1px solid #f44336;
          background-color: rgba(244,67,54,0.1);
        }
        .section-title {
          margin: 18px 20px 6px;
          font-size: 12pt;
          font-weight: bold;
          text-decoration: underline;
        }
        table:not(.header-table) {
          width: calc(100% - 40px);
          margin: 0 20px 16px;
          border-collapse: collapse;
          font-size: 10pt;
        }
        table:not(.header-table) th, table:not(.header-table) td {
          border: 1px solid #000;
          padding: 4px 6px;
        }
        table:not(.header-table) th {
          background: #f2f2f2;
          font-weight: bold;
          text-align: center;
        }
        .double-border {
          border: 2px solid #000 !important;
        }
        .footer {
          margin: 20px;
          text-align: center;
          font-size: 10pt;
        }
        .footer p { margin: 4px 0; }

        /* Echantillon table styles */
        .echantillon-table {
          width: 100%;
          border-collapse: collapse;
        }

        /* Column width adjustments */
        .code-echantillon-col {
          width: 45%;
        }

        .date-reception-col {
          width: 12%;
        }

        .nature-col {
          width: 12%;
        }

        .provenance-col {
          width: 13%;
        }

        .lot-col, .reference-col {
          width: 9%;
        }

        /* Nested table styles */
        .nested-table {
          width: 100%;
          margin: 5px 0 0 0;
          border-collapse: collapse;
        }

        .nested-table tr {
          border-bottom: 1px solid #e9ecef;
        }

        .nested-table tr:last-child {
          border-bottom: none;
        }

        .nested-table th {
          background-color: #f8f9fa;
          color: #495057;
          font-weight: 600;
          font-size: 9pt;
          padding: 3px 5px;
          text-align: left;
          border: 1px solid #dee2e6;
          width: 40%;
          white-space: nowrap;
        }

        .nested-table td {
          padding: 3px 5px;
          text-align: left;
          font-size: 9pt;
          border: 1px solid #dee2e6;
          white-space: nowrap;
        }

        /* Editable Fields Styles */
        .editable-field {
          min-height: 20px;
          border: 1px dashed #ccc;
          padding: 2px 5px;
          background-color: #f9f9f9;
          display: inline-block;
        }
        .editable-field:focus {
          outline: none;
          border: 1px dashed #2496d3;
          background-color: #e9f7fe;
        }

        /* Print Controls Styles */
        .print-controls {
          position: fixed;
          top: 10px;
          right: 10px;
          background-color: #2496d3;
          color: white;
          padding: 10px;
          border-radius: 5px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          z-index: 1000;
          cursor: move;
          user-select: none;
        }
        .print-controls .drag-handle {
          padding: 5px 0;
          margin-bottom: 10px;
          text-align: center;
          font-weight: bold;
          border-bottom: 1px solid rgba(255,255,255,0.3);
          cursor: move;
        }
        .print-controls button {
          background-color: white;
          color: #2496d3;
          border: none;
          padding: 8px 15px;
          margin: 5px;
          border-radius: 3px;
          cursor: pointer;
          font-weight: bold;
          display: block;
          width: 100%;
        }
        .print-controls button:hover {
          background-color: #f0f0f0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <!-- Entête Gouvernementale -->
        <div class="government-header">
          <p><strong>République Tunisienne</strong></p>
          <p><strong>Ministère de l’Agriculture et des Ressources Hydrauliques</strong></p>
          <p><em>Institution de la Recherche et l’Enseignement Supérieur Agricoles</em></p>
          <p><em>Institut National des Sciences et Technologies de la Mer</em></p>
        </div>

        <!-- En-tête tableau (logos + titre + cartouche) -->
        <table class="header-table">
          <tr>
            <td class="logo-left" style="width: 20%;">
              <img src="${window.location.origin}/kls.png" alt="Logo INSTM" />
            </td>
            <td class="center-title" style="width: 50%;">
              <div>RAPPORT</div>
              <div>D’ANALYSE</div>
            </td>
            <td class="logo-right" style="width: 30%;">
              <div style="display: flex; align-items: center;">
                <div style="margin-right: 10px;">
                  ${
                    this.analyses_accredite === 1
                      ? `<img src="${window.location.origin}/Image2.png" alt="Logo Tunac" style="height: 90px; width: auto;" />`
                      : `<div style="width: 90px; height: 90px;"></div>`
                  }
                </div>
                <div class="cartouche">
                  <strong>CODE :</strong> PE/01-PET/09<br>
                  <strong>VERSION :</strong> 07<br>
                  <strong>DATE :</strong> ${formattedDate}<br>
                  <strong>PAGE :</strong> 1/1
                </div>
              </div>
            </td>
          </tr>
        </table>
        <h4 style="text-align: center; font-size: 1.25rem; margin: 15px 0;">N°: ${this.formatRapportId(this.reportData.rapport_id)}</h4>

        <!-- Section Client & Laboratoire -->
        <p class="section-title">Client & Laboratoire</p>
        <table class="double-border">
          <tr>
            <td style="width:50%;">
              <strong>Client</strong><br>
              Nom et Prénom : <div contenteditable="true" class="editable-field">${this.clientInfo.name || '-'} ${this.clientInfo.nickname || ''}</div><br>
              Adresse : <div contenteditable="true" class="editable-field">${this.clientInfo.adress || '-'}</div><br>
              Téléphone : <div contenteditable="true" class="editable-field">${this.clientInfo.phone || '-'}</div>
            </td>
            <td style="width:50%;">
              <strong>Laboratoire d'Analyse</strong><br>
              ${this.labInfo.nom}<br>
              ${this.labInfo.adresse}<br>
              ${this.labInfo.contact}
            </td>
          </tr>
        </table>

        <!-- Description de l’échantillon -->
        <p class="section-title">Description de l'échantillon</p>
        <table class="double-border echantillon-table">
          <thead>
            <tr>
              <th class="code-echantillon-col">Code échantillon</th>
              <th class="date-reception-col">Date réception</th>
              <th class="nature-col">Nature</th>
              <th class="provenance-col">Provenance</th>
              <th class="lot-col">Lot</th>
              <th class="reference-col">Référence</th>
            </tr>
          </thead>
          <tbody>
            ${this.samples.map(s => `
              <tr>
                <td class="code-echantillon-col">
                  <div style="text-align: center; font-weight: bold; font-size: 12pt; margin-bottom: 5px;">${s.identification_echantillon}</div>
                  <div class="nested-info" style="margin-top: 5px; font-size: 9pt; text-align: left;">
                    <div style="white-space: nowrap; margin-bottom: 3px;"><strong>Origine prélèvement:</strong> ${s.origine_prelevement}</div>
                    <div style="margin-bottom: 3px;"><strong>Date prélèvement:</strong> ${this.formatDate(s.date_prelevement)}</div>
                    <div style="margin-bottom: 3px;"><strong>Site:</strong> ${s.site}</div>
                    <div style="margin-bottom: 3px;"><strong>Préleveur:</strong> ${s.nom_preleveur}</div>
                  </div>
                </td>
                <td class="date-reception-col">${this.formatDate(s.reception_date)}</td>
                <td class="nature-col">${s.nature_echantillon}</td>
                <td class="provenance-col">${s.provenance}</td>
                <td class="lot-col">${s.lot}</td>
                <td class="reference-col">${s.reference}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Analyses demandées -->
        <p class="section-title">Analyses demandées</p>
        <table class="double-border">
          <thead>
            <tr><th>N°</th><th>Paramètre</th><th>Méthode proposée</th><th>Délai d'exécution souhaité</th></tr>
          </thead>
          <tbody>
            ${this.uniqueParameters.length
              ? this.uniqueParameters.map((param, i) => `
                <tr>
                  <td>${('0'+(i+1)).slice(-2)}</td>
                  <td>${param}</td>
                  <td>${this.standardMethodAnalyses[i]?.parametre || '-'}</td>
                  ${i === 0 ? `<td rowspan="${this.uniqueParameters.length}" style="text-align:center;">${this.delaiSouhaite || '-'}</td>` : ''}
                </tr>
              `).join('')
              : `<tr><td>01</td><td>-</td><td>-</td><td style="text-align:center">${this.delaiSouhaite||'-'}</td></tr>`
            }
          </tbody>
        </table>

        <!-- Résultats d’analyses -->
        <p class="section-title">Résultats d'analyses</p>
        <table class="double-border">
          <thead>
            <tr>
              <th>Code échantillon</th><th>Paramètre</th><th>Mesurande</th><th>Unité</th>
              <th>Limite d'acceptabilité</th><th>Méthode utilisée</th><th>Date analyse</th>
            </tr>
          </thead>
          <tbody>
            ${this.analysisResults.map(r => `
              <tr>
                <td>${r.code_echantillon}</td><td>${r.parametre}</td>
                <td>${r.mesurande||'-'}</td><td>${r.unite||'-'}</td>
                <td>${r.limite_acceptabilite||'-'}</td>
                <td>${r.methode_analyse_utilisee}</td>
                <td>${r.date_analyse ? this.formatDate(r.date_analyse) : '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Pied de page -->
        <div class="footer">
          <p>(*) Valeur inférieure à la limite de quantification</p>
          <p>Date : ${formattedDate}</p>
          <p style="font-weight:bold; margin-top:30px;">Responsable du Laboratoire</p>
          <p style="font-size:9pt;">Fin de rapport</p>
        </div>
      </div>

      <!-- Print Controls (Draggable) -->
      <div class="print-controls" id="draggable-print-controls">
        <div class="drag-handle">⋮⋮ Déplacer</div>
        <button id="print-button">Imprimer</button>
        <button id="cancel-button">Annuler</button>
      </div>

      <script>
        // Make print controls draggable
        (function() {
          const dragElement = document.getElementById('draggable-print-controls');
          if (!dragElement) return;

          let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
          const dragHandle = document.querySelector('.drag-handle');

          if (dragHandle) {
            dragHandle.onmousedown = dragMouseDown;
          } else {
            dragElement.onmousedown = dragMouseDown;
          }

          function dragMouseDown(e) {
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
          }

          function elementDrag(e) {
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            dragElement.style.top = (dragElement.offsetTop - pos2) + 'px';
            dragElement.style.left = (dragElement.offsetLeft - pos1) + 'px';
            dragElement.style.right = 'auto';
            dragElement.style.bottom = 'auto';
          }

          function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
          }
        })();

        // Add event listeners to buttons
        document.getElementById('print-button').addEventListener('click', function() {
          document.querySelector('.print-controls').style.display = 'none';
          setTimeout(function() {
            window.print();
            document.querySelector('.print-controls').style.display = 'block';
          }, 100);
        });

        document.getElementById('cancel-button').addEventListener('click', function() {
          window.close();
        });
      </script>
    </body>
    </html>
  `;

  // Open a new window for editing and printing
  this.editablePrintWindow = window.open('', '_blank', 'width=800,height=600');
  if (this.editablePrintWindow) {
    this.editablePrintWindow.document.write(printHtml);
    this.editablePrintWindow.document.close();
    this.isEditingPdf = true; // Note: Consider renaming to isEditingHtml if more appropriate
  } else {
    console.error('Failed to open print window. Please allow pop-ups.');
  }
}