@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ SECTION PRINCIPALE */
.tarif-table-section {
  margin: 0 auto;  /* Centrage horizontal parfait */
  padding: 100px 5%;  /* Ajustement du padding pour un meilleur alignement */
  background: radial-gradient(circle at top left, #0c0032, #190061);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* ✅ TITRE AMÉLIORÉ */
.tab-title {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-align: center;
    width: 100%;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 5px solid #2496d3;
    animation: fadeIn 1.5s ease-in-out forwards;
}

/* ✅ CONTENEUR DU TABLEAU */
.table-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 1400px; /* Ajusté */
  width: 100%;
  margin: auto;
}

/* ✅ TABLEAU FLUIDE & DESIGN */
.styled-table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
}

/* ✅ EN-TÊTE */
.styled-table thead {
    background: linear-gradient(90deg, #00c3ff, #007bff);
    color: white;
    text-transform: uppercase;
}

.styled-table th {
    padding: 20px;
    font-size: 17px;
    transition: background 0.3s ease, transform 0.2s ease;
}

.styled-table th:hover {
    background: linear-gradient(90deg, #ff00ff, #2496d3);
    transform: scale(1.1);
}

/* ✅ CELLULES */
.styled-table td {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

/* ✅ ANIMATION SUR LES LIGNES */
.styled-table tbody tr {
    transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
}

.styled-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
}

/* ✅ PRIX EN SURBRILLANCE */
.styled-table tbody tr td:last-child {
    font-weight: bold;
    font-size: 16px;
    color: #ffcc00;
    text-align: center;
}

/* ✅ ANIMATION DE FONDU */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-15px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✅ NOTE EN BAS DU TABLEAU */
.note-card {
    position: relative;
    width: 70%;
    margin-top: 40px;
    padding: 25px;
    background: linear-gradient(135deg, #ff8c42, #ff5e62);
    color: white;
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    text-align: center;
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    animation: float 2s ease-in-out infinite alternate;
    transform: rotate(-2deg);
    position: relative;
}

/* ✅ EFFET FLOTTANT */
@keyframes float {
    from { transform: translateY(0px) rotate(-2deg); }
    to { transform: translateY(10px) rotate(2deg); }
}

/* ✅ RUBAN ADHÉSIF POUR LE STYLE */
.tape {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%) rotate(-5deg);
    width: 140px;
    height: 25px;
    background: repeating-linear-gradient(45deg, #007bff, #007bff 10px, #0056b3 10px, #0056b3 20px);
    opacity: 0.8;
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 1024px) {
    .tab-title {
        font-size: 16.5px;
    }

    .styled-table th, .styled-table td {
        font-size: 14.5px;
        padding: 15px;
    }

    .note-card {
        font-size: 14.5px;
    }

    .table-container {
        overflow-x: auto;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .styled-table {
        font-size: 15px;
    }

    .styled-table th, .styled-table td {
        font-size: 16px;
        padding: 10px;
    }

    .note-card {
        width: 90%;
        font-size: 17px;
    }

    .table-container {
        overflow-x: auto;
        overflow-y: auto;
    }
}
