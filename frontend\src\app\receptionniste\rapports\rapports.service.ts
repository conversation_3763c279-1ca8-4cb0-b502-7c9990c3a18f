import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class RapportsService {

  private apiUrl = 'http://127.0.0.1:8000/api';
  private baseUrl = 'http://127.0.0.1:8000';

  constructor(private http: HttpClient) {}
/*************  ✨ Windsurf Command ⭐  *************/
/**
 * Fetches all rapports from the API.
 * @returns An observable containing an array of rapports.
 */

/*******  c51b43e3-8ffd-43a2-a84f-22f5f54dc2bb  *******/
   getAllRapports(): Observable<any[]> {
      return this.http.get<any[]>(`${this.apiUrl}/rapports`);
    }
    sendRapport(rapportId: number): Observable<any> {
      return this.http.put(`http://127.0.0.1:8000/api/rapport/director/${rapportId}`, {}); // ✅ API call to send the report
    }

    sendRapportToClient(demandeId: string): Observable<any> {
      return this.http.put(`${this.apiUrl}/client/rapport/send/${demandeId}`, {}); // ✅ API call to send the report to client
    }
    getRapportDetails(rapportId: number): Observable<any> {
      return this.http.get(`${this.apiUrl}/rapports/${rapportId}`);
    }
    updateAnalysisResult(analysisId: number, updatedData: any): Observable<any> {
      return this.http.put(`${this.apiUrl}/update-rapport-analysis/${analysisId}`, updatedData);
    }

    // Get results for a specific demande
    getResultsByDemandeId(demande_numero: string): Observable<any> {
      return this.http.get(`${this.apiUrl}/results/demande/${demande_numero}`);
    }
    getResultsAnalyseByDemandeId(demande_numero: string): Observable<any> {
      return this.http.get(`${this.apiUrl}/results/analyse/${demande_numero}`);
    }

    // Download results file
    downloadResults(resultId: number): Observable<any> {
      return this.http.get(`${this.apiUrl}/results/download/${resultId}`, {
        responseType: 'blob'
      });
    }

    // Get base URL for constructing absolute URLs
    getBaseUrl(): string {
      return this.baseUrl;
    }
}
