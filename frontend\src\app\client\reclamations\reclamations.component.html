<div class="reclamations-container">
  <!-- Title Section -->
  <div class="title-container">
    <div class="circle-icon">
      <fa-icon [icon]="faExclamationTriangle"></fa-icon>
    </div>
    <h2 class="main-title">Mes Réclamations</h2>
  </div>

  <!-- Create New Button -->
  <div class="action-buttons">
    <button class="btn-create" (click)="createNewReclamation()">
      <fa-icon [icon]="faPlus"></fa-icon> Nouvelle Réclamation
    </button>
  </div>

  <!-- Filter Section -->
  <div class="filter-section">
    <div class="filter-header" (click)="toggleFilters()">
      <fa-icon [icon]="faFilter"></fa-icon> Filtres
    </div>

    <div class="filter-content" [class.show]="showFilters">
      <div class="filter-row">
        <div class="filter-group">
          <label for="searchTerm">Recherche:</label>
          <div class="input-with-icon">
            <fa-icon [icon]="faSearch"></fa-icon>
            <input
              type="text"
              id="searchTerm"
              [(ngModel)]="searchTerm"
              placeholder="ID ou sujet..."
            >
          </div>
        </div>

        <div class="filter-group">
          <label for="status">Statut:</label>
          <select id="status" [(ngModel)]="selectedStatus">
            <option value="">Tous</option>
            <option value="pending">En attente</option>
            <option value="in_progress">En cours</option>
            <option value="resolved">Résolu</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="date">Date:</label>
          <div class="input-with-icon">
            <fa-icon [icon]="faCalendarAlt"></fa-icon>
            <input
              type="date"
              id="date"
              [(ngModel)]="selectedDate"
            >
          </div>
        </div>
      </div>

      <div class="filter-actions">
        <button class="btn-apply" (click)="applyFilters()">
          <fa-icon [icon]="faFilter"></fa-icon> Appliquer les filtres
        </button>
        <button class="btn-reset" (click)="resetFilters()">
          <fa-icon [icon]="faEraser"></fa-icon> Réinitialiser
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner"></div>
    <p>Chargement en cours...</p>
  </div>

  <!-- No Results Message -->
  <div *ngIf="!isLoading && filteredReclamations.length === 0" class="no-results">
    <p>Aucune réclamation trouvée.</p>
  </div>

  <!-- Reclamations Table -->
  <div *ngIf="!isLoading && filteredReclamations.length > 0" class="table-responsive">
    <table class="styled-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Date</th>
          <th>Sujet</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let reclamation of filteredReclamations | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }">
          <td>{{ reclamation.id }}</td>
          <td>{{ reclamation.date | date:'dd/MM/yyyy' }}</td>
          <td>{{ reclamation.subject }}</td>
          <td>
            <span class="status-tag" [ngClass]="getStatusClass(reclamation.status)">
              <fa-icon [icon]="getStatusIcon(reclamation.status)" [spin]="isInProgress(reclamation.status)"></fa-icon>
              {{ getStatusLabel(reclamation.status) }}
            </span>
          </td>
          <td class="action-cell">
            <button class="btn-details" (click)="viewReclamationDetails(reclamation.id)">
              <fa-icon [icon]="faEye"></fa-icon> Voir détails
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div *ngIf="!isLoading && filteredReclamations.length > 0" class="pagination-container">
    <pagination-controls
      (pageChange)="currentPage = $event"
      previousLabel="Précédent"
      nextLabel="Suivant"
      screenReaderPaginationLabel="Pagination"
      screenReaderPageLabel="Page"
      screenReaderCurrentLabel="Vous êtes sur la page">
    </pagination-controls>
  </div>
</div>
