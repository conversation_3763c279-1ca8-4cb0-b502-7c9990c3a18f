<div class="excel-viewer-overlay">
  <div class="excel-viewer-modal">
    <div class="excel-viewer-header">
      <h3>{{ fileName }}</h3>
      <button class="close-btn" (click)="closeViewer()">
        <fa-icon [icon]="faTimes"></fa-icon>
      </button>
    </div>
    
    <div class="excel-viewer-content">
      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="spinner"></div>
        <p>Chargement du fichier Excel...</p>
      </div>
      
      <!-- Error message -->
      <div *ngIf="error" class="error-container">
        <p class="error-message">{{ error }}</p>
      </div>
      
      <!-- Excel data -->
      <div *ngIf="!isLoading && !error" class="excel-data-container">
        <!-- Sheet tabs -->
        <div *ngIf="sheets.length > 1" class="sheet-tabs">
          <button 
            *ngFor="let sheet of sheets" 
            [class.active]="sheet === activeSheet"
            (click)="loadSheet(sheet)"
          >
            {{ sheet }}
          </button>
        </div>
        
        <!-- Excel table -->
        <div class="excel-table-container">
          <table class="excel-table">
            <thead>
              <tr>
                <th *ngFor="let header of headers">{{ header }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of data">
                <td *ngFor="let cell of row">{{ cell }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Empty state -->
        <div *ngIf="data.length === 0 && !isLoading && !error" class="empty-state">
          <p>Aucune donnée trouvée dans cette feuille.</p>
        </div>
      </div>
    </div>
  </div>
</div>
