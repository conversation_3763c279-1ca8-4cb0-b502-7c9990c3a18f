import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { Demande } from './demande.model';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DemandeService {
  private apiUrl = 'http://localhost:8000/api/demande'; // Ensure correct backend URL

  constructor(private http: HttpClient) {}

  getDemandeDetails(demandeId: string): Observable<Demande> {
    console.log(`Fetching demande details for ID: ${demandeId}`);

    return this.http.get<{ demande: Demande }>(`http://localhost:8000/api/demande/${demandeId}`).pipe(
      map(response => {
        if (!response || !response.demande) {
          throw new Error('Invalid API response structure');
        }
        console.log('Processed API response:', response.demande);
        return response.demande;
      }),
      catchError(error => {
        console.error('API error:', error);
        return throwError(() => new Error('Failed to fetch demande details.'));
      })
    );
  }
  updateStatusToOngoing(demandeId: string): Observable<any> {
    return this.http.patch(`http://localhost:8000/api/receptionist/demandes/${demandeId}/ongoing`, {});
  }
  updateStatusToValidated(demandeId: string): Observable<any> {
    return this.http.patch(`http://localhost:8000/api/receptionist/demandes/${demandeId}/validate`, {});
  }
  
  updateStatusToDerogation(demandeId: string): Observable<any> {
    return this.http.patch(`http://localhost:8000/api/receptionist/demandes/${demandeId}/validated-with-derogation`, {});
  }
  
}
