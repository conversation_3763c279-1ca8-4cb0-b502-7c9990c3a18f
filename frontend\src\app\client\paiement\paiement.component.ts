import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { PaiementService } from './paiement.service';
import { DemandeService } from '../demande-details/demande.service';
import { Demande } from '../demande-details/demande.model';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faUpload, faSave,faPaperPlane, faArrowLeft, faSync } from '@fortawesome/free-solid-svg-icons';
import { PaymentHistoryComponent } from './payment-history/payment-history.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-paiement',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FontAwesomeModule, PaymentHistoryComponent],
  templateUrl: './paiement.component.html',
  styleUrls: ['./paiement.component.css']
})
export class PaiementComponent implements OnInit, OnDestroy {
  // Font Awesome icons
  faUpload = faUpload;
  faSave = faSave;
  faArrowLeft = faArrowLeft;
  faSync = faSync;
  faPaperPlane = faPaperPlane;
  demande: Demande | null = null;
  isLoading = true;
  isSubmitting = false; // New loading state specifically for form submission
  errorMessage: string | null = null;
  successMessage: string | null = null;
  paymentForm: FormGroup;
  selectedFile: File | null = null;
  filePreview: string | null = null;

  // Payment status polling
  private statusPollingSubscription: Subscription | null = null;
  hasPaymentHistory = false;
  isRefreshingHistory = false;
  devis: any[] = [];
  isDevisLoading = false; // Added for devis loading state

  // Modal properties
  showSuccessModal = false;
  modalMessage = '';


  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private demandeService: DemandeService,
    private fb: FormBuilder,
    private paiementService: PaiementService,
    private cdr: ChangeDetectorRef
  ) {
    this.paymentForm = this.fb.group({
      payment_proof: [null, Validators.required],
      amount: [{value: '', disabled: true}, [Validators.required, Validators.pattern(/^\d+(\.\d{1,2})?$/)]],
      notes: ['']
    });
  }

  ngOnInit(): void {
    this.fetchDemandeDetails();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when component is destroyed
    if (this.statusPollingSubscription) {
      this.statusPollingSubscription.unsubscribe();
    }
  }

  fetchDemandeDetails(): void {
    const demandeId = this.route.snapshot.paramMap.get('demande_id');
    if (!demandeId) {
      this.errorMessage = 'Identifiant de demande manquant';
      this.isLoading = false;
      return;
    }

    this.demandeService.getDemandeDetails(demandeId).subscribe({
      next: (response) => {
        this.demande = response;
        this.isLoading = false;

        // Fetch devis details to calculate the total amount
        this.fetchDevisDetails(demandeId);

        // Check for payment history and start polling for status updates
        this.checkPaymentHistory(demandeId);
      },
      error: (error) => {
        console.error('Erreur lors de la récupération des détails de la demande:', error);
        this.errorMessage = 'Impossible de charger les détails de la demande';
        this.isLoading = false;
      }
    });
  }

  // This section was moved to the bottom of the file

  // This section was moved to the bottom of the file


  fetchDevisDetails(demandeId: string): void {
    console.log('Fetching devis for demande ID:', demandeId);

    // Set devis loading state to true
    this.isDevisLoading = true;

    this.demandeService.getDevis(demandeId).subscribe({
      next: (response) => {
        if (Array.isArray(response)) {
          // Store the devis data
          this.devis = response;
          console.log('Devis fetched successfully:', this.devis);

          // Update the amount field with the calculated total
          const total = this.calculateTotalAmount();
          this.paymentForm.get('amount')?.setValue(total);
        }

        // Set devis loading state to false
        this.isDevisLoading = false;

        // Force change detection to update the view
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching devis details:', error);
        this.isDevisLoading = false;
      }
    });
  }

  calculateTotalAmount(): number {
    try {
      // If we have devis data, calculate from it
      if (this.devis && Array.isArray(this.devis) && this.devis.length > 0) {
        return this.devis.reduce((sum: number, item: any) => {
          // Handle different possible formats
          if (item.prix_total) {
            // If prix_total is available, use it directly
            return sum + parseFloat(item.prix_total || '0');
          } else {
            // Otherwise calculate from prix_unitaire and quantite
            const price = parseFloat(item.prix_unitaire || '0');
            const quantity = parseInt(item.quantite || '0', 10);
            return sum + (price * quantity);
          }
        }, 0);
      }
      return 0;
    } catch (error) {
      console.error('Error calculating total amount:', error);
      return 0;
    }
  }

  /**
   * Check if the demande has a payment
   */
  hasPayment(): boolean {
    return this.demande?.payment_id !== null && this.demande?.payment_id !== undefined;
  }

  /**
   * Handle payment deletion event from the payment history component
   */
  onPaymentDeleted(): void {
    // Refresh the demande details to update payment status
    const demandeId = this.route.snapshot.paramMap.get('demande_id');
    if (demandeId) {
      // Show loading indicator
      this.isLoading = true;

      this.demandeService.getDemandeDetails(demandeId).subscribe({
        next: (updatedDemande) => {
          this.demande = updatedDemande;

          // Check if there are still payments
          this.checkPaymentHistory(demandeId);

          // Show success message in modal
          this.showSuccessMessageInModal('Le paiement a été supprimé avec succès');

          // Hide loading indicator
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error refreshing demande details:', error);
          this.errorMessage = 'Impossible de mettre à jour les informations de la demande';
          this.isLoading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (!this.selectedFile) {
      this.errorMessage = 'Veuillez sélectionner un justificatif de paiement';
      return;
    }

    if (!this.demande) {
      this.errorMessage = 'Informations de demande manquantes';
      return;
    }

    // Show loading indicator
    this.isSubmitting = true;
    this.errorMessage = null;

    const formData = new FormData();
    formData.append('payment_proof', this.selectedFile);
    formData.append('demande_id', this.demande.demande_id);
    formData.append('amount', this.paymentForm.get('amount')?.value);
    formData.append('notes', this.paymentForm.get('notes')?.value || '');

    this.paiementService.submitPaymentProof(formData).subscribe({
      next: () => {
        // Hide loading indicator
        this.isSubmitting = false;

        // Show success message in modal instead of regular success message
        this.showSuccessMessageInModal('Justificatif de paiement envoyé avec succès');
        this.errorMessage = null;

        // Reset form and file selection
        this.paymentForm.reset();
        this.selectedFile = null;
        this.filePreview = null;

        // Refresh payment history
        this.hasPaymentHistory = true;
        this.refreshPaymentHistory();

        // Reload demande details to update payment_id status
        const demandeId = this.route.snapshot.paramMap.get('demande_id');
        if (demandeId) {
          this.demandeService.getDemandeDetails(demandeId).subscribe({
            next: (updatedDemande) => {
              this.demande = updatedDemande;
            }
          });
        }
      },
      error: (error) => {
        // Hide loading indicator
        this.isSubmitting = false;

        console.error('Erreur lors de l\'envoi du justificatif de paiement:', error);

        // Handle different types of errors
        if (error.status === 422 && error.error && error.error.errors) {
          // Validation errors
          const validationErrors = error.error.errors;
          let errorMsg = 'Erreur de validation: ';

          // Check for specific validation errors
          if (validationErrors.payment_proof) {
            errorMsg += validationErrors.payment_proof.join(', ');
          } else if (validationErrors.demande_id) {
            errorMsg += 'ID de demande invalide';
          } else if (validationErrors.amount) {
            errorMsg += 'Montant invalide';
          } else {
            // Generic validation error message
            errorMsg += Object.values(validationErrors).flat().join(', ');
          }

          this.errorMessage = errorMsg;
        } else if (error.status === 401) {
          // Authentication errors
          this.errorMessage = 'Vous devez être connecté pour effectuer cette action';

          // Redirect to login page after a delay
          setTimeout(() => {
            localStorage.removeItem('token');
            this.router.navigate(['/login']);
          }, 3000);
        } else if (error.status === 500 && error.error && error.error.debug) {
          // Server errors with debug info
          this.errorMessage = `Erreur serveur: ${error.error.message}. Détails: ${error.error.debug}`;
        } else {
          // Generic error message
          this.errorMessage = 'Erreur lors de l\'envoi du justificatif de paiement';
        }

        this.successMessage = null;
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/client/devis']);
  }

  /**
   * Show success message in modal
   * @param message The success message to display
   */
  showSuccessMessageInModal(message: string): void {
    // Hide loading spinner when showing success modal
    this.isLoading = false;

    // Set modal message and show modal
    this.modalMessage = message;
    this.showSuccessModal = true;
  }

  /**
   * Close the success modal
   */
  closeSuccessModal(): void {
    this.showSuccessModal = false;
    this.modalMessage = '';
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        this.errorMessage = 'Format de fichier non valide. Veuillez sélectionner une image (JPEG, JPG, PNG) ou un PDF.';
        this.selectedFile = null;
        this.filePreview = null;
        input.value = ''; // Reset the file input
        return;
      }

      // Validate file size (25MB max)
      const maxSize = 25 * 1024 * 1024; // 25MB in bytes
      if (file.size > maxSize) {
        this.errorMessage = 'Le fichier est trop volumineux. La taille maximale est de 25 Mo.';
        this.selectedFile = null;
        this.filePreview = null;
        input.value = ''; // Reset the file input
        return;
      }

      this.selectedFile = file;
      this.errorMessage = null;

      // Create file preview
      const reader = new FileReader();
      reader.onload = () => {
        this.filePreview = reader.result as string;
        // Force change detection to update the UI
        this.cdr.detectChanges();
      };
      reader.readAsDataURL(this.selectedFile);

      // Mark the payment_proof control as touched to trigger validation
      this.paymentForm.get('payment_proof')?.markAsTouched();
      this.paymentForm.updateValueAndValidity();
    }
  }

  checkPaymentHistory(demandeId: string): void {
    this.paiementService.getPaymentByDemandeId(demandeId).subscribe({
      next: (response) => {
        this.hasPaymentHistory = response?.data?.length > 0;

        // Start polling for payment status updates
        this.startPaymentStatusPolling(demandeId);
      },
      error: (error) => {
        console.error('Erreur lors de la vérification de l\'historique des paiements:', error);
        // Even if there's an error, we'll assume no payment history
        this.hasPaymentHistory = false;
      }
    });
  }

  startPaymentStatusPolling(demandeId: string): void {
    // Clean up any existing subscription
    if (this.statusPollingSubscription) {
      this.statusPollingSubscription.unsubscribe();
    }

    // Start polling every 10 seconds
    this.statusPollingSubscription = this.paiementService.startPaymentStatusPolling(demandeId, 10000)
      .subscribe({
        next: (response) => {
          // Update payment history flag
          this.hasPaymentHistory = response?.data?.length > 0;

          // If a payment is approved, show success message in modal

        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour du statut de paiement:', error);
        }
      });
  }

  refreshPaymentHistory(): void {
    if (!this.demande) return;

    this.isRefreshingHistory = true;
    this.paiementService.getPaymentByDemandeId(this.demande.demande_id).subscribe({
      next: () => {
        this.isRefreshingHistory = false;
      },
      error: () => {
        this.isRefreshingHistory = false;
      }
    });
  }

}
