<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Derogation extends Model
{
    use HasFactory;

    protected $fillable = [
        'demande_id',
        'identification_echantillon',
        'nature_echantillon',
        'reference',
        'provenance',
        'demandeur',
        'date_demande_client',
        'masse_echantillon',
        'analyses_demandees',
        'description_ecart',
        'reponse_du_client',
        'decision',
        'date_et_visa_du_DL',
        'date_et_visa_du_demander',
    ];

    protected $casts = [
        'analyses_demandees' => 'array', // Ensures JSON casting for array fields
        'date_demande_client' => 'date',
        'date_et_visa_du_DL' => 'date',
        'date_et_visa_du_demander' => 'date',
    ];

    public function demande()
    {
        return $this->belongsTo(Demande::class);
    }
}
