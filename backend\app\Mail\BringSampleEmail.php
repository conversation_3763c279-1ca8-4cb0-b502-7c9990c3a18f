<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BringSampleEmail extends Mailable
{
    use Queueable, SerializesModels;
    
    public $notification;
    public $demande;
    public $user;

    /**
     * Create a new message instance.
     *
     * @param object $notification The notification object
     * @param object $demande The demande object
     * @param object $user The user object
     */
    public function __construct($notification, $demande, $user)
    {
        $this->notification = $notification;
        $this->demande = $demande;
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Demande Échantillon - ' . $this->demande->demande_id,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.bringsample',
            with: [
                'notification' => $this->notification,
                'demande' => $this->demande,
                'user' => $this->user
            ]
        );
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.bringsample')
                    ->with([
                        'notification' => $this->notification,
                        'demande' => $this->demande,
                        'user' => $this->user
                    ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
