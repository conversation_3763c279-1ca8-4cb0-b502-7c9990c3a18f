<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::table('fiches', function (Blueprint $table) {
        $table->date('date_transmission')->nullable()->change(); // ✅ Make it nullable
    });
}

public function down()
{
    Schema::table('fiches', function (Blueprint $table) {
        $table->date('date_transmission')->nullable(false)->change();
    });
}

};
