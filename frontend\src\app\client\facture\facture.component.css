@import url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

.facture-container {
  overflow: hidden;  /* Empêche immédiatement le scroll */
  padding: 40px;
  margin: 30px auto;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  font-family: 'Poppins', sans-serif;
  color: #333;
  transform: scale(1.02);
  margin-bottom: 0 !important; /* Neutralise toute marge inférieure excessive */
  text-align: center;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.ministere {
  text-align: left;
  margin-left: 50px; /* ✅ Eloignement vers la droite */
  font-size: 14px;
  line-height: 1.7;
}
    /* ✅ Styles essentiels à intégrer directement dans le HTML pour le PDF */
    .pdf-content {
      font-family: Arial, sans-serif;
      padding: 30px;
      color: #222;
    }
    .pdf-content .header, .pdf-content .styled-table {
      border-collapse: collapse;
    }

    .pdf-content .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2px solid #ddd;
      padding-bottom: 15px;
      margin-bottom: 20px;
    }

    .pdf-content .left {
      text-align: left;
      margin-left: 40px;
      font-size: 13px;
      line-height: 1.5;
    }

    .pdf-content .right {
      text-align: right;
      font-size: 13px;
    }

    .pdf-content .logo {
      width: 100px;
    }

    .pdf-content .title {
      font-size: 20px;
      text-align: center;
      margin: 20px 0;
    }

    .pdf-content .styled-table th, .pdf-content .styled-table td {
      padding: 8px;
      border: 1px solid #ddd;
      font-size: 12px;
      text-align: center;
    }

    .pdf-content .styled-table th {
      background-color: #2496d3;
      color: #fff;
    }

    .pdf-content .total {
      text-align: right;
      font-weight: bold;
      font-size: 14px;
      margin-top: 15px;
    }

    .pdf-content .footer, .pdf-content .ccp, .pdf-content .arrete {
      font-size: 12px;
      text-align: center;
      margin-top: 10px;
    }

    /* Supprimez toutes les transformations et animations pour le PDF */
    .pdf-content, .pdf-content * {
      animation: none !important;
      transform: none !important;
      opacity: 1 !important;
    }
.header .right {
  text-align: right;
  font-size: 14px;
}

.logo {
  width: 120px;
  height: auto;
  animation: fadeIn 1.2s ease-in-out;
}

.title {
  font-size: 24px;
  margin: 25px 0 10px;
  font-weight: 600;
  color: #222;
  animation: slideDown 1s ease-in-out;
}

.doit {
  font-size: 16px;
  margin-bottom: 15px;
  color: #555;
}

.styled-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 25px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0,0,0,0.05);
  animation: fadeIn 1.2s ease-in-out;
}

.styled-table th, .styled-table td {
  padding: 12px;
  border: 1px solid #eee;
  text-align: center;
  font-size: 14px;
}

.styled-table th {
  background: #2496d3;
  color: white;
}

.styled-table td {
  background: #fbfdff;
}

.total {
  font-size: 18px;
  text-align: right;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 2px solid #e1e8ee;
  color: #222;
}

.arrete, .ccp {
  font-size: 14px;
  margin-top: 10px;
}

.footer {
  font-size: 13px;
  color: #888;
  margin-top: 15px;
  border-top: 1px dashed #ddd;
  padding-top: 10px;
}

.btn-download {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background: #2496d3;
  color: white;
  font-size: 15px;
  font-weight: bold;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounceIn 1s ease;
}

.btn-download:hover {
  background: #1c7bac;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
}

/* ✅ Animations subtiles et fluides */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-15px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
@import url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

.facture-container {
  padding: 40px;
  margin: 30px auto;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  font-family: 'Poppins', sans-serif;
  color: #333;
  transform: scale(1.02);
  text-align: center;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.ministere {
  text-align: left;
  margin-left: 50px; /* ✅ Eloignement vers la droite */
  font-size: 14px;
  line-height: 1.7;
}

.header .right {
  text-align: right;
  font-size: 14px;
}

.logo {
  width: 120px;
  height: auto;
  animation: fadeIn 1.2s ease-in-out;
}

.title {
  font-size: 24px;
  margin: 25px 0 10px;
  font-weight: 600;
  color: #222;
  animation: slideDown 1s ease-in-out;
}

.doit {
  font-size: 16px;
  margin-bottom: 15px;
  color: #555;
}

.styled-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 25px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0,0,0,0.05);
  animation: fadeIn 1.2s ease-in-out;
}

.styled-table th, .styled-table td {
  padding: 12px;
  border: 1px solid #eee;
  text-align: center;
  font-size: 14px;
}

.styled-table th {
  background: #2496d3;
  color: white;
}

.styled-table td {
  background: #fbfdff;
}

.total {
  font-size: 18px;
  text-align: right;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 2px solid #e1e8ee;
  color: #222;
}

.arrete, .ccp {
  font-size: 14px;
  margin-top: 10px;
}

.footer {
  font-size: 13px;
  color: #888;
  margin-top: 15px;
  border-top: 1px dashed #ddd;
  padding-top: 10px;
}

.btn-download {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background: #2496d3;
  color: white;
  font-size: 15px;
  font-weight: bold;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounceIn 1s ease;
}

.btn-download:hover {
  background: #1c7bac;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
}

/* ✅ Animations subtiles et fluides */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-15px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
