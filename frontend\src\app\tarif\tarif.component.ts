import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TarifTableComponent } from '../tarif-table/tarif-table.component';
import { Router } from '@angular/router';
@Component({
    selector: 'app-tarif',standalone: true,
    imports: [CommonModule, TarifTableComponent],
    templateUrl: './tarif.component.html',
    styleUrl: './tarif.component.css'
})
export class TarifComponent {
    constructor( private router: Router,){

    }
    navigateTo(path: string): void {
        this.router.navigate([path]);
      }
}
