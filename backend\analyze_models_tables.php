<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Get all existing tables
$tables = DB::select('SHOW TABLES');
$existingTables = [];
foreach($tables as $table) {
    $tableName = array_values((array)$table)[0];
    $existingTables[] = $tableName;
}

// Define models and their expected table names
$models = [
    'Analysis' => 'analyses',
    'Demande' => 'demandes', 
    'Derogation' => 'derogations',
    'Devis' => 'devis',
    'Facture' => 'factures',
    'Fiche' => 'fiches',
    'FicheTransmission' => 'fiches_transmission',
    'Notification' => 'notifications',
    'Payment' => 'payments',
    'Rapport' => 'rapports',
    'RapportAnalyse' => 'rapport_analyses',
    'Registre' => 'registres',
    'RegistreSuivi' => 'registre_suivis',
    'Result' => 'results',
    'ResultatClient' => 'resultat_client',
    'Sample' => 'samples',
    'User' => 'users'
];

echo "=================================================================\n";
echo "                    MODELS vs TABLES ANALYSIS                   \n";
echo "=================================================================\n\n";

echo "Total Models Found: " . count($models) . "\n";
echo "Total Tables in Database: " . count($existingTables) . "\n\n";

$existingCount = 0;
$missingCount = 0;
$missingTables = [];

foreach($models as $modelName => $expectedTable) {
    $exists = in_array($expectedTable, $existingTables);
    $status = $exists ? "✅ EXISTS" : "❌ MISSING";
    
    if ($exists) {
        $existingCount++;
        
        // Get table structure for existing tables
        try {
            $columns = DB::select("DESCRIBE {$expectedTable}");
            $columnCount = count($columns);
            echo sprintf("%-20s | %-20s | %-10s | %d columns\n", 
                $modelName, $expectedTable, $status, $columnCount);
        } catch (Exception $e) {
            echo sprintf("%-20s | %-20s | %-10s | Error reading\n", 
                $modelName, $expectedTable, $status);
        }
    } else {
        $missingCount++;
        $missingTables[] = $expectedTable;
        echo sprintf("%-20s | %-20s | %-10s | N/A\n", 
            $modelName, $expectedTable, $status);
    }
}

echo "\n=================================================================\n";
echo "                           SUMMARY                              \n";
echo "=================================================================\n";
echo "✅ Tables that exist: {$existingCount}/{" . count($models) . "}\n";
echo "❌ Tables missing: {$missingCount}/{" . count($models) . "}\n\n";

if ($missingCount > 0) {
    echo "Missing Tables:\n";
    echo "---------------\n";
    foreach($missingTables as $table) {
        echo "- {$table}\n";
    }
    echo "\n";
}

// Check for extra tables (tables that exist but don't have models)
$extraTables = array_diff($existingTables, array_values($models));
$systemTables = ['migrations', 'cache', 'cache_locks', 'failed_jobs', 'job_batches', 'jobs', 'password_reset_tokens', 'password_resets', 'personal_access_tokens', 'sessions'];
$extraTables = array_diff($extraTables, $systemTables);

if (!empty($extraTables)) {
    echo "Extra Tables (exist but no model found):\n";
    echo "----------------------------------------\n";
    foreach($extraTables as $table) {
        echo "- {$table}\n";
    }
    echo "\n";
}

echo "=================================================================\n";
echo "                      DETAILED TABLE INFO                       \n";
echo "=================================================================\n\n";

foreach($models as $modelName => $expectedTable) {
    if (in_array($expectedTable, $existingTables)) {
        echo "📋 {$modelName} Model -> {$expectedTable} Table\n";
        echo "   Columns:\n";
        
        try {
            $columns = DB::select("DESCRIBE {$expectedTable}");
            foreach($columns as $column) {
                $nullable = $column->Null === 'YES' ? 'NULL' : 'NOT NULL';
                $default = $column->Default ? " DEFAULT '{$column->Default}'" : '';
                echo "   - {$column->Field} ({$column->Type}) {$nullable}{$default}\n";
            }
            
            // Count records
            $count = DB::table($expectedTable)->count();
            echo "   Records: {$count}\n";
            
        } catch (Exception $e) {
            echo "   Error: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
}
