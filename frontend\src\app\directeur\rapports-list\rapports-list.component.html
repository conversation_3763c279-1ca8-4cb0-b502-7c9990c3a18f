<div class="admin-panel">
    <h2>Les Rapports</h2>

    <!-- Barr<PERSON> de filtrage -->
    <div class="filter-bar">
      <div class="filter-group">
        <label for="search">Rechercher par ID ou demande:</label>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          class="filter-input"
        />
      </div>
      <div class="filter-group">
        <label for="date">Date:</label>
        <input type="date" id="date" [(ngModel)]="selectedDate" />
      </div>
      <div class="filter-group">
        <label for="status">Statut:</label>
        <select id="status" [(ngModel)]="selectedStatus" class="status-select">
          <option value="">Tous</option>
          <option value="validated">Validés</option>
          <option value="rejected">Rejetés</option>
          <option value="pending">En attente</option>
        </select>
      </div>
      <div class="filter-buttons">
        <button class="btn-filter-apply" (click)="onFilterChange()">
          <fa-icon [icon]="faFilter"></fa-icon> Appliquer
        </button>
        <button class="btn-clear" (click)="clearFilters()">
          <fa-icon [icon]="faEraser"></fa-icon> Effacer
        </button>
      </div>
    </div>

    <!-- Table with loading state -->
    <table class="reports-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Demande Numéro</th>
          <th>Date de Création</th>
          <th>Statut</th>
        
          <th>Actions</th>
        </tr>
      </thead>

      <!-- No reports message -->
      <tbody *ngIf="!isLoading && (!filteredReports || filteredReports.length === 0)" class="empty-tbody">
        <tr>
          <td colspan="6" class="empty-message">
            Aucun rapport disponible.
          </td>
        </tr>
      </tbody>

      <!-- Reports data -->
      <tbody *ngIf="!isLoading && filteredReports && filteredReports.length > 0">

        <!-- On parcourt les rapports filtrés -->
        <tr *ngFor="let report of filteredReports">
            <td>{{ report.id }}</td>
            <td>{{ report.demande_id }}</td>
            <td>{{ report.creation_date | date:'dd/MM/yyyy' }}</td>

            <!-- Status column with styled spans -->
            <td>
              <span class="{{ report.status === 'Validé' ? 'status-approved' : (report.status === 'Rejeté' ? 'status-rejected' : 'status-pending') }}">
                {{ (report.status === 'Validé' || report.status === 'Rejeté') ? report.status : 'En attente de validation' }}
              </span>
            </td>

          
            <td class="action-buttons">
              <button class="details-btn" (click)="viewReportDetails(report.id)">
                <fa-icon [icon]="faEye"></fa-icon> Voir détails
              </button>

            

              <!-- Sent confirmation for reports that have been sent -->
              <span *ngIf="report.sentToClient" class="sent-confirmation">
                <fa-icon [icon]="faCheck"></fa-icon> Envoyé au client
              </span>
            </td>
          </tr>

      </tbody>
    </table>
  </div>
