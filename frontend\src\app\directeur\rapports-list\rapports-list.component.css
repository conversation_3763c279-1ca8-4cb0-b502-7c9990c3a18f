/* ------------------------------------------------------
   1) Importation des Polices
   ------------------------------------------------------ */
   @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

   /* ------------------------------------------------------
      2) Global Styling
      ------------------------------------------------------ */
   * {
     margin: 0;
     padding: 0;
     box-sizing: border-box;
     font-family: 'Montserrat', sans-serif;
   }

   body {
     background-color: #f7f7f7;
     color: #333;
   }

   /* Animation de fondu général */
   .fade-bg {
     animation: fadeInBg 1.2s ease-in-out forwards;
   }

   /* ------------------------------------------------------
      3) Admin Panel
      ------------------------------------------------------ */
   .admin-panel {
     margin: 50px auto;
     padding: 30px;
     background: #fff;
     border-radius: 10px;
     box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
     max-width: 90%;
     text-align: center;
     /* Animation plus visible */
     animation: fadeInUp 1s ease-in-out forwards;
   }

   /* Titre du panneau avec effet "glowText" */
   .admin-panel h2 {
     font-family: 'Orbitron', sans-serif;
     font-size: 22px;
     font-weight: bold;
     text-transform: uppercase;
     letter-spacing: 2px;
     margin-bottom: 20px;
     border-bottom: 4px solid #2496d3;
     display: inline-block;
     padding-bottom: 8px;
     animation: glowText 1.5s infinite alternate;
     background: linear-gradient(90deg, black, grey);
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
   }

   /* ------------------------------------------------------
      4) Table des Rapports
      ------------------------------------------------------ */
   .reports-table {
     width: 100%;
     border-collapse: collapse;
     margin-top: 20px;
     background: white;
     border-radius: 8px;
     overflow: hidden;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   }

   /* En-têtes */
   .reports-table thead tr th {
     background-color: #2496d3;
     color: white;
     padding: 15px;
     text-align: center;
     font-weight: 600;
     text-transform: uppercase;
     font-size: 14px;
   }

   /* Lignes du tableau */
   .reports-table tbody tr td {
     padding: 15px;
     text-align: center;
     font-size: 14px;
     color: #333;
     border-bottom: 1px solid #f2f2f2;
     transition: background-color 0.3s ease;
   }

   .reports-table tbody tr:hover td {
     background-color: #f8faff;
   }

   /* ✅ Status Styling */
   .status-approved {
     color: #28a745;
     font-weight: bold;
     padding: 4px 8px;
     border-radius: 4px;
     background-color: rgba(40, 167, 69, 0.1);
   }

   .status-rejected {
     color: #dc3545;
     font-weight: bold;
     padding: 4px 8px;
     border-radius: 4px;
     background-color: rgba(220, 53, 69, 0.1);
   }

   .status-pending {
     color: #ffc107;
     font-weight: bold;
     padding: 4px 8px;
     border-radius: 4px;
     background-color: rgba(255, 193, 7, 0.1);
   }

   /* ------------------------------------------------------
      5) Boutons d'Action
      ------------------------------------------------------ */
   .action-buttons {
     display: flex;
     justify-content: center;
     gap: 10px;
     flex-wrap: wrap;
   }

   .approve-btn,
   .reject-btn,
   .details-btn,
   .send-btn {
     padding: 10px 20px;
     font-size: 14px;
     border-radius: 25px;
     cursor: pointer;
     transition: all 0.3s ease;
     border: none;
     text-transform: uppercase;
     outline: none;
     display: flex;
     align-items: center;
     justify-content: center;
     gap: 8px;
   }

   /* Sent confirmation message */
   .sent-confirmation {
     display: flex;
     align-items: center;
     gap: 8px;
     color: #28a745;
     font-weight: bold;
     padding: 10px;
     border-radius: 25px;
     background-color: rgba(40, 167, 69, 0.1);
   }

   /* Bouton "Valider" (approve) */
   .approve-btn {
     background-color: #2496d3;
     color: white;
     box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
   }
   .approve-btn:hover {
     background-color: #1a6a8e;
     transform: scale(1.05);
   }

   /* Bouton "Rejeter" (reject) */
   .reject-btn {
     background-color: #e74c3c;
     color: white;
     box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
   }
   .reject-btn:hover {
     background-color: #c0392b;
     transform: scale(1.05);
   }

   /* Bouton "Voir Détail" */
   .details-btn {
     background-color: #555;
     color: #fff;
     box-shadow: 0 5px 15px rgba(85, 85, 85, 0.3);
   }
   .details-btn:hover {
     background-color: #333;
     transform: scale(1.05);
   }

   /* Bouton "Envoyer au client" */
   .send-btn {
     background-color: #28a745;
     color: white;
     box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
   }
   .send-btn:hover {
     background-color: #218838;
     transform: scale(1.05);
   }
   .send-btn:disabled {
     background-color: #6c757d;
     cursor: not-allowed;
     transform: none;
     opacity: 0.7;
   }

   /* ------------------------------------------------------
      6) Filter Bar
      ------------------------------------------------------ */
   .filter-bar {
     display: flex;
     flex-wrap: wrap;
     align-items: center;
     justify-content: center;
     gap: 1rem;
     padding: 1rem;
     background-color: #f5f5f5;
     border-radius: 8px;
     max-width: 75%;
     margin: 0 auto 20px auto;
   }

   .filter-group {
     display: flex;
     flex-direction: column;
     gap: 0.5rem;
   }

   .filter-group label {
     font-weight: 600;
     color: #555;
     white-space: nowrap;
     font-size: 14px;
   }

   .filter-group input,
   .filter-group select {
     width: 150px;
     padding: 0.5rem;
     border: 1px solid #ccc;
     border-radius: 4px;
     background-color: white;
     transition: border-color 0.2s, box-shadow 0.2s;
   }

   .filter-group input:focus,
   .filter-group select:focus {
     border-color: #2496d3;
     box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.2);
     outline: none;
   }

   .status-select {
     width: 180px;
     appearance: none;
     background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0h12L6 6z" fill="%23666"/></svg>');
     background-repeat: no-repeat;
     background-position: right 10px center;
     padding-right: 30px;
   }

   #search {
     width: 250px;
   }

   /* Filter Buttons */
   .filter-buttons {
     display: flex;
     gap: 10px;
     margin-left: auto;
   }

   .btn-filter-apply,
   .btn-clear {
     padding: 10px 15px;
     border: none;
     border-radius: 4px;
     cursor: pointer;
     font-weight: bold;
     display: flex;
     align-items: center;
     justify-content: center;
     gap: 5px;
     transition: all 0.3s ease;
   }

   .btn-filter-apply {
     background-color: #28a745;
     color: white;
   }

   .btn-filter-apply:hover {
     background-color: #218838;
     transform: translateY(-2px);
   }

   .btn-clear {
     background-color: #6c757d;
     color: white;
   }

   .btn-clear:hover {
     background-color: #5a6268;
     transform: translateY(-2px);
   }

   .empty-message {
     padding: 30px !important;
     text-align: center;
     color: #666;
     font-style: italic;
     font-size: 16px;
   }

   /* ------------------------------------------------------
      8) Animations
      ------------------------------------------------------ */
   /* Fondu de l'arrière-plan */
   @keyframes fadeInBg {
     0%   { opacity: 0; }
     100% { opacity: 1; }
   }

   /* Slide vers le haut + fade */
   @keyframes fadeInUp {
     0% {
       opacity: 0;
       transform: translateY(40px);
     }
     100% {
       opacity: 1;
       transform: translateY(0);
     }
   }
   /* Already defined above */
   /* Effet "glow" sur le texte du titre */
   @keyframes glowText {
     0% {
       text-shadow: 0 0 10px rgba(36, 150, 211, 0.4);
     }
     100% {
       text-shadow: 0 0 20px rgba(36, 150, 211, 0.8);
     }
   }
