/* ✅ GLOBAL */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}

body {
  background-color: #f7f7f7;
  color: #333;
}
.back-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: 0.3s;
}

.back-btn:hover {
  background: #0056b3;
}
/* ✅ CONTENEUR PRINCIPAL */
.fiche-transmission-container {
  width: 90%;

  margin: 50px auto;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 1s ease-in-out;
  overflow: hidden;
}

/* ✅ TITRE DE LA FICHE */
.fiche-transmission-container h3 {
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #2496d3;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3;
  padding-bottom: 10px;
}

/* ✅ MESSAGE DE CHARGEMENT & ERREUR */
.loading-message {
  font-size: 16px;
  color: #ff9800;
  text-align: center;
  margin-bottom: 15px;
}

.error {
  font-size: 16px;
  color: red;
  text-align: center;
  margin-bottom: 15px;
  font-weight: bold;
}

/* ✅ TABLEAU DE LA FICHE DE TRANSMISSION */
.fiche-transmission-container table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
}

/* ✅ EN-TÊTE DU TABLEAU */
.fiche-transmission-container thead tr th {
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  border-bottom: 2px solid #f2f2f2;
}

/* ✅ CELLULES DU TABLEAU */
.fiche-transmission-container tbody tr td {
  padding: 15px;
  text-align: left;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f2f2f2;
  transition: all 0.3s ease;
}

.fiche-transmission-container tbody tr:hover td {
  background-color: #f5f5f5;
}

/* ✅ LISTE DES ANALYSES DEMANDÉES */
.fiche-transmission-container ul {
  padding-left: 20px;
}

.fiche-transmission-container ul li {
  font-size: 14px;
  color: #2496d3;
}

/* ✅ MESSAGE AUCUNE FICHE TROUVÉE */
.no-data {
  font-size: 16px;
  color: #d32f2f;
  text-align: center;
  margin-top: 20px;
  font-weight: bold;
}

/* ✅ ANIMATION D'APPARITION */
@keyframes fadeIn {
  0% { opacity: 0; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1); }
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .fiche-transmission-container {
      padding: 20px 10px;
  }

  .fiche-transmission-container table {
      font-size: 12px;
  }

  .fiche-transmission-container thead tr th,
  .fiche-transmission-container tbody tr td {
      padding: 10px;
  }
}
