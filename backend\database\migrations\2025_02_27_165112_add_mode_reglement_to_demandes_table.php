<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->string('mode_reglement')->after('user_id'); // Add column
        });
    }
    
    public function down()
    {
        Schema::table('demandes', function (Blueprint $table) {
            $table->dropColumn('mode_reglement');
        });
    }
    
};
