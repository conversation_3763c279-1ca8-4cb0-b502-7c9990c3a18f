import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormControl, Validators, FormsModule, FormBuilder } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faFilter, faSearch, faEraser, faEye, faEdit, faTrash, faPlus, faSave, faTimes, faUser } from '@fortawesome/free-solid-svg-icons';

import { UsersService } from './users.service';
import { User } from '../../../models/user.model';

@Component({
  selector: 'app-users',
  standalone: true,
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.css'],
  imports: [CommonModule, ReactiveFormsModule, FormsModule, FontAwesomeModule, NgxPaginationModule],
})
export class UsersComponent implements OnInit {
  // Font Awesome icons
  faFilter = faFilter;
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faEdit = faEdit;
  faTrash = faTrash;
  faPlus = faPlus;
  faSave = faSave;
  faTimes = faTimes;
  faUser = faUser;

  errorMessage: string | null = null;
  users: User[] = [];
  showUserModal = false;
  editMode = false;
  userForm: FormGroup;
  selectedUser: User | null = null;
  nameFilter: string = '';
  roleFilter: string = '';
  filteredUsers: User[] = [];
  isLoading: boolean = false;

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  constructor(private userService: UsersService, private fb: FormBuilder) {
    this.userForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      role: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      password_confirmation: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  ngOnInit(): void {
    this.loadUsers();
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('password_confirmation')?.value;

    if (password !== confirmPassword) {
      form.get('password_confirmation')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  loadUsers(): void {
    this.isLoading = true;
    this.userService.getUsers().subscribe({
      next: (data) => {
        console.log('Users fetched:', data);
        this.users = data;
        console.log('users',);
        this.filteredUsers = [...this.users];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching users:', error);
        this.isLoading = false;
      }
    });
  }

  openUserModal(user: User | null = null): void {
    this.editMode = !!user;
    this.selectedUser = user;

    if (user) {
      this.userForm.patchValue({
        name: user.name,
        email: user.email,
        role: user.role
      });

      // When editing, don't require password
      this.userForm.get('password')?.setValidators(null);
      this.userForm.get('password')?.updateValueAndValidity();
      this.userForm.get('password_confirmation')?.setValidators(null);
      this.userForm.get('password_confirmation')?.updateValueAndValidity();
    } else {
      this.userForm.reset();
      // Reset validators for new user
      this.userForm.get('password')?.setValidators([Validators.required, Validators.minLength(8)]);
      this.userForm.get('password')?.updateValueAndValidity();
      this.userForm.get('password_confirmation')?.setValidators([Validators.required]);
      this.userForm.get('password_confirmation')?.updateValueAndValidity();
    }

    this.showUserModal = true;
  }

  closeUserModal(): void {
    this.showUserModal = false;
    this.errorMessage = null;
  }

  submitUserForm(): void {
    if (this.userForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.userForm.controls).forEach(key => {
        this.userForm.get(key)?.markAsTouched();
      });
      return;
    }

    let userData: any;

    if (this.editMode && this.selectedUser) {
      // Extract only the role from the form data for updating
      userData = {
        role: this.userForm.get('role')?.value
      };

      this.userService.updateUser(this.selectedUser.id ?? 0, userData).subscribe({
        next: () => {
          this.loadUsers();
          this.closeUserModal();
        },
        error: (error) => {
          console.error("Error updating user:", error);
        }
      });
    } else {
      // For adding a new user, send all form data
      const formData = {
        name: this.userForm.get('name')?.value,
        email: this.userForm.get('email')?.value,
        password: this.userForm.get('password')?.value,
        password_confirmation: this.userForm.get('password_confirmation')?.value,
        role: this.userForm.get('role')?.value
      };

      this.userService.addUser(formData).subscribe({
        next: () => {
          this.loadUsers();
          this.closeUserModal();
        },
        error: (error) => {
          if (error.status === 422 && error.error?.errors?.email) {
            this.userForm.get('email')?.setErrors({ emailTaken: true });
          } else {
            this.errorMessage = "Le compte n'a pas été créé, réessayez plus tard.";
          }
        }
      });
    }
  }

  deleteUser(userId: number): void {
    if (confirm('Voulez-vous vraiment supprimer cet utilisateur ?')) {
      this.userService.deleteUser(userId).subscribe({
        next: () => {
          this.loadUsers();
        },
        error: (error) => {
          console.error('Error deleting user:', error);
        }
      });
    }
  }

  applyFilters(): void {
    this.filteredUsers = this.users.filter(user =>
      (this.nameFilter ? user.name.toLowerCase().includes(this.nameFilter.toLowerCase()) : true) &&
      (this.roleFilter ? user.role === this.roleFilter : true)
    );
  }

  clearFilters(): void {
    this.nameFilter = '';
    this.roleFilter = '';
    this.filteredUsers = [...this.users];
  }

  translateRole(role: string): string {
    const roleMap: { [key: string]: string } = {
      'admin': 'Administrateur',
      'receptionist': 'Réceptionniste',
      'responsable': 'Analyste',
      'director': 'Directeur',
      'client': 'Client',

    };

    return roleMap[role] || role;
  }
}
