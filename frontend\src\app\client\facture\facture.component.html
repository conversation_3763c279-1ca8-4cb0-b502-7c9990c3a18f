<head>
  <style>
    /* ✅ Styles essentiels à intégrer directement dans le HTML pour le PDF */
    .pdf-content {
      font-family: Arial, sans-serif;
      width: 100%;
      padding: 30px;
      color: #222;
    }

    .pdf-content .header, .pdf-content .styled-table {
      width: 100%;
      border-collapse: collapse;
    }

    .pdf-content .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2px solid #ddd;
      padding-bottom: 15px;
      margin-bottom: 20px;
    }

    .pdf-content .left {
      text-align: left;
      margin-left: 40px;
      font-size: 13px;
      line-height: 1.5;
    }

    .pdf-content .right {
      text-align: right;
      font-size: 13px;
    }

    .pdf-content .logo {
      width: 100px;
    }

    .pdf-content .title {
      font-size: 20px;
      text-align: center;
      margin: 20px 0;
    }

    .pdf-content .styled-table th, .pdf-content .styled-table td {
      padding: 8px;
      border: 1px solid #ddd;
      font-size: 12px;
      text-align: center;
    }

    .pdf-content .styled-table th {
      background-color: #2496d3;
      color: #fff;
    }

    .pdf-content .total {
      text-align: right;
      font-weight: bold;
      font-size: 14px;
      margin-top: 15px;
    }

    .pdf-content .footer, .pdf-content .ccp, .pdf-content .arrete {
      font-size: 12px;
      text-align: center;
      margin-top: 10px;
    }

    /* Supprimez toutes les transformations et animations pour le PDF */
    .pdf-content, .pdf-content * {
      animation: none !important;
      transform: none !important;
      opacity: 1 !important;
    }
  </style>
</head>

<div class="facture-container">
  <!-- Bouton avec icône MD -->
  <button class="btn-download" (click)="downloadFacture()">
    <i class="material-icons">file_download</i> Télécharger la facture
  </button>
  <!-- Zone de contenu -->
  <div #factureContent class="pdf-content">
    <!-- En-tête -->
    <div class="header">
      <div class="left">
        <p class="ministere">
          Ministère de L'Agriculture<br>
          et des Ressources Hydrauliques et de la pêche<br>
          Institut de la Recherche et de l'Enseignement Supérieur Agricoles<br>
          Institut National des Sciences et Technologies de la Mer
        </p>
      </div>
      <div class="right">
        <img src="/lab-logo.png" alt="Logo" class="logo"/>
        <p><strong>Date :</strong> {{ currentDate }}</p>
      </div>
    </div>

    <!-- Titre -->
    <h2 class="title">Facture N° {{ facture?.id }}/2024</h2>
    <p class="doit"><strong>Doit :</strong> <i>... /Tunis</i></p>

    <!-- Tableau des articles -->
    <table class="styled-table">
      <thead>
        <tr>
          <th>N°</th>
          <th>Objet</th>
          <th>Quantité</th>
          <th>Prix Unitaire (DT)</th>
          <th>Total (DT)</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of facture?.items; let i = index">
          <td>{{ i + 1 }}</td>
          <td>Lot: {{ item.object }}</td>
          <td>{{ item.quantity }}</td>
          <td>{{ item.unit_price | number:'1.2-2' }}</td>
          <td>{{ item.total | number:'1.2-2' }}</td>
        </tr>
      </tbody>
    </table>

    <!-- Montant total -->
    <p class="total">TOTAL : <strong>{{ facture?.total | number:'1.2-2' }} DT</strong></p>
    <p class="arrete">Arrêté la présente facture à la somme de : <strong>..............</strong></p>

    <!-- CCP -->
    <p class="ccp">Montant à virer au CCP N°17001000000273374803</p>

    <!-- Footer -->
    <div class="footer">
      <p>28 Rue 2 mars 1934-2025 - Salammbô – Tunisie | TVA: 609519G/N/N/000</p>
    </div>
  </div>
</div>
