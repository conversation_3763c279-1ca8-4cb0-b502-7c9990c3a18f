/* ✅ IMPORTATION DES POLICES */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap');

/* ✅ NAVBAR BASE STYLES */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1200;
  background: white;
  padding: 0 2rem;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.navbar:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 1;
  padding: 15px 3%;
  max-width: 1400px;
  margin: 0 auto;
}

/* ✅ LOGO + BRAND */
.navbar-brand {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
}

.navbar-logo {
  height: 45px;
  width: auto;
  max-width: 150px;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #252525;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ✅ NAVIGATION LINKS */
.nav-links {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 30px;
  margin: 0;
  padding: 0;
}

.nav-links-text {
  font-size: 22px;
  font-family: 'Poppins', sans-serif;
  color: #252525;
  position: relative;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 16px 0;
  margin: 11px 0;

}

.nav-links-text::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: #2595d2;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-links-text:hover,
.nav-links-text:focus {
  color: #2595d2;
  cursor: pointer;
}

.nav-links-text:hover::after,
.nav-links-text:focus::after {
  transform: scaleX(1);
}

/* Dropdown styles */
.dropdown-toggle-no-line {
  font-size: 22px;
  font-family: 'Poppins', sans-serif;
  color: #252525;
  position: relative;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 16px 0;
  margin: 11px 0;
}

.dropdown-toggle-no-line:hover,
.dropdown-toggle-no-line:focus {
  color: #2595d2;
  cursor: pointer;
}

.dropdown-toggle-no-line::after {
  display: none !important;
}

.dropdown-toggle-no-line:hover::after,
.dropdown-toggle-no-line:focus::after {
  transform: scaleX(0);
}

/* ✅ BUTTON STYLES */
.nav-button {
  padding: 12px 25px;
  font-weight: 600;
  background: #2595d2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.nav-button:hover {
  background: #4381d1;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(37, 149, 210, 0.3);
}

.nav-button:active {
  transform: translateY(0);
}

/* ✅ NOTIFICATION BADGE */
.notification-icon {
  position: relative;
  margin-right: 20px;
  cursor: pointer;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 18px;
  height: 18px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

/* ✅ PROFILE ICON */
.nav-icons {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-icons img {
  height: 40px;
  transition: transform 0.2s ease;
  cursor: pointer;
}

.nav-icons img:hover {
  transform: scale(1.05);
}

/* ✅ RESPONSIVE STYLES */
@media (max-width: 1024px) {
  .navbar-container {
    padding: 15px;
  }

  .nav-links {
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .navbar-toggler {
    border: none;
    outline: none;
    background: transparent;
    padding: 10px;
    cursor: pointer;
  }

  .navbar-toggler-icon {
    width: 24px;
    height: 2px;
    background: #252525;
    display: block;
    transition: all 0.3s;
  }

  .navbar-toggler-icon::before,
  .navbar-toggler-icon::after {
    content: '';
    display: block;
    width: 24px;
    height: 2px;
    background: #252525;
    transition: all 0.3s;
    margin-top: 6px;
  }

  .navbar-toggler:hover .navbar-toggler-icon {
    background: #2595d2;
  }

  .navbar-toggler:hover .navbar-toggler-icon::before,
  .navbar-toggler:hover .navbar-toggler-icon::after {
    background: #2595d2;
  }
}

@media (max-width: 576px) {
  .navbar-logo {
    height: 35px;
  }

  .brand-name {
    font-size: 1.1rem;
  }

  .nav-button {
    padding: 10px 20px;
    font-size: 13px;
  }
}