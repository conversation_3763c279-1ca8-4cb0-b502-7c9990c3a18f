<div class="results-container">
  <!-- 1. En‑tête -->
  <div class="results-header">
    <h2 class="results-title">
      <fa-icon [icon]="faThumbtack" class="title-icon"></fa-icon>
      Gestion des résultats d'analyses
    </h2>
    <button class="back-btn" (click)="goToDashboard()">
      <fa-icon [icon]="faArrowLeft"></fa-icon>
      Retour au Tableau de Bord
    </button>
  </div>

  <!-- 2. Barre de filtres -->
  <div class="filter-bar">
    <!-- Recherche libre -->
    <div class="filter-group">
      <label for="searchTerm">
        Rechercher par numéro ou client :
      </label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          id="searchTerm"
          type="text"
          class="filter-input"
          placeholder="Rechercher…"
          [(ngModel)]="searchTerm"
        />
      </div>
    </div>

    <!-- Statut des résultats -->
    <div class="filter-group">
      <label for="statusSelect">

        Statut des résultats :
      </label>

      <select
        id="statusSelect"
        class="filter-select"
        [(ngModel)]="selectedStatus"
      >
        <option value="">Tous les statuts</option>
        <option value="with_results">Avec résultats</option>
        <option value="without_results">Sans résultats</option>
      </select>
    </div>

    <!-- Filtre date -->
    <div class="filter-group">
      <label for="dateFilter">
        Date :
      </label>
      <input
        id="dateFilter"
        type="date"
        class="filter-input"
        [(ngModel)]="selectedDate"
      />
    </div>

    <button class="btn-filter-apply" (click)="onFilterChange()">
      <fa-icon [icon]="faFilter"></fa-icon>
      Appliquer les filtres
    </button>
    <button class="btn-clear" (click)="clearFilters()">
      <fa-icon [icon]="faEraser"></fa-icon>
      Effacer les filtres
    </button>
  </div>

  <!-- Tableau des demandes -->
  <table class="results-table">
    <thead>
      <tr>
        <th>Demande Numéro</th>
        <th>Nom Client</th>
        <th>Date Réception</th>
        <th>Statut des Résultats</th>
        <th class="actions-header">Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="isLoading" class="loading-row">
        <td colspan="5" class="text-center">
          <fa-icon [icon]="faSpinner" [spin]="true"></fa-icon> Chargement...
        </td>
      </tr>

      <!-- Message quand aucune demande n'est trouvée -->
      <tr *ngIf="!isLoading && filteredDemandes.length === 0" class="empty-row">
        <td colspan="5" class="text-center">
          Aucune demande trouvée.
        </td>
      </tr>

      <!-- Affichage des demandes -->
      <ng-container *ngIf="!isLoading && filteredDemandes.length > 0">
        <tr
          *ngFor="
            let demande of filteredDemandes
             | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
        >
          <td>{{ demande.demande_id }}</td>
          <td>
            {{ demande.userName || 'Chargement...' }} {{ demande.userNick }}
          </td>
          <td>{{ demande.demande_date }}</td>
          <td>
            <span class="status" [ngClass]="{'status-available': demande.result_id, 'status-unavailable': !demande.result_id}">
              {{ getResultStatus(demande) }}
            </span>
          </td>
          <td class="actions-cell">
            <div class="actions-col">
              <!-- Bouton : Voir les détails -->
              <button class="btn-detail" (click)="navigateToDemandeDetails(demande)">
                <fa-icon [icon]="faEye" size="lg"></fa-icon>
                Voir détails
              </button>

              <!-- Bouton : Ajouter/Télécharger les résultats -->
              <button
                *ngIf="!demande.result_id"
                class="btn-create"
                (click)="uploadResults(demande)"
              >
                <fa-icon [icon]="faUpload" size="lg"></fa-icon>
                Ajouter résultats
              </button>

              <button
                *ngIf="demande.result_id"
                class="btn-download"
                (click)="downloadResults(demande)"
              >
                <fa-icon [icon]="faFileAlt" size="lg"></fa-icon>
                Télécharger résultats
              </button>

              <button
                *ngIf="demande.result_id"
                class="btn-view"
                (click)="viewExcelFile(demande)"
              >
                <fa-icon [icon]="faEye" size="lg"></fa-icon>
                Visualiser Excel
              </button>

              <button
                *ngIf="demande.result_id"
                class="btn-delete"
                (click)="confirmDeleteResult(demande, $event)"
              >
                <fa-icon [icon]="faTrash" size="lg"></fa-icon>
                Supprimer
              </button>
            </div>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls (pageChange)="currentPage = $event"></pagination-controls>
</div>

<!-- Excel Viewer Modal -->
<app-excel-viewer
  *ngIf="showExcelViewer"
  [excelData]="currentExcelData"
  [fileName]="currentFileName"
  (close)="closeExcelViewer()"
></app-excel-viewer>

<!-- Delete Confirmation Modal -->
<div *ngIf="showDeleteConfirmation" class="modal-overlay">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Confirmation de suppression</h3>
    </div>
    <div class="modal-body">
      <p>Êtes-vous sûr de vouloir supprimer les résultats pour la demande <strong>{{ resultToDelete?.demande_id }}</strong>?</p>
      <p class="warning">Cette action est irréversible.</p>
    </div>
    <div class="modal-footer">
      <button class="btn-cancel" (click)="cancelDelete()" [disabled]="isDeleting">Annuler</button>
      <button class="btn-confirm" (click)="deleteResult()" [disabled]="isDeleting">
        <span *ngIf="!isDeleting">Confirmer la suppression</span>
        <span *ngIf="isDeleting">
          <fa-icon [icon]="faSpinner" [spin]="true"></fa-icon> Suppression en cours...
        </span>
      </button>
    </div>
  </div>
</div>
