import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';

import { catchError, map } from 'rxjs/operators';
import { Devi<PERSON> } from '../../../models/devis';
import { Demande } from './deviss.model';

@Injectable({
  providedIn: 'root'
})
export class DemandeService {
  
  private devisUrl = 'http://localhost:8000/api/user/devis';
  constructor(private http: HttpClient) {}

  getUserDevis(): Observable<Demande[]> {
    // Add authentication token if required
    const token = localStorage.getItem('token'); // Adjust based on your auth setup
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    return this.http.get<any>(this.devisUrl, { headers }).pipe(
      map(response => {
        // Adjust based on your backend response structure
        // If response is an array: return response as Demande[]
        // If response has 'data': return response.data as Demande[]
        return response as Demande[]; // Modify if needed
      }),
      catchError(error => {
        console.error('Error fetching devis:', error);
        return throwError(error);
      })
    );
  }
  
  
}
