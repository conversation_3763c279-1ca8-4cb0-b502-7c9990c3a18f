<!-- Loading overlay for the entire interface -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="loading-spinner-container">
    <div class="spinner-large"></div>
    <p>Chargement des données...</p>
  </div>
</div>

<!-- Conteneur principal de la facturation -->
<div class="facturation-container" [class.blur-content]="isLoading">
  <h2>Détails de la facture</h2>

  <!-- 1) Informations principales sous forme de liste non numérotée -->
  <ng-container *ngIf="facture; else noFacture">
    <ul class="facture-info-list" aria-label="Informations de la facture">
      <li>
        <fa-icon [icon]="faMoneyBill" aria-hidden="true" class="icon-match-text"></fa-icon>
        <strong>Montant total :</strong>
        <span class="info-value">{{ getTotal() }}</span>
      </li>
      <li>
        <fa-icon [icon]="faCalendar" aria-hidden="true" class="icon-match-text"></fa-icon>
        <strong>Date de création :</strong>
        <span class="info-value">{{ facture.facture_date }}</span>
      </li>
      <li class="status-item">
        <fa-icon [icon]="faTag" aria-hidden="true" class="icon-match-text"></fa-icon>
        <strong> Statut :</strong>
        <span class="payment-status-text {{ payments && payments.length > 0 ? (payments[0].status === 'approved' ? 'status-text-approved' : (payments[0].status === 'rejected' ? 'status-text-rejected' : 'status-text-pending')) : 'status-text-pending' }}" *ngIf="payments && payments.length > 0">
          <fa-icon [icon]="payments[0].status === 'pending' ? faSpinner : getStatusIcon(payments[0].status)" [animation]="payments[0].status === 'pending' ? 'spin' : undefined" class="status-icon"></fa-icon>
          {{ getStatusText(payments[0].status) }}
        </span>
        <span class="payment-status-text status-text-pending" *ngIf="!payments || payments.length === 0">

          En attente de paiement
        </span>
      </li>
    </ul>
  </ng-container>

  <!-- Si aucune facture n’est chargée -->
  <ng-template #noFacture>
    <p class="no-facture">Chargement...</p>
  </ng-template>

  <!-- Tableau des détails de la facture (analyses, quantités, etc.) -->
  <table *ngIf="facture">
    <thead>
      <tr>
        <th>N°</th>
        <th>Analyse</th>
        <th>Méthode</th>
        <th>Prix unitaire (DT)</th>
        <th>Quantité</th>
        <th>Prix total (DT)</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let devi of groupDevisData(); let i = index">
        <td>{{ i + 1 }}</td>
        <td>
          <!-- Mise en surbrillance si c'est "lipide" -->
          <span [ngClass]="{'text-primary fw-bold': devi.analyse.toLowerCase() === 'lipide'}">
            {{ devi.analyse }}
          </span>
        </td>
        <td>{{ devi.methode }}</td>
        <td class="text-primary fw-bold">{{ devi.prix_unitaire }}</td>
        <td class="text-primary fw-bold">{{ devi.quantite }}</td>
        <td class="text-primary fw-bold">{{ devi.prix_total }}</td>
      </tr>
      <!-- Ligne affichant le total général -->
      <tr class="total-row">
        <td colspan="5" class="text-end">Montant total (DT)</td>
        <td class="text-primary">{{ getTotal() }}</td>
      </tr>
    </tbody>
  </table>

  <!-- Section des paiements -->
  <div class="payments-section" *ngIf="facture">
    <h3>Justificatifs de Paiements</h3>

    <!-- Affichage du chargement -->
    <div *ngIf="isLoadingPayments" class="loading-payments">
      <fa-icon [icon]="faSpinner" [animation]="'spin'"></fa-icon> Chargement des paiements...
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="paymentError" class="payment-error">
      <fa-icon [icon]="faExclamationTriangle"></fa-icon> {{ paymentError }}
    </div>
   
    <!-- Tableau des paiements -->
    <div *ngIf="!isLoadingPayments && !paymentError && payments.length > 0">
      <table class="payments-table">
        <thead>
          <tr>
            <th>Date payment</th>
            <th>Montant (DT)</th>
            <th>Statut</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let payment of payments">
          <tr>
            <td>{{ payment.payment_date | date:'dd/MM/yyyy' }}</td>
            <td>{{ payment.amount }}</td>
            <td>
              <span class="payment-status" [ngClass]="getStatusClass(payment.status)">
                <fa-icon [icon]="payment.status === 'pending' ? faSpinner : getStatusIcon(payment.status)" [animation]="payment.status === 'pending' ? 'spin' : undefined" class="status-icon"></fa-icon>
                {{ getStatusText(payment.status) }}
              </span>
              <!-- Affichage de la raison du rejet si le statut est 'rejected' -->
              
            </td>
            <td class="actions-cell">
              <!-- View button -->
              <button
                class="btn-action view"
                [class.active]="(showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id)"
                (click)="viewPaymentProof(payment.payment_proof, payment.id)"
                title="{{ (showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id) ? 'Masquer le justificatif' : 'Voir le justificatif' }}"
              >
                <fa-icon [icon]="(showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id) ? faEyeSlash : faEye"></fa-icon>
              </button>

              <!-- Download button -->
              <button
                class="btn-action download"
                (click)="downloadPaymentProof(payment.payment_proof)"
                title="Télécharger le justificatif"
              >
                <fa-icon [icon]="faDownload"></fa-icon>
              </button>
            </td>
          </tr>

          <!-- Preview row that appears under the payment row -->
          <tr *ngIf="showPreview && previewPaymentId === payment.id" class="preview-row">
            <td colspan="4" class="preview-cell">
              <div class="preview-container">
                <!-- Loading spinner -->
                <div *ngIf="isLoadingPreview" class="loading-spinner">
                  <div class="spinner"></div>
                  <p>Chargement de l'aperçu...</p>
                </div>

                <!-- Error message -->
                <div *ngIf="!isLoadingPreview && previewError" class="error-message">
                  <p>{{ previewError }}</p>
                  <button class="btn-retry" (click)="viewPaymentProof(payment.payment_proof, payment.id)">
                    <fa-icon [icon]="faSync"></fa-icon> Réessayer
                  </button>
                </div>

                <!-- Preview content -->
                <div *ngIf="!isLoadingPreview && !previewError" class="preview-content">
                  <!-- File preview -->
                  <div class="file-preview">
                    <h4>Aperçu du fichier</h4>

                    <!-- Image preview -->
                    <div *ngIf="fileType === 'image'" class="image-container">
                      <img
                        [src]="currentPayment?.payment_proof_url || currentPayment?.direct_url || currentPreviewUrl"
                        alt="Aperçu du justificatif"
                        class="preview-image"
                        (error)="handleImageError($event)"
                      >
                      <p class="file-name">{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : '' }}</p>
                    </div>

                    <!-- PDF preview -->
                    <div *ngIf="fileType === 'pdf'" class="pdf-preview">
                      <div class="pdf-icon">
                        <fa-icon [icon]="faFilePdf" size="3x"></fa-icon>
                      </div>
                      <p>Fichier PDF sélectionné</p>
                      <p>{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : 'Fichier PDF' }}</p>
                      <button class="btn-open-pdf" (click)="openPdfInNewTab()">
                        <fa-icon [icon]="faExternalLinkAlt"></fa-icon> Ouvrir le PDF
                      </button>
                    </div>

                    <!-- Unknown file type -->
                    <div *ngIf="fileType === 'other'" class="unknown-file">
                      <div class="file-icon">
                        <fa-icon [icon]="faFile" size="3x"></fa-icon>
                      </div>
                      <p>Type de fichier non reconnu</p>
                      <p>{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : 'Fichier inconnu' }}</p>

                      <!-- Direct download button -->
                      <button class="btn-download" (click)="downloadPaymentProof(currentPreviewPath || '')">
                        <fa-icon [icon]="faDownload"></fa-icon> Télécharger le fichier
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </ng-container>
        </tbody>
      </table>
    </div>

    <!-- Direct Image Preview Section -->
    <div *ngIf="directImagePreview" class="direct-image-preview" style="margin-top: 30px; text-align: center; padding: 20px; background-color: #f9f9f9; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

      <div style="position: relative; display: inline-block;">
        <img
          [src]="directImageUrl"
          alt="Aperçu du justificatif"
          style="max-width: 100%; max-height: 600px; border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);"
          (error)="handleDirectImageError($event)"
        >
        <button
          (click)="closeDirectImagePreview()"
          style="position: absolute; top: 10px; right: 10px; background-color: rgba(255,255,255,0.8); border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='rgba(240,240,240,0.9)'" onmouseout="this.style.backgroundColor='rgba(255,255,255,0.8)'"
          title="Fermer l'aperçu"
        >
          <fa-icon [icon]="faTimes" class="icon-match-text"></fa-icon>
        </button>
      </div>
      <p *ngIf="currentImageFileName" style="margin-top: 10px; font-style: italic;">{{ currentImageFileName }}</p>
    </div>

    <!-- Message si aucun paiement -->
    <div *ngIf="!isLoadingPayments && !paymentError && payments.length === 0" class="no-payments">
      <fa-icon [icon]="faInfoCircle" class="icon-match-text"></fa-icon> Aucun paiement n'a été enregistré pour cette facture.
    </div>
  </div>

  <!-- Zone d’action : bouton d’impression -->
  <div class="actions" *ngIf="facture">
    <button (click)="printFacture()">
      <fa-icon [icon]="faPrint" class="print-icon"></fa-icon>
      Imprimer la facture
    </button>
  </div>
</div>

<!-- Notification de soumission -->
<div *ngIf="showNotification" class="notification-overlay">
  <div class="notification">
    <div class="notification-icon" [style.color]="notificationColor">{{ notificationIcon }}</div>
    <div class="notification-message">{{ notificationMessage }}</div>
  </div>
</div>
