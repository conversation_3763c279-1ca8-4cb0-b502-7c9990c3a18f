<!-- src/app/components/derogation/derogation.component.html -->
<!-- Main Loading Spinner Overlay -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="spinner"></div>
</div>

<div class="demandes-container" [class.content-hidden]="isLoading">
    <!-- ✅ Error Message -->
    <div *ngIf="errorMessage && !isLoading" class="error">{{ errorMessage }}</div>

    <!-- ✅ Demande Details -->
    <div *ngIf="demande && !isLoading">
      <h2>Détails de la Demande</h2>
      <p><strong>Demande N°:</strong> {{ demande?.demande_id }}</p>
      <p><strong>Date de réception:</strong> {{ demande?.demande_date }}</p>
      <p><strong>Nom du client:</strong> {{ demande?.user_name }} {{ demande?.user_name }}({{ demande?.user_email }})</p>
      <p><strong>Mode de règlement:</strong> {{ demande?.mode_reglement || 'Non spécifié' }}</p>

      <div class="status-container">
        <p><strong>Statut:</strong>
          <span class="status-tag pending" *ngIf="demande?.status === 'pending'">🟡 En attente</span>
          <span class="status-tag ongoing" *ngIf="demande?.status === 'ongoing_validation'">🔵 En cours de validation</span>
          <span class="status-tag derogation" *ngIf="demande?.status === 'derogation'">🟠 En cours de validation avec dérogation</span>
          <span class="status-tag derogation" *ngIf="demande?.status === 'rejected'">
            ❌ Demande rejetée
          </span>
          <span class="status-tag valid" *ngIf="demande?.status === 'valid'">
            ✅ Demande validée
          </span>
        </p>
      </div>

      <!-- ✅ Samples Table -->
      <div *ngIf="demande.samples?.length; else noSamples">
        <table>
          <thead>
            <tr>
              <th>Identification Échantillon</th>
              <th>Référence</th>
              <th>Nature</th>
              <th>Provenance</th>
              <th>Masse (G)</th>
              <th>État</th>
              <th>Analyses Demandées</th>
              <th>Délai Souhaité</th>
              <th>Analyse Souhaitée</th>
              <th>Lot</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let sample of demande.samples">
              <td>{{ sample.identification_echantillon }}</td>
              <td>{{ sample.reference }}</td>
              <td>{{ sample.nature_echantillon }}</td>
              <td>{{ sample.provenance }}</td>
              <td>{{ sample.masse_echantillon | number:'1.0-0' }}</td>
              <td>{{ sample.etat }}</td>
              <td>{{ sample.analyses_demandees?.join(', ') }}</td>
              <td>{{ sample.delai_souhaite }}</td>
              <td>{{ sample.analyse_souhaite }}</td>
              <td>{{ sample.lot }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <ng-template #noSamples>
        <p>Aucun échantillon disponible pour cette demande.</p>
      </ng-template>

      <!-- ✅ Derogation Details (Shown if available) -->
      <div *ngIf="derogationDetails " class="derogation-details">
        <h3>Détails des Dérogations</h3>

        <table>
          <thead>
            <tr>

              <th>Échantillon</th>
              <th>Masse (g)</th>
              <th>Description Écart</th>
              <th>Réponse Client</th>

              <th>Date Demande</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let derogation of derogationDetails.derogations">

              <td>{{ derogation.identification_echantillon }}</td>
              <td>{{ derogation.masse_echantillon | number:'1.0-0' }}</td>
              <td>{{ derogation.description_ecart }}</td>
              <td>{{ derogation.reponse_du_client }}</td>

              <td>{{ derogation.date_demande_client | date:'dd/MM/yyyy' }}</td>
            </tr>
          </tbody>
        </table>
      </div>



          <div *ngIf="demande?.status === 'derogation' && demande?.status !== 'valid'"
               class="validation-buttons">
            <button class="btn-validation" (click)="validateDemande()">
              <fa-icon [icon]="faCheck" style="margin-right: 10px;"></fa-icon>Valider
            </button>
            <button class="btn-derogation" (click)="rejectDemande()">
              <fa-icon [icon]="faTimes" style="margin-right: 10px;"></fa-icon>Rejeter
            </button>

    </div>
    </div>
  </div>

<!-- Loading Spinner Overlay for Modal Actions -->
<div *ngIf="showLoadingModal" class="loading-overlay">
  <div class="loading-popup">
    <div class="spinner"></div>
    <h3>{{ loadingMessage }}</h3>
  </div>
</div>

<!-- Notification de soumission -->
<div *ngIf="showNotification" class="notification-overlay">
  <div class="notification">
    <div class="notification-icon" [style.color]="notificationColor">{{ notificationIcon }}</div>
    <div class="notification-message">{{ notificationMessage }}</div>
  </div>
</div>