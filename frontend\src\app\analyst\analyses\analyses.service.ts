import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Sample } from '../../../models/sample.model';

@Injectable({
  providedIn: 'root',
})
export class AnalysesService {
  private apiUrl = 'http://127.0.0.1:8000/api/samples'; // Laravel API URL

  constructor(private http: HttpClient) {}

  // Get all samples
  getSamples(): Observable<Sample[]> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.get<Sample[]>(this.apiUrl, { headers });
  }

  // Create a new sample
  createSample(sample: Sample): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.post<any>(this.apiUrl, sample, { headers });
  }

  // Get a single sample by ID
  getSampleById(id: number): Observable<Sample> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.get<Sample>(`${this.apiUrl}/${id}`, { headers });
  }

  // Update a sample
  updateSample(id: number, sample: Partial<Sample>): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.put<any>(`${this.apiUrl}/update/${id}`, sample, { headers });
  }

  // Delete a sample
  deleteSample(id: number): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    });
    return this.http.delete<any>(`${this.apiUrl}/${id}`, { headers });
  }
}
