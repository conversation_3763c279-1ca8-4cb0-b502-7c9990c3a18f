@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Main Container */
.notifications-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}

/* ✅ Title Styling */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Table Styling */
.notification-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ Table Headers */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Table Rows */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Row Hover Effect */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
  cursor: pointer;
}

/* ✅ Unread Notification Styling */
.unread {
  background: rgba(255, 193, 7, 0.1);
  font-weight: bold;
  color: #d39e00;
}

/* ✅ New Tag */
.new-tag {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  text-transform: uppercase;
}

/* ✅ Read Tag */
.read-tag {
  background: rgba(150, 150, 150, 0.1);
  color: #6c757d;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  text-transform: uppercase;
}

/* ✅ No Notifications Message */
.no-notifications {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}

/* ✅ Pagination Controls */
pagination-controls {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

pagination-controls .ng-star-inserted {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  padding: 8px 15px;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

pagination-controls .ng-star-inserted:hover {
  background-color: #2496d3;
  color: white;
}

/* ✅ Success/Error Messages */
.message-container {
  margin: 20px 0;
  padding: 0;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  font-weight: 500;
  animation: fadeIn 0.5s ease-in-out;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  font-weight: 500;
  animation: fadeIn 0.5s ease-in-out;
}

/* ✅ Notification Actions */
.notification-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.btn-remove-all {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-remove-all:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-2px);
}

.btn-remove-all:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-remove-all.loading {
  position: relative;
  color: transparent;
}

.btn-remove-all.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ✅ Actions Column */
.actions-column {
  width: 120px;
  text-align: center;
}

.btn-remove {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.btn-remove:hover:not(:disabled) {
  background-color: #c82333;
  transform: scale(1.05);
}

.btn-remove:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-remove.loading {
  position: relative;
}

.btn-remove.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-top: 1px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ✅ Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ✅ Glow Animation */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}
