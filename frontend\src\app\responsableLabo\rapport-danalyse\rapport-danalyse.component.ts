import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule,ReactiveFormsModule } from '@angular/forms';
import { CommonModule, DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';

import { RapportsService } from '../reports/rapports.service';
import { DemandeService } from '../../receptionniste/demandes/demande.service';
import { ModalAccreditationComponent } from '../../receptionniste/modal-accreditation/modal-accreditation.component';
import { ActivatedRoute } from '@angular/router';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPen, faCheck, faPaperPlane, faPrint } from '@fortawesome/free-solid-svg-icons';

interface LabInfo {
  nom: string;
  adresse: string;
  contact: string;
}

@Component({
  selector: 'app-rapport-danalyse',
  standalone: true,
  imports: [FormsModule, CommonModule, DatePipe, ReactiveFormsModule, FontAwesomeModule],
  templateUrl: './rapport-danalyse.component.html',
  styleUrls: ['./rapport-danalyse.component.css']
})
export class RapportDanalyseComponent implements OnInit {
  // Font Awesome icons
  faPen = faPen;
  faCheck = faCheck;
  faPaperPlane = faPaperPlane;
  faPrint = faPrint;

  reportData: any = null;
  clientInfo: any = [];
  samples: any[] = [];
  requestedAnalyses: any[] = [];
  analysisResults: any[] = [];
  hasAccreditedAnalysis: boolean = false;
  hideButtons: boolean = false;
  userCache: { [key: number]: any } = {}; // Stores full user data
  demandeSamples: any[] = [];
  editingIndex: number | null = null;
  analyses_accredite: number | null = null;
  delaiSouhaite: string | null = null;
  areFieldsEmpty: boolean = true; // Track if report fields are empty

  // Loading and notification properties
  showLoadingModal = false;
  loadingMessage = 'Traitement en cours...';
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';
  labInfo: LabInfo = {
    nom: 'Biotechnologie Bleue et Bioproduits Aquatiques - B³Aqua',
    adresse: 'INSTIM Port de Pêche La Goulette',
    contact: '216 71 735 848'
  };
  isEditing: { [key: number]: boolean } = {}; // ✅ Track edit state per row
  analysisForm!: FormGroup; // ✅ Reactive form
   reportid: number | null = null;
ficheTransmissionId: number | null = null;
  constructor(
    public dialog: MatDialog, private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private reportService: RapportsService,
    private demandeService: DemandeService,
   private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.reportid = params['id'];
      if (this.reportid) {
        this.loadRapportDetails(this.reportid);
      }
    }); // Load a report when the component initializes
  }

  /**
   * Fetches the report details and then retrieves the associated user details.
   */
  loadRapportDetails(rapportId: number) {
    this.reportService.getRapportDetails(rapportId).subscribe({
      next: (data) => {
        this.reportData = data;
        console.log('Report data loaded:', this.reportData);

        // Check if status is in the format returned by the API or needs translation
        if (this.reportData.status === 'not_sent') {
          this.reportData.status = 'not_sent';
        }

        this.samples = this.extractSamples(data.analyses);
        this.analysisResults = data.analyses;
        this.requestedAnalyses = this.groupAnalysesBySample(data.analyses);
        this.hasAccreditedAnalysis = this.analysisResults.some(r => r.isAccredited);
        this.initializeForm();
        this.checkIfFieldsEmpty(); // Check if fields are empty
        if (this.reportData.demande_id) {
          this.fetchDemandeUserDetails(this.reportData.demande_id); // ✅ Fetch user details via demande
        } else {
          console.warn('No demande_id found in report.');
        }
      },
      error: () => {
        console.error(`Error fetching report details for ID ${rapportId}`);
      }
    });
  }
  initializeForm() {
    this.analysisForm = this.fb.group({});
    this.analysisResults.forEach((result, index) => {
      this.analysisForm.addControl(`mesurande_${index}`, new FormControl({ value: result.mesurande || '', disabled: true }));
      this.analysisForm.addControl(`unite_${index}`, new FormControl({ value: result.unite || '', disabled: true }));
      this.analysisForm.addControl(`limite_acceptabilite_${index}`, new FormControl({ value: result.limite_acceptabilite || '', disabled: true }));
      this.analysisForm.addControl(`date_analyse_${index}`, new FormControl({ value: result.date_analyse || '', disabled: true }));
      this.isEditing[index] = false; // Initially, all rows are not in edit mode
    });
  }
  toggleEdit(index: number): void {
    this.isEditing[index] = !this.isEditing[index]; // ✅ Toggle edit mode

    const controls = [
      `mesurande_${index}`,
      `unite_${index}`,
      `limite_acceptabilite_${index}`,
      `date_analyse_${index}`
    ];

    controls.forEach(controlName => {
      const control = this.analysisForm.get(controlName);
      if (control) {
        this.isEditing[index] ? control.enable() : control.disable();
      }
    });

    this.cdr.detectChanges(); // ✅ Force UI update
  }
  updateRow(index: number) {
    const updatedData: any = {};
    const analysis = this.analysisResults[index];

    const fields = [
      'mesurande',
      'unite',
      'limite_acceptabilite',
      'date_analyse'
    ];

    fields.forEach(field => {
      const formField = `${field}_${index}`;
      if (this.analysisForm.controls[formField].value !== analysis[field]) {
        updatedData[field] = this.analysisForm.controls[formField].value;
      }
    });

    if (Object.keys(updatedData).length === 0) {

      this.toggleEdit(index); // Lock the fields again
      return;
    }

    // ✅ Send update request
    this.reportService.updateAnalysis(analysis.id, updatedData).subscribe({
      next: () => {

        Object.assign(this.analysisResults[index], updatedData); // ✅ Update UI
        this.toggleEdit(index); // Lock the fields again
        this.checkIfFieldsEmpty(); // Check if fields are still empty after update
      },
      error: (err) => {
        console.error('Erreur de mise à jour du rapport analyse:', err);
        alert("Échec de la mise à jour.");
      }
    });
  }

  /**
   * Fetches the `demande` details using `demande_id` to retrieve `user_id`, then fetches user details.
   */
  fetchDemandeUserDetails(demande_id: number): void {
    if (!demande_id) {
      this.clientInfo = { name: '⏳ Chargement...', email: '', phone: '', address: '' };
      return;
    }

    this.demandeService.getDemandeBy(demande_id).subscribe({
      next: (response) => {
        const demande = response;
        if (demande && demande.user_id) {
          this.fetchUserDetails(demande.user_id);
        } else {
          console.warn(`No user_id found in demande ${demande_id}`);
          this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
        }
        this.delaiSouhaite = demande.delai_souhaite || null;
        this.analyses_accredite = demande.analyses_accredite ?? null;
        console.log('accred', this.analyses_accredite);
        console.log('delai', this.delaiSouhaite);
      },
      error: () => {
        console.error(`Error fetching demande ${demande_id}`);
        this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
      }
    });
  }

  /**
   * Fetches user details using `user_id`, caches the data, and updates `clientInfo`.
   */
  fetchUserDetails(user_id: number): void {
    this.demandeService.getUserDetails(user_id).subscribe({
      next: (response) => {
        this.clientInfo=response;
        console.log('Client info:', this.clientInfo);
          this.cdr.detectChanges(); // ✅ Force Angular to detect changes

      },
      error: () => {
        console.error(`Error fetching user ${user_id}`);

      }
    });
  }



  private groupAnalysesBySample(analyses: any[]) {
    const grouped: { [key: string]: any[] } = {};
    analyses.forEach((analysis) => {
      if (!grouped[analysis.code_echantillon]) {
        grouped[analysis.code_echantillon] = [];
      }
      grouped[analysis.code_echantillon].push(analysis);
    });

    return Object.entries(grouped).map(([key, value]) => ({
      code_echantillon: key,
      analyses: value
    }));
  }

  /**
   * Extracts unique samples from the analyses.
   */
  private extractSamples(analyses: any[]) {
    const uniqueSamples: { [key: string]: any } = {};
    analyses.forEach((analysis) => {
      const code = analysis.code_echantillon;
      if (!uniqueSamples[code] && analysis.sample) {
        uniqueSamples[code] = {
          identification_echantillon: code,
          reception_date: this.reportData?.creation_date, // Using report creation_date as a proxy; adjust if a specific sample reception date exists
          nature_echantillon: analysis.sample.nature_echantillon,
          provenance: analysis.sample.provenance,
          origine_prelevement: analysis.sample.origine_prelevement,
          date_prelevement: analysis.sample.date_prelevement,
          site: analysis.sample.site,
          lot: analysis.sample.lot,
          nom_preleveur: analysis.sample.nom_preleveur,
          reference: analysis.sample.reference,
        };
      }
    });
    return Object.values(uniqueSamples);
  }
  /**
   * Opens the accreditation confirmation modal.
   */


  /**
   * Formats a date string to DD/MM/YYYY format
   */
  formatDate(date: string): string {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  /**
   * Generates and prints a formatted report
   */
  /**
   * Checks if any of the required fields in the report are empty
   */
  checkIfFieldsEmpty(): void {
    if (!this.analysisResults || this.analysisResults.length === 0) {
      this.areFieldsEmpty = true;
      return;
    }

    // Check if any analysis has empty required fields
    this.areFieldsEmpty = this.analysisResults.some(result => {
      return !result.mesurande || !result.unite || !result.date_analyse;
    });
  }

  /**
   * Sends the report to the client
   */
  envoyerRapport(): void {
    if (!this.reportid) {
      console.error('No report ID available to send.');
      return;
    }

    if (this.areFieldsEmpty) {
      // Show notification for empty fields
      this.notificationMessage = "Veuillez remplir tous les champs requis avant d'envoyer le rapport.";
      this.notificationIcon = '⚠️';
      this.notificationColor = '#ffc107';
      this.showNotification = true;

      // Hide notification after 5 seconds
      setTimeout(() => {
        this.showNotification = false;
      }, 4000);
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Envoi du rapport...';

    this.reportService.sendRapport(this.reportid).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log('Rapport envoyé:', response);

        // Show success notification
        this.notificationMessage = "Le rapport a été envoyé avec succès!";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Update the status in the UI
        if (this.reportData) {
          this.reportData.status = 'sent';
          console.log('Status updated to:', this.reportData.status);
        }

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      },
      error: (err) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error('Erreur lors de l\'envoi du rapport:', err);

        // Show error notification
        this.notificationMessage = "Échec de l'envoi du rapport. Veuillez réessayer.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }

  printRapport(): void {
    if (!this.reportData) {
      console.error('No report data available to print.');
      return;
    }

    const formattedDate = this.formatDate(this.reportData.creation_date);

    const printHtml = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Rapport d'Analyse</title>
        <style>
          @media print {
            @page {
              size: A4;
              margin: 5mm 5mm;
            }
            body {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
          }
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12pt;
          }
          .container {
            width: 100%;
            margin: 0 auto;
            padding: 10px;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-top: 4px double black;
            border-bottom: 4px double black;
            padding: 10px 0;
          }
          .logo-left {
            width: 100px;
            height: auto;
          }
          .log {
            width: 130px;
            height: auto;
            padding-bottom:10px;
          }
          .logo-right {
            width: 100px;
            height: auto;
          }
          .header-content {
            display: flex;
            align-items: center;
          }
          .text-content {
            margin-left: 10px;
          }
          .bold {
            font-weight: bold;
          }
          .italic {
            font-style: italic;
          }
          .centered {
            text-align: center;
            margin-bottom: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .double-border {
            border: 2px solid black;
          }
          .double-border td {
            padding: 8px;
            border: 1px solid black;
          }
          .results-table {
            border: 2px solid black;
          }
          .results-table th, .results-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
          }
          .results-table th {
            background-color: #f2f2f2;
          }
          .footer {
            margin-top: 30px;
          }
          .small-text {
            font-size: 10pt;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="centered">
            <p class="bold" style="font-size: 13px">RÉPUBLIQUE TUNISIENNE</p>
            <p class="bold" style="font-size: 13px">Ministère de l'Agriculture et des Ressources Hydrauliques</p>
            <p class="italic" style="font-size: 12px" >Institution de la Recherche et de l'Enseignement Supérieur Agricoles</p>
            <p class="italic" style="font-size: 12px">Institut National des Sciences et Technologies de la Mer</p>
          </div>

          <!-- Header section with two divs side by side below -->
          <div class="header">
            <div>
              <img src="${window.location.origin}/kls.png" alt="Logo Left" class="logo-left" style=" width: 120px;
            height: auto;">

            </div>
            <div class="header-content">
              ${this.analyses_accredite === 1 ?
                `<img src="${window.location.origin}/Image2.png" alt="Logo Right" class="logo-right" style="margin-right: 30px; width: 110px;
            height: auto;">` :
                `<div class="logo-right" style="margin-right: 30px;"></div>`}
              <div class="text-content">
                <p class="bold">CODE: PE/01-PET/09</p>
                <p class="bold">VERSION: 07</p>
                <p class="bold">DATE: ${formattedDate}</p>
                <p class="bold">PAGE: 1/1</p>
              </div>
            </div>
          </div>
          <p class="bold" style="text-align: center;">RAPPORT D'ANALYSE N° ${this.reportData.rapport_id}</p>
          <!-- Client Information -->
          <table class="double-border">
            <tr>
              <td style="width: 50%;">
                <p><strong>Client</strong></p>
                <p>Nom: ${this.clientInfo.name || 'Non spécifié'}</p>
                <p>Email: ${this.clientInfo.email || 'Non spécifié'}</p>
                <p>Téléphone: ${this.clientInfo.phone || 'Non spécifié'}</p>
                <p>Adresse: ${this.clientInfo.adress || 'Non spécifié'}</p>
              </td>
              <td style="width: 50%;">
                <p><strong>Laboratoire</strong></p>
                <p>Nom: ${this.labInfo.nom}</p>
                <p>Adresse: ${this.labInfo.adresse}</p>
                <p>Contact: ${this.labInfo.contact}</p>
              </td>
            </tr>
          </table>

          <!-- Sample Description -->
          <p class="bold">DESCRIPTION DE L'ÉCHANTILLON</p>
          <table class="results-table">
            <thead>
              <tr>
                <th>Code échantillon</th>
                <th>Date de réception</th>
                <th>Nature</th>
                <th>Provenance</th>
                <th>Origine du prélèvement</th>
                <th>Date de prélèvement</th>
                <th>Site</th>
                <th>Nom du préleveur</th>
                <th>Lot</th>
                <th>Référence</th>
              </tr>
            </thead>
            <tbody>
              ${this.samples.map(sample => `
                <tr>
                  <td>${sample.identification_echantillon || '-'}</td>
                  <td>${sample.reception_date ? this.formatDate(sample.reception_date) : '-'}</td>
                  <td>${sample.nature_echantillon || '-'}</td>
                  <td>${sample.provenance || '-'}</td>
                  <td>${sample.origine_prelevement || '-'}</td>
                  <td>${sample.date_prelevement ? this.formatDate(sample.date_prelevement) : '-'}</td>
                  <td>${sample.site || '-'}</td>
                  <td>${sample.nom_preleveur || '-'}</td>
                  <td>${sample.lot || '-'}</td>
                  <td>${sample.reference || '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Analysis Results -->
          <p class="bold">RÉSULTATS D'ANALYSES</p>
          <table class="results-table">
            <thead>
              <tr>
                <th>Code échantillon</th>
                <th>Paramètre</th>
                <th>Mesurande</th>
                <th>Unité</th>
                <th>Limite d'acceptabilité</th>
                <th>Méthode d'Analyse utilisée</th>
                <th>Date d'Analyse</th>
              </tr>
            </thead>
            <tbody>
              ${this.analysisResults.map(result => `
                <tr>
                  <td>${result.code_echantillon || '-'}</td>
                  <td>${result.parametre || '-'}</td>
                  <td>${result.mesurande || '-'}</td>
                  <td>${result.unite || '-'}</td>
                  <td>${result.limite_acceptabilite || '-'}</td>
                  <td>${result.methode_analyse_utilisee || '-'}</td>
                  <td>${result.date_analyse ? this.formatDate(result.date_analyse) : '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Footer -->
          <div class="footer">
            <p class="small-text" style="text-align: left;">(*) La valeur trouvée est inférieure à la limite de quantification de la méthode</p>
            <p class="bold">Date: ${formattedDate}</p>
            <p style="text-align: center; font-weight: bold; font-size: 20px; padding-bottom: 90px;">Responsable du Laboratoire</p>
            <p style="text-align: center; font-weight: bold; font-size: 15px;">Fin de rapport</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHtml);
      printWindow.document.close();
      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
          // printWindow.close(); // Uncomment if you want the window to close after printing
        }, 500);
      };
    } else {
      console.error('Failed to open print window. Please allow pop-ups.');
    }
  }
}
