@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* =========================
   1. Main Container
   ========================= */
.reclamations-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  margin-top: 40px;
  margin-bottom: 40px;
}

/* =========================
   2. Title Section
   ========================= */
.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  gap: 15px;
}

.circle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #2496d3, #0a6ebd);
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(36, 150, 211, 0.3);
}

.circle-icon fa-icon {
  font-size: 24px;
  color: white;
}

.main-title {
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 0;
  border-bottom: 2px solid #2496d3;
  padding-bottom: 8px;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

/* =========================
   3. Action Buttons
   ========================= */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.btn-create {
  background: linear-gradient(to right, #2496d3, #0a6ebd);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(36, 150, 211, 0.2);
}

.btn-create:hover {
  background: linear-gradient(to right, #0a6ebd, #2496d3);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(36, 150, 211, 0.3);
}

/* =========================
   4. Filter Section
   ========================= */
.filter-section {
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 75%;
  margin-left: auto;
  margin-right: auto;
}

.filter-header {
  background: #e9ecef;
  padding: 12px 20px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.3s;
}

.filter-header:hover {
  background: #dee2e6;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.filter-content.show {
  max-height: 300px;
  padding: 20px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.filter-group {
  flex: 1;
  min-width: 200px;
  text-align: left;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
}

.input-with-icon {
  position: relative;
}

.input-with-icon fa-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.input-with-icon input {
  padding-left: 35px;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.btn-apply,
.btn-reset {
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
}

.btn-apply {
  background: #2496d3;
  color: white;
}

.btn-apply:hover {
  background: #0a6ebd;
}

.btn-reset {
  background: #6c757d;
  color: white;
}

.btn-reset:hover {
  background: #5a6268;
}

/* =========================
   5. Loading & No Results
   ========================= */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(36, 150, 211, 0.2);
  border-top: 5px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-results {
  padding: 30px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
  font-size: 18px;
  color: #6c757d;
}

/* =========================
   6. Table Styles
   ========================= */
.table-responsive {
  overflow-x: auto;
  margin-bottom: 20px;
}

.styled-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.styled-table thead th {
  background: #2496d3;
  color: white;
  padding: 14px;
  text-transform: uppercase;
  font-weight: bold;
  border: none;
  text-align: center;
}

.styled-table td {
  border: 1px solid #ddd;
  padding: 14px;
  text-align: center;
  font-size: 16px;
  vertical-align: middle;
}

.styled-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

.action-cell {
  width: 150px;
}

.btn-details {
  background: #2496d3;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  transition: all 0.3s;
}

.btn-details:hover {
  background: #0a6ebd;
  transform: translateY(-2px);
}

/* Status Tags */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.in-progress {
  background: rgba(13, 110, 253, 0.1);
  color: #0d6efd;
}

.resolved {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

/* =========================
   7. Pagination
   ========================= */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* =========================
   8. Responsive Design
   ========================= */
@media (max-width: 768px) {
  .reclamations-container {
    padding: 30px 15px;
  }
  
  .filter-section {
    width: 100%;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-group {
    min-width: 100%;
  }
  
  .filter-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .btn-apply, .btn-reset {
    width: 100%;
    justify-content: center;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
