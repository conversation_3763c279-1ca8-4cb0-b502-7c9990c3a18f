<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // The recipient of the notification
            $table->string('title'); // Notification title
            $table->text('message'); // Notification message
            $table->string('type')->nullable(); // Type of notification (ex: 'demande', 'derogation')
            $table->unsignedBigInteger('demande_id')->nullable(); // Related demande if applicable
            $table->boolean('is_read')->default(false); // Mark as read/unread
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};