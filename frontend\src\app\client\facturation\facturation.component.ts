import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-facturation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './facturation.component.html',
  styleUrls: ['./facturation.component.css']
})
export class FacturationComponent {
  factures = [
    { id: 101, date: '2024-03-01', total: 120.5,  status: 'Payée' },
    { id: 102, date: '2024-03-10', total: 250,    status: 'En attente' },
    { id: 103, date: '2024-03-15', total: 89.99,  status: 'Payée' },
    { id: 104, date: '2024-04-05', total: 299.99, status: 'Non payée' },
    { id: 105, date: '2024-05-12', total: 499,    status: 'En attente' },
    { id: 106, date: '2024-05-19', total: 999,    status: 'Payée' }
  ];

  constructor(private router: Router) {}

  /**
   * ✅ Naviguer vers les détails de la facture sélectionnée
   */
  voirFacture(id: number) {
    this.router.navigate(['/client/facture', id]);
  }
}
