<div class="devis-container">
  <h2> Résultats d'Analyses</h2>


  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro:</label>
      <input
        type="text"
        id="search"
        placeholder="Rechercher..."
        [(ngModel)]="searchTerm"
        (input)="onFilterChange()"
        class="filter-input"
      />
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="validated">Valid<PERSON></option>
        <option value="rejected">Rejetés</option>
        <option value="pending">En attente</option>
      </select>
    </div>
    <button class="btn-clear" (click)="clearFilters()">
      <fa-icon [icon]="faEraser"></fa-icon> Effacer les filtres
    </button>
  </div>

  <!-- Affichage conditionnel du tableau -->
  <table class="styled-table" *ngIf="!isLoading && !hasError">
    <thead>
      <tr>
        <th style="width: 15%;">Demande Numéro</th>
        <th style="width: 15%;">Date de réception</th>
        <th style="width: 20%;">Action</th>
      </tr>
    </thead>
    <tbody>
      <!-- Message quand aucun rapport n'est trouvé -->
      <tr *ngIf="!isLoading && filteredReports.length === 0" class="empty-row">
        <td colspan="6" class="text-center">
          Aucun résultat trouvé.
        </td>
      </tr>

      <!-- Affichage des rapports -->
      <ng-container *ngIf="!isLoading && filteredReports.length > 0">
        <tr
          *ngFor="
            let report of filteredReports
            | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
          class="table-row"
        >
          <td>{{ report.demande_id }}</td>
          <td>{{ report.sent_at | date:'dd/MM/yyyy' }}</td>
          <td class="action-cell">
            <button
              class="btn-details"
              (click)="viewRapportDetails(report.demande_id)"
            >
              <fa-icon [icon]="faEye" style="margin-right: 8px; font-size: 16px;"></fa-icon>Voir détails
            </button>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    *ngIf="!isLoading && filteredReports.length > 0"
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>

  <!-- Message de chargement -->
  <div class="loading-message" *ngIf="isLoading">
    Chargement...
  </div>

  <!-- Message d'erreur -->
  <div class="error-message" *ngIf="hasError">
    Une erreur s'est produite lors de la récupération des données.
  </div>
</div>
