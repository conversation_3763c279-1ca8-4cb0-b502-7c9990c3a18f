<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Events\DemandeStatusUpdate;
use App\Models\Demande;
use App\Models\Derogation;
use App\Models\Devis;
use App\Models\Notification;
use App\Models\User;
use App\Mail\RejectionEmail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DirectorController extends Controller
{
    public function rejectDerogation($id, Request $request)
{
    $demande = Demande::with('samples') // Eager load related samples
    ->where('demande_id', $id)
    ->first();
    $demande->status = 'rejected';
    $demande->save();
    // Create notification for the user
    $notification = Notification::create([
        'user_id' => $demande->user_id,
        'title' => 'Demande Rejetée',
        'message' => 'Votre demande a été rejetée. Raison: ' . $request->reason,
        'type' => 'demande',
        'demande_id' => null, // Don't use string value in numeric field
        'demande' => $demande->demande_id, // Store the string value here
        'is_read' => 0
    ]);

    // Create notification for the receptionist
    Notification::create([

        'title' => 'Demande Rejetée',
        'message' => 'Demande de ' . $demande->user->name . ' de numero ' . $demande->demande_id . ' a été rejetée',
        'type' => 'demande_rejection',
        'demande_id' => null, // Don't use string value in numeric field
        'demande' => $demande->demande_id, // Store the string value here
        'is_read' => 0
    ]);

    // Send email to the user

        $user = User::find($demande->user_id);
        Mail::to($user->email)
            ->send(new RejectionEmail($notification, $demande, $user, $request->reason));

  

    $message = "Your demande has been rejected. Reason: " . $request->reason;
    broadcast(new DemandeStatusUpdate($demande, $message))->toOthers();

    return response()->json(['message' => 'Client and receptionist notified of rejection']);
}
public function validateDerogation($id)
{
    $demande = Demande::with('samples') // Eager load related samples
                      ->where('demande_id', $id)
                      ->first();
    $demande->status = 'valid';
    $demande->save();
    Notification::create([
        'user_id' => $demande->user_id, // Assuming user_id belongs to the demande's creator
        'title' => 'Derogation validated',
        'message' => 'A derogation has been created for your demande. Please review it.',
        'type' => 'demande',
        'demande_id' => $demande->demande_id,

    ]);
    $message = "Your demande is now valid with the Devis Proforma.";
    broadcast(new DemandeStatusUpdate($demande, $message))->toOthers();

    return response()->json(['message' => 'Client and receptionist notified']);
}
public function getDerogationsByDemandeId($demande_id)
{
    // Fetch all derogations where demande_id matches
    $derogations = Derogation::where('demande_id', $demande_id)->get();

    // Check if derogations exist
    if ($derogations->isEmpty()) {
        return response()->json(['message' => 'No derogations found for this demande.'], 404);
    }

    // Return the derogations
    return response()->json([
        'message' => 'Derogations retrieved successfully.',
        'derogations' => $derogations
    ]);
}
public function getDerogationNotifications()
    {
        // Get all notifications with title "Nouvelle Demande"
        $notifications = Notification::where('title', 'derogation created')
            ->get();

        return response()->json([
            'notifications' => $notifications,
        ]);
    }
    public function validateDemandeFinale($demande_id)
    {
        $demande = Demande::where('demande_id', $demande_id)->firstOrFail();

        $demande->status = 'valid';
        $demande->save();

        // Retrieve the first Devis associated with the demande
        $devis = Devis::where('demande_id', $demande->id)->first();

        // Calculate the results date (7 working days after validation)
        $resultsDate = $this->calculateResultsDate(Carbon::now(), 7);

        // Notify the user
        $message = "Votre demande a été validée avec succès. Veuillez consulter le devis correspondant à votre demande..";

       Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'Demande Validée',
            'message' => "Demande de ".$demande->user->name." de numero ".$demande->demande_id." a été validée",
            'type' => 'demande validée receptionniste',
            'demande_id' =>$demande->id,
            'demande'=>$demande_id,
            'devis_id' => $devis ? $devis->id : null, // Save devis_id if it exists
        ]);
        broadcast(new DemandeStatusUpdate($demande, $message))->toOthers();

        return response()->json([
            'message' => 'Client notified and status updated',
            'results_date' => $resultsDate->format('Y-m-d'),
            'devis_id' => $devis ? $devis->id : null,
        ]);
    }
    private function calculateResultsDate(Carbon $startDate, int $days)
    {
        $currentDate = clone $startDate;
        $count = 0;

        while ($count < $days) {
            $currentDate->addDay();
            if (!$currentDate->isWeekend()) { // Exclude Saturdays and Sundays
                $count++;
            }
        }

        return $currentDate;
    }
}
