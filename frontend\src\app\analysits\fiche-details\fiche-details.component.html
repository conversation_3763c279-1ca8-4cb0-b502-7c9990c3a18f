<div class="fiche-details-container">
  <div class="fiches-header">
    <h3 class="fiches-title"> <fa-icon [icon]="faClipboardList"></fa-icon> Détails de la Fiche de Transmission n°{{ ficheTransmissionId }}</h3>
    <div class="header-buttons">
      <button class="print-btn" (click)="printFiche()">
        <fa-icon [icon]="faPrint"></fa-icon> Imprimer
      </button>
      <button class="back-btn" (click)="goToFicheList()">
        <fa-icon [icon]="faArrowLeft"></fa-icon> Retour aux Fiches
      </button>
    </div>
  </div>

  <!-- Show loading indicator -->
  <div *ngIf="loading" class="loading-message">
    <i class="fas fa-spinner fa-spin"></i> Chargement des fiches...
  </div>

  <!-- Show error message -->
  <div *ngIf="errorMessage" class="error">
    <fa-icon [icon]="faExclamationTriangle"></fa-icon> {{ errorMessage }}
  </div>

  <!-- Display table only when data is available -->
  <table *ngIf="!loading && !errorMessage && fiches.length > 0">
    <thead>
      <tr>
        <th>ID Fiche</th>
        <th>Code Laboratoire</th>
        <th>Nature Échantillon</th>
        <th>Masse (g)</th>
        <th>Date Transmission</th>
        <th>Analyses Demandées</th>
        <th>Observations</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let fiche of fiches">
        <td>{{ fiche.id }}</td>
        <td>{{ fiche.code_laboratoire }}</td>
        <td>{{ fiche.nature_echantillon }}</td>
        <td>{{ fiche.masse_echantillon }}</td>
        <td>{{ fiche.date_transmission ? fiche.date_transmission : 'Pas encore envoyé' }}</td>
        <td>
          <ul>
            <li *ngFor="let analysis of fiche.analyses_demandees">{{ analysis }}</li>
          </ul>
        </td>
        <td>{{ fiche.observations }}</td>
      </tr>
    </tbody>
  </table>

  <!-- Show message when no fiches exist -->
  <div *ngIf="!loading && !errorMessage && fiches.length === 0" class="no-data">
    <fa-icon [icon]="faInfoCircle"></fa-icon> Aucune fiche trouvée pour cette transmission.
  </div>

  <!-- Rapport Notes Section - Only show if notes exist -->
  <div *ngIf="!loading && !errorMessage && fiches.length > 0 && rapportNotes && rapportNotes.notes" class="rapport-notes-section">
    <h3 class="rapport-notes-title">
      <fa-icon [icon]="faExclamationTriangle" class="notes-icon"></fa-icon>
      Notes du Rapport
    </h3>
    <div class="rapport-notes-container">
      <div class="rapport-info">
        <p><strong>Demande:</strong> {{ rapportNotes.demande_id }}</p>
        <p><strong>Statut:</strong>
          <span [ngClass]="{
            'status-approved': rapportNotes.validation === 1,
            'status-rejected': rapportNotes.validation === 0,
            'status-pending': rapportNotes.validation === 2
          }">
            {{ getValidationStatus(rapportNotes.validation) }}
          </span>
        </p>
      </div>
      <div class="rapport-notes-content">
        <h4>Motif du rejet:</h4>
        <p>{{ rapportNotes.notes }}</p>
      </div>
    </div>
  </div>
</div>
