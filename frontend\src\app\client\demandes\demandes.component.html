<div class="demandes-container" [@fadeInContainer]>
  <h2 class="title">Soumission d'une demande d'analyse</h2>
<!--

  <div class="client-info">
    <div class="client-row">
      <span><strong>Client : </strong> {{ name }}</span>
      <span><strong>Tél : </strong> {{ phone }}</span>
    </div>
    <div class="client-row">
      <span><strong>Adresse : </strong> {{ adress }}</span>
      <span><strong>Fax : </strong> {{ fax }}</span>
    </div>
    <div class="client-row">
      <span><strong>Mail : </strong> {{ email }}</span>
    </div>
  </div> -->

  <!-- Mode de règlement (en dehors du formulaire) -->
  <span *ngIf="(formSubmitted && !modeReglementValue)" class="error">
    Le mode de règlement est requis.
  </span>
  <div class="input-group input-group-half">
    <label>
      Mode de règlement <span class="required-star">*</span> :

    </label>
    <select id="modeReglement" (change)="updateModeReglement($event)" required>
      <option value="" disabled selected>Sélectionner...</option>
      <option value="Virement bancaire">Virement bancaire</option>
    </select>
  </div>

  <!-- Loading Spinner Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-popup">
      <div class="spinner"></div>
      <h3>Traitement en cours...</h3>
    </div>
  </div>

  <!-- Notification de soumission -->
  <div *ngIf="showNotification" class="notification-overlay">
    <div class="notification">
      <div class="notification-icon">✅</div>
      <div class="notification-message">Votre demande a été envoyée avec succès !</div>

    </div>
  </div>

  <form [formGroup]="demandeForm" (ngSubmit)="onSubmit()">
    <div class="input-group">
      <label>Délai souhaité :</label>
      <input
        type="date"
        formControlName="delai_souhaite"
        placeholder="Sélectionner une date"
      />
    </div>

    <div
      *ngFor="let echantillon of enregistrements.controls; let i = index"
      class="echantillon-card"
      [formGroup]="getEchantillonFormGroup(i)"
      [@fadeInEchantillon]
    >
      <h3>Échantillon {{ i + 1 }}</h3>
      <p class="required-note">
        <span class="required-star">*</span> Les champs marqués d'une étoile sont
        obligatoires.
      </p>
      <span *ngIf="(echantillon.get('reference')?.touched || formSubmitted) && echantillon.get('reference')?.errors?.['required']" class="error">
        La référence est requise.
      </span>
      <div class="input-group">
        <div class="label-container">
          <label>Référence <span class="required-star">*</span>:</label>

        </div>
        <input
          type="text"
          formControlName="reference"
          placeholder="Ex: REF-001-POIS, REF-ALG-002"
          required
        />
      </div>


      <span *ngIf="(echantillon.get('nature_echantillon')?.touched || formSubmitted) && echantillon.get('nature_echantillon')?.errors?.['required']" class="error">
        La nature d'échantillon est requise.
      </span>
      <div class="input-group">
        <label>Nature d'échantillon <span class="required-star">*</span>:</label>
        <select formControlName="nature_echantillon" required>

          <option value="" disabled selected>Choisir le type d'échantillon souhaité...</option>

          <!-- Poissons -->
          <option value="Poissons">Poissons</option>

          <!-- Mollusque + sous-catégories -->
          <optgroup label="Mollusque">
            <option value="Coquillage">Coquillage</option>
            <option value="Seiche, poulpe, coquillage">Seiche, poulpe, coquillage</option>
          </optgroup>

          <!-- Crustacés -->
          <option value="Crustacés (crevettes, crabes)">Crustacés (crevettes, crabes)</option>

          <!-- Algues -->
          <option value="Algues">Algues</option>

          <!-- Matrice séchée produit de la mer -->
          <option value="Matrice séchée produit de la mer">
            Matrice séchée produit de la mer
          </option>

          <!-- Produit de la mer en conserve -->
          <option value="Produit de la mer en conserve">
            Produit de la mer en conserve
          </option>
        </select>

      </div>


      <div class="input-group">
        <label>Date de prélèvement :</label>
        <input
          type="date"
          formControlName="date_prelevement"
          placeholder="Sélectionner une date"
        />
      </div>

      <div class="input-group">
        <label>Origine de prélèvement :</label>
        <!-- Par ex.: en mer, zone portuaire, élevage, etc. -->
        <input
          type="text"
          formControlName="origine_prelevement"
          placeholder="Ex: Zone portuaire, élevage, etc."
        />
      </div>

      <div class="input-group">
        <label>Nom du préleveur :</label>
        <input
          type="text"
          formControlName="nom_preleveur"
          placeholder="Ex: Transporteur"
        />
      </div>

      <div class="input-group">
        <label>Site :</label>
        <!-- S’il y a une désignation particulière du lieu (ex. Labo, Station) -->
        <input
          type="text"
          formControlName="site"
          placeholder="Ex: Station X, Labo Y"
        />
      </div>
      <span *ngIf="(echantillon.get('provenance')?.touched || formSubmitted) && echantillon.get('provenance')?.errors?.['required']" class="error">
        La provenance est requise.
      </span>
      <div class="input-group">
        <div class="label-container">
          <label>Provenance <span class="required-star">*</span>:</label>

        </div>
        <!-- Ex.: conditions isothermes avec glace, boîte scellée, etc. -->
        <input
          type="text"
          formControlName="provenance"
          placeholder="Ex: Transport isotherme sur glace"
          required
        />
      </div>
      <span *ngIf="(echantillon.get('masse_echantillon')?.touched || formSubmitted) && echantillon.get('masse_echantillon')?.errors" class="error">
        <span *ngIf="echantillon.get('masse_echantillon')?.errors?.['required']">La masse est requise.</span>
        <span *ngIf="echantillon.get('masse_echantillon')?.errors?.['pattern']">La masse doit être un nombre valide.</span>
      </span>
      <div class="input-group">
        <label>
          Masse exacte (g) <span class="required-star">*</span>:

        </label>
        <!-- Faites référence aux quantités minimales recommandées (ex. 50g MF, 100g MF, etc.) -->
        <input
          type="number"
          formControlName="masse_echantillon"
          placeholder="Ex: 50 (Poisson/Crustacé) | 100 (Mollusque) | 50 MS (algues séchées)"
          required
        />
      </div>
      <span *ngIf="(echantillon.get('analyses_demandees')?.touched || formSubmitted) && echantillon.get('analyses_demandees')?.errors?.['required']" class="error">
        Au moins une analyse doit être sélectionnée.
      </span>
      <div class="input-group">
        <label>Analyses demandées <span class="required-star">*</span> :</label>
        <div class="custom-dropdown">
          <div class="dropdown-toggle" (click)="toggleDropdown(i)">
            <span *ngIf="echantillon.get('analyses_demandees')?.value?.length === 0">
              Sélectionner analyse . . .
            </span>
            <span *ngIf="echantillon.get('analyses_demandees')?.value?.length > 0">
              {{ echantillon.get('analyses_demandees')?.value.length }} analyse(s)
              sélectionnée(s)
            </span>
          </div>
          <div class="dropdown-list" [ngClass]="{ 'open': dropdownOpen[i] }">
            <!-- Loading indicator -->
            <div *ngIf="loadingAnalyses" class="loading-analyses">
              <fa-icon [icon]="faSpinner" animation="spin"></fa-icon> Chargement des analyses...
            </div>

            <!-- Analyses list -->
            <label *ngFor="let analysis of analysesDisponibles">
              <input
                type="checkbox"
                [checked]="
                  echantillon
                    .get('analyses_demandees')
                    ?.value.includes(analysis.name)
                "
                (change)="toggleAnalysisSelection(i, analysis.name, $event)"
              />
              <span class="analysis-name">{{ analysis.name }}</span>
              <span class="analysis-price">{{ analysis.price | number:'1.0-0' }} DT</span>
              <span *ngIf="!analysis.accredited" class="not-accredited">(Non accrédité)</span>
            </label>

            <!-- No analyses message -->
            <div *ngIf="!loadingAnalyses && analysesDisponibles.length === 0" class="no-analyses">
              Aucune analyse disponible
            </div>
          </div>
        </div>

      </div>
      <div class="input-group">
        <label>Analyse souhaitée :</label>
        <!-- Placez ici quelques exemples tirés du tableau (pH, ABVT, TMA, Btox...) -->
        <input
          type="text"
          formControlName="analyse_souhaite"
          placeholder="Ex: pH, ABVT, TMA, Btox..."
        />
      </div>
      <span *ngIf="(echantillon.get('etat')?.touched || formSubmitted) && echantillon.get('etat')?.errors?.['required']" class="error">
        L'état est requis.
      </span>
      <div class="input-group">
        <label>
          État <span class="required-star">*</span>:

        </label>
        <!-- Au besoin, précisez Congelé, Réfrigéré, etc. selon la nature du produit -->
        <select formControlName="etat" required>
          <option value="" disabled selected>Sélectionner...</option>
          <option value="Solide">Solide</option>
          <option value="Congelé">Congelé</option>
          <option value="Refrigéré">Réfrigéré</option>
          <option value="Liquide">Liquide</option>
        </select>
      </div>
      <span *ngIf="(echantillon.get('lot')?.touched || formSubmitted) && echantillon.get('lot')?.errors?.['required']" class="error">
        Le lot est requis.
      </span>
      <div class="input-group">
        <label>Lot <span class="required-star">*</span>:</label>
        <!-- Numéro de lot, s’il existe -->
        <input
          type="text"
          formControlName="lot"
          placeholder="Ex: LOT2025"
          required

        />

      </div>

      <div class="input-group">
        <label>Observation :</label>
        <!-- Champ d'observation optionnel -->
        <textarea
          formControlName="observation"
          placeholder="Observations supplémentaires sur l'échantillon..."
          rows="3"
        ></textarea>
      </div>

      <!-- Bouton Supprimer -->
      <button
        type="button"
        class="btn-delete"
        *ngIf="enregistrements.length > 1"
        (click)="removeEnregistrement(i)"
      >
        <fa-icon [icon]="faTrash" style="margin-right: 10px;"></fa-icon>Supprimer
      </button>
    </div>

    <!-- Form Validation Error -->
    <div *ngIf="formSubmitted && demandeForm.invalid" class="error-text">
      Veuillez corriger les erreurs dans le formulaire avant de soumettre.
    </div>

    <div class="btns">
      <div class="btn-group-left">
        <button type="button" class="btn-add" (click)="addEnregistrement()">
          <fa-icon [icon]="faPlus" style="margin-right: 10px;"></fa-icon>Ajouter un autre échantillon
        </button>
        <!-- <button type="button" class="btn-reset" (click)="resetForm()">
          <fa-icon [icon]="faUndo" style="margin-right: 10px;"></fa-icon>Réinitialiser
        </button> -->
      </div>
      <button
        type="submit"
        class="btn-submit"
      >
        <fa-icon [icon]="faPaperPlane" style="margin-right: 10px;"></fa-icon>Envoyer la demande
      </button>
    </div>
  </form>
</div>
