import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RegistreSuiviService } from './registre.service';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faFilter, faSearch, faCalendarAlt, faEraser, faEye, faSpinner, faSyncAlt } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-registre-suivi-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxPaginationModule,
    FontAwesomeModule
  ],
  templateUrl: './registres.component.html',
  styleUrls: ['./registres.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Pour permettre l'attribut [spin] sur fa-icon
})
export class RegistreSuiviListComponent implements OnInit {
  // Font Awesome icons
  faFilter = faFilter;
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faEraser = faEraser;
  faEye = faEye;
  faSpinner = faSpinner;
  faSyncAlt = faSyncAlt;

  // Data properties
  registres: any[] = [];
  filteredRegistres: any[] = [];
  loading: boolean = false;
  error: string | null = null;
  successMessage: string | null = null;

  // Filtering properties
  searchTerm: string = '';
  selectedDate: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  constructor(private registreSuiviService: RegistreSuiviService, private router: Router) {}

  ngOnInit(): void {
    this.loadRegistres();
  }

  loadRegistres(): void {
    this.loading = true;
    this.error = null;

    this.registreSuiviService.getAll().subscribe({
      next: (data) => {
        this.registres = data;
        this.filteredRegistres = [...this.registres];
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching registres:', err);
        this.error = 'Échec du chargement des registres.';
        this.loading = false;
      }
    });
  }

  // Filtering methods
  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page
    const term = this.searchTerm.toLowerCase();
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredRegistres = this.registres.filter((registre) => {
      const matchesSearch =
        (registre.id && registre.id.toString().toLowerCase().includes(term)) ||
        (registre.demande_id && registre.demande_id.toString().toLowerCase().includes(term)) ||
        (registre.nom_client && registre.nom_client.toLowerCase().includes(term));

      const registreDate = registre.date_reception ? new Date(registre.date_reception) : null;
      const matchesDate = filterDate && registreDate ?
        registreDate.toDateString() === filterDate.toDateString() :
        !filterDate;

      return matchesSearch && matchesDate;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.filteredRegistres = [...this.registres];
    this.currentPage = 1;
  }
  goToRegistres(ficheId: number) {
    this.router.navigate(['receptionist/registres/', ficheId]);
  }
}
