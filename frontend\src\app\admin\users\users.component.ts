import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormControl, Validators, FormsModule, FormBuilder } from '@angular/forms';

import { UsersService } from './users.service';
import { User } from '../../../models/user.model';




@Component({
  selector: 'app-users',
  standalone: true,
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.css'],
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
})
export class UsersComponent  {
  errorMessage: string | null = null;
  users: User[] = [];
  showUserModal = false;
  editMode = false;
  userForm: FormGroup;
  selectedUser: User | null = null;
  nameFilter: string = '';
  roleFilter: string = '';
  dateFilter: string = '';

  constructor(private userService: UsersService, private fb: FormBuilder) {
    this.userForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.pattern('[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}')]],
      password: ['', [Validators.required, Validators.minLength(8)]], // Password must be at least 8 characters
      password_confirmation: ['', Validators.required], // Ensure confirmation field is required
      role: ['', Validators.required] 
    }, { validator: this.passwordMatchValidator });
    
  }
  passwordMatchValidator(formGroup: FormGroup) {
    const password = formGroup.get('password')?.value;
    const confirmPassword = formGroup.get('password_confirmation')?.value;

    if (password !== confirmPassword) {
      formGroup.get('password_confirmation')?.setErrors({ mismatch: true });
    } else {
      // Remove the mismatch error if passwords match (keeping any other errors intact)
      const errors = formGroup.get('password_confirmation')?.errors;
      if (errors) {
        delete errors['mismatch'];
        if (!Object.keys(errors).length) {
          formGroup.get('password_confirmation')?.setErrors(null);
        }
      }
    }
    return null;
  }
  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (data) => {
        console.log('Users fetched:', data); // Debugging
        this.users = data;
      },
      error: (error) => {
        console.error('Error fetching users:', error);
      }
    });
  }

  openUserModal(user: User | null = null): void {
    this.editMode = !!user;
    this.selectedUser = user;

    if (user) {
      this.userForm.patchValue({
        name: user.name,
        email: user.email,
        role: user.role
      });

      // When editing, don't require password
      this.userForm.get('password')?.setValidators(null);
      this.userForm.get('password_confirmation')?.setValidators(null);
    } else {
      this.userForm.reset();
    }

    this.showUserModal = true;
  }

  closeUserModal(): void {
    this.showUserModal = false;
  }

  saveUser(): void {
    if (this.userForm.invalid) {
      this.userForm.markAllAsTouched();
      return;
    }
  
    this.errorMessage = null;
    let userData = this.userForm.value;
  
    if (this.editMode && this.selectedUser) {
      // Extract only the role from the form data for updating
      userData = {
        role: this.userForm.get('role')?.value
      };
  
      this.userService.updateUser(this.selectedUser.id ?? 0, userData).subscribe({
        next: () => {
          this.loadUsers();
          this.closeUserModal();
        },
        error: (error) => {
          console.error("Error updating user:", error);
        }
      });
    } else {
      // For adding a new user, send all form data
      this.userService.addUser(this.userForm.value).subscribe({
        next: () => {
          this.loadUsers();
          this.closeUserModal();
        },
        error: (error) => {
          if (error.status === 422 && error.error?.errors?.email) {
            this.userForm.get('email')?.setErrors({ emailTaken: true });
          } else {
            this.errorMessage = "Le compte n’a pas été créé, réessayez plus tard.";
          }
        }
      });
    }
  }
  
  


  deleteUser(userId: number): void {
    if (confirm('Voulez-vous vraiment supprimer cet utilisateur ?')) {
      this.userService.deleteUser(userId).subscribe(() => {
        this.loadUsers();
      });
    }
  }

  applyFilters(): void {
    this.userService.getUsers().subscribe({
      next: (data) => {
        this.users = data.filter(user => 
          (this.nameFilter ? user.name.toLowerCase().includes(this.nameFilter.toLowerCase()) : true) &&
          (this.roleFilter ? user.role === this.roleFilter : true)
        );
      },
      error: (error) => {
        console.error('Error fetching users:', error);
      }
    });
  }
  
 
}
