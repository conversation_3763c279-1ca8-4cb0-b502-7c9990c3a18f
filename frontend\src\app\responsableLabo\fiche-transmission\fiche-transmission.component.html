<div class="fiche-transmission-container">
  <h3>📑 Détails de la Fiche de Transmission n°{{ ficheTransmissionId }}</h3>

  <!-- Show loading indicator -->
  <div *ngIf="loading" class="loading-message">⏳ Chargement des fiches...</div>
  <button class="back-btn" (click)="goToFicheList()">⬅ Retour aux Fiches</button>

  <!-- Show error message -->
  <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>

  <!-- Display table only when data is available -->
  <table border="1" *ngIf="!loading && !errorMessage && fiches.length > 0">
    <thead>
      <tr>
        <th>ID Fiche</th>
        <th>Code Laboratoire</th>
        <th>Nature Échantillon</th>
        <th>Masse (g)</th>
        <th>Date Transmission</th>
        <th>Date Remise Résultats</th>
        <th>Analyses Demandées</th>
        <th>Observations</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let fiche of fiches">
        <td>{{ fiche.id }}</td>
        <td>{{ fiche.code_laboratoire }}</td>
        <td>{{ fiche.nature_echantillon }}</td>
        <td>{{ fiche.masse_echantillon }}</td>
        <td>{{ fiche.date_transmission ? fiche.date_transmission : 'Pas encore envoyé' }}</td>
        <td>{{ fiche.date_remise_resultats }}</td>
        <td>
          <ul>
            <li *ngFor="let analysis of fiche.analyses_demandees">{{ analysis }}</li>
          </ul>
        </td>
        <td>{{ fiche.observations }}</td>
      </tr>
    </tbody>
  </table>

  <!-- Show message when no fiches exist -->
  <div *ngIf="!loading && !errorMessage && fiches.length === 0" class="no-data">
    ❌ Aucune fiche trouvée pour cette transmission.
  </div>
</div>
