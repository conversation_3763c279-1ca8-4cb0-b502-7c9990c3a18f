/* ------------------------------------------------------
   1) Importation des Polices
   ------------------------------------------------------ */
   @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

   /* ------------------------------------------------------
      2) Global Styling
      ------------------------------------------------------ */
   * {
     margin: 0;
     padding: 0;
     box-sizing: border-box;
     font-family: 'Montserrat', sans-serif;
   }

   body {
     background-color: #f7f7f7;
     color: #333;
   }

   /* On peut appliquer une animation de fondu général au body ou à .presentation-section */
   body {
     animation: fadeInBg 1.2s ease-in-out forwards;
   }

   /* ------------------------------------------------------
      3) Admin Panel
      ------------------------------------------------------ */
   .admin-panel {
     margin: 50px auto;
     padding: 30px;
     background: #fff;
     border-radius: 10px;
     box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
     max-width: 90%;
     text-align: center;
     /* Animation plus visible */
     animation: fadeInUp 1s ease-in-out forwards;
   }

   /* Titre du panneau avec effet "glowText" */
   .admin-panel h2 {
     font-family: 'Orbitron', sans-serif;
     font-size: 24px;
     font-weight: bold;
     margin-bottom: 20px;
     text-transform: uppercase;
     position: relative;
     display: inline-block;
     padding-bottom: 10px;
     background: linear-gradient(90deg, #2496d3, #1a6a8e);
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
     animation: glowText 1.5s infinite alternate;
     letter-spacing: 2px;
     border-bottom: 3px solid #2496d3;
   }

   /* Séparateur sous le titre (optionnel) */
   .admin-panel h2::after {
     content: "";
     display: block;
     margin: 0 auto;
     width: 80px;
     border-bottom: 3px solid #2496d3;
     margin-top: 5px;
   }

   /* ------------------------------------------------------
      4) Table des Rapports
      ------------------------------------------------------ */
   .reports-table {
     width: 100%;
     border-collapse: collapse;
     margin-top: 20px;
     background: white;
     border-radius: 8px;
     overflow: hidden;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   }

   /* En-têtes */
   .reports-table thead tr th {
     background-color: #2496d3;
     color: white;
     padding: 15px;
     text-align: center;
     font-weight: 600;
     text-transform: uppercase;
     font-size: 14px;
   }

   /* Lignes du tableau */
   .reports-table tbody tr td {
     padding: 15px;
     text-align: center;
     font-size: 14px;
     color: #333;
     border-bottom: 1px solid #f2f2f2;
     transition: background-color 0.3s ease;
   }

   .reports-table tbody tr:hover td {
     background-color: #f8faff;
   }

   /* ------------------------------------------------------
      5) Boutons d'Action
      ------------------------------------------------------ */
   .action-buttons {
     display: flex;
     justify-content: center;
     gap: 10px;
   }

   .approve-btn,
   .reject-btn,
   .details-btn {
     padding: 10px 20px;
     font-size: 14px;
     border-radius: 25px;
     cursor: pointer;
     transition: all 0.3s ease;
     border: none;
     text-transform: uppercase;
     outline: none;
   }

   /* Bouton "Valider" (approve) */
   .approve-btn {
     background-color: #2496d3;
     color: white;
     box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
   }
   .approve-btn:hover {
     background-color: #1a6a8e;
     transform: scale(1.05);
   }

   /* Bouton "Rejeter" (reject) */
   .reject-btn {
     background-color: #e74c3c;
     color: white;
     box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
   }
   .reject-btn:hover {
     background-color: #c0392b;
     transform: scale(1.05);
   }

   /* Bouton "Voir Détail" */
   .details-btn {
     background-color: #555;
     color: #fff;
     box-shadow: 0 5px 15px rgba(85, 85, 85, 0.3);
   }
   .details-btn:hover {
     background-color: #333;
     transform: scale(1.05);
   }

   /* ------------------------------------------------------
      6) Animations
      ------------------------------------------------------ */
   /* Fondu de l'arrière-plan */
   @keyframes fadeInBg {
     0%   { opacity: 0; }
     100% { opacity: 1; }
   }

   /* Slide vers le haut + fade */
   @keyframes fadeInUp {
     0% {
       opacity: 0;
       transform: translateY(40px);
     }
     100% {
       opacity: 1;
       transform: translateY(0);
     }
   }

   /* Effet "glow" sur le texte du titre */
   @keyframes glowText {
     0% {
       text-shadow: 0 0 10px rgba(36, 150, 211, 0.4);
     }
     100% {
       text-shadow: 0 0 20px rgba(36, 150, 211, 0.8);
     }
   }
