<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "Checking MySQL database connection...\n";
    echo "Database: " . config('database.connections.mysql.database') . "\n";
    echo "Host: " . config('database.connections.mysql.host') . "\n";
    echo "Port: " . config('database.connections.mysql.port') . "\n";
    echo "Username: " . config('database.connections.mysql.username') . "\n\n";

    // Test connection
    DB::connection()->getPdo();
    echo "✅ Successfully connected to MySQL database!\n\n";

    // Get all tables
    $tables = DB::select('SHOW TABLES');
    echo "Found " . count($tables) . " tables in database 'b3aqua':\n";
    echo "==========================================\n";

    foreach($tables as $table) {
        $tableName = array_values((array)$table)[0];
        echo "- " . $tableName . "\n";
    }

    // Check specifically for rapports table
    echo "\n";
    $rapportsExists = false;
    foreach($tables as $table) {
        $tableName = array_values((array)$table)[0];
        if ($tableName === 'rapports') {
            $rapportsExists = true;
            break;
        }
    }

    if ($rapportsExists) {
        echo "✅ Rapports table exists!\n";

        // Check rapports table structure
        $columns = DB::select("DESCRIBE rapports");
        echo "\nRapports table structure:\n";
        echo "========================\n";
        $hasNotesColumn = false;
        foreach($columns as $column) {
            echo "- {$column->Field} ({$column->Type}) " . ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
            if ($column->Field === 'notes') {
                $hasNotesColumn = true;
            }
        }

        if ($hasNotesColumn) {
            echo "\n✅ Notes column exists in rapports table!\n";
        } else {
            echo "\n❌ Notes column does not exist in rapports table yet\n";
        }

        // Count rapports
        $count = DB::table('rapports')->count();
        echo "\nNumber of rapports: {$count}\n";

    } else {
        echo "❌ Rapports table does not exist\n";
    }

} catch(Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nThis might mean:\n";
    echo "1. MySQL server is not running\n";
    echo "2. Database 'b3aqua' doesn't exist\n";
    echo "3. Connection credentials are incorrect\n";
    echo "4. Port 8889 is not accessible\n";
}
