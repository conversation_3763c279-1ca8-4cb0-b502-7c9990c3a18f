/* ------------------------------------ */
/* 1. GLOBAL RESET + GOOGLE FONT IMPORT */
/* ------------------------------------ */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
  text-decoration: none;
  list-style: none;
}

/* ------------------------------------ */
/* 2. BODY STYLES (GRADIENT BACKGROUND) */
/* ------------------------------------ */
body {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #e2e2e2, #c9d6ff);
}

/* ------------------------------------ */
/* 3. SIGNUP CONTAINER */
/* ------------------------------------ */
.login-container {
  width: clamp(320px, 80%, 600px);
  margin: 2rem auto;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  padding: 2rem 1.5rem;
  text-align: left;
  animation: fadeIn 0.7s ease-in-out forwards;
  position: relative;
}



/* ------------------------------------ */
/* 4. LOADING OVERLAY */
/* ------------------------------------ */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.loading-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-popup h3 {
  margin-top: 20px;
  color: #333;
  font-size: 18px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ------------------------------------ */
/* 5. TITLE */
/* ------------------------------------ */
.title {
  font-size: 30px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 30px;
  display: block;
  text-align: center;
  position: relative;
  color: #333;
  animation: glowText 1.5s infinite alternate;
}



/* ------------------------------------ */
/* 5. FORM STYLES */
/* ------------------------------------ */
form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  text-align: left;
  width: 100%;
  margin: 0 auto;
  max-width: 100%;
}

/* ------------------------------------ */
/* 6. INPUT GROUPS */
/* ------------------------------------ */
.input-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

/* Highlight invalid fields */
.input-group.invalid label,
.input-group.invalid input {
  color: #d40000;
  border-color: #d40000 !important;
}

.input-group.invalid input {
  background-color: #ffe6e6;
}

/* Label styles */
.input-group label {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

/* ------------------------------------ */
/* 7. INPUT FIELDS */
/* ------------------------------------ */
input[type="text"],
input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 13px 20px;
  border-radius: 8px;
  border: 2px solid #eeeeee;
  background: #f9f9f9;
  font-size: 16px;
  color: #333;
  outline: none;
  transition: all 0.3s ease;
}

/* Placeholder styling */
input::placeholder {
  color: #888;
  font-weight: 400;
}

/* On focus */
input:focus {
  border-color: #7494e7;
  background-color: #f4f7ff;
  transform: scale(1.03);
}
.input-label-form{
    margin-bottom: 10px;
}
.input-field-form{
    margin-bottom: 30px;
}
.login-link{

    margin-top: 20px;
}
.btn-singup {
    font-size: 16px;
    color: #007BFF; /* Blue color for link */
    text-decoration: none; /* Removes underline */
    cursor: default; /* Keeps the cursor as the default arrow */
    transition: color 0.2s ease-in-out; /* Smooth color transition for hover effect */
}
.rest-password{

}
.btn-reset {

  font-size: 16px;
  color: #007BFF; /* Blue color for link */
  text-decoration: none; /* Removes underline */
  cursor: default; /* Keeps the cursor as the default arrow */
  transition: color 0.2s ease-in-out; /* Smooth color transition for hover effect */
}
.btn-singup:hover, .btn-singup:focus {
    color: #0056b3; /* Darker blue on hover/focus */
    text-decoration: underline; /* Adds underline on hover/focus for better visibility */
}
/* ------------------------------------ */
/* 8. PRIMARY BUTTON */
/* ------------------------------------ */
.btn-primary {

    width: 160px;
    height: 46px;
    background: #2595d2;
    border: 2px solid #ffffff;
    color: #fff;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    z-index: 1;
  }

  .btn-primary:hover {
    background: rgba(15, 151, 192, 0.2);
  /* Even darker border */
    transform: scale(1.05);
  }




/* ------------------------------------ */
/* 9. SUCCESS / ERROR MESSAGES */
/* ------------------------------------ */
.success {
  color: rgb(49, 177, 49);

  padding: 10px;
  border-radius: 8px;
  text-align: center;

  font-size: 16px;
  animation: fadeIn 0.5s ease-in-out;
}
.btn-primary:focus {
  outline: none;
  border: 2px solid #ffffff;
}
.error {
  color: #d40000;

  padding: 10px;
  border-radius: 8px;
  text-align: center;

  font-size: 15px;
  animation: fadeIn 0.5s ease-in-out;
}
