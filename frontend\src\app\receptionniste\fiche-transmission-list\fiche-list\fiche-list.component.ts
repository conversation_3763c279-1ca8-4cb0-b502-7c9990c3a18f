import { ChangeDetectorRef,Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';

import { FicheTransmissionService } from '../../fiche-transmission/fiche.service';
import { DemandeService } from '../../demandes/demande.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faEye, faPaperPlane, faFilter, faEraser, faSearch, faSpinner, faFileAlt, faFilePdf } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-fiche-list',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule, NgxPaginationModule,],
  templateUrl: './fiche-list.component.html',
  styleUrls: ['./fiche-list.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Pour permettre l'attribut [spin] sur fa-icon
})
export class FicheListComponent implements OnInit {
  // Font Awesome icons
  faEye = faEye;
  faPaperPlane = faPaperPlane;
  faFilter = faFilter;
  faEraser = faEraser;
  faSearch = faSearch;
  faSpinner = faSpinner;
  faFileAlt = faFileAlt;
  faFilePdf = faFilePdf;
  fiches: any[] = [];
  filteredFiches: any[] = [];
  loading: boolean = true;
  errorMessage: string = '';
  searchTerm: string = '';
  selectedStatus: string = '';
  selectedDate: string = '';

  userCache: { [key: number]: { name: string; nickname: string } } = {};
  sentStatus: { [key: number]: boolean } = {};

  searchDemandeNumber: string = '';
  searchClientName: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  constructor(
    private ficheService: FicheTransmissionService,
    private demandeService: DemandeService,
    private router: Router,private cdr:ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.loadFiches();
  }

  loadFiches() {
    this.ficheService.getAllFiches().subscribe({
      next: (data) => {
        this.fiches = data;
        this.filteredFiches = [...this.fiches];
        this.loading = false;

        this.fiches.forEach(fiche => {
          this.sentStatus[fiche.id] = fiche.status === 'sented';

          // Initialize rapport status if not set
          if (!fiche.statusRapport) {
            fiche.statusRapport = 'not_created';
          }

          // Fetch rapport_id for each fiche with a demande_id
          if (fiche.status === 'sented' && fiche.demande?.demande_id) {
            this.fetchRapportId(fiche.demande.demande_id, fiche);
          }
        });

        const userIds = [
          ...new Set(
            this.fiches
              .map(fiche => fiche.demande?.user_id)
              .filter(id => id)
          )
        ];

        if (userIds.length > 0) {
          this.demandeService.getUsersDetails(userIds).subscribe({
            next: (users) => {
              users.forEach(user => {
                this.userCache[user.id] = {
                  name: user.name,
                  nickname: user.nickname || ''
                };
              });
            },
            error: (error) => {
              console.error('Error fetching user details:', error);
              userIds.forEach(id => {
                this.userCache[id] = { name: 'Inconnu', nickname: '' };
              });
            }
          });
        }
      },
      error: (error) => {
        this.errorMessage = 'Échec du chargement des fiches';
        console.error('Error fetching fiches:', error);
        this.loading = false;
      }
    });
  }

  onFilterChange(): void {
    let temp = [...this.fiches];

    // Filter by search term (number or client)
    if (this.searchTerm.trim() !== '') {
      const searchLower = this.searchTerm.toLowerCase();
      temp = temp.filter(fiche =>
        fiche.demande?.demande_id?.toLowerCase().includes(searchLower) ||
        this.fetchUserName(fiche.demande?.user_id).toLowerCase().includes(searchLower)
      );
    }

    // Filter by status
    if (this.selectedStatus) {
      if (this.selectedStatus === 'sent') {
        temp = temp.filter(fiche => this.sentStatus[fiche.id] === true);
      } else if (this.selectedStatus === 'not_sent') {
        temp = temp.filter(fiche => !this.sentStatus[fiche.id]);
      }
    }

    // Filter by date
    if (this.selectedDate) {
      temp = temp.filter(fiche => {
        const ficheDate = fiche.date_transmission
          ? new Date(fiche.date_transmission).toISOString().split('T')[0]
          : null;
        return ficheDate === this.selectedDate;
      });
    }

    this.filteredFiches = temp;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedDate = '';
    this.onFilterChange();
  }

  fetchUserName(userId: number): string {
    if (!userId) return '...';
    if (this.userCache[userId]) {
      const info = this.userCache[userId];
      return info.name + (info.nickname ? ' ' + info.nickname : '');
    }
    return '...';
  }

  sendFiche(ficheId: number) {
    this.ficheService.sendFiche(ficheId).subscribe({
      next: (response) => {
        this.sentStatus[ficheId] = true;
        const fiche = this.fiches.find(f => f.id === ficheId);
        if (fiche) {
          fiche.date_transmission = response.date_transmission;
          fiche.status = 'sented';
        }
        this.onFilterChange();
        alert('Fiche de transmission envoyée avec succès!');
      },
      error: (error) => {
        console.error('Error sending fiche:', error);
        alert('Erreur lors de l\'envoi de la fiche de transmission.');
      }
    });
  }

  resendFiche(ficheId: number) {
    if (confirm('Êtes-vous sûr de vouloir renvoyer cette fiche de transmission?')) {
      this.ficheService.sendFiche(ficheId).subscribe({
        next: (response) => {
          const fiche = this.fiches.find(f => f.id === ficheId);
          if (fiche) {
            fiche.date_transmission = response.date_transmission;
            fiche.status = 'sented';
          }
          this.onFilterChange();
          alert('Fiche de transmission renvoyée avec succès!');
        },
        error: (error) => {
          console.error('Error resending fiche:', error);
          alert('Erreur lors du renvoi de la fiche de transmission.');
        }
      });
    }
  }

  goToFicheTransmission(ficheId: number) {
    this.router.navigate(['receptionist/fichedetails', ficheId]);
  }

  goToRapport(fiche: any) {
    console.log("🔎 Full fiche object:", fiche);
    console.log(`🛠️ Navigating to rapport with ID: ${fiche.rapport_id}`);

    if (fiche.rapport_id && fiche.rapport_id !== 'pending') {
      this.router.navigate(['receptionist/rapports/', fiche.rapport_id]);
    } else if (fiche.rapport_id === 'pending') {
      console.warn("⚠️ Le rapport est en cours de traitement. Veuillez réessayer plus tard.");
      alert("Le rapport est en cours de traitement. Veuillez réessayer plus tard ou rafraîchir la page.");
    } else {
      console.warn("⚠️ Aucun rapport trouvé pour cette demande.");
      alert("Aucun rapport trouvé pour cette demande.");
    }
  }
  // Rapport creation functionality has been moved to the validation component
  // This method is kept to fetch rapport IDs for display purposes only
  fetchRapportId(demandeId: string | number, fiche: any) {
    this.ficheService.getRapportIdByDemande(demandeId.toString()).subscribe({
      next: (response) => {
        console.log(`✅ Rapport ID found for demande ${demandeId}:`, response);

        if (response && response.rapport_id) {
          fiche.rapport_id = response.rapport_id;
          fiche.statusRapport = 'created';
          console.log('Updated fiche with rapport_id:', fiche.rapport_id);
        } else {
          console.warn('No rapport_id found in response:', response);
          fiche.rapport_id = null;
          fiche.statusRapport = 'not_created';
        }

        this.cdr.detectChanges(); // ✅ Force UI refresh
      },
      error: (error) => {
        console.error('Error fetching rapport_id:', error);
        fiche.rapport_id = null;
        fiche.statusRapport = 'not_created';
        this.cdr.detectChanges();
      }
    });
  }
}