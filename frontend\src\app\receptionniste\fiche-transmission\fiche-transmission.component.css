/* -------------------------------
   1) RESET GLOBAL & FOND
------------------------------- */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}

/* Empêcher tout scroll horizontal */
html, body {
  overflow-x: hidden;
}

body {
  background-color: #f7f7f7;
  color: #333;
  font-size: 1em; /* Ajustable */
}

/* -------------------------------
   2) CONTENEUR PRINCIPAL
------------------------------- */
.fiche-transmission-container {
  margin: 40px auto;
  padding: 25px 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  /* Animation d’apparition */
  animation: fadeInUp 0.8s ease-in-out;
  overflow: hidden; /* Pas de scroll horizontal interne */
}

/* -------------------------------
   3) BOUTON DE RETOUR
------------------------------- */
.back-btn {
  display: inline-block;
  background: #007bff;
  color: #fff;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 15px;
  transition: 0.3s;
}
.back-btn:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

/* -------------------------------
   4) TITRE PRINCIPAL
------------------------------- */
.fiche-transmission-container h3 {
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #2496d3;
  margin-bottom: 20px;
  border-bottom: 3px solid #2496d3;
  padding-bottom: 10px;
  position: relative;
}

/* (Optionnel) Icône décorative devant le titre */
.fiche-transmission-container h3::before {
  content: " ";
  position: absolute;
  left: calc(50% - 80px);
  transform: translateX(-100%);
  font-size: 1.2rem;
}

/* -------------------------------
   5) BLOC RÉSUMÉ (client, nb échantillons...)
------------------------------- */
.fiche-summary {
  text-align: center;
  margin-bottom: 20px;
  font-size: 0.95rem;
  color: #444;
  background: #f9fcff;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.fiche-summary p {
  margin: 5px 0;
  line-height: 1.4;
}

/* -------------------------------
   6) INDICATEURS / ERREUR / MESSAGES
------------------------------- */
.loading-message {
  font-size: 16px;
  color: #ff9800;
  text-align: center;
  margin-bottom: 15px;
  font-style: italic;
}

.error {
  font-size: 16px;
  color: red;
  text-align: center;
  margin-bottom: 15px;
  font-weight: bold;
}

.no-data {
  font-size: 16px;
  color: #d32f2f;
  text-align: center;
  margin-top: 20px;
  font-weight: bold;
}

/* -------------------------------
   7) TABLEAU DE DONNÉES
------------------------------- */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  background: #fff;
}

table thead tr th {
  background-color: #2496d3;
  color: white;
  padding: 1em;
  text-align: left;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 0.5px;
}

table tbody tr td {
  padding: 0.9em;
  text-align: left;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f2f2f2;
  transition: background-color 0.3s ease;
}

/* Lignes paires en gris clair (striped) */
table tbody tr:nth-child(even) {
  background-color: #fafafa;
}

/* Survol d’une ligne */
table tbody tr:hover td {
  background-color: #f0f8ff;
}

/* -------------------------------
   8) LISTE DES ANALYSES DEMANDÉES
------------------------------- */
.analyses-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* “Badges” pour les analyses */
.analysis-badge {
  background: #e0f4ff;
  color: #007bff;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 13px;
  white-space: nowrap;
}

/* -------------------------------
   9) TAG / Observations RAS
------------------------------- */
.observations-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
}
.tag-green {
  background-color: #dff0d8;
  color: #3c763d;
}

/* -------------------------------
   10) STATS / BOUTON IMPRIMER
------------------------------- */
.fiche-stats {
  margin-top: 20px;
  text-align: center;
  font-size: 0.95rem;
  color: #555;
}
.fiche-stats p {
  margin-bottom: 10px;
  font-weight: 500;
}

/* Bouton “Imprimer” */
.print-btn {
  background: #2496d3;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 5px;
  transition: 0.3s;
}
.print-btn:hover {
  background: #1c7bac;
  transform: translateY(-2px);
}

/* -------------------------------
   11) ANIMATION D’APPARITION
------------------------------- */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* -------------------------------
   12) RESPONSIVE
------------------------------- */
@media (max-width: 768px) {
  .fiche-transmission-container {
    padding: 15px;
  }

  table thead tr th,
  table tbody tr td {
    padding: 0.7em;
    font-size: 13px;
  }

  .fiche-summary,
  .fiche-stats {
    font-size: 0.88rem;
  }
  .analysis-badge {
    font-size: 12px;
    padding: 2px 6px;
  }

  .back-btn,
  .print-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
}
