import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Notification } from '../../../models/notification.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NotifclientService {
  // private apiUrl = 'http://localhost:8000/api';
  // constructor(private http: HttpClient) {}
  //   getBringSampleNotifications(): Observable<{ notifications: Notification[] }> {
  //     const headers = new HttpHeaders({
  //           'Authorization': `Bearer ${localStorage.getItem('token')}`,
  //           'Content-Type': 'application/json',
  //         });
  //     return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/samples`, { headers });
  //   }
}
