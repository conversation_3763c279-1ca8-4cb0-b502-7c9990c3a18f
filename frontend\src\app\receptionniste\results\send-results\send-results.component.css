.send-results-container {
  width: 95%;
  max-width: 1200px;
  margin: 30px auto;
  padding: 25px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 15px;
}

h2 {
  font-size: 24px;
  color: #2496d3;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.icon-primary {
  color: #2496d3;
}

.back-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background-color: #5a6268;
}

/* Loading, error, and success messages */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #2496d3;
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Details section */
.details-section {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

h3 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 20px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-label {
  font-weight: bold;
  color: #666;
  font-size: 14px;
}

.detail-value {
  font-size: 16px;
  color: #333;
}

/* Files section */
.files-section {
  margin-bottom: 25px;
}

/* Files container - new layout */
.files-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Existing files display */
.existing-files {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

/* Status section */
.status-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  border-radius: 6px;
  padding: 12px 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-icon {
  color: #2496d3;
  font-size: 20px;
}

.file-name {
  font-weight: 500;
  color: #333;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.file-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-action-btn.view {
  background-color: #2496d3;
  color: white;
}

.file-action-btn.view:hover {
  background-color: #1a7bb9;
}

.file-action-btn.download {
  background-color: #4caf50;
  color: white;
}

.file-action-btn.download:hover {
  background-color: #3d8b40;
}

.file-action-btn.delete {
  background-color: #f44336;
  color: white;
}

.file-action-btn.delete:hover {
  background-color: #d32f2f;
}

/* Status display */
.status-display {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 6px;
  justify-content: center;
}

.status-label {
  font-weight: bold;
  color: #333;
  font-size: 16px;
}

.status-value {
  font-size: 16px;
  font-weight: bold;
  padding: 5px 12px;
  border-radius: 4px;
}

.status-sent {
  color: #2e7d32;
}

.status-pending {
  color: #f57c00;
}

.send-action {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Upload section */
.upload-section {
  margin-bottom: 25px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.upload-info {
  margin-bottom: 20px;
  color: #666;
  font-weight: 500;
}

.file-upload-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.file-upload-box {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 0;
  margin-bottom: 15px;
  color: #2496d3;
}

.file-upload-area {
  position: relative;
  border: 2px dashed #ccc;
  border-radius: 5px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s;
}

.file-upload-area:hover {
  border-color: #2496d3;
  background-color: #f0f7fc;
}

.file-upload-area.has-file {
  border-color: #4caf50;
  background-color: #f1f8e9;
}

.file-upload-input {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.file-upload-label fa-icon {
  font-size: 24px;
  color: #2496d3;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
  min-width: 200px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-btn {
  background-color: #2496d3;
  color: white;
}

.upload-btn:hover:not(:disabled) {
  background-color: #1a7bb9;
}

.send-btn {
  background-color: #4caf50;
  color: white;
}

.send-btn:hover:not(:disabled) {
  background-color: #3d8b40;
}

/* Instructions */
.instructions {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-top: 30px;
  position: relative;
  border-left: 4px solid transparent;
  overflow: hidden;
}

.instructions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #2496d3;
  background-size: 100% 200%;
  animation: shimmer 2s infinite linear;
}

@keyframes shimmer {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 0% 200%;
  }
}

.instructions h4 {
  color: #333;
  margin-top: 0;
  display: flex;
  align-items: center;
}


.instructions ul {
  padding-left: 20px;
  margin-bottom: 0;
}

.instructions li {
  margin-bottom: 12px;
  color: #555;
  position: relative;
  padding-left: 5px;
  list-style-type: none;
  display: flex;
  align-items: center;
}

.instructions li::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 12px;
  background-color: #2496d3;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(36, 150, 211, 0.3);
}

/* Responsive styles */
@media (max-width: 768px) {
  .send-results-container {
    width: 100%;
    padding: 15px;
    margin: 15px auto;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .file-upload-container {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}