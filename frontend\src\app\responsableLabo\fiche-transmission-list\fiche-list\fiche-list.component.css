/* -------------------------------
   1) RESET GLOBAL
------------------------------- */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Mont<PERSON><PERSON>', sans-serif;
}

/* Empêcher le scroll horizontal global */
html, body {
  overflow-x: hidden;
}

/* -------------------------------
   2) CONTENEUR PRINCIPAL
------------------------------- */
.transmission-container {
  margin: 40px auto;
  padding: 25px 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
}

/* Titre principal */
.transmission-container h3 {
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5em;
  font-weight: bold;
  text-align: center;
  color: #2496d3;
  margin-bottom: 1.5em;
  border-bottom: 3px solid #2496d3;
  padding-bottom: 0.625em;
}

/* -------------------------------
   3) BARRE DE RECHERCHE + FILTRES
------------------------------- */
.filter-bar {
  display: flex;
  max-width: 90%;
  margin: 0 auto 20px;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  flex-direction: column; /* Stacks the label and input vertically */
  gap: 0.5rem; /* Adds space between label and input */
}

.filter-group label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #2496d3;
  box-shadow: 0 0 0 3px rgba(36, 150, 211, 0.2);
  outline: none;
}

#search-demande {
  width: 180px;
}

#search-client {
  width: 180px;
}

.btn-clear {
  margin-left: auto;
  padding: 0.5rem 1rem;
  background-color: #808080; /* Changed to gray */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 600;
  align-self: flex-end;
}

.btn-clear:hover {
  background-color: #666666; /* Darker gray on hover */
}

/* -------------------------------
   4) TABLEAU DE DONNÉES
------------------------------- */
.transmission-container table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1.25em;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.transmission-container table thead tr {
  background-color: #2496d3;
  color: white;
  text-align: left;
}

.transmission-container table thead tr th {
  padding: 0.938em;
  font-weight: 600;
}

.transmission-container table tbody tr {
  border-bottom: 1px solid #ddd;
  transition: background-color 0.3s ease;
}

.transmission-container table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.transmission-container table tbody tr:hover {
  background-color: #f1f1f1;
}

.transmission-container table tbody tr td {
  padding: 0.938em;
  vertical-align: middle;
}

/* -------------------------------
   5) BOUTONS D'ACTION
------------------------------- */
.transmission-container table tbody tr td button {
  padding: 0.5em 0.75em;
  margin-right: 0.313em;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875em;
  transition: all 0.3s ease;
  color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.transmission-container table tbody tr td button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Bouton "Voir détails" */
.details-btn {
  background-color: #555;
}

.details-btn:hover {
  background-color: #333;
}

/* Bouton "Créer Rapport" */
.create-btn {
  background-color: #1e78b5; /* Perfect blue */
}

.create-btn:hover {
  background-color: #1a6a9e; /* Darker blue on hover */
}

/* Loading button style */
.loading-btn {
  opacity: 0.8;
  cursor: not-allowed;
}

/* Loading spinner animation */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Bouton "Voir Rapport" */
.view-btn {
  background-color: #1e78b5; /* Perfect blue */
}

.view-btn:hover {
  background-color: #1a6a9e; /* Darker blue on hover */
}

/* Pending button style */
.pending-btn {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #1e78b5;
  position: relative;
  overflow: hidden;
}

.pending-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  100% {
    left: 100%;
  }
}

/* -------------------------------
   6) STATUTS & MESSAGES
------------------------------- */
.loading-message, .error, .no-data {
  text-align: center;
  padding: 1.25em;
  margin: 1.25em 0;
  border-radius: 5px;
}

.loading-message {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.error {
  background-color: #ffebee;
  color: #c62828;
}

.no-data {
  background-color: #f5f5f5;
  color: #616161;
}

.status-created {
  color: #4caf50;
  font-weight: bold;
  display: inline-block;
  padding: 0.313em 0.625em;
  border-radius: 4px;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-not-created {
  color: #f44336;
  font-weight: bold;
  display: inline-block;
  padding: 0.313em 0.625em;
  border-radius: 4px;
  background-color: rgba(244, 67, 54, 0.1);
}

/* -------------------------------
   7) RESPONSIVE
------------------------------- */
@media (max-width: 48em) { /* ~768px */
  .transmission-container table thead tr th,
  .transmission-container table tbody tr td {
    padding: 0.625em;
    font-size: 0.9em;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group input,
  .filter-group select {
    width: 100%;
  }

  .btn-clear {
    margin-left: 0;
    margin-top: 1rem;
    width: 100%;
  }
}

@media (max-width: 30em) { /* ~480px */
  .transmission-container {
    padding: 15px;
  }

  .transmission-container table {
    display: block;
    overflow-x: auto;
  }
}
