import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RegistreSuiviService } from '../registres-list/registre.service';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPencilAlt, faCheckCircle, faSpinner } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-registre',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FontAwesomeModule],
  templateUrl: './registre.component.html',
  styleUrl: './registre.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // For FontAwesome icons
})
export class RegistreSuiviDetailComponent implements OnInit {
  registre: any = null;
  registreForm: FormGroup = this.fb.group({});
  loading: boolean = false;
  error: string | null = null;
  registreId!: number;
  isEditing: { [key: number]: boolean } = {}; // ✅ Track edit state per row
  analysisForm!: FormGroup; // ✅ Reactive form

  // Font Awesome icons
  faPencilAlt = faPencilAlt;
  faCheckCircle = faCheckCircle;
  faSpinner = faSpinner;

  // Loading spinner properties
  showLoadingModal = false;
  loadingMessage = 'Traitement en cours...';
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';
  constructor(
    private route: ActivatedRoute,
    private registreSuiviService: RegistreSuiviService,
    private fb: FormBuilder
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.registreId = params['id'];
      if (this.registreId) {
        this.loadRegistreDetails(this.registreId);
      }
    });
  }

  loadRegistreDetails(registreId: number): void {
    this.loading = true;
    this.error = null;

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Chargement des détails du registre...';

    this.registreSuiviService.getById(registreId).subscribe({
      next: (data) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        this.registre = data;
        this.initializeForm();
        this.loading = false;
      },
      error: (err) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error('Error fetching registre details:', err);
        this.error = 'Failed to load registre details.';
        this.loading = false;

        // Show error notification
        this.notificationMessage = "Erreur lors du chargement des détails du registre.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }

  initializeForm(): void {
    const formControls: any = {};

    this.registre.registres.forEach((reg: any) => {
      formControls[`echantillon_envoye_par_${reg.id}`] = this.fb.control({
        value: reg.echantillon_envoye_par || '',
        disabled: true
      });
      formControls[`echantillon_recu_par_${reg.id}`] = this.fb.control({
        value: reg.echantillon_recu_par || '',
        disabled: true
      });
      formControls[`lieu_conservation_${reg.id}`] = this.fb.control({
        value: reg.lieu_conservation || '',
        disabled: true
      });
      formControls[`date_effective_analyse_${reg.id}`] = this.fb.control({
        value: reg.date_effective_analyse || '',
        disabled: true
      });
      formControls[`date_emission_rapport_${reg.id}`] = this.fb.control({
        value: reg.date_emission_rapport || '',
        disabled: true
      });
      this.isEditing[reg.id] = false; // Still set to false initially
    });

    this.registreForm = this.fb.group(formControls);
  }

  toggleEdit(regId: number): void {
    this.isEditing[regId] = !this.isEditing[regId]; // Toggle editing state
    const controls = [
      `echantillon_envoye_par_${regId}`,
      `echantillon_recu_par_${regId}`,
      `lieu_conservation_${regId}`,
      `date_effective_analyse_${regId}`,
      `date_emission_rapport_${regId}`
    ];

    controls.forEach(controlName => {
      const control = this.registreForm.get(controlName);
      if (control) {
        if (this.isEditing[regId]) {
          control.enable(); // Enable when editing
        } else {
          control.disable(); // Disable when not editing
        }
      }
    });
  }

  updateRegistre(reg: any): void {
    const updatedData: any = {};

    const fields = [
      'echantillon_envoye_par',
      'echantillon_recu_par',
      'lieu_conservation',
      'date_effective_analyse',
      'date_emission_rapport'
    ];

    fields.forEach(field => {
      const formField = `${field}_${reg.id}`;
      if (this.registreForm.controls[formField].value !== reg[field]) {
        updatedData[field] = this.registreForm.controls[formField].value;
      }
    });

    if (Object.keys(updatedData).length === 0) {
      // Show notification for no changes
      this.notificationMessage = "Aucune modification détectée.";
      this.notificationIcon = '⚠️';
      this.notificationColor = '#ffc107';
      this.showNotification = true;

      // Hide notification after 3 seconds
      setTimeout(() => {
        this.showNotification = false;
      }, 3000);
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Mise à jour du registre...';

    this.registreSuiviService.update(reg.id, updatedData).subscribe({
      next: () => {
        // Hide loading spinner
        this.showLoadingModal = false;

        // Show success notification
        this.notificationMessage = 'Registre mis à jour avec succès !';
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 3 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 3000);

        this.loadRegistreDetails(this.registreId); // Reload data after update
        this.isEditing[reg.id] = false; // Lock the fields again
      },
      error: (err) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error('Erreur de mise à jour du registre:', err);

        // Show error notification
        this.notificationMessage = "Échec de la mise à jour.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }
}
