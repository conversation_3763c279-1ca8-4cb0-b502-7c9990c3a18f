/* Import Fonts and Icons */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* Container Styles */
.fiches-container {
  font-family: 'Montserrat', sans-serif;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 1rem auto;
  max-width:90%;
}

/* 2. HEADER */
.fiches-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.fiches-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 4px solid #2496d3;
  padding-bottom: 0.25rem;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, #000, #888);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* Style for the icon inside the title to make it visible */
.fiches-title fa-icon {
  color: #000000; /* Blue color to match the border */
  -webkit-text-fill-color: #2496d3; /* Override the transparent text */
  filter: drop-shadow(0 0 2px rgba(36,150,211,0.5)); /* Add a subtle glow */
}

.back-btn {
  background: #007bff;
  color: #fff;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  transition: transform 0.3s, box-shadow 0.3s;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Glow animation */
@keyframes glowText {
  from { text-shadow: 0 0 8px rgba(36,150,211,0.4); }
  to   { text-shadow: 0 0 16px rgba(36,150,211,0.8); }
}

/* 3. FILTER BAR */
.filter-bar {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 0.5rem; /* Reduced from 1rem to make input fields closer together */
  margin-bottom: 1.5rem;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  justify-content: flex-start; /* Changed from space-between to ensure buttons stay at end */
}

.filters-section {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;

  margin-bottom: 1.5rem;
  max-width: 75%;
  margin-left: auto;
  margin-right: auto;
}

/* Group to contain the input fields closer together */
.filter-inputs-group {
  display: flex;
  gap: 0.25rem; /* Further reduced gap between input fields */
  flex: 1;
}

.filter-group {
  display: flex;
  flex-direction: column;
  flex: 0.8; /* Reduced from 1 to make it slightly smaller */
  min-width: 170px; /* Further reduced from 180px */
  margin-right: 0; /* Remove right margin to bring groups closer */
}

.filter-group label {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-bottom: 0.2rem; /* Reduce space between label and input */
}

.filter-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.3s, box-shadow 0.3s;
  width: 80%;
  
}

.filter-input:focus {
  outline: none;
  border-color: #2496d3;
  box-shadow: 0 0 4px rgba(36,150,211,0.3);
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.input-with-icon .filter-input {
  padding-left: 2.5rem;
}

/* Filter buttons container */
.filter-buttons {
  display: flex;
  gap: 0.8rem;
  margin-left: 2rem; /* Add more space between buttons and input fields */
}

/* Filter buttons */
.btn-filter-apply,
.btn-clear {
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  border: none;
  color: #fff;
  font-weight: bold;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  transition: transform 0.3s, box-shadow 0.3s;
  white-space: nowrap;
}

.btn-filter-apply {
  background: #28a745;
}

.btn-filter-apply:hover {
  background: #5cb85c;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40,167,69,0.3);
}

.btn-clear {
  background: #6c757d;
}

.btn-clear:hover {
  background: #868e96;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108,117,125,0.3);
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

thead {
  background: #2496d3;
  color: white;
}

th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

tbody tr {
  background-color: white;
  transition: background-color 0.3s;
}

tbody tr:hover {
  background-color: #f1f3f5;
}

tbody tr:last-child td {
  border-bottom: none;
}

/* Button Styles */
.details-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  max-width: 150px;
}

.details-btn:hover {
  background: linear-gradient(to right, #1a7bb9, #085585);
  transform: translateY(-2px);
}

/* Message Styles */
.loading-message, .error-message, .no-data-message {
  text-align: center;
  padding: 2rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.loading-message {
  background-color: #e9f5fe;
  color: #2496d3;
}

.error-message {
  background-color: #ffe9e9;
  color: #dc3545;
}

.no-data-message {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* 7. RESPONSIVE */
@media (max-width: 768px) {
  .fiches-header {
    flex-direction: column;
    gap: 1rem;
  }

  .filters-section {
    flex-direction: column;
    max-width: 100%;
  }

  .filter-inputs-group {
    flex-direction: column;
    width: 100%;
    gap: 0.8rem;
  }

  .filter-group {
    width: 100%;
  }

  .filter-buttons {
    margin-left: 0;
    width: 100%;
    justify-content: space-between;
    margin-top: 1rem; /* Add space above buttons on mobile */
  }

  .btn-filter-apply,
  .btn-clear {
    flex: 1;
  }

  table {
    display: block;
    overflow-x: auto;
  }

  th, td {
    padding: 0.8rem;
  }

  .details-btn {
    max-width: none;
  }
}
