import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RegistreSuiviService {
  private apiUrl = 'http://127.0.0.1:8000/api/registre-suivis'; // Adjust based on your Laravel API URL

  constructor(private http: HttpClient) {}

  // Get all registre suivis (summary view)
  getAll(): Observable<any> {
    return this.http.get<any>(this.apiUrl);
  }

  // Get detailed information for a specific registre suivi
  getById(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${id}`);
  }

  // Create a new registre suivi from a given demande_id
  create(demandeId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/${demandeId}`, {});
  }

  // Update a registre with new data
  update(id: number, updatedData: any): Observable<any> {
    return this.http.put<any>(`http://localhost:8000/api/registres/${id}`, updatedData);
  }
}
