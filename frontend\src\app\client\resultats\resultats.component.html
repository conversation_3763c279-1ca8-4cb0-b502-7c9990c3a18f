<div class="resultats-container">
  <div class="header">
    <h2>
      <span class="title-text">Résultats d'Analyses</span>
    </h2>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner">
      <i class="fas fa-spinner fa-spin fa-2x"></i>
    </div>
    <p>Chargement des résultats...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="error-message">
    <i class="fas fa-exclamation-triangle"></i>
    <p>{{ error }}</p>
  </div>

  <!-- No results message -->
  <div *ngIf="!isLoading && !error && !hasAnyFiles()" class="no-results">
    <i class="fas fa-exclamation-triangle fa-2x"></i>
    <p>Aucun résultat n'est disponible pour cette demande.</p>
  </div>

  <!-- Results content -->
  <div *ngIf="!isLoading && !error && hasAnyFiles()" class="results-content">
    <div class="demande-info">
      <h3>Informations de la Demande</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="info-label">Numéro de Demande:</span>
          <span class="info-value">{{ demandeId }}</span>
        </div>
        <div class="info-item" *ngIf="demande?.demande_date">
          <span class="info-label">Date de Création:</span>
          <span class="info-value">{{ demande.demande_date }}</span>
        </div>
        <div class="info-item" *ngIf="rapport?.status">
          <span class="info-label">Statut du Rapport:</span>
          <span class="info-value status-badge">{{ rapport.status === 'sent' ? 'Envoyé' : 'En attente' }}</span>
        </div>
        <div class="info-item" *ngIf="demande?.samples">
          <span class="info-label">Nombre d'Échantillons:</span>
          <span class="info-value">{{ demande.samples.length }}</span>
        </div>
      </div>
    </div>

    <div class="files-section">
      <h3>Fichiers Disponibles</h3>
      <div class="files-grid">
        <!-- Report file -->
        <div *ngIf="hasReportFile()" class="file-card">

          <div class="file-icon">
            <i class="fas fa-file-invoice fa-4x"></i>
          </div>
          <div class="file-info">
            <h4>Rapport d'Analyse</h4>
            <p class="file-name">{{ getReportFileName() }}</p>
          </div>
          <button class="download-button" (click)="downloadFile('report')">
            <i class="fas fa-download"></i>
            <span>Télécharger</span>
          </button>
        </div>

        <!-- Invoice file -->
        <div *ngIf="hasInvoiceFile()" class="file-card">

          <div class="file-icon">
            <i class="fas fa-file-invoice fa-4x"></i>
          </div>
          <div class="file-info">
            <h4>Facture</h4>
            <p class="file-name">{{ getInvoiceFileName() }}</p>
          </div>
          <button class="download-button" (click)="downloadFile('invoice')">
            <i class="fas fa-download"></i>
            <span>Télécharger</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Note section -->
    <div class="note-section">
      <h3>Note</h3>
      <p>Cliquez sur le bouton "Télécharger" pour obtenir vos fichiers.</p>
      <p>Pour toute question concernant vos résultats, veuillez contacter notre service client.</p>
    </div>
  </div>
</div>
