<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FicheTransmission extends Model
{
    use HasFactory;

    protected $table = 'fiches_transmission'; // ✅ Ensure correct table name

    protected $fillable = [
        'demande_id',
        'client_name',
        'client_nickname',
        'date_transmission',
        'statusRapport'
    ];

    // ✅ Define the correct relationship (Belongs to a single Demande)
    public function demande()
    {
        return $this->belongsTo(Demande::class, 'demande_id', 'demande_id'); // ✅ Reference string demande_id
    }

    public function fiches()
    {
        return $this->hasMany(Fiche::class, 'fiche_transmission_id');
    }
    
}
