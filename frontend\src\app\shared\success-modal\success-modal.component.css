/* ✅ Import Google Font to match existing design */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap");

/* ✅ Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
  font-family: "Poppins", sans-serif;
}

/* ✅ Modal Container */
.modal-container {
  background: #ffffff;
  border-radius: 15px;
  padding: 40px 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 450px;
  width: 90%;
  animation: modalSlideIn 0.4s ease-out;
  position: relative;
}

/* ✅ Success Icon Container */
.success-icon-container {
  margin-bottom: 25px;
}

.success-icon {
  display: inline-block;
  animation: successIconPulse 0.6s ease-out;
}

/* ✅ Modal Content */
.modal-content {
  color: #333;
}

.modal-title {
  font-size: 28px;
  font-weight: 600;
  color: #2496d3;
  margin-bottom: 15px;
  letter-spacing: 1px;
}

.modal-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.5;
}

/* ✅ Modal Actions */
.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

/* ✅ Primary Button (matches signup component) */
.btn-primary {
  background: #2496d3;
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 600;
  padding: 15px 40px;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.4);
  min-width: 200px;
}

.btn-primary:hover {
  background: rgba(15, 151, 192, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.5);
}

.btn-primary:focus {
  outline: 2px solid #2496d3;
  outline-offset: 2px;
}

/* ✅ Secondary Button */
.btn-secondary {
  background: transparent;
  color: #666;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  padding: 10px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  min-width: 120px;
}

.btn-secondary:hover {
  background: #f5f5f5;
  border-color: #ccc;
  color: #333;
}

.btn-secondary:focus {
  outline: 2px solid #666;
  outline-offset: 2px;
}

/* ✅ Animations */
@keyframes fadeIn {
  from { 
    opacity: 0; 
  }
  to { 
    opacity: 1; 
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes successIconPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ✅ Responsive Design */
@media (max-width: 768px) {
  .modal-container {
    padding: 30px 20px;
    max-width: 350px;
  }

  .modal-title {
    font-size: 24px;
  }

  .modal-message {
    font-size: 14px;
  }

  .btn-primary {
    font-size: 14px;
    padding: 12px 30px;
    min-width: 180px;
  }

  .btn-secondary {
    font-size: 12px;
    padding: 8px 16px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .modal-container {
    padding: 25px 15px;
    max-width: 300px;
  }

  .modal-title {
    font-size: 22px;
  }

  .success-icon svg {
    width: 50px;
    height: 50px;
  }
}
