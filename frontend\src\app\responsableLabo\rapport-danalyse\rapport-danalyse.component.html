<div class="rapport-container" *ngIf="reportData">
  <!-- Header -->
  <header class="rapport-header">
    <div class="header-top">


      <div class="lab-info">
        <h2>RAPPORT D'ANALYSE N° {{ reportData.rapport_id }}</h2>
        <p>Laboratoire de gestion des analyses d'échantillons de produits aquatiques</p>
      </div>


    </div>
  </header>

  <!-- Infos Client + Laboratoire -->
  <section class="rapport-additional-info">
    <div class="info-left client-info">
      <h3>Client</h3>
      <table>
        <tbody>
          <tr>
            <td class="label"><strong>Nom :</strong></td>
            <td class="input-field">{{ clientInfo.name || '⏳ Chargement...' }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Email :</strong></td>
            <td class="input-field">{{ clientInfo.email || '--' }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Téléphone :</strong></td>
            <td class="input-field">{{ clientInfo?.phone || '--' }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Adresse :</strong></td>
            <td class="input-field">{{ clientInfo?.adress || '--' }}</td>
          </tr>


        </tbody>
      </table>
    </div>

    <div class="info-right laboratory-info">
      <h3>Laboratoire d'Analyse</h3>
      <table>
        <tbody>
          <tr>
            <td class="label"><strong>Nom :</strong></td>
            <td class="lab-info">{{ labInfo.nom }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Adresse :</strong></td>
            <td class="lab-info">{{ labInfo.adresse }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Contact :</strong></td>
            <td class="lab-info">+{{ labInfo.contact }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>

  <!-- Description de l’échantillon -->
  <section class="rapport-description">
    <h2>DESCRIPTION DE L'ÉCHANTILLON</h2>
    <table>
      <thead>
        <tr>
          <th>Code échantillon (Client)</th>
          <th>Date de réception</th>
          <th>Nature</th>
          <th>Provenance</th>
          <th>Origine du prélèvement</th>
          <th>Date de prélèvement</th>
          <th>Site</th>
          <th>Nom du préleveur</th>
          <th>Lot</th>
          <th>Référence</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let sample of samples">
          <td>{{ sample.identification_echantillon }}</td>
          <td>{{ sample.reception_date | date: 'yyyy-MM-dd' }}</td>
          <td>{{ sample.nature_echantillon }}</td>
          <td>{{ sample.provenance }}</td>
          <td>{{ sample.origine_prelevement }}</td>
          <td>{{ sample.date_prelevement | date: 'yyyy-MM-dd' }}</td>
          <td>{{ sample.site }}</td>
          <td>{{ sample.nom_preleveur }}</td> <!-- Nom du préleveur not available in sample data -->
          <td>{{ sample.lot }}</td>
          <td>{{ sample.reference }}</td>
        </tr>
      </tbody>
    </table>
  </section>
  <!-- Résultats d'analyses -->
  <section class="rapport-resultats">
    <h2>RÉSULTATS D'ANALYSES</h2>
    <form [formGroup]="analysisForm">
      <table>
        <thead>
          <tr>

            <th>Code échantillon</th>
            <th>Paramètre</th>
            <th>Mesurande</th>
            <th>Unité</th>
            <th>Limite d'acceptabilité</th>
            <th>Méthode d'Analyse utilisée</th>
            <th>Date d'Analyse</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let result of analysisResults; let i = index" [ngClass]="{'editing-row': isEditing[i]}">
            <!-- Edit / Update Button -->


            <!-- Read-only or Editable Fields -->
            <td>{{ result.code_echantillon }}</td>
            <td>{{ result.parametre }}</td>
            <td><input type="text" [formControlName]="'mesurande_' + i" placeholder="Mesurande"></td>
            <td><input type="text" [formControlName]="'unite_' + i" placeholder="Unité"></td>
            <td><input type="text" [formControlName]="'limite_acceptabilite_' + i" placeholder="Limite Acceptabilité"></td>
            <td>{{ result.methode_analyse_utilisee }}</td>
            <td><input type="date" [formControlName]="'date_analyse_' + i"></td>
            <td class="action-buttons-cell">
              <button *ngIf="!isEditing[i]" class="edit-btn" (click)="toggleEdit(i)">
                <fa-icon [icon]="faPen" style="margin-right: 10px;"></fa-icon>Modifier
              </button>
              <button *ngIf="isEditing[i]" class="update-btn" (click)="updateRow(i)">
                <fa-icon [icon]="faCheck" style="margin-right: 10px;"></fa-icon>Mettre à jour
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </form>
  </section>

  <div class="action-buttons" [hidden]="hideButtons">
    <button
      *ngIf="reportData && reportData.status !== 'sent'"
      class="send-btn"
      [ngClass]="{'approve-btn': !areFieldsEmpty, 'disabled-btn': areFieldsEmpty}"
      [disabled]="areFieldsEmpty"
      (click)="envoyerRapport()"
    >
      <fa-icon [icon]="faPaperPlane" style="margin-right: 10px;"></fa-icon>Envoyer
    </button>

    <button class="print-btn" (click)="printRapport()">
      <fa-icon [icon]="faPrint" style="margin-right: 10px;"></fa-icon>Exporter PDF
    </button>
  </div>
</div>

<!-- Loading Spinner Overlay -->
<div *ngIf="showLoadingModal" class="loading-overlay">
  <div class="loading-popup">
    <div class="spinner"></div>
    <h3>{{ loadingMessage }}</h3>
  </div>
</div>

<!-- Notification de soumission -->
<div *ngIf="showNotification" class="notification-overlay">
  <div class="notification-popup">
    <div class="notification-icon" [style.color]="notificationColor">{{ notificationIcon }}</div>
    <div class="notification-message">{{ notificationMessage }}</div>
  </div>
</div>
