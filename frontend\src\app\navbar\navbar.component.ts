import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NotificationsComponent } from '../notification/notification.component';
import { ViewportScroller } from '@angular/common';



@Component({
    selector: 'app-navbar',
    standalone: true,
    imports: [CommonModule,NotificationsComponent],
    templateUrl: './navbar.component.html',
    styleUrls: ['./navbar.component.css']
})
export class NavbarComponent implements OnInit {
  role: string | null = null;
  user: [] | null = null;

  constructor(private router: Router, private viewportScroller: ViewportScroller) {}

  ngOnInit(): void {
    this.role = localStorage.getItem('user_role');

    // Listen for storage events to detect changes in role (for multi-tab synchronization)
    window.addEventListener('storage', (event) => {
      if (event.key === 'user_role') {
        this.role = localStorage.getItem('user_role');
      }
    });

  }
  // Special method to navigate to the about section
  navigateToAbout(): void {
    console.log('Navigating to about section');

    // Check if we're already on the home page
    if (this.router.url === '/' || this.router.url === '') {
      // Scroll to about section directly
      const aboutDescriptionSection = document.querySelector('.about-container div[id="about-section"]');

      if (aboutDescriptionSection) {
        console.log('Found about description section directly, scrolling to it');
        aboutDescriptionSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else {
        console.log('About description section not found directly');
      }
    } else {
      // Navigate to home page first, then scroll to about section
      console.log('Navigating to home page, then scrolling to about section');
      this.router.navigate(['']).then(() => {
        // Wait for navigation to complete and DOM to update
        setTimeout(() => {
          const aboutDescriptionSection = document.querySelector('.about-container div[id="about-section"]');

          if (aboutDescriptionSection) {
            console.log('Found about description section after navigation, scrolling to it');
            aboutDescriptionSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
          } else {
            console.log('About description section not found after navigation');
          }
        }, 1000);
      });
    }
  }

  navigateTo(path: string): void {
    // Special handling for services section when on home page
    if (path === '/services' && this.role === null) {
      // Check if we're already on the home page
      if (this.router.url === '/' || this.router.url === '') {
        // Scroll to services section smoothly and center it
        const servicesElement = document.getElementById('services-section');
        if (servicesElement) {
          // Calculate position to center the element in the viewport
          const elementRect = servicesElement.getBoundingClientRect();
          const absoluteElementTop = elementRect.top + window.pageYOffset;
          const middle = absoluteElementTop - (window.innerHeight / 2) + (elementRect.height / 2);

          // Scroll to the calculated position smoothly
          window.scrollTo({
            top: middle,
            behavior: 'smooth'
          });
        } else {
          // If element not found, navigate to services page as fallback
          this.router.navigate([path]).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
        }
      } else {
        // If not on home page, navigate to home page first, then scroll to services
        this.router.navigate(['/']).then(() => {
          // Wait for navigation to complete and DOM to update
          setTimeout(() => {
            const servicesElement = document.getElementById('services-section');
            if (servicesElement) {
              // Calculate position to center the element in the viewport
              const elementRect = servicesElement.getBoundingClientRect();
              const absoluteElementTop = elementRect.top + window.pageYOffset;
              const middle = absoluteElementTop - (window.innerHeight / 2) + (elementRect.height / 2);

              // Scroll to the calculated position smoothly
              window.scrollTo({
                top: middle,
                behavior: 'smooth'
              });
            }
          }, 500); // Short delay to ensure DOM is updated
        });
      }
      return;
    }

    // Special handling for profile navigation based on user role
    if (path === '/profile') {
      const role = localStorage.getItem('user_role');

      switch(role) {
        case 'receptionist':
          this.router.navigate(['/receptionist/profile']).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
          break;
        case 'client':
          this.router.navigate(['/client/profile']).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
          break;
        case 'admin':
          this.router.navigate(['/admin/profile']).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
          break;
        case 'responsable':
          this.router.navigate(['/responsable/profile']).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
          break;
        case 'director':
          this.router.navigate(['/director/profile']).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
          break;
        default:
          this.router.navigate([path]).then(() => {
            this.viewportScroller.scrollToPosition([0, 0]);
          });
      }
    } else {
      // For all other navigation, navigate and scroll to top
      this.router.navigate([path]).then(() => {
        this.viewportScroller.scrollToPosition([0, 0]);
      });
    }
  }
  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user_role');
    localStorage.removeItem('user');
    this.role = null;
    this.user=[];
    this.router.navigate(['/login']);
  }


}
