import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  imports: [CommonModule, RouterModule] // ✅ Nécessaire pour la navigation
})
export class DashboardDirectorComponent implements OnInit {
  name: string | null = null;
  nickname: string | null = null;
  constructor(private router: Router) {}

  ngOnInit(): void {
    console.log("📌 Tableau de bord du réceptionniste chargé.");
    
    this.name=this.getUserProperty('name')
    this.nickname=this.getUserProperty('nickname')
  }
  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');
    
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null; // Return the value if exists, otherwise null
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }

    return null;
  }
  // ✅ Méthodes individuelles pour chaque action
  openDerogationes() {
    this.router.navigate(['/derogation']);
  }

  openValidation() {
    this.router.navigate(['/director/validation']);
  }

 


  openNotifications() {
    this.router.navigate(['director/Derogated/notifications/']);
  }


  openFicheDerogation() {
    this.router.navigate(['/director/fiche-derogation']);
  }

  openDevis() {
    this.router.navigate(['/director/devis']);
  }

  openRapports() {
    this.router.navigate(['director/reports']);
  }
}
