import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { DashboardService, DashboardStatistics } from './dashboard.service';
import { StatisticsRefreshService } from '../../shared/services/statistics-refresh.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  imports: [CommonModule, RouterModule] // ✅ Nécessaire pour la navigation
})
export class DashboardDirectorComponent implements OnInit, OnDestroy {
  name: string | null = null;
  nickname: string | null = null;

  // Statistics properties
  pendingDerogations: number = 0;
  pendingRapports: number = 0;
  isLoadingStats: boolean = true;
  statsError: string | null = null;

  // Subscription for refresh events
  private refreshSubscription: Subscription = new Subscription();

  constructor(
    private router: Router,
    private dashboardService: DashboardService,
    private statisticsRefreshService: StatisticsRefreshService
  ) {}

  ngOnInit(): void {
    console.log("📌 Tableau de bord du directeur chargé.");

    this.name = this.getUserProperty('name');
    this.nickname = this.getUserProperty('nickname');

    // Load dashboard statistics
    this.loadDashboardStatistics();

    // Subscribe to refresh events
    this.refreshSubscription = this.statisticsRefreshService.refresh$.subscribe(() => {
      this.loadDashboardStatistics();
    });
  }

  ngOnDestroy(): void {
    // Clean up subscription
    this.refreshSubscription.unsubscribe();
  }

  /**
   * Load dashboard statistics from the backend
   */
  loadDashboardStatistics(): void {
    this.isLoadingStats = true;
    this.statsError = null;

    this.dashboardService.getDashboardStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.pendingDerogations = response.data.pending_derogations;
          this.pendingRapports = response.data.pending_rapports;
        } else {
          this.statsError = response.message || 'Erreur lors du chargement des statistiques';
        }
        this.isLoadingStats = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
        this.statsError = 'Erreur lors du chargement des statistiques';
        this.isLoadingStats = false;
      }
    });
  }

  /**
   * Refresh statistics - can be called after actions
   */
  refreshStatistics(): void {
    this.loadDashboardStatistics();
  }
  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');
    
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null; // Return the value if exists, otherwise null
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }

    return null;
  }
  // ✅ Méthodes individuelles pour chaque action
  openDerogationes() {
    this.router.navigate(['/derogation']);
  }

  openValidation() {
    this.router.navigate(['/director/validation']);
  }

 


  openNotifications() {
    this.router.navigate(['director/Derogated/notifications/']);
  }


  openFicheDerogation() {
    this.router.navigate(['/director/fiche-derogation']);
  }

  openDevis() {
    this.router.navigate(['/director/devis']);
  }

  openRapports() {
    this.router.navigate(['director/reports']);
  }
}
