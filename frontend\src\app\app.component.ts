import { Component ,HostListener} from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from './navbar/navbar.component';
import { FooterComponent } from './footer/footer.component';
import { LaboratoireGestionAnalyseComponent } from './laboratoire-gestion-analyse/laboratoire-gestion-analyse.component';
import {FontAwesomeModule} from '@fortawesome/angular-fontawesome';
@Component({
    selector: 'app-root',standalone: true,
    imports: [RouterOutlet, NavbarComponent, FooterComponent, LaboratoireGestionAnalyseComponent,FontAwesomeModule],
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = 'front-labo';
  isShown: boolean = false;

  // Listen to the window scroll event
  @HostListener('window:scroll', [])
  onWindowScroll() {
    // Show the button if scrolled more than 100px, hide otherwise
    this.isShown = window.pageYOffset > 100;
  }

  // Scroll to top when the button is clicked
  scrollToTop(event: Event) {
    event.preventDefault(); // Prevent default anchor behavior
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
