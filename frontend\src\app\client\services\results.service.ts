import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ResultsService {
  private apiUrl = 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) { }

  /**
   * Get all rapports with status "sent"
   */
  getSentRapports(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/results`);
  }

  /**
   * Get rapport details by ID
   */
  getRapportDetails(rapportId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/rapports/${rapportId}`);
  }
}
