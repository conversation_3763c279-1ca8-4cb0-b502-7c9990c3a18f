<div class="hero-section">
  <div class="hero-container">

    <!-- ✅ Contenu principal -->
    <div class="hero-content">
      <h1 class="title">Biotechnologie Bleue & Bioproduits Aquatiques</h1>
      <p class="description">
        Bienvenue chez B3<PERSON>qua, votre laboratoire d’analyse de référence à La Goulette, Tunisie. Nous offrons des services experts pour l’analyse précise et fiable de produits marins.
      </p>
      <button class="cta-button" (click)="navigateToAccept()">Commencer</button>
    </div>

    <!-- ✅ Carrousel d'images avec transition dynamique -->
    <div class="hero-image" (mouseover)="stopAutoSlide()" (mouseleave)="startAutoSlide()">
      <img #carouselImage src="/labbbb.jpg" alt="Laboratoire 1" class="active">
      <img #carouselImage src="/lab3b.jpg" alt="Laboratoire 2">
      <img #carouselImage src="/lab2b.jpg" alt="Laboratoire 3">

      <!-- ✅ Boutons de navigation -->
      <button class="prev-btn" (click)="prevImage()">&#10094;</button>
      <button class="next-btn" (click)="nextImage()">&#10095;</button>

      <!-- ✅ Indicateurs (Dots) -->
      <div class="carousel-indicators">
        <span *ngFor="let img of images; let i = index"
              class="dot"
              [class.active]="i === currentIndex"
              (click)="goToImage(i)">
        </span>
      </div>
    </div>
  </div>
</div>
