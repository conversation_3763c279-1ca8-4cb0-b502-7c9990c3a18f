import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, interval, BehaviorSubject } from 'rxjs';
import { switchMap, tap, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PaiementService {
  private apiUrl = 'http://localhost:8000/api';

  // BehaviorSubject to track payment status changes
  private paymentStatusSubject = new BehaviorSubject<any>(null);
  public paymentStatus$ = this.paymentStatusSubject.asObservable();

  constructor(private http: HttpClient) { }

  /**
   * Get the user ID from localStorage
   * @returns The user ID or null if not found
   */
  private getUserIdFromLocalStorage(): string | null {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        return user.id ? user.id.toString() : null;
      }
      return null;
    } catch (e) {
      console.error('Error getting user ID from localStorage:', e);
      return null;
    }
  }

  /**
   * Submit payment proof for a demande
   * @param formData FormData containing payment proof and details
   * @returns Observable of the API response
   */
  submitPaymentProof(formData: FormData): Observable<any> {
    const token = localStorage.getItem('token');
    const userId = this.getUserIdFromLocalStorage();

    // Check if token exists
    if (!token && !userId) {
      return new Observable(observer => {
        observer.error({
          status: 401,
          error: {
            message: 'User not authenticated',
            errors: { auth: ['Vous devez être connecté pour effectuer cette action'] }
          }
        });
      });
    }

    // Add headers with token if available
    const headers = new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : ''
    });

    // Add user_id to formData if token is not available
    if (!token && userId) {
      formData.append('user_id', userId);
    }

    return this.http.post(`${this.apiUrl}/payments`, formData, { headers });
  }

  /**
   * Get payment details for a specific demande
   * @param demandeId The demande ID to get payment for
   * @returns Observable of the payment details
   */
  getPaymentByDemandeId(demandeId: string): Observable<any> {
    const token = localStorage.getItem('token');
    const userId = this.getUserIdFromLocalStorage();

    // Check if token exists
    if (!token && !userId) {
      return new Observable(observer => {
        observer.error({
          status: 401,
          error: {
            message: 'User not authenticated',
            errors: { auth: ['Vous devez être connecté pour effectuer cette action'] }
          }
        });
      });
    }

    // Add headers with token if available
    const headers = new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : ''
    });

    // Create URL with query params if needed
    let url = `${this.apiUrl}/payments/demande/${demandeId}`;
    if (!token && userId) {
      url += `?user_id=${userId}`;
    }

    return this.http.get(url, { headers }).pipe(
      tap(response => {
        // Update the payment status subject with the latest data
        this.paymentStatusSubject.next(response);
      }),
      catchError(error => {
        console.error('Error fetching payments:', error);

        // If unauthorized, clear token
        if (error.status === 401) {
          localStorage.removeItem('token');
        }

        throw error;
      })
    );
  }

  /**
   * Start polling for payment status updates
   * @param demandeId The demande ID to check payment status for
   * @param intervalMs The polling interval in milliseconds (default: 10000 ms = 10 seconds)
   */
  startPaymentStatusPolling(demandeId: string, intervalMs: number = 10000): Observable<any> {
    return interval(intervalMs).pipe(
      switchMap(() => this.getPaymentByDemandeId(demandeId)),
      catchError(error => {
        console.error('Error polling payment status:', error);
        throw error;
      })
    );
  }

  /**
   * Get the latest payment status
   * @returns The current value of the payment status subject
   */
  getCurrentPaymentStatus(): any {
    return this.paymentStatusSubject.getValue();
  }

  /**
   * Delete a payment
   * @param paymentId The ID of the payment to delete
   * @returns Observable of the delete response
   */
  deletePayment(paymentId: number): Observable<any> {
    const token = localStorage.getItem('token');
    const userId = this.getUserIdFromLocalStorage();

    // Check if token exists
    if (!token && !userId) {
      return new Observable(observer => {
        observer.error({
          status: 401,
          error: {
            message: 'User not authenticated',
            errors: { auth: ['Vous devez être connecté pour effectuer cette action'] }
          }
        });
      });
    }

    // Add headers with token if available
    const headers = new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : ''
    });

    // Create URL with query params if needed
    let url = `${this.apiUrl}/payments/${paymentId}`;
    if (!token && userId) {
      url += `?user_id=${userId}`;
    }

    return this.http.delete(url, { headers });
  }

  /**
   * Get the full URL for a payment proof file
   * @param paymentProofPath The relative path to the payment proof file
   * @returns The full URL to the payment proof file
   */
  getPaymentProofUrl(paymentProofPath: string): string {
    if (!paymentProofPath) return '';

    // Check if the URL already contains http:// or https://
    if (paymentProofPath.startsWith('http://') || paymentProofPath.startsWith('https://')) {
      return paymentProofPath;
    }

    // Extract just the filename if it contains a path
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    } else if (fileName.includes('\\')) {
      fileName = fileName.split('\\').pop() ?? fileName;
    }

    // Log the extracted filename for debugging
    console.log('Extracted filename for URL construction:', fileName);

    // Check if this is likely an image file
    const isImage = this.isImageFile(fileName);

    // For images, use the dedicated image-file endpoint with CORS headers
    if (isImage) {
      const imageFileUrl = `${this.apiUrl}/image-file/${encodeURIComponent(fileName)}`;
      console.log('Using image-file endpoint for image:', imageFileUrl);
      return imageFileUrl;
    }

    // Add the payments/ prefix if it's not already there and doesn't have another path
    if (!fileName.includes('/') && !fileName.startsWith('payments/')) {
      // Check if it's likely a payment proof file (based on naming convention)
      if (fileName.startsWith('payment_') ||
          fileName.includes('_payment') ||
          fileName.includes('proof') ||
          fileName.includes('justificatif')) {
        fileName = `payments/${fileName}`;
        console.log('Added payments/ prefix to filename:', fileName);
      }
    }

    // Try the direct file route for non-image files
    const directFileUrl = `${this.apiUrl}/direct-file/${encodeURIComponent(fileName)}`;
    console.log('Constructed direct file URL:', directFileUrl);

    return directFileUrl;
  }

  /**
   * Check if a file is an image based on its extension
   * @param filePath The file path or name
   * @returns True if the file is an image, false otherwise
   */
  private isImageFile(filePath: string): boolean {
    if (!filePath) return false;

    const lowerPath = filePath.toLowerCase();
    return lowerPath.endsWith('.jpg') ||
           lowerPath.endsWith('.jpeg') ||
           lowerPath.endsWith('.png') ||
           lowerPath.endsWith('.gif') ||
           lowerPath.endsWith('.bmp');
  }

  /**
   * Get the download URL for a payment proof file
   * @param paymentProofPath The relative path to the payment proof file
   * @returns The download URL for the payment proof file
   */
  getPaymentProofDownloadUrl(paymentProofPath: string): string {
    // Use the same URL construction logic as getPaymentProofUrl
    return this.getPaymentProofUrl(paymentProofPath);
  }

  /**
   * View a payment proof file directly
   * @param paymentProofPath The relative path to the payment proof file
   * @returns Observable of the file response
   */
  viewPaymentProof(paymentProofPath: string): Observable<any> {
    const token = localStorage.getItem('token');
    const userId = this.getUserIdFromLocalStorage();

    // Add headers with token if available
    const headers = new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : ''
    });

    // Extract just the filename
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    }

    // Try multiple approaches for maximum compatibility

    // Create URL with query params if needed
    let directFileUrl = `${this.apiUrl}/direct-file/${encodeURIComponent(fileName)}`;
    if (!token && userId) {
      directFileUrl += `?user_id=${userId}`;
    }

    // 1. Try the direct file route first (most reliable)
    return this.http.get(directFileUrl, {
      headers,
      responseType: 'blob'
    }).pipe(
      catchError(error => {
        console.error('Error using direct file route:', error);

        // 2. Fall back to the view endpoint
        let viewUrl = `${this.apiUrl}/payments/view/${encodeURIComponent(fileName)}`;
        if (!token && userId) {
          viewUrl += `?user_id=${userId}`;
        }

        return this.http.get(viewUrl, {
          headers,
          responseType: 'blob'
        });
      })
    );
  }

  /**
   * Download a payment proof file
   * @param paymentProofPath The relative path to the payment proof file
   * @returns Observable of the file response
   */
  downloadPaymentProof(paymentProofPath: string): Observable<Blob> {
    const token = localStorage.getItem('token');
    const userId = this.getUserIdFromLocalStorage();

    // Add headers with token if available
    const headers = new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : ''
    });

    // Extract just the filename
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    }

    // Try multiple approaches for maximum compatibility

    // Create URL with query params if needed
    let directFileUrl = `${this.apiUrl}/direct-file/${encodeURIComponent(fileName)}`;
    if (!token && userId) {
      directFileUrl += `?user_id=${userId}`;
    }

    // 1. Try the direct file route first (most reliable)
    return this.http.get(directFileUrl, {
      headers,
      responseType: 'blob'
    }).pipe(
      catchError(error => {
        console.error('Error using direct file route for download:', error);

        // 2. Fall back to the download endpoint
        let downloadUrl = `${this.apiUrl}/payments/download/${encodeURIComponent(fileName)}`;
        if (!token && userId) {
          downloadUrl += `?user_id=${userId}`;
        }

        return this.http.get(downloadUrl, {
          headers,
          responseType: 'blob'
        });
      })
    );
  }

  // These methods are not needed anymore since we're using direct file access
}
