import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FicheTransmissionService } from './fiche.service';
import { DemandeService } from '../../receptionniste/demandes/demande.service';



@Component({
  selector: 'app-fiche-transmission',
  standalone: true,
  templateUrl: './fiche-transmission.component.html',
  styleUrls: ['./fiche-transmission.component.css'],
  imports: [CommonModule]
})
export class FicheTransmissionDetailsComponent implements OnInit {
  fiches: any[] = [];
  loading: boolean = true;
  errorMessage: string = '';
  ficheTransmissionId!: number;
  userCache: { [key: number]: { name: string; nickname: string } } = {}; // ✅ Cache for user details
  constructor(
    private ficheService: FicheTransmissionService,private demandeService: DemandeService,
    private route: ActivatedRoute,private router: Router
  ) {}

  ngOnInit() {
    // ✅ Get fiche_transmission_id from URL
    this.route.params.subscribe(params => {
      this.ficheTransmissionId = params['id'];
      if (this.ficheTransmissionId) {
        this.loadFiches(this.ficheTransmissionId);
      }
    });
  }
  fetchUserName(user_id: number): string {
    if (!user_id) return '⏳ Chargement...';

    // ✅ Check if user details are already cached
    if (this.userCache[user_id]) {
      return `${this.userCache[user_id].name} ${this.userCache[user_id].nickname || ''}`;
    }

    // ✅ Fetch from API if not cached
    this.demandeService.getUserDetails(user_id).subscribe({
      next: (user) => {
        this.userCache[user_id] = { name: user.name, nickname: user.nickname }; // ✅ Cache result
      },
      error: (error) => {
        console.error(`Error fetching user details for user ID ${user_id}:`, error);
        this.userCache[user_id] = { name: 'Inconnu', nickname: '' };
      }
    });

    return '⏳ Chargement...'; // Temporary placeholder while fetching
  }
  // ✅ Load fiche details by transmission ID
  loadFiches(ficheTransmissionId: number) {
    this.ficheService.getFichesByTransmissionId(ficheTransmissionId).subscribe({
      next: (data) => {
        console.log('Fiches:', data);
        this.fiches = data;
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = 'Échec du chargement des fiches';
        console.error('Error fetching fiches:', error);
        this.loading = false;
      }
    });
  }
    goToFicheList() {
    this.router.navigate(['/responsable/fiche-transmission']); // 🔄 Redirect to the list of fiches
  }

}
