/* Import Fonts and Icons */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

/* General Styles */
.receptionist-dashboard {
  text-align: center;
  padding: 3rem 5%;
  font-family: 'Montserrat', sans-serif;
  background: white;
  color: black;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Title Container */
.dashboard-title-container {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  justify-content: center;
  margin-bottom: 1rem;
}

/* Circle Icon */
.circle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 2px solid #2496d3;
  border-radius: 50%;
  font-size: 2rem;
  color: #2496d3;
  box-shadow: 0 0 10px rgba(36, 150, 211, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.circle-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(36, 150, 211, 0.4);
}

/* Dashboard Title */
.dashboard-title {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(1.8rem, calc(1vw + 1.2rem), 2.5rem);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.15rem;
  margin: 0;
  border-bottom: 2px solid #2496d3;
  padding-bottom: 0.4rem;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Welcome Message */
.welcome-message p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #555;
}
.welcome-message strong {
  font-weight: bold;
  color: #2496d3;
}

/* Statistics Section */
.statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.statistic-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeInUp 0.5s ease-in-out;
}
.statistic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.statistic-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #2496d3;
}

.statistic-title {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.statistic-value {
  font-size: 2.8rem;
  font-weight: bold;
  color: #007bff;
}

/* Action Buttons */
.receptionist-actions {
  display: flex;
  justify-content: center;
  gap: 1.25rem;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.btn-receptionist {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-size: clamp(1rem, calc(0.5rem + 0.8vw), 1.2rem);
  border-radius: 1.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  font-weight: 600;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
  text-transform: capitalize;
  gap: 0.625rem;
  min-width: 12.5rem;
  color: white;
  animation: fadeInUp 0.5s ease-in-out;
}
.btn-receptionist:hover {
  transform: translateY(-0.25rem) scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
.btn-receptionist:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
}

/* Dashboard Tabs */
.dashboard-tabs {
  margin-bottom: 2rem;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.tab-button {
  padding: 0.8rem 1.5rem;
  background: #f5f5f5;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab-button:hover {
  background: #e9e9e9;
  transform: translateY(-2px);
}

.tab-button.active {
  background: #2496d3;
  color: white;
  box-shadow: 0 4px 8px rgba(36, 150, 211, 0.3);
}

.tab-content {
  position: relative;
}

.tab-pane {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}

.tab-pane.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Button-Specific Colors - Consistent Color Scheme */
/* Primary Actions - Blue Gradient */
.demandes,
.gestion-analyse,
.results {
  background: linear-gradient(to right, #2496d3, #1a7bb9);
}
.demandes:hover,
.gestion-analyse:hover,
.results:hover {
  background: linear-gradient(to right, #5eb8f7, #3a9fd3);
}

/* Secondary Actions - Teal Gradient */
.validation,
.registre,
.rapports {
  background: linear-gradient(to right, #17a2b8, #117a8b);
}
.validation:hover,
.registre:hover,
.rapports:hover {
  background: linear-gradient(to right, #5bc0de, #2a9fd6);
}

/* Administrative Actions - Purple Gradient */
.facturation,
.users {
  background: linear-gradient(to right, #6f42c1, #4e2d89);
}
.facturation:hover,
.users:hover {
  background: linear-gradient(to right, #9a7ad5, #7952b3);
}

/* Document Actions - Orange Gradient */
.transmission {
  background: linear-gradient(to right, #fd7e14, #e36209);
}
.transmission:hover {
  background: linear-gradient(to right, #ff9c4a, #f08135);
}

/* Alert Actions - Red/Orange Gradient */
.reclamations,
.notifications {
  background: linear-gradient(to right, #dc3545, #c82333);
}
.reclamations:hover,
.notifications:hover {
  background: linear-gradient(to right, #f68b8b, #e46b6b);
}

/* Responsive Design */
@media (max-width: 57.8125rem) {
  .statistics { grid-template-columns: 1fr; }
  .statistic-card { width: 90%; margin: 0 auto 1.5rem; }
  .receptionist-actions { flex-direction: column; align-items: center; gap: 0.8rem; }
  .btn-receptionist { width: 85%; padding: 0.8rem 1rem; }
  .receptionist-dashboard { padding: 2rem 3%; }

  .tab-buttons {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    justify-content: flex-start;
  }

  .tab-button {
    flex: 0 0 auto;
    white-space: nowrap;
  }
}

@media (max-width: 37.5rem) {
  .btn-receptionist { width: 100%; font-size: 0.9rem; padding: 0.7rem 0.8rem; }
  .dashboard-title { font-size: 1.3rem; }
  .receptionist-dashboard { padding: 1.5rem 2%; }

  .tab-buttons {
    gap: 0.3rem;
  }

  .tab-button {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}