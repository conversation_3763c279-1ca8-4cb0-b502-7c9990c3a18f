<div class="demandes-container">
  <h2>Registre de suivi</h2>

  <!-- Notification de succès -->
  <div *ngIf="successMessage" class="notification">
    {{ successMessage }}
  </div>

  <!-- Notification d'erreur -->
  <div *ngIf="error" class="error-notification">
    {{ error }}
  </div>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro ou client:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <button (click)="clearFilters()" class="btn-clear">
      <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>
      Effacer les filtres
    </button>
  </div>

  <!-- Tableau des registres -->
  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Numéro de demande</th>
        <th>Nom Client</th>
        <th>Date Réception</th>
        <th class="actions-col">Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="loading" class="loading-row">
        <td colspan="5" class="text-center">
          <div class="spinner-container">
            <div class="spinner"></div>
            <span>Chargement...</span>
          </div>
        </td>
      </tr>

      <!-- Message quand aucun registre n'est trouvé -->
      <tr *ngIf="!loading && filteredRegistres.length === 0" class="empty-row">
        <td colspan="5" class="text-center">
          Aucun registre trouvé.
        </td>
      </tr>

      <!-- Affichage des registres -->
      <ng-container *ngIf="!loading && filteredRegistres.length > 0">
        <tr
          *ngFor="
            let registre of filteredRegistres
             | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
          class="clickable-row"
        >
          <td>{{ registre.id }}</td>
          <td>{{ registre.demande_id }}</td>
          <td>{{ registre.nom_client }}</td>
          <td>{{ registre.date_reception }}</td>
          <td class="actions-col">
            <button
              class="btn-details"
              (click)="goToRegistres(registre.id)"
            >
              <fa-icon [icon]="faEye" style="margin-right: 10px;"></fa-icon>
              Voir détails
            </button>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>
</div>
