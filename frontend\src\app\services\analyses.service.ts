import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Analyse } from '../../models/analyse.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AnalysesService {
  private apiUrl = environment?.apiUrl || 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) {}

  // Get headers with auth token
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });
  }

  // Get all analyses
  getAllAnalyses(): Observable<Analyse[]> {
    return this.http.get<{status: string, data: Analyse[]}>(`${this.apiUrl}/analyses`, { headers: this.getHeaders() })
      .pipe(
        map(response => response.data)
      );
  }

  // Get a single analysis by ID
  getAnalysisById(id: number): Observable<Analyse> {
    return this.http.get<{status: string, data: Analyse}>(`${this.apiUrl}/analyses/${id}`, { headers: this.getHeaders() })
      .pipe(
        map(response => response.data)
      );
  }

  // Create a new analysis
  createAnalysis(analysis: Analyse): Observable<any> {
    return this.http.post<{status: string, message: string, data: Analyse}>(`${this.apiUrl}/analyses`, analysis, { headers: this.getHeaders() })
      .pipe(
        map(response => response.data)
      );
  }

  // Update an analysis
  updateAnalysis(id: number, analysis: Partial<Analyse>): Observable<any> {
    return this.http.put<{status: string, message: string, data: Analyse}>(`${this.apiUrl}/analyses/${id}`, analysis, { headers: this.getHeaders() })
      .pipe(
        map(response => response.data)
      );
  }

  // Delete an analysis
  deleteAnalysis(id: number): Observable<any> {
    return this.http.delete<{status: string, message: string}>(`${this.apiUrl}/analyses/${id}`, { headers: this.getHeaders() });
  }
}
