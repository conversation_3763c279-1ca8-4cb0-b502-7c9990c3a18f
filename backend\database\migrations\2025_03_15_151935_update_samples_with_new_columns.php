<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('samples', function (Blueprint $table) {
           $table->date('date_prelevement')->nullable();
           $table->string('origine_prelevement')->nullable();
           $table->string('site')->nullable();
           $table->string('nom_preleveur')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('samples', function (Blueprint $table) {
            $table->dropColumn('date_prelevement');
            $table->dropColumn('origine_prelevement');
            $table->dropColumn('site');
            $table->dropColumn('nom_preleveur');
        });
    }
};
