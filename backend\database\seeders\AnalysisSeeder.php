<?php

namespace Database\Seeders;

use App\Models\Analysis;
use Illuminate\Database\Seeder;

class AnalysisSeeder extends Seeder
{
    public function run(): void
    {
        $analyses = [
            ['analyse' => 'Biotoxine (DSP)', 'parameter' => 'NF EN 16024: 2013 Par LCMS/MS', 'price' => 350, 'is_accredited' => 'Yes'],
            ['analyse' => 'Biotoxine (PSP)', 'parameter' => 'Méthode interne validée Par LCMS/MS', 'price' => 350, 'is_accredited' => 'Yes'],
            ['analyse' => 'Biotoxine (ASP)', 'parameter' => 'Accréditée selon la norme ISO/CEI 17025:2017', 'price' => 200, 'is_accredited' => 'Yes'],
            ['analyse' => 'Lipide', 'parameter' => 'Méthode interne validée extraction gravimétrique', 'price' => 45, 'is_accredited' => 'Yes'],
            ['analyse' => 'Acide gras', 'parameter' => 'ISO12966-2:2012/ISO12966-4:2015 (CPG)', 'price' => 130, 'is_accredited' => 'Yes'],
            ['analyse' => 'Humidité', 'parameter' => 'NFV04-401', 'price' => 20, 'is_accredited' => 'Yes'],
            ['analyse' => 'Cendres', 'parameter' => 'NFV04-404', 'price' => 20, 'is_accredited' => 'Yes'],
            ['analyse' => 'Protéine', 'parameter' => 'Méthode interne valide Spectrophotométrie', 'price' => 45, 'is_accredited' => 'Yes'],
            ['analyse' => 'Histamines', 'parameter' => 'ISO 19343: 2017 (Fr) HPLC Chromatographe', 'price' => 45, 'is_accredited' => 'Yes'],
            ['analyse' => 'Autres amines Biogènes', 'parameter' => 'ISO 19343: 2017 (Fr) HPLC Chromatographe', 'price' => 85, 'is_accredited' => 'No'],
            ['analyse' => 'Azote basique volatile total (ABVT)', 'parameter' => 'Méthode interne validée (FIA)', 'price' => 45, 'is_accredited' => 'Yes'],
            ['analyse' => 'pH', 'parameter' => 'NT ISO 11289-1993', 'price' => 10, 'is_accredited' => 'Yes'],
            ['analyse' => 'Hydrate de carbone (Sucre)', 'parameter' => 'Méthode interne validée', 'price' => 45, 'is_accredited' => 'Yes'],
            ['analyse' => 'Triméthylamine (TMA)', 'parameter' => 'Méthode interne validée (FIA)', 'price' => 45, 'is_accredited' => 'Yes'],
            ['analyse' => 'Valeur énergétique', 'parameter' => 'FAO Food and nutrition', 'price' => 10, 'is_accredited' => 'Yes'],
            ['analyse' => 'Acides aminés (Hydroxyproline, Serine, Histidine, Arginine, Glycine, Leucine, A.Glutamate, Alanine, Threonine, Isoleucine, Phenylalanine)', 'parameter' => 'Méthode interne validée HPLC', 'price' => 230, 'is_accredited' => 'Yes'],
            ['analyse' => 'Autre acides aminés (A.Aspartique, Valine, Méthionine, Lysine, Asparagine, Glutamine, Cystine, Tryptophane, Tyrosine)', 'parameter' => 'Méthode interne validée HPLC', 'price' => 230, 'is_accredited' => 'No'],
            ['analyse' => 'Azote total', 'parameter' => 'Méthode interne', 'price' => 50, 'is_accredited' => 'No'],
            ['analyse' => 'Cholestérol', 'parameter' => 'Méthode interne', 'price' => 75, 'is_accredited' => 'No'],
            ['analyse' => 'Sodium', 'parameter' => 'Méthode interne', 'price' => 25, 'is_accredited' => 'No'],
            ['analyse' => 'Potassium', 'parameter' => 'Méthode interne', 'price' => 25, 'is_accredited' => 'No'],
            ['analyse' => 'Calcium', 'parameter' => 'Méthode interne', 'price' => 25, 'is_accredited' => 'No'],
            ['analyse' => 'Polyphénol', 'parameter' => 'Méthode interne', 'price' => 35, 'is_accredited' => 'No'],
            ['analyse' => 'Vitamines (A)', 'parameter' => 'Méthode interne', 'price' => 75, 'is_accredited' => 'No'],
            ['analyse' => 'Vitamines (E)', 'parameter' => 'Méthode interne', 'price' => 75, 'is_accredited' => 'No'],
            ['analyse' => 'activité anti-oxydante (DPPH)', 'parameter' => 'Méthode interne', 'price' => 80, 'is_accredited' => 'No'],
            ['analyse' => 'Caroténoïde', 'parameter' => 'Méthode interne', 'price' => 230, 'is_accredited' => 'No'],
            ['analyse' => 'Analyse moléculaire', 'parameter' => 'Méthode interne', 'price' => 450, 'is_accredited' => 'No'],
            ['analyse' => 'TBARS', 'parameter' => 'Méthode interne', 'price' => 50, 'is_accredited' => 'No'],
        ];
        foreach ($analyses as $analysis) {
            Analysis::create($analysis);
        }
    }
}