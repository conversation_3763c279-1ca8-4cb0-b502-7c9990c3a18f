import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG } from '../app.config';
import { SuccessModalComponent } from '../shared/success-modal/success-modal.component';

@Component({
    selector: 'app-authentification-singup',standalone: true,
    imports: [CommonModule, ReactiveFormsModule, FormsModule, SuccessModalComponent],
    templateUrl: './authentification-singup.component.html',
    styleUrls: ['./authentification-singup.component.css']
})
export class AuthentificationSingupComponent implements OnInit {
  registerForm!: FormGroup;
  errorMessage: string | null = null;
  isLoading: boolean = false;
  showSuccessModal: boolean = false;
  private registerUrl = `${APP_CONFIG.apiBase}/register`;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.registerForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(4)]],
      nickname: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [
        Validators.required,
        Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')
      ]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      password_confirmation: ['', [Validators.required, Validators.minLength(8)]],
      role: ['client', Validators.required], // Default role set to 'client'
      adress: ['', [Validators.required, Validators.minLength(10)]],
      phone: ['', [
        Validators.required,
        Validators.pattern('^(2|3|4|5|7|9)[0-9]{7}$') // Tunisia phone number validation
      ]],
      fax: ['', [
        Validators.pattern('^(2|3|4|5|7|9)[0-9]{7}$') // Optional, follows the same Tunisia phone pattern
      ]]
    }, { validator: this.passwordMatchValidator });
  }

  /**
   * Ensure that password and password_confirmation match.
   */
  passwordMatchValidator(formGroup: FormGroup) {
    const password = formGroup.get('password')?.value;
    const confirmPassword = formGroup.get('password_confirmation')?.value;

    if (password !== confirmPassword) {
      formGroup.get('password_confirmation')?.setErrors({ mismatch: true });
    } else {
      const errors = formGroup.get('password_confirmation')?.errors;
      if (errors) {
        delete errors['mismatch'];
        if (!Object.keys(errors).length) {
          formGroup.get('password_confirmation')?.setErrors(null);
        }
      }
    }
    return null;
  }

  navigateTo(path: string): void {
    this.router.navigate([path]);
  }

  registerFormSubmit(): void {
    this.errorMessage = null;

    if (this.registerForm.invalid) {
      this.registerForm.markAllAsTouched();
      return;
    }

    // Show loading spinner
    this.isLoading = true;

    this.http.post(this.registerUrl, this.registerForm.value).subscribe({
      next: (response) => {
        console.log('Registration success:', response);
        // Hide loading spinner
        this.isLoading = false;
        // Show success modal instead of direct navigation
        this.showSuccessModal = true;
      },
      error: (error) => {
        console.error('Registration error:', error);
        // Hide loading spinner
        this.isLoading = false;

        if (error.status === 422 && error.error?.errors?.email) {
          this.registerForm.get('email')?.setErrors({ emailTaken: true });
        } else {
          this.errorMessage = "Account didn't create, try again later.";
        }
      }
    });
  }

  /**
   * Handle success modal close event
   */
  onSuccessModalClose(): void {
    this.showSuccessModal = false;
  }

  /**
   * Handle success modal confirm event (navigate to login)
   */
  onSuccessModalConfirm(): void {
    this.showSuccessModal = false;
    // Navigation is handled by the modal component itself
  }
}