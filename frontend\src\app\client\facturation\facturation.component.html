<!-- Conteneur principal -->
<div class="facturation-container">
  <!-- Titre principal -->
  <h2 class="list">📜 Liste des Factures</h2>

  <!-- Tableau stylé -->
  <table class="styled-table">
    <thead>
      <tr>
        <th>N° Facture</th>
        <th>Date</th>
        <th>Montant Total (DT)</th>
        <th>Statut</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <!-- Boucle Angular pour afficher la liste des factures -->
      <tr *ngFor="let facture of factures">
        <td>{{ facture.id }}</td>
        <td>{{ facture.date }}</td>
        <td>{{ facture.total }} DT</td>

        <!-- Cellule du Statut -->
        <td>
          <div class="status-container">
            <!--
              On applique dynamiquement la classe en fonction du statut.
              Ajuste les conditions selon les valeurs réelles de `facture.status`
              (Ex. 'Payée', 'En attente', 'Non payée').
            -->
            <span
              [ngClass]="{
                'paid': facture.status === 'Payée',
                'pending': facture.status === 'En attente',
                'unpaid': facture.status === 'Non payée'
              }"
            >
              {{ facture.status }}
            </span>
          </div>
        </td>

        <!-- Cellule Action avec le bouton « Voir » -->
        <td>
          <button class="btn-view" (click)="voirFacture(facture.id)">
            <i class="fa fa-eye"></i>
            Voir
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
