@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ---------------------------------
   1) ANIMATIONS
---------------------------------- */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ---------------------------------
   2) CONTENEUR PRINCIPAL
---------------------------------- */
.notifications-container {
  margin: 40px auto;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden;
  text-align: center;
  transition: all 0.4s ease-in-out;
}

/* ---------------------------------
   3) TITRE PRINCIPAL
---------------------------------- */
.notifications-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 30px;
  border-bottom: 4px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ---------------------------------
   4) BARRE DE FILTRAGE
---------------------------------- */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 8px;
  border-bottom: 1px solid #ddd;
  max-width: 75%;
  margin: 0 auto 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #333;
  text-align: left;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.btn-apply {
  padding: 10px 15px;
  background-color: #2496d3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-apply:hover {
  background-color: #1e78b5;
  transform: translateY(-2px);
}

.btn-clear {
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* ---------------------------------
   5) SUCCESS/ERROR MESSAGES
---------------------------------- */
.message-container {
  margin: 20px 0;
  padding: 0;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  font-weight: 500;
  animation: fadeIn 0.5s ease-in-out;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  font-weight: 500;
  animation: fadeIn 0.5s ease-in-out;
}

/* ---------------------------------
   6) NOTIFICATION ACTIONS
---------------------------------- */
.notification-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.btn-remove-all {
  padding: 10px 15px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-remove-all:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-2px);
}

.btn-remove-all:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-remove-all.loading {
  position: relative;
  color: transparent;
}

.btn-remove-all.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ---------------------------------
   6) TABLE STYLING
---------------------------------- */
.notification-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* Table Headers */
.notification-table thead tr th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-transform: uppercase;
  text-align: center;
}

/* Table Rows */
.notification-table tbody tr td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
  background-color: white;
}

/* Row Hover Effect */
.notification-table tbody tr:hover td {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* Unread Notification Styling */
.unread td {
  background: rgba(255, 193, 7, 0.1);
  font-weight: bold;
  color: #d39e00;
}

/* ---------------------------------
   7) STATUS TAGS
---------------------------------- */
/* New Tag */
.new-tag {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  text-transform: uppercase;
  box-shadow: 0px 3px 8px rgba(40, 167, 69, 0.4);
}

/* Read Tag */
.read-tag {
  background: rgba(150, 150, 150, 0.1);
  color: #6c757d;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  text-transform: uppercase;
  box-shadow: 0px 3px 8px rgba(150, 150, 150, 0.4);
}

/* ---------------------------------
   8) ACTION BUTTONS
---------------------------------- */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.btn-details {
  padding: 8px 12px;
  background-color: #2496d3;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-details:hover {
  background-color: #1e78b5;
  transform: scale(1.05);
}

.btn-remove {
  padding: 8px 12px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-remove:hover:not(:disabled) {
  background-color: #c82333;
  transform: scale(1.05);
}

.btn-remove:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-remove.loading {
  position: relative;
}

.btn-remove.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-top: 1px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ---------------------------------
   9) LOADING & EMPTY STATES
---------------------------------- */
/* No Notifications Message */
.no-notifications {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}

/* Loading Spinner */
.loading-row {
  height: 100px;
}

.loading-row td {
  text-align: center;
  vertical-align: middle;
  font-size: 18px;
  color: #6c757d;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.text-center {
  text-align: center;
}

/* ---------------------------------
   10) PAGINATION
---------------------------------- */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* ---------------------------------
   11) RESPONSIVE
---------------------------------- */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    max-width: 95%;
    height: auto;
    padding: 1rem;
  }

  .filter-group {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .filter-group input,
  .filter-group select {
    max-width: 100%;
    width: 100%;
  }

  .filter-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn-apply, .btn-clear {
    width: 100%;
    margin-top: 5px;
  }

  .notification-actions {
    justify-content: center;
  }

  .btn-remove-all {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn-details, .btn-remove {
    width: 100%;
    margin: 2px 0;
  }

  .notifications-container {
    padding: 20px;
  }

  .notification-table thead tr th,
  .notification-table tbody tr td {
    padding: 10px;
    font-size: 13px;
  }
}

/* ✅ Glow Animation */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}
