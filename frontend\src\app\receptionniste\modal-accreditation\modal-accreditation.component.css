/* Fond semi-transparent pour le MatDialog */
.custom-backdrop-class {
  background: rgba(0, 0, 0, 0.5) !important;
}

/* Centrage du contenu du MatDialog */
.custom-dialog-container {
  margin: 50px auto;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 1s ease-in-out;
  overflow: hidden;
}

/* Correction du MatDialog pour être centré */
.mat-dialog-container {
  margin: 50px auto;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 1s ease-in-out;
  overflow: hidden;
}
/* Assurer que le texte est bien centré */
.modal-content {
  text-align: center;
}

/* Titre du modal */
.modal-title {
  font-size: 22px;
  font-weight: bold;
  color: #2496d3;
  text-align: center;
}

/* Style des boutons */
.modal-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.btn-confirm {
  background-color: #4caf50 !important;
  color: white !important;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  transition: all 0.3s;
}

.btn-confirm:hover {
  background-color: #3e9d40 !important;
}

.btn-cancel {
  background-color: #f44336 !important;
  color: white !important;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  transition: all 0.3s;
}

.btn-cancel:hover {
  background-color: #e53935 !important;
}
