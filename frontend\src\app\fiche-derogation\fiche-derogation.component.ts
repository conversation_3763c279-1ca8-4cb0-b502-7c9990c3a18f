import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-fiche-derogation',standalone: true,
    imports: [CommonModule, ReactiveFormsModule], // ✅ Import ReactiveFormsModule
    templateUrl: './fiche-derogation.component.html',
    styleUrls: ['./fiche-derogation.component.css']
})
export class FicheDerogationComponent implements OnInit {
  ficheForm!: FormGroup; // ✅ Declare ficheForm

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.ficheForm = this.fb.group({
      codeEchantillon: ['', Validators.required],
      dateReception: ['', Validators.required],
      dateDemande: ['', Validators.required],
      demandeur: ['', Validators.required],
      natureEchantillon: ['', Validators.required],
      referenceDemande: ['', Validators.required],
      parametresDemandes: ['', Validators.required],
      descriptionEcart: ['', Validators.required],
      reponseClient: ['', Validators.required],
      decision: ['', Validators.required],
      dateVisaDL: ['', Validators.required],
      dateVisaDemandeur: ['', Validators.required]
    });
  }

  submitForm() {
    if (this.ficheForm.valid) {
      console.log('Fiche envoyée avec succès !', this.ficheForm.value);
    }
  }
}
