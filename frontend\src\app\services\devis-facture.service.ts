import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DevisFactureService {
  private apiUrl = 'http://localhost:8000/api'; // Remplacez par l'URL réelle de l'API

  constructor(private http: HttpClient) { }

  /** 🔹 Récupérer le devis d'un client */
  getDevis(clientId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/devis/${clientId}`);
  }

  /** 🔹 Récupérer la facture d'un client */
  getFacture(clientId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/facture/${clientId}`);
  }
}
