<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('nickname')->after('name');
            $table->string('phone')->after('email');
            $table->string('fax')->nullable()->after('phone'); // Moved after phone
            $table->string('adress')->after('fax'); // Moved after fax
            $table->enum('role', ['client', 'director', 'receptionist', 'responsable'])->after('adress'); // Fixed enum column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('nickname');
            $table->dropColumn('phone');
            $table->dropColumn('fax');
            $table->dropColumn('adress');
            $table->dropColumn('role'); // Ensure role is removed
        });
    }
};
