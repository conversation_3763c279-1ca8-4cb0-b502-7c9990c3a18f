
<div class="change-password-container">
    <h1 class="title">Changer le mot de passe</h1>
  
    <!-- Error Message -->
    <div *ngIf="errorMessage" class="error">
      {{ errorMessage }}
    </div>
  
    <!-- Success Message -->
    <div *ngIf="successMessage" class="success">
      {{ successMessage }}
    </div>
  
    <form [formGroup]="changePasswordForm" (ngSubmit)="onSubmit()">
      <div class="description">
        Entrez un nouveau mot de passe ci-dessous pour changer votre mot de passe.
      </div>
  
      <div class="input-group">
        <div class="field-group">
          <label for="password" class="input-label-form">Nouveau mot de passe:
            <span 
            *ngIf="changePasswordForm.get('password')?.touched && changePasswordForm.get('password')?.errors" 
            class="error"
          >
            <span *ngIf="changePasswordForm.get('password')?.errors?.['required']" >
              Le mot de passe est requis.
            </span>
            <span *ngIf="changePasswordForm.get('password')?.errors?.['minlength']" >
              Le mot de passe doit contenir au moins 8 caractères.
            </span>
        </span>
          <input id="password" type="password" formControlName="password" class="input-field-form" placeholder="Entrez le nouveau mot de passe">
        
        </label>
        </div>
  
        <div class="field-group">
          <label for="password_confirmation" class="input-label-form">Confirmez le nouveau mot de passe:
            <span 
            *ngIf="changePasswordForm.get('password_confirmation')?.touched && 
                    changePasswordForm.get('password_confirmation')?.errors" 
            class="error"
          >
            <span *ngIf="changePasswordForm.get('password_confirmation')?.errors?.['required']">
              La confirmation du mot de passe est requise.
            </span>
            <span *ngIf="changePasswordForm.get('password_confirmation')?.errors?.['minlength']">
              La confirmation du mot de passe doit contenir au moins 8 caractères.
            </span>
            <span *ngIf="changePasswordForm.get('password_confirmation')?.errors?.['mismatch']" >
              Les mots de passe ne correspondent pas.
            </span>
          </span>
          </label>
          <input id="password_confirmation" type="password" formControlName="password_confirmation" class="input-field-form" placeholder="Confirmez le nouveau mot de passe">
          
        </div>
      </div>
  
      <button type="submit" class="btn-primary">
        Envoyer
      </button>
    </form>
  </div>
