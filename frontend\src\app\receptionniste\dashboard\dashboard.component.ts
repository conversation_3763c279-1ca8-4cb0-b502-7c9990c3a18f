import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { DemandeService } from '../demandes/demande.service';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { RapportsService } from '../rapports/rapports.service';
import { ResultsService } from '../results/results.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  imports: [CommonModule, RouterModule] // ✅ Nécessaire pour la navigation
})
export class DashboardComponent implements OnInit {
  name: string | null = null;
  nickname: string | null = null;
  nonValidDemandesCount: number = 0;
  nonSentRapports: number = 0;
  validReportsCount: number = 0;

  constructor(
    private router: Router,
    private demandeService: DemandeService,
    private rapportService: RapportsService,
    private resultsService: ResultsService
  ) {}

  ngOnInit(): void {
    this.name = this.getUserProperty('name');
    this.nickname = this.getUserProperty('nickname');
    console.log("📌 Tableau de bord du réceptionniste chargé.");
    this.loadNonValidDemandesCount();
    this.loadNonSentRapportsCount();
    this.loadValidReportsCount();

    // Initialize tabs after view is initialized
    setTimeout(() => {
      this.initializeTabs();
    });
  }

  // Initialize tab functionality
  initializeTabs(): void {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Remove active class from all buttons and panes
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabPanes.forEach(pane => pane.classList.remove('active'));

        // Add active class to clicked button
        button.classList.add('active');

        // Get the tab ID and activate corresponding pane
        const tabId = button.getAttribute('data-tab');
        const pane = document.getElementById(`${tabId}-tab`);
        if (pane) {
          pane.classList.add('active');
        }
      });
    });
  }


  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');

    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null; // Return the value if exists, otherwise null
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }

    return null;
  }

  // ✅ Méthodes individuelles pour chaque action
  openDemandes() {
    this.router.navigate(['/receptionist/demandes']);
  }

  openValidation() {
    this.router.navigate(['/receptionist/validation']);
  }

  openSuivi() {
    this.router.navigate(['/receptionist/suivi']);
  }
  openRegistre() {
    this.router.navigate(['receptionist/registre']);
  }

  openFicheTransmission() {
    this.router.navigate(['/receptionist/fiche-transmission']);
  }

  openNotifications() {
    this.router.navigate(['/receptionist/notifications']);
  }

  openFacturation() {
    this.router.navigate(['/receptionist/facture']);
  }

  openFicheDerogation() {
    this.router.navigate(['/receptionist/fiche-derogation']);
  }

  openDevis() {
    this.router.navigate(['/receptionist/devis']);
  }

  openRapports() {
    this.router.navigate(['/receptionist/rapports']);
  }

  openUsers() {
    this.router.navigate(['/receptionist/users']);
  }

  openProfile() {
    this.router.navigate(['/receptionist/profile']);
  }

  openResults() {
    this.router.navigate(['/receptionist/results']);
  }

  openReclamations() {
    this.router.navigate(['/receptionist/reclamations']);
  }

  openGestionAnalyse() {
    this.router.navigate(['/gestion-analyse']);
  }
  loadNonValidDemandesCount(): void {
    this.getCountOfNonValidDemandes().subscribe({
      next: (count) => {
        this.nonValidDemandesCount = count;
      },
      error: (error) => {
        console.error('Error fetching non-valid demandes count:', error);
      }
    })

  }
  loadNonSentRapportsCount(): void {
    this.getCountOfNotSentRapports().subscribe({
      next: (count) => {
        this.nonSentRapports = count;
      },
      error: (error) => {
        console.error('Error fetching non-sent rapports count:', error);
      }
    })
  }
  getCountOfNonValidDemandes(): Observable<number> {
    return this.demandeService.getAllDemandes().pipe(
      map(demandes => {
        return demandes.filter(demande => demande.status !== 'valid').length;
      }),
      catchError(error => {
        console.error('Error counting non-valid demandes:', error);
        return throwError(() => new Error('Failed to count non-valid demandes. Please try again.'));
      })
    );
  }
  getCountOfNotSentRapports(): Observable<number> {
    return this.rapportService.getAllRapports().pipe(
      map(rapports => {
        return rapports.filter(rapport => rapport.status_director === 'not_sent').length;
      }),
      catchError(error => {
        console.error('Error counting not sent rapports:', error);
        return throwError(() => new Error('Failed to count not sent rapports. Please try again.'));
      })
    );
  }

  loadValidReportsCount(): void {
    this.resultsService.getValidReports().subscribe({
      next: (reports) => {
        this.validReportsCount = reports.length;
      },
      error: (error) => {
        console.error('Error fetching valid reports count:', error);
        this.validReportsCount = 0;
      }
    });
  }
}
