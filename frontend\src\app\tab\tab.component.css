@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ Section principale */
.tab-section {
  background: radial-gradient(circle at top left, #0c0032, #190061);
  color: white;
  font-family: 'Poppins', sans-serif;

  
  text-align: center;
  padding: 100px 5%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* ✅ Texte d’introduction */
.tab-subtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 19px;
  font-weight: 600;
  color: #2496d3;
  text-align: center;
  max-width: 90%;
  margin-bottom: 70px; /* 🔥 Plus d'espacement */
  padding: 20px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  animation: fadeIn 1.5s ease-in forwards;
}

/* ✅ Titre du tableau */
.tab-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 50px; /* 🔥 Ajout d’espace */
  border-bottom: 6px solid #2496d3;
  display: inline-block;
  padding-bottom: 15px;
  animation: fadeIn 1.5s ease-in forwards;
}

/* ✅ Conteneur du tableau avec un padding plus large */
.table-wrapper {
  overflow-x: auto; /* ✅ Permet un défilement horizontal */
  width: 100%;
  max-width: 95vw;
  padding: 20px; /* 🔥 Plus d'espace autour */
}

/* ✅ Tableau */
.styled-table {
  width: 100%;
  min-width: 1200px; /* ✅ Fixe une largeur minimale pour éviter les coupures */
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  animation: fadeIn 2s ease-in forwards;
  margin-top: 60px; /* 🔥 Décalage vers le bas */
}

/* ✅ Colonnes */
.styled-table th, .styled-table td {
  padding: 25px; /* 🔥 Espacement accru */
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 17px; /* 🔥 Texte plus grand */
  text-align: center;
  white-space: nowrap;
}

/* ✅ En-tête du tableau */
.styled-table th {
  background: linear-gradient(90deg, #00c3ff, #007bff);
  color: white;
  text-transform: uppercase;
  font-size: 18px;
  cursor: pointer;
  transition: background 0.3s, transform 0.3s;
}

/* ✅ Effet hover */
.styled-table th:hover {
  background: linear-gradient(90deg, #ff00ff, #2496d3);
  transform: scale(1.05);
}

/* ✅ Alternance des lignes */
.styled-table tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.08);
}

.styled-table tr:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.02);
  transition: 0.3s ease-in-out;
}

/* ✅ Note flottante */
.note-card {
  position: relative;
  width: 80%;
  margin: 50px auto; /* 🔥 Ajout d’espace */
  padding: 25px;
  background: linear-gradient(135deg, #ff8c42, #ff5e62);
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 17px;
  text-align: center;
  box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  animation: float 2s ease-in-out infinite alternate;
  transform: rotate(-2deg);
  position: relative;
}

/* ✅ Animation flottante */
@keyframes float {
  from { transform: translateY(0px) rotate(-2deg); }
  to { transform: translateY(10px) rotate(2deg); }
}

/* ✅ Ruban adhésif */
.tape {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%) rotate(-5deg);
  width: 120px;
  height: 20px;
  background: repeating-linear-gradient(45deg, #007bff, #007bff 10px, #0056b3 10px, #0056b3 20px);
  opacity: 0.7;
}

/* ✅ Animation fade-in */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ✅ Responsive Design */
@media (max-width: 1024px) {
  .table-wrapper {
      overflow-x: auto; /* ✅ Scroll horizontal activé */
  }

  .styled-table {
      min-width: 1000px; /* ✅ Évite la déformation sur petits écrans */
  }

  .styled-table th, .styled-table td {
      font-size: 15.5px;
      padding: 15px;
  }
}

@media (max-width: 768px) {
  .styled-table {
      min-width: 800px; /* ✅ Ajustement pour mobile */
  }

  .styled-table th, .styled-table td {
      font-size: 15px;
      padding: 10px;
  }
}
