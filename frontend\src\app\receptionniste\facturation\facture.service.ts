import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

// Define interfaces for type safety
export interface Payment {
  id: number;
  demande_id: string;
  user_id: number;
  user_name?: string;
  user_email?: string;
  amount: number;
  payment_proof: string;
  direct_url?: string;
  status: 'pending' | 'approved' | 'rejected';
  payment_date: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Facture {
  id: number;
  total_amount: number;
  facture_date: string;
  status: string;
  demande: string; // This is the demande_id string
  created_at?: string;
  updated_at?: string;
  payment?: Payment; // Add payment information
}

@Injectable({
  providedIn: 'root'
})
export class FactureService {

  private apiUrl = 'http://localhost:8000/api'; // Replace with your actual API URL

  constructor(private http: HttpClient) {}

  getFactures(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/factures`);
  }

  getFactureDetails(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/factures/${id}`);
  }

  // Get payments by demande ID
  getPaymentsByDemandeId(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/payments/demande/${demandeId}`);
  }

  // Get all payments
  getAllPayments(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/payments`);
  }

  // Get demande details by demande_id
  getDemandeDetails(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/demande/${demandeId}`);
  }
}
