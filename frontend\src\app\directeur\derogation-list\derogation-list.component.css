@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Main Container */
.derogations-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 50px;
  max-width: 90%;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}

/* ✅ Title Styling */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* Changed to Blue */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Loading and Error Messages */
.loading {
  font-size: 18px;
  color: #2496d3; /* Blue */
  font-weight: bold;
  margin-top: 20px;
}

.error {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}

.no-derogations {
  font-size: 18px;
  color: #666;
  margin-top: 20px;
}

/* ✅ Table Styling */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3); /* Changed to Blue Shadow */
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ Table Header */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* Changed to Blue */
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Table Rows */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Row Styling */
.derogation-row:hover {
  background: rgba(36, 150, 211, 0.1); /* Changed to Light Blue Hover */
  transition: background 0.3s ease-in-out;
}

/* ✅ Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.details-btn {
  padding: 8px 15px;
  font-size: 14px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-transform: uppercase;
  outline: none;
  background-color: #555;
  color: #fff;
  box-shadow: 0 3px 10px rgba(85, 85, 85, 0.3);
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: bold;
}

.details-btn:hover {
  background-color: #333;
  transform: scale(1.05);
}

/* ✅ Glow Animation */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4); /* Changed to Blue Glow */
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* ✅ Filter Bar */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 8px;
  max-width: 75%;
  margin: 0 auto 20px auto;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #555;
  white-space: nowrap;
  font-size: 14px;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #2496d3;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.2);
  outline: none;
}

.status-select {
  width: 180px;
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0h12L6 6z" fill="%23666"/></svg>');
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

#search {
  width: 250px;
}

/* ✅ Filter Buttons */
.filter-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.btn-filter-apply,
.btn-clear {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.btn-filter-apply {
  background-color: #28a745;
  color: white;
}

.btn-filter-apply:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.btn-clear {
  background-color: #6c757d;
  color: white;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* ✅ Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-cell {
  padding: 30px;
}

.empty-message {
  padding: 20px;
  color: #666;
  font-style: italic;
}

/* ✅ Status Styling */
.status-approved {
  color: #28a745;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(40, 167, 69, 0.1);
}

.status-rejected {
  color: #dc3545;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(220, 53, 69, 0.1);
}

.status-pending {
  color: #ffc107;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(255, 193, 7, 0.1);
}

.status-derogation {
  color: #17a2b8;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(23, 162, 184, 0.1);
}
