import { Component, OnInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { RapportsService } from './rapports.service';
import { DemandeService } from '../../client/demande-details/demande.service';
import { Demande } from '../../client/demande-details/demande.model';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSearch, faEraser, faEye, faPaperPlane, faSpinner, faFilter, faCalendarAlt, faCheck, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { NgxPaginationModule } from 'ngx-pagination';
@Component({
  selector: 'app-reports-receptionist',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule, NgxPaginationModule],
  templateUrl: './rapports.component.html',
  styleUrls: ['./rapports.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // For FontAwesome icons
})
export class RapportsReceptionistComponent implements OnInit {
  reports: any[] = [];
  filteredReports: any[] = [];
  demande: Demande | null = null;
  isLoading = true;
  error: string | null = null;

  // Filtering properties
  searchTerm: string = '';
  selectedDate: string = '';
  selectedStatus: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Font Awesome icons
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faPaperPlane = faPaperPlane;
  faSpinner = faSpinner;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;
  faCheck = faCheck;
  faEnvelope = faEnvelope;

  // Loading properties
  showLoadingModal = false;
  loadingMessage = 'Traitement en cours...';
  constructor(
    private rapportService: RapportsService,
    private router: Router,
    private cdr: ChangeDetectorRef, private demandeService: DemandeService,// ✅ Detect UI changes
  ) {}

  ngOnInit() {
    this.loadAllRapports();
  }

  // Fetch all reports
  loadAllRapports() {
    this.isLoading = true;
    this.error = null;

    // Show loading modal for initial load
    this.showLoadingModal = true;
    this.loadingMessage = 'Chargement des rapports...';

    this.rapportService.getAllRapports().subscribe({
      next: (data) => {
        this.reports = data.map(report => ({
          ...report,
          status: this.translateStatus(report.status), // ✅ Convert status to French
          status_director: this.translateStatus(report.status_director),
          client_status_display: this.translateClientStatus(report.client_status),
          sentToClient: report.client_status === 'sent',
          isSending: false // For tracking the sending state
        }));
        this.filteredReports = [...this.reports]; // Initialize filtered reports
        console.log('Rapports:', this.reports);

        // Hide loading indicators
        this.isLoading = false;
        this.showLoadingModal = false;

        this.cdr.detectChanges(); // ✅ Detect UI changes
      },
      error: (error) => {
        console.error('Error fetching rapports:', error);
        this.error = 'Erreur lors du chargement des rapports.';

        // Hide loading indicators
        this.isLoading = false;
        this.showLoadingModal = false;

        this.cdr.detectChanges();
      }
    });
  }

  translateStatus(status: string): string {
    switch (status) {
      case 'not_sent': return 'Non envoyé';
      case 'sent': return 'Envoyé au directeur';
      case 'valid': return 'Rapport Approuvé';
      case 'rejected': return 'Rapport Refusé';
      default: return status;
    }
  }

  translateClientStatus(status: string): string {
    switch (status) {
      case 'sent': return 'Envoyé au client';
      case 'not_sent': return 'Non envoyé au client';
      default: return 'Non envoyé au client';
    }
  }
  createInvoice(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Création de la facture...';

    this.demandeService.createFacture(this.demande.demande_id).subscribe({
      next: (response:any) => {
        // Hide loading spinner
        this.showLoadingModal = false;
        console.log("facture cree!", response);

        // Reload data
        this.loadAllRapports();
      },
      error: (error: any) => {
        // Hide loading spinner
        this.showLoadingModal = false;
        console.error("Erreur lors de l'envoi du devis:", error);

        // Show error message
        this.error = "Échec de création de la facture.";
        this.cdr.detectChanges();
      }
    });
  }
  // Send a report
  envoyerRapport(rapportId: number) {
    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Envoi du rapport...';

    this.rapportService.sendRapport(rapportId).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;
        console.log('Rapport envoyé:', response);

        // Update the UI instantly without reloading the entire list
        const report = this.reports.find(r => r.id === rapportId);
        if (report) {
          report.status_director = 'Envoyé au directeur'; // Update status in UI
          this.cdr.detectChanges(); // Detect UI changes dynamically
        }

        // Reload data after a short delay
        setTimeout(() => {
          this.loadAllRapports(); // Reload data instead of full page refresh
        }, 1000);
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;
        console.error('Erreur lors de l\'envoi du rapport:', error);

        // Show error message
        this.error = "Erreur lors de l'envoi du rapport. Veuillez réessayer.";
        this.cdr.detectChanges();
      }
    });
  }

  // Send a report to client
  envoyerRapportAuClient(demandeId: string, rapportId: number) {
    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Envoi du rapport au client...';

    // Find the report to update its sending status
    const report = this.reports.find(r => r.id === rapportId);
    if (report) {
      report.isSending = true; // Show loading spinner
      this.cdr.detectChanges();
    }

    this.rapportService.sendRapportToClient(demandeId).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;
        console.log('Rapport envoyé au client:', response);

        if (report) {
          report.isSending = false; // Hide loading spinner
          report.sentToClient = true; // Mark as sent to client
          report.client_status_display = 'Envoyé au client';
          this.cdr.detectChanges();
        }

        // Show success message
        this.loadingMessage = 'Rapport envoyé au client avec succès!';
        setTimeout(() => {
          this.loadAllRapports(); // Reload data after a short delay
        }, 1000);
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;
        console.error('Erreur lors de l\'envoi du rapport au client:', error);

        if (report) {
          report.isSending = false; // Hide loading spinner on error too
          this.cdr.detectChanges();
        }

        // Show error message
        this.error = "Erreur lors de l'envoi du rapport au client. Veuillez réessayer.";
        this.cdr.detectChanges();
      }
    });
  }

  // View Report Details
  viewReportDetails(rapportId: number) {
    this.router.navigate(['receptionist/rapports', rapportId]);
  }

  // Filtering methods
  onFilterChange(): void {
    const term = this.searchTerm.toLowerCase();
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredReports = this.reports.filter((report) => {
      // Filter by search term (ID or demande number)
      const matchesSearch =
        (report.id && report.id.toString().toLowerCase().includes(term)) ||
        (report.demande_id && report.demande_id.toString().toLowerCase().includes(term));

      // Filter by date
      const reportDate = report.creation_date ? new Date(report.creation_date) : null;
      const matchesDate = filterDate && reportDate ?
        reportDate.toDateString() === filterDate.toDateString() :
        !filterDate;

      // Filter by status (envoyé or not)
      let matchesStatus = true;
      if (this.selectedStatus) {
        if (this.selectedStatus === 'sent') {
          matchesStatus = report.status_director === 'Envoyé au directeur';
        } else if (this.selectedStatus === 'not_sent') {
          matchesStatus = report.status_director === 'Non envoyé';
        }
      }

      return matchesSearch && matchesDate && matchesStatus;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.selectedStatus = '';
    this.filteredReports = [...this.reports];
  }
}
