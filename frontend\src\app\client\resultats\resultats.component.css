@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* Main container */
.resultats-container {
  width: 85%;
  max-width: 1200px;
  margin: 50px auto;
  padding: 35px;
  background: white;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  font-family: 'Montserrat', sans-serif;
  animation: fadeInUp 1s ease-in-out forwards;
  transition: all 0.3s ease-in-out;
  border: 1px solid #f0f0f0;
}

/* Header styles */
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 35px;
  padding-bottom: 20px;
  border-bottom: 3px solid #2496d3;
}

h2 {
  color: #2496d3;
  font-size: 30px;
  margin: 0;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.title-text {
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  position: relative;
  text-align: center;
  width: 100%;
}

@keyframes glowText {
  from {
    text-shadow: 0 0 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0 0 20px rgba(36, 150, 211, 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.back-btn {
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 8px;
  padding: 10px 18px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.back-btn:hover {
  background-color: #e8e8e8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading and error styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #2496d3;
}

.loading-container .spinner {
  margin-bottom: 20px;
}

.loading-container p {
  font-size: 16px;
  font-weight: 500;
  color: #555;
}

.error-message, .no-results {
  background-color: #fff8f8;
  border-left: 4px solid #ff6b6b;
  padding: 25px;
  margin: 30px 0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 20px;
  color: #d32f2f;
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.1);
}

.error-message i, .no-results i {
  font-size: 24px;
}

.error-message p, .no-results p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.no-results {
  background-color: #fffbf0;
  border-left-color: #ffc107;
  color: #6c757d;
  flex-direction: column;
  text-align: center;
  padding: 50px 30px;
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.1);
}

.no-results i {
  font-size: 40px;
  color: #ffc107;
  margin-bottom: 20px;
}

/* Results content styles */
.results-content {
  margin-top: 40px;
}

.demande-info {
  background-color: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 40px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #eaeaea;
}

h3 {
  color: #2496d3;
  font-size: 22px;
  margin-top: 0;
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
  padding-bottom: 10px;
}



.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: transform 0.3s ease;
}

.info-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.info-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.status-badge {
  display: inline-block;
  padding: 6px 12px;
  background-color: #28a745;
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(40, 167, 69, 0.2);
}

/* Files section styles */
.files-section {
  margin-bottom: 30px;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 30px;
}

.file-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border: 1px solid #eaeaea;
  min-height: 220px;
  justify-content: space-between;
}

.file-type-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #2496d3;
  color: white;
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(36, 150, 211, 0.2);
}

.file-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(36, 150, 211, 0.15);
  border-color: #2496d3;
}

.file-card:active {
  transform: translateY(-2px);
}

.file-icon {
  color: #2496d3;
  margin-bottom: 25px;
  transition: transform 0.3s ease;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-card:hover .file-icon {
  transform: scale(1.1);
  color: #1a7bb9;
}

.file-info {
  text-align: center;
  margin-bottom: 25px;
  width: 100%;
}

.file-info h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.file-name {
  color: #6c757d;
  font-size: 15px;
  margin: 0;
  word-break: break-all;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 10px;
}

.download-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background-color: #28a745;
  color: white;
  padding: 14px 25px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  width: 70%;
  margin-top: auto;
  box-shadow: 0 4px 10px rgba(40, 167, 69, 0.2);
  border: none;
  cursor: pointer;
}

.download-button:hover {
  background-color: #218838;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
  transform: translateY(-2px);
}

.download-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

/* Download animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.download-button.downloading {
  animation: pulse 0.5s ease-in-out;
  background-color: #218838;
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Note section styles */
.note-section {
  background-color: #f0f7ff;
  padding: 25px;
  border-radius: 12px;
  border-left: 4px solid #2496d3;
  margin-top: 40px;
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.08);
}

.note-section h3 {
  color: #2496d3;
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 15px;
  font-weight: 600;
}

.note-section p {
  margin: 12px 0;
  color: #555;
  line-height: 1.6;
  font-size: 15px;
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .resultats-container {
    width: 95%;
    padding: 20px;
    margin: 30px auto;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .back-btn {
    align-self: flex-start;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .file-card {
    padding: 25px 15px;
    min-height: 200px;
  }

  .file-icon {
    height: 60px;
    margin-bottom: 15px;
  }

  .download-button {
    width: 90%;
    padding: 12px 20px;
    font-size: 15px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  h2 {
    font-size: 24px;
  }

  .note-section {
    padding: 20px;
  }
}
