<!-- Main Loading Spinner Overlay -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="spinner"></div>
</div>

<!-- Main Content Container -->
<div class="main-content" [class.content-hidden]="isLoading">
  <div class="demandes-container">

  <!-- ✅ Message d’erreur -->
  <div *ngIf="errorMessage" class="error">
    {{ errorMessage }}
  </div>

  <!-- ✅ Détails de la demande -->
  <div *ngIf="demande">
    <div class="header-actions">
      <div class="title-print-container">
        <h2>Détails de la demande</h2>
        <button class="btn-print" (click)="printDemande()" title="Imprimer la demande">
          <fa-icon [icon]="faPrint"></fa-icon> Imprimer
        </button>
      </div>
    </div>
    <p><strong>Demande n° :</strong> {{ demande?.demande_id }}</p>
    <p><strong>Date de réception :</strong> {{ demande?.demande_date }}</p>
    <p><strong>Nom du client :</strong> {{ demande?.user_name }} {{ demande?.user_nickname ? '(' + demande?.user_nickname + ')' : '' }} ({{ demande?.user_email }})</p>
    <p><strong>Mode de règlement :</strong> {{ demande?.mode_reglement || 'Non spécifié' }}</p>

    <div class="status-container">
      <p>
        <strong>Statut :</strong>
        <span class="status-tag pending" *ngIf="demande?.status === 'pending'">
          🟡 En attente
        </span>
        <span class="status-tag ongoing" *ngIf="demande?.status === 'ongoing_validation'">
          🔵 En cours de validation
        </span>
        <span class="status-tag derogation" *ngIf="demande?.status === 'derogation'">
          🟠 En cours de validation avec dérogation
        </span>
        <span class="status-tag valid" *ngIf="demande?.status === 'valid'">
          ✅ Demande validée
        </span>
        <span class="status-tag rejected" *ngIf="demande?.status === 'rejected'">
          ❌ Demande rejetée
        </span>
      </p>
    </div>
<!-- Rejection Reason Message - Only shown when demande is rejected -->
<div *ngIf="demande?.status === 'rejected'" class="rejection-reason">
  <i class="fas fa-exclamation-circle" style="color: #dc3545; margin-right: 10px; font-size: 18px;"></i>
  <div class="rejection-content">
    <h4>Motif de refus:</h4>
    <p>La demande a été refusée pour la raison suivante :<br>
      Les échantillons fournis ne correspondent pas aux critères d'analyse requis par notre laboratoire. </p>
  </div>
</div>
    <div *ngIf="demande.samples?.length; else noSamples">
      <table>
        <thead>
          <tr>
            <th>Identification échantillon</th>
            <th>Référence</th>
            <th>Nature</th>
            <th>Provenance</th>
            <th>Masse (G)</th>
            <th>État</th>
            <th>Analyses demandées</th>
            <th>Délai souhaité</th>
            <th>Analyse souhaitée</th>
            <th>Lot</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let sample of demande.samples">
            <td style="font-size: 16px; ">{{ sample.identification_echantillon }}</td>
            <td style="font-size: 16px; ">{{ sample.reference }}</td>
            <td style="font-size: 16px; ">{{ sample.nature_echantillon }}</td>
            <td style="font-size: 16px; ">{{ sample.provenance }}</td>
            <td style="font-size: 16px; ">{{ sample.masse_echantillon }}</td>
            <td style="font-size: 16px; ">{{ sample.etat }}</td>
            <td style="font-size: 16px; ">{{ sample.analyses_demandees?.join(', ') }}</td>
            <td style="font-size: 16px; ">{{ sample.delai_souhaite || 'Non spécifié'}}</td>
            <td style="font-size: 16px; ">{{ sample.analyse_souhaite || 'Non spécifié'}}</td>
            <td style="font-size: 16px; ">{{ sample.lot }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <ng-template #noSamples>
      <p>Aucun échantillon disponible pour cette demande.</p>
    </ng-template>

    <!-- ✅ Boutons d’action -->
    <div class="buttons-container">
      <!-- Pré-validation si statut = pending -->
      <button
        *ngIf="demande?.status === 'pending'"
        class="btn-prevalider"
        (click)="preValidateDemande()"
      >
        Pré-valider la demande
      </button>

      <!-- Validation complète / dérogation si statut = ongoing_validation -->
      <div
        *ngIf="demande?.status === 'ongoing_validation'"
        class="validation-buttons"
      >
        <button class="btn-validation" (click)="validateDemande()">
          Validation complète
        </button>

        <button class="btn-derogation" (click)="validateWithDerogation()">
          Validation avec dérogation
        </button>
      </div>



      <!-- Devis Section -->
      <div *ngIf="demande?.status === 'valid'" class="devis-section">
        <!-- Loading Spinner for Devis -->
        <div *ngIf="isDevisLoading" class="devis-loading">
          <div class="spinner"></div>
        </div>

        <!-- Devis Content when available -->
        <div style="font-size: 16px;" *ngIf="!isDevisLoading && devis.length > 0" class="devis-container">
        <div style="font-size: 16px; " *ngIf="demande?.devis_sent === 'yes'" class="statusDenvoir status-sent">
          Le devis a été envoyé au client
        </div>
        <div *ngIf="demande?.devis_sent === null || demande?.devis_sent === 'no'" class="statusDenvoir status-not-sent">
          Le devis n’a pas encore été envoyé
        </div>

        <h3 class="devis-label">
          Devis lié à la demande n° {{ demande?.demande_id }}
        </h3>
        <p class="devis-subtitle">
          Détails des analyses et coûts associés
        </p>

        <form [formGroup]="devisForm">
          <table class="table table-bordered text-center devis-table">
            <thead class="table-light">
              <tr>
                <th>N°</th>
                <th>Analyse</th>
                <th>Méthode</th>
                <th>Prix unitaire (DT)</th>
                <th>Quantité</th>
                <th>Prix total (DT)</th>
                <th *ngIf="demande?.devis_sent !== 'yes'">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let d of groupedDevis; let i = index">
                <td>{{ i + 1 }}</td>
                <td style="font-size: 16px;font-weight: bold">
                  <span >{{ d.analyse }}</span>
                </td>
                <td style="font-size: 16px; ">{{ d.methode }}</td>
                <td class="text-primary fw-bold">
                  <input
                    [hidden]="!isEditing[d.analyse]"
                    type="number"
                    [formControlName]="'prix_' + d.analyse"
                    class="form-control"
                  />
                  <span *ngIf="!isEditing[d.analyse]">
                    {{ d.prix_unitaire }}
                  </span>
                </td>
                <td class="text-primary fw-bold">
                  {{ d.quantite }}
                </td>
                <td class="text-primary fw-bold">
                  {{ d.prix_total }}
                </td>
                <td *ngIf="demande?.devis_sent !== 'yes'">
                  <button
                    type="button"
                    class="btn btn-sm btn-primary"
                    (click)="toggleEdit(d.analyse)"
                  >
                    {{ isEditing[d.analyse] ? 'Annuler' : 'Modifier' }}
                  </button>
                  <button
                    *ngIf="isEditing[d.analyse]"
                    type="button"
                    class="btn btn-sm btn-success"
                    (click)="updateRegistre(d.analyse)"
                  >
                    Sauvegarder
                  </button>
                </td>
              </tr>
              <tr class="fw-bold total-row">
                <td [attr.colspan]="demande?.devis_sent === 'yes' ? 5 : 6" class="text-end">
                  Montant total (DT)
                </td>
                <td class="text-primary">
                  {{ getTotal() }}
                </td>
              </tr>
            </tbody>
          </table>
        </form>
        <div class="button-row" id="specific-part">
          <button
            *ngIf="(demande?.devis_sent === null || demande?.devis_sent === 'no') && demande?.status === 'valid'"
            class="btn-validation"
            (click)="envoyerDevis()"
          >
            Envoyer devis
          </button>
          <!-- Facture creation button removed - now available in validation component -->

        </div>
        <!-- Payment Information Section -->
        <div class="payment-section">
          <h3 class="payment-label">Justificatifs de paiement</h3>

          <!-- Loading Spinner for Payments -->
          <div *ngIf="isLoadingPayments" class="payment-loading">
            <div class="spinner"></div>
            <p>Chargement des informations de paiement...</p>
          </div>

          <!-- Payment Error Message -->
          <div *ngIf="!isLoadingPayments && paymentError" class="payment-error">
            <p>{{ paymentError }}</p>
          </div>

          <!-- No Payments Message -->
          <div *ngIf="!isLoadingPayments && !paymentError && payments.length === 0" class="no-payments">
            <p>Aucun justificatif de paiement n'a été soumis pour cette demande.</p>
          </div>

          <!-- Rejection Reason Input -->
          <div *ngIf="showRejectionInput" class="rejection-reason-container" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;">
            <h4 style="margin-bottom: 15px; color: #333;">Raison du rejet</h4>
            <p style="margin-bottom: 10px; color: #666;">Veuillez indiquer la raison pour laquelle vous rejetez ce paiement.</p>
            <div style="margin-bottom: 15px;">
              <textarea
                [(ngModel)]="rejectionReason"
                name="rejectionReason"
                placeholder="Saisissez la raison du rejet ici..."
                style="width: 100%; padding: 10px; border: 1px solid #ced4da; border-radius: 4px; min-height: 100px;"
              ></textarea>
            </div>
            <div style="display: flex; justify-content: flex-end; gap: 10px;">
              <button
                (click)="cancelRejection()"
                style="padding: 8px 15px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;"
              >Annuler</button>
              <button
                (click)="submitRejection()"
                style="padding: 8px 15px; background-color: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;"
              >Rejeter</button>
            </div>
          </div>

          <!-- Results Date Information (shown when calculated) -->
          <div *ngIf="calculatedResultsDate" class="results-date-container" style="margin-bottom: 20px; padding: 15px; background-color: #e8f5e9; border-radius: 8px; border-left: 4px solid #4caf50;">
            <h4 style="margin-bottom: 10px; color: #2e7d32;">Date prévue des résultats</h4>
            <p style="font-size: 16px; color: #333;">
              <strong>Les résultats seront prêts d'ici le : {{ calculatedResultsDate }}</strong>
            </p>
            <p style="font-size: 14px; color: #666; margin-top: 5px;">
              Cette date est calculée à partir de l'approbation du paiement (7 jours ouvrables).
            </p>
          </div>

          <!-- Payment Table -->
          <div *ngIf="!isLoadingPayments && !paymentError && payments.length > 0" class="payment-table-container">
            <table class="payment-table">
              <thead>
                <tr>
                  <th>Date d'envoi</th>
                  <th>Montant (DT)</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let payment of payments">
                  <tr>
                    <td>{{ payment.payment_date | date:'dd/MM/yyyy' }}</td>
                    <td>{{ payment.amount }}</td>
                    <td>
                      <span class="payment-status" [ngClass]="getStatusClass(payment.status)">
                        <fa-icon [icon]="getStatusIcon(payment.status)"></fa-icon>
                        {{ getStatusText(payment.status) }}
                      </span>
                    </td>
                    <td class="actions-cell">
                      <!-- View button -->
                      <button
                        class="btn-action view"
                        [class.active]="(showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id)"
                        (click)="viewPaymentProof(payment.payment_proof, payment.id)"
                        title="{{ (showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id) ? 'Masquer le justificatif' : 'Voir le justificatif' }}"
                      >
                        <fa-icon [icon]="(showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id) ? faEyeSlash : faEye"></fa-icon>
                      </button>

                      <!-- Download button -->
                      <button
                        class="btn-action download"
                        (click)="downloadPaymentProof(payment.payment_proof)"
                        title="Télécharger le justificatif"
                      >
                        <fa-icon [icon]="faDownload"></fa-icon>
                      </button>

                      <!-- Accept button (show for pending and rejected status) -->
                      <button
                        *ngIf="payment.status === 'pending' || payment.status === 'rejected'"
                        class="btn-action approve"
                        (click)="updatePaymentStatus(payment.id, 'approved')"
                        title="Approuver le paiement"
                      >
                        <fa-icon [icon]="faCheck"></fa-icon>
                      </button>

                      <!-- Reject button (show for pending and approved status) -->
                      <button
                        *ngIf="payment.status === 'pending' || payment.status === 'approved'"
                        class="btn-action reject"
                        (click)="showRejectPaymentForm(payment.id)"
                        title="Rejeter le paiement"
                      >
                        <fa-icon [icon]="faTimes"></fa-icon>
                      </button>
                    </td>
                  </tr>

                  <!-- Preview row that appears under the payment row -->
                  <tr *ngIf="showPreview && previewPaymentId === payment.id" class="preview-row">
                    <td colspan="5" class="preview-cell">
                      <div class="preview-container">
                        <!-- Loading spinner -->
                        <div *ngIf="isLoadingPreview" class="loading-spinner">
                          <div class="spinner"></div>
                          <p>Chargement de l'aperçu...</p>
                        </div>

                        <!-- Error message -->
                        <div *ngIf="!isLoadingPreview && previewError" class="error-message">
                          <p>{{ previewError }}</p>
                          <button class="btn-retry" (click)="viewPaymentProof(payment.payment_proof, payment.id)">
                            <fa-icon [icon]="faSync"></fa-icon> Réessayer
                          </button>
                        </div>

                        <!-- Preview content -->
                        <div *ngIf="!isLoadingPreview && !previewError" class="preview-content">
                          <!-- File preview -->
                          <div class="file-preview">
                            <h4>Aperçu du justificatif</h4>

                            <!-- Image preview -->
                            <div *ngIf="fileType === 'image'" class="image-container">
                              <img
                                [src]="currentPayment?.payment_proof_url || currentPayment?.direct_url || currentPreviewUrl"
                                alt="Aperçu du justificatif"
                                class="preview-image"
                                (error)="handleImageError($event)"
                              >

                            </div>

                            <!-- PDF preview -->
                            <div *ngIf="fileType === 'pdf'" class="pdf-container">
                              <div class="pdf-icon">
                                <fa-icon [icon]="faFilePdf" size="3x"></fa-icon>
                              </div>
                              <p>Fichier PDF sélectionné</p>
                              <p>{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : 'Fichier PDF' }}</p>
                              <button class="btn-open-pdf" (click)="openPdfInNewTab()">
                                <fa-icon [icon]="faExternalLinkAlt"></fa-icon> Ouvrir le PDF
                              </button>
                            </div>

                            <!-- Unknown file type -->
                            <div *ngIf="fileType === 'unknown'" class="unknown-container">
                              <div class="file-icon">
                                <fa-icon [icon]="faFile" size="3x"></fa-icon>
                              </div>
                              <p>Type de fichier non reconnu</p>
                              <p>{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : 'Fichier inconnu' }}</p>

                              <!-- Direct download button -->
                              <button class="btn-download" (click)="downloadCurrentFile()">
                                <fa-icon [icon]="faDownload"></fa-icon> Télécharger le fichier
                              </button>

                              <!-- Direct open button -->
                              <button class="btn-open-direct" (click)="openDirectUrl()">
                                <fa-icon [icon]="faExternalLinkAlt"></fa-icon> Ouvrir directement
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>

          <!-- Direct Image Preview Section -->
          <div *ngIf="directImagePreview" class="direct-image-preview" style="margin-top: 30px; text-align: center; padding: 20px; background-color: #f9f9f9; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 15px; color: #333;">Aperçu du justificatif</h3>
            <div style="position: relative; display: inline-block; max-width: 100%;">
              <!-- Loading indicator while image is loading -->
              <div *ngIf="!directImageUrl" class="loading-spinner" style="padding: 20px; text-align: center;">
                <div class="spinner"></div>
                <p>Chargement de l'image...</p>
              </div>

              <img
                *ngIf="directImageUrl"
                [src]="directImageUrl"
                alt="Aperçu du justificatif"
                style="max-width: 100%; max-height: 600px; border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);"
                (error)="handleDirectImageError($event)"
                crossorigin="anonymous"
                onerror="this.onerror=null; console.log('HTML onerror handler triggered');"
              >

              <!-- Fallback content if image fails to load -->
              <div *ngIf="directImageRetryCount >= 3 && !directImageUrl" style="text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 8px; margin-top: 10px;">
                <fa-icon [icon]="faImage" size="3x" style="color: #6c757d; margin-bottom: 10px;"></fa-icon>
                <p>Impossible de charger l'image. Veuillez essayer de télécharger le fichier.</p>
              </div>

              <button
                (click)="closeDirectImagePreview()"
                style="position: absolute; top: 10px; right: 10px; background-color: rgba(255,255,255,0.8); border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: background-color 0.3s ease;"
                onmouseover="this.style.backgroundColor='rgba(240,240,240,0.9)'"
                onmouseout="this.style.backgroundColor='rgba(255,255,255,0.8)'"
                title="Fermer l'aperçu"
              >
                <fa-icon [icon]="faTimes"></fa-icon>
              </button>
            </div>

          </div>
        </div>
      </div>

        <!-- No Devis Message -->
        <div *ngIf="!isDevisLoading && (!devis || devis.length === 0)" class="no-devis">
          <p>Aucun devis disponible pour cette demande.</p>
        </div>
      </div>


    </div>
  </div>
</div>
</div>

<!-- Loading Spinner Overlay for Modal Actions -->
<div *ngIf="showLoadingModal" class="loading-overlay">
  <div class="spinner"></div>
</div>

<!-- Notification de soumission -->
<div *ngIf="showNotification" class="notification-overlay">
  <div class="notification">
    <div class="notification-icon" [style.color]="notificationColor">{{ notificationIcon }}</div>
    <div class="notification-message">{{ notificationMessage }}</div>
  </div>
</div>
