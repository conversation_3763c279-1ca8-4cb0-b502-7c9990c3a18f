<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fiches_transmission', function (Blueprint $table) {
            // Add statusRapport column to track rapport creation status
            $table->string('statusRapport')->nullable()->default('not_created');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fiches_transmission', function (Blueprint $table) {
            $table->dropColumn('statusRapport');
        });
    }
};
