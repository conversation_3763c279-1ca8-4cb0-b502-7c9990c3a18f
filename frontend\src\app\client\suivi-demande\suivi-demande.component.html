<div class="devis-container">
  <h2>Suivi des demandes</h2>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro:</label>
      <input
        type="text"
        id="search"
        placeholder="Rechercher..."
        [(ngModel)]="searchTerm"
        (input)="onFilterChange()"
        class="filter-input"
      />
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="pending">En attente</option>
        <option value="ongoing_validation">En cours de validation</option>
        <option value="valid">Validée</option>
        <option value="derogation">Dérogation</option>
        <option value="rejected">Rejetée</option>
      </select>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <div class="filter-group btn-group">
      <label class="invisible">Action:</label>
      <button (click)="clearFilters()" class="btn-clear">
        <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>
        Effacer les filtres
      </button>
    </div>
  </div>

  <!-- Affichage conditionnel du tableau -->
  <table class="styled-table" *ngIf="!isLoading && !hasError">
    <thead>
      <tr>
        <th style="width: 20%;">Demande Numéro</th>
        <th style="width: 25%;">Date de Soumission</th>
        <th style="width: 35%;">Statut Demande</th>
        <th style="width: 20%;">Action</th>
      </tr>
    </thead>
    <tbody>
      <!-- Exemples d'itération statique ou dynamique -->
      <!-- Message quand aucune demande n'est trouvée -->
      <tr *ngIf="!isLoading && filteredDemandes.length === 0" class="empty-row">
        <td colspan="4" class="text-center">
          Aucune demande trouvée.
        </td>
      </tr>

      <!-- Affichage des demandes -->
      <ng-container *ngIf="!isLoading && filteredDemandes.length > 0">
        <tr
          *ngFor="
            let demande of filteredDemandes
            | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
          class="table-row"
        >
        <td>{{ demande.demande_id }}</td>
        <td>{{ demande.demande_date }}</td>
        <td>
          <!-- Badges de statut -->
          <span class="status-tag pending" *ngIf="demande?.status === 'pending'">
            <fa-icon [icon]="faCircleDot" style="color: #ffc107;"></fa-icon> En attente
          </span>
          <span class="status-tag ongoing" *ngIf="demande?.status === 'ongoing_validation'">
            <fa-icon [icon]="faSpinner" style="color: #007bff;"></fa-icon> En cours de validation
          </span>
          <span class="status-tag ongoing" *ngIf="demande?.status === 'derogation'">
            <fa-icon [icon]="faCircleExclamation" style="color: #007bff;"></fa-icon> En cours de validation
          </span>
          <span class="status-tag valid" *ngIf="demande?.status === 'valid'">
            <fa-icon [icon]="faCircleCheck" style="color: #28a745;"></fa-icon> Demande validée
          </span>
          <span class="status-tag rejected" *ngIf="demande?.status === 'rejected'">
            <fa-icon [icon]="faCircleXmark" style="color: #dc3545;"></fa-icon> Demande rejetée
          </span>

        </td>
        <td class="action-cell">
          <button
          class="btn-details"
          (click)="navigateToDetails(demande.demande_id, $event)"
        >
          <fa-icon [icon]="faEye" style="margin-right: 8px; font-size: 16px;"></fa-icon>Voir détails
        </button>
        </td>
      </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    *ngIf="!isLoading && filteredDemandes.length > 0"
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>

  <!-- Message de chargement -->
  <div class="loading-message" *ngIf="isLoading">
    Chargement...
  </div>

  <!-- Message d'erreur -->
  <div class="error-message" *ngIf="hasError">
    Une erreur s’est produite lors de la récupération des données.
  </div>
</div>
