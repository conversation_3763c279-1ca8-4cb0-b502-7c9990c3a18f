import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-tarif-table',
    imports: [CommonModule],standalone: true,
    templateUrl: './tarif-table.component.html',
    styleUrl: './tarif-table.component.css'
})
export class TarifTableComponent {
  tarifs = [
    { analyse: 'Biotoxine (DSP)', methode: 'Méthode interne validée', prix: 350 },
    { analyse: 'Biotoxine (ASP)', methode: 'Accréditée LC-MS', prix: 130 },
    { analyse: 'Lipide', methode: 'Méthode normalisée extraction', prix: 45 },
    { analyse: 'Acide gras', methode: 'ISO 12966-2:2017', prix: 40 },
    { analyse: 'Protéines', methode: 'NF V04-407', prix: 45 },
    { analyse: 'Azote basique volatil total (ABVT)', methode: 'ISO 937:1978', prix: 45 },
    { analyse: 'pH', methode: 'NF ISO 2917', prix: 25 },
    { analyse: 'Hydrate de carbone (Sucre)', methode: 'Méthode interne validée', prix: 30 },
    { analyse: 'Triméthylamine (TMA)', methode: 'Méthode interne validée', prix: 35 },
    { analyse: 'Valeur énergétique', methode: 'Méthode validée HPLC', prix: 50 },
    { analyse: 'Sodium', methode: 'Méthode interne', prix: 25 },
    { analyse: 'Potassium', methode: 'Méthode interne', prix: 25 },
    { analyse: 'Vitamines (A & C)', methode: 'Méthode interne', prix: 50 },
    { analyse: 'Caroténoïde', methode: 'Méthode interne', prix: 30 },
    { analyse: 'TBARS', methode: 'Méthode interne', prix: 40 }
  ];
}
