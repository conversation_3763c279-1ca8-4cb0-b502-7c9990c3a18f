<div class="user-management">
  <h2>Gestion des Utilisateurs</h2>

  <div class="filter-container">
    <label for="nameFilter">Filtrer par nom :</label>
    <input type="text" id="nameFilter" [(ngModel)]="nameFilter" (input)="applyFilters()" placeholder="🔍 Rechercher par nom...">
  
    <label for="roleFilter">Filtrer par rôle :</label>
    <select id="roleFilter" [(ngModel)]="roleFilter" (change)="applyFilters()">
      <option value="">Tous</option>
      <option value="admin">Administrateur</option>
      <option value="receptionist">Réceptionniste</option>
      <option value="analyste">Analyste</option>
      <option value="client">Client</option>
    </select>
  </div>
  

  <button class="add-user-btn" (click)="openUserModal()">➕ Ajouter un Utilisateur</button>

  <table class="user-table">
    <thead>
      <tr>
        <th>Nom</th>
        <th>Email</th>
        <th>Rôle</th>
        <th>Date d'Ajout</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of users ?? []">
        <td>{{ user.name }}</td>
        <td>{{ user.email }}</td>
        <td>{{ user.role }}</td>
        <td>{{ user.created_at ? (user.created_at | date:'dd/MM/yyyy') : 'N/A' }}</td>
        <td>
          <button (click)="openUserModal(user)">✏️</button>
          <button (click)="deleteUser(user.id ?? 0)">🗑️</button>
        </td>
      </tr>
    </tbody>
  </table>

  <div class="modal" *ngIf="showUserModal">
    <div class="modal-content">
        <h3>{{ editMode ? 'Modifier l\'utilisateur' : 'Ajouter un utilisateur' }}</h3>

        <form [formGroup]="userForm" (ngSubmit)="saveUser()">
            <!-- Name -->
            <label>Nom :
                <span 
                    *ngIf="userForm.get('name')?.touched && userForm.get('name')?.errors" 
                    class="error"
                >
                    <span *ngIf="userForm.get('name')?.errors?.['required']">
                        Le nom est requis.
                    </span>
                    <span *ngIf="userForm.get('name')?.errors?.['minlength']">
                        Le nom doit contenir au moins 4 caractères.
                    </span>
                </span>
            </label>
            <ng-container *ngIf="editMode; else editNameField">
              <p>{{ userForm.get('name')?.value }}</p> 
          </ng-container>
          <ng-template #editNameField>
              <input type="text" formControlName="name" required>
          </ng-template>
            

            <!-- Email -->
            <label>Email :
                <span 
                    *ngIf="userForm.get('email')?.touched && userForm.get('email')?.errors" 
                    class="error">
                    <span *ngIf="userForm.get('email')?.errors?.['required']">
                        L'email est requis.
                    </span>
                    <span *ngIf="userForm.get('email')?.errors?.['pattern']">
                        Format d'email invalide.
                    </span>
                    <span *ngIf="userForm.get('email')?.errors?.['emailTaken']">
                        Cet e-mail existe déjà dans la base de données.
                    </span>
                </span>
            </label>
            <ng-container *ngIf="editMode; else editEmailField">
              <p>{{ userForm.get('email')?.value }}</p>
          </ng-container>
          <ng-template #editEmailField>
              <input type="email" formControlName="email" required>
          </ng-template>
            
            <!-- Password Fields (Only in Create Mode) -->
            <ng-container *ngIf="!editMode">
                <label>Mot de passe :
                    <span 
                        *ngIf="userForm.get('password')?.touched && userForm.get('password')?.errors" 
                        class="error"
                    >
                        <span *ngIf="userForm.get('password')?.errors?.['required']">
                            Le mot de passe est requis.
                        </span>
                        <span *ngIf="userForm.get('password')?.errors?.['minlength']">
                            Le mot de passe doit contenir au moins 8 caractères.
                        </span>
                    </span>
                </label>
                <input type="password" formControlName="password" required minlength="8">

                <label>Confirmer le mot de passe :
                    <span 
                        *ngIf="userForm.get('password_confirmation')?.touched && 
                                userForm.get('password_confirmation')?.errors" 
                        class="error"
                    >
                        <span *ngIf="userForm.get('password_confirmation')?.errors?.['required']">
                            La confirmation du mot de passe est requise.
                        </span>
                        <span *ngIf="userForm.get('password_confirmation')?.errors?.['minlength']">
                            La confirmation du mot de passe doit contenir au moins 8 caractères.
                        </span>
                        <span *ngIf="userForm.get('password_confirmation')?.errors?.['mismatch']">
                            Les mots de passe ne correspondent pas.
                        </span>
                    </span>
                </label>
                <input type="password" formControlName="password_confirmation" required>
            </ng-container>

            <!-- Role (Editable in both modes) -->
            <label>Rôle :
                <span 
                    *ngIf="userForm.get('role')?.touched && userForm.get('role')?.errors" 
                    class="error"
                >
                    <span *ngIf="userForm.get('role')?.errors?.['required']">
                        Choisir un rôle.
                    </span>
                </span>
            </label>
            <select formControlName="role">
                <option value="admin">Administrateur</option>
                <option value="receptionist">Réceptionniste</option>
                <option value="analyste">Analyste</option>
                <option value="client">Client</option>
            </select>

            <!-- Buttons -->
            <div class="modal-actions">
                <button type="button" (click)="closeUserModal()" class="cancel-btn">❌ Annuler</button>
                <button type="submit" class="save-btn">💾 Sauvegarder</button>
                
            </div>
        </form>
    </div>
</div>

  
</div>
