<?php

namespace App\Http\Controllers;

use App\Models\Demande;
use App\Models\Result;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ResultController extends Controller
{
    /**
     * Get all results
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $results = Result::with('demande')->get();

        return response()->json([
            'status' => 'success',
            'data' => $results
        ]);
    }

    /**
     * Store a new result
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Log the request for debugging
        Log::info('Results store method called', [
            'request_data' => $request->all(),
            'files' => $request->hasFile('results_file') ? 'File present' : 'No file'
        ]);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'results_file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            Log::error('Results validation failed', ['errors' => $validator->errors()]);
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get demande_id from request body
        $demandeId = $request->input('demande_id');

        // If demande_id is not provided, return an error
        if (!$demandeId) {
            Log::error('Demande ID is missing in the request');
            return response()->json([
                'status' => 'error',
                'message' => 'Demande ID is required'
            ], 422);
        }

        // Find the demande by demande_id (string identifier) or by numeric ID
        $demande = Demande::where('demande_id', $demandeId)->first();
        
        // If not found by string ID, try to find by numeric ID
        if (!$demande && is_numeric($demandeId)) {
            $demande = Demande::find($demandeId);
        }

        if (!$demande) {
            Log::error('Demande not found', ['demande_id' => $demandeId]);
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Handle file upload
        if ($request->hasFile('results_file')) {
            $file = $request->file('results_file');
            $fileName = 'results_' . $demande->demande_id . '_' . time() . '.' . $file->getClientOriginalExtension();

            // Store the file in the 'results' directory within the storage
            $filePath = $file->storeAs('results', $fileName, 'public');

            // Check if a result already exists for this demande
            $existingResult = Result::where('demande_id', $demande->id)->first();

            if ($existingResult) {
                // Delete the old file if it exists
                $oldFilePath = storage_path('app/public/' . $existingResult->results_file);
                if (file_exists($oldFilePath)) {
                    unlink($oldFilePath);
                }

                // Update the existing result
                $existingResult->results_file = $filePath;
                $existingResult->save();

                $result = $existingResult;
            } else {
                // Create a new result
                $result = new Result();
                $result->demande_id = $demande->id;
                $result->results_file = $filePath;
                $result->save();

                // Update the demande with the result_id
                $demande->result_id = $result->id;
                $demande->save();
            }

            Log::info('Results uploaded successfully', [
                'demande_id' => $demande->demande_id,
                'result_id' => $result->id
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Results uploaded successfully',
                'data' => $result
            ]);
        }

        Log::error('No file was uploaded');
        return response()->json([
            'status' => 'error',
            'message' => 'No file was uploaded'
        ], 400);
    }

    /**
     * Submit results for a specific demande
     *
     * @param Request $request
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitResults(Request $request, $demandeId)
    {
        // Log the request for debugging
        Log::info('Results submitResults method called', [
            'demande_id' => $demandeId,
            'request_data' => $request->all(),
            'files' => $request->hasFile('results_file') ? 'File present' : 'No file'
        ]);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'results_file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            Log::error('Results validation failed', ['errors' => $validator->errors()]);
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the demande by demande_id (string identifier) or by numeric ID
        $demande = Demande::where('demande_id', $demandeId)->first();
        
        // If not found by string ID, try to find by numeric ID
        if (!$demande && is_numeric($demandeId)) {
            $demande = Demande::find($demandeId);
        }

        if (!$demande) {
            Log::error('Demande not found', ['demande_id' => $demandeId]);
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Handle file upload
        if ($request->hasFile('results_file')) {
            $file = $request->file('results_file');
            $fileName = 'results_' . $demande->demande_id . '_' . time() . '.' . $file->getClientOriginalExtension();

            // Store the file in the 'results' directory within the storage
            $filePath = $file->storeAs('results', $fileName, 'public');

            // Check if a result already exists for this demande
            $existingResult = Result::where('demande_id', $demande->id)->first();

            if ($existingResult) {
                // Delete the old file if it exists
                $oldFilePath = storage_path('app/public/' . $existingResult->results_file);
                if (file_exists($oldFilePath)) {
                    unlink($oldFilePath);
                }

                // Update the existing result
                $existingResult->results_file = $filePath;
                $existingResult->save();

                $result = $existingResult;
            } else {
                // Create a new result
                $result = new Result();
                $result->demande_id = $demande->id;
                $result->results_file = $filePath;
                $result->save();

                // Update the demande with the result_id
                $demande->result_id = $result->id;
                $demande->save();
            }

            Log::info('Results uploaded successfully', [
                'demande_id' => $demande->demande_id,
                'result_id' => $result->id
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Results uploaded successfully',
                'data' => $result
            ]);
        }

        Log::error('No file was uploaded');
        return response()->json([
            'status' => 'error',
            'message' => 'No file was uploaded'
        ], 400);
    }

    /**
     * Get results for a specific demande
     *
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getResultsByDemandeId($demandeId)
    {
        // Find the demande by demande_id (string identifier) or by numeric ID
        $demande = Demande::where('demande_id', $demandeId)->first();
        
        // If not found by string ID, try to find by numeric ID
        if (!$demande && is_numeric($demandeId)) {
            $demande = Demande::find($demandeId);
        }

        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Get the result associated with this demande
        $result = Result::where('demande_id', $demande->id)->first();

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $result
        ]);
    }

    /**
     * Download results file
     *
     * @param Request $request
     * @param int $resultId
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function downloadResults(Request $request, $resultId)
    {
        $result = Result::find($resultId);

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => 'Result not found'
            ], 404);
        }

        // Check if the file exists in storage
        $filePath = storage_path('app/public/' . $result->results_file);

        if (!file_exists($filePath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Results file not found'
            ], 404);
        }

        // For direct file download
        if ($request->has('format') && $request->format === 'json') {
            // Generate a URL to the file
            $url = asset('storage/' . $result->results_file);

            return response()->json([
                'status' => 'success',
                'file_url' => $url,
                'file_path' => $result->results_file
            ]);
        }

        // Default: serve the file directly
        return response()->file($filePath);
    }

    /**
     * Get results for the authenticated client
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientResults()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get all demandes for this user
        $demandes = Demande::where('user_id', $user->id)->pluck('id');

        // Get all results for these demandes
        $results = Result::whereIn('demande_id', $demandes)
            ->with('demande')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $results
        ]);
    }

    /**
     * Remove a result
     *
     * @param int $resultId
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($resultId)
    {
        // Find the result
        $result = Result::find($resultId);

        if (!$result) {
            return response()->json([
                'status' => 'error',
                'message' => 'Result not found'
            ], 404);
        }

        // Get the associated demande to update its result_id
        $demande = Demande::where('result_id', $result->id)->first();

        // Delete the file from storage if it exists
        $filePath = storage_path('app/public/' . $result->results_file);
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // Update the demande to remove the result_id reference
        if ($demande) {
            $demande->result_id = null;
            $demande->save();
        }

        // Delete the result record
        $result->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Result deleted successfully'
        ]);
    }
}
