import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AnalysisService {
  private apiUrl = 'https://api.lab.com/analysis';

  constructor(private http: HttpClient) {}

  /** ✅ Recevoir une notification pour une nouvelle demande */
  getNewAnalysisNotifications(): Observable<any> {
    return this.http.get(`${this.apiUrl}/notifications`);
  }

  /** ✅ Effectuer une analyse */
  processAnalysis(requestId: string, results: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/${requestId}/process`, results);
  }

  /** ✅ Réceptionniste : Vérifier les résultats */
  verifyResults(requestId: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/${requestId}/verify`, {});
  }

  /** ✅ Administrateur : Valider/Rejeter une analyse */
  validateResults(requestId: string, status: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/${requestId}/validate`, { status });
  }

  /** ✅ Client : Télécharger le rapport */
  downloadReport(requestId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/${requestId}/download`, { responseType: 'blob' });
  }
}
