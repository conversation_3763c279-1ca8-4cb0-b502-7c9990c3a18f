import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = 'https://api.lab.com/auth';
  private userRole = new BehaviorSubject<string | null>(null); // Stocke le rôle actuel de l'utilisateur

  constructor(private http: HttpClient) {}

  /** ✅ Connexion */
  login(credentials: { email: string; password: string }): Observable<any> {
    return this.http.post(`${this.apiUrl}/login`, credentials);
  }

  /** ✅ Déconnexion */
  logout(): void {
    localStorage.removeItem('token');
    this.userRole.next(null);
  }

  /** ✅ Vérifier si l'utilisateur est authentifié */
  isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  }

  /** ✅ Récupérer les informations utilisateur */
  getUserRole(): Observable<string | null> {
    return this.userRole.asObservable();
  }

  /** ✅ Récupérer la liste des utilisateurs */
  getUsers(): Observable<any> {
    return this.http.get(`${this.apiUrl}/users`);
  }

  /** ✅ Créer un utilisateur */
  createUser(userData: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/register`, userData);
  }

  /** ✅ Modifier un utilisateur */
  updateUser(userId: string, userData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/${userId}`, userData);
  }

  /** ✅ Supprimer un utilisateur */
  deleteUser(userId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/users/${userId}`);
  }
}
