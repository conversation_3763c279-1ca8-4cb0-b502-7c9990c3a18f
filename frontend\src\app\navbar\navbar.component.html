<nav class="navbar navbar-expand-lg">
  <div class="container-fluid">
    <!-- ✅ Logo + Platform Name -->
    <a class="navbar-brand" routerLink="/">
      <img src="/logoo.png" alt="B3 Aqua Logo" class="navbar-logo">
       <!-- Optional: Add brand name here -->
    </a>

    <!-- Toggler for smaller screens -->
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <!-- ✅ Navigation Links (Centered) -->
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav mx-auto"> <!-- Use mx-auto to center the navbar links -->
        <!-- Unauthenticated Links -->
        <li *ngIf="role === null" class="nav-item">
          <a (click)="navigateTo('/')" class="nav-link nav-links-text me-3">Accueil</a>
        </li>
        <li *ngIf="role === null" class="nav-item">
          <a (click)="navigateTo('/services')" class="nav-link nav-links-text me-3">Services</a>
        </li>




      <li *ngIf="role === null" class="nav-item">
        <a (click)="navigateTo('/analyse')" class="nav-link nav-links-text me-3">Tarifs</a>
      </li>

      <li *ngIf="role === null" class="nav-item">
        <a (click)="navigateToAbout()" class="nav-link nav-links-text me-3">À propos</a>
      </li>
      <li *ngIf="role === null" class="nav-item">
        <a (click)="navigateTo('/contact')" class="nav-link nav-links-text me-3">Contact</a>
      </li>
        <!-- Admin specific navbar items -->
        <li *ngIf="role === 'admin'" class="nav-item">
          <a (click)="navigateTo('/admin/dashboard')" class=" nav-links-text  me-3">Accueil</a>
        </li>
        <li *ngIf="role === 'admin'" class="nav-item">
          <a (click)="navigateTo('/admin/users')" class=" nav-links-text me-3">Utilisateurs</a>
        </li>
        <li *ngIf="role === 'admin'" class="nav-item">
          <a (click)="navigateTo('/admin/reports')" class=" nav-links-text me-3">Rapports</a>
        </li>


        <!-- Client specific navbar items -->
        <li *ngIf="role === 'client'" class="nav-item">
          <a (click)="navigateTo('/client/dashboard')" class="nav-link nav-links-text me-3">Accueil</a>
        </li>

        <li *ngIf="role === 'client'" class="nav-item">
          <a (click)="navigateTo('/client/demandes')" class="nav-link nav-links-text me-3">Demande</a>
        </li>
        <li *ngIf="role === 'client'" class="nav-item">
          <a (click)="navigateTo('/client/suivi')" class="nav-link nav-links-text me-3">Suivi</a>
        </li>
        <li *ngIf="role === 'client'" class="nav-item">
          <a (click)="navigateTo('/client/devis')" class="nav-link nav-links-text me-3">Devis</a>
        </li>

        <li *ngIf="role === 'client'" class="nav-item">
          <a (click)="navigateTo('/client/results')" class="nav-link nav-links-text me-3">Résultat</a>
        </li>
        <li *ngIf="role === 'responsable'" class="nav-item">
          <a (click)="navigateTo('/analysits/dashboard')" class="nav-link nav-links-text me-3">Accueil</a>
        </li>
        <li *ngIf="role === 'responsable'" class="nav-item">
          <a (click)="navigateTo('/analysits/fiche-transmission')" class="nav-link nav-links-text me-3">Fiches de transmission</a>

        </li>
        <li *ngIf="role === 'responsable'" class="nav-item">
          <a (click)="navigateTo('/analysits/results')" class="nav-link nav-links-text me-3">Gestions des Résultats</a>
        </li>
        <li *ngIf="role === 'receptionist'" class="nav-item">
          <a (click)="navigateTo('receptionist/dashboard')" class="nav-link nav-links-text me-3">Accueil</a>
        </li>

        <li *ngIf="role === 'receptionist'" class="nav-item">
          <a (click)="navigateTo('receptionist/demandes')" class="nav-link nav-links-text me-3">Demandes</a>
        </li>
        <li *ngIf="role === 'receptionist'" class="nav-item">
          <a (click)="navigateTo('receptionist/validation')" class="nav-link nav-links-text me-3">Demandes Validées</a>
        </li>
        <li *ngIf="role === 'receptionist'" class="nav-item">
          <a (click)="navigateTo('/receptionist/results')" class="nav-link nav-links-text me-3">Résultats</a>
        </li>
        <li *ngIf="role === 'receptionist'" class="nav-item dropdown">
          <a class="nav-link dropdown-toggle-no-line dropdown-toggle me-3" href="#" id="receptionistDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            Plus <i class="fas fa-caret-down ms-1"></i>
          </a>
          <ul class="dropdown-menu" aria-labelledby="receptionistDropdown">
            <li><a class="dropdown-item" (click)="navigateTo('/receptionist/registre')">Registres de suivi</a></li>
            <li><a class="dropdown-item" (click)="navigateTo('receptionist/fiche-transmission')">Fiches de transmission</a></li>
            <li><a class="dropdown-item" (click)="navigateTo('/receptionist/facture')">Factures</a></li>
            <li><a class="dropdown-item" (click)="navigateTo('/receptionist/rapports')" >Rapports</a></li>
            <li><a class="dropdown-item" (click)="navigateTo('/receptionist/users')">Gestion Utilisateurs</a></li>
            <li><a class="dropdown-item" (click)="navigateTo('/gestion-analyse')">Gestion des Analyses</a></li>
          </ul>
        </li>


        <li *ngIf="role === 'director'" class="nav-item">
          <a (click)="navigateTo('director/dashboard')" class="nav-link nav-links-text me-3">Accueil</a>
        </li>
        <li *ngIf="role === 'director'" class="nav-item">
          <a (click)="navigateTo('/derogation')" class="nav-link nav-links-text me-3">Demandes avec dérogations</a>
        </li>
        <li *ngIf="role === 'director'" class="nav-item">
          <a (click)="navigateTo('/director/reports')" class="nav-link nav-links-text me-3">Rapports</a>
        </li>




      </ul>
      <div *ngIf="role === 'client'" class="nav-item">
        <notifications></notifications>

      </div>
      <div *ngIf="role === 'receptionist'" class="nav-item">
        <notifications></notifications>

      </div>
      <div *ngIf="role === 'director'" class="nav-item">
        <notifications></notifications>

      </div>
      <div *ngIf="role === 'responsable'" class="nav-item">
        <notifications></notifications>

      </div>
      <div *ngIf="role !== null" class="nav-item me-3  mt-3 mb-3">
        <a (click)="navigateTo('/profile')" class="k nav-links-text">Profile</a>
      </div>

      <!-- Logout button -->
      <div *ngIf="role !== null" class="nav-item">
        <a href="#" (click)="logout()" class="btn btn-primary nav-button">Déconnexion</a>
      </div>

      <!-- Login Button (only for unauthenticated users) -->
      <div *ngIf="role === null" class="nav-item">
        <button (click)="navigateTo('/signup')" class="btn btn-primary nav-button">Connexion</button>
      </div>
    </div>

    <!-- ✅ Profile and Logout/Connexion Buttons (Aligned to the right) -->

      <!-- Profile link -->


  </div>
</nav>
