export interface Devis {
  id: number;
  demande_id: number;
  analyse: string;
  methode: string;
  prix_unitaire: string;
  quantite: number;
  prix_total: string;
  created_at: string;
  updated_at: string;
  demande: {
    id: number;
    user_id: number;
    mode_reglement: string;
    demande_id: string;
    total_enregistrements: number;
    demande_date: string;
    created_at: string;
    updated_at: string;
    status: string;
    lot: string | null;
    reference: string | null;
    facture_id: number | null;
  };
}