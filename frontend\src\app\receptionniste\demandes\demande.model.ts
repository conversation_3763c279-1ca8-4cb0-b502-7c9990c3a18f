export interface Sample {
    identification_echantillon: string;
    reference: string;
    nature_echantillon: string;
    provenance: string;
    masse_echantillon: number;
    etat: string;
    analyses_demandees: string[];
    delai_souhaite: string;
    analyse_souhaite: string;
    lot: string;
  }

  export interface Demande {
    id: number;
    validation?:number;
    facture_id?: number;
    registre_id?: number;
    analyses_accredite?:number;
    delai_souhaite?: string;
    fiche_id?: number;
    demande_id: string;
    total_enregistrements: number;
    demande_date: string;
    user_id: number;
    mode_reglement: string;
    status: string;
    samples: Sample[];
    userName?: string;
    userNick?:string;
    // Loading states
    isCreatingFiche?: boolean;
    isCreatingRegistre?: boolean;
    isCreatingFacture?: boolean;
    isCreatingRapport?: boolean;
  }
