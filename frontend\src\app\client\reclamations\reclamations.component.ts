import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import {
  faSearch,
  faCalendarAlt,
  faFilter,
  faEraser,
  faEye,
  faPlus,
  faExclamationTriangle,
  faCircleDot,
  faSpinner,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';

// Define interfaces for type safety
interface Reclamation {
  id: string;
  date: string;
  subject: string;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved';
  demandeId?: string;
  attachmentUrl?: string;
}

@Component({
  selector: 'app-reclamations',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxPaginationModule,
    FontAwesomeModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './reclamations.component.html',
  styleUrl: './reclamations.component.css'
})
export class ReclamationsComponent implements OnInit {
  // Font Awesome icons
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faFilter = faFilter;
  faEraser = faEraser;
  faEye = faEye;
  faPlus = faPlus;
  faExclamationTriangle = faExclamationTriangle;
  faCircleDot = faCircleDot;
  faSpinner = faSpinner;
  faCheckCircle = faCheckCircle;

  // Component state
  isLoading = true;
  reclamations: Reclamation[] = [];
  filteredReclamations: Reclamation[] = [];

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;

  // Filter properties
  searchTerm = '';
  selectedStatus = '';
  selectedDate = '';
  showFilters = false;

  constructor(
    private router: Router,
    private library: FaIconLibrary
  ) {
    // Add icons to the library for the [spin] property to work
    library.addIcons(
      faSearch,
      faCalendarAlt,
      faFilter,
      faEraser,
      faEye,
      faPlus,
      faExclamationTriangle,
      faCircleDot,
      faSpinner,
      faCheckCircle
    );
  }

  ngOnInit(): void {
    this.loadReclamations();
  }

  // Load reclamations (static data for now)
  loadReclamations(): void {
    // Simulate API call
    setTimeout(() => {
      this.reclamations = [
        {
          id: 'REC-001',
          date: '2023-06-15',
          subject: 'Résultats incorrects',
          description: 'Les résultats d\'analyse reçus ne correspondent pas à ma demande initiale.',
          status: 'pending',
          demandeId: '0001-100'
        },
        {
          id: 'REC-002',
          date: '2023-06-28',
          subject: 'Délai d\'analyse dépassé',
          description: 'Le délai annoncé pour l\'analyse a été dépassé de plus de 10 jours.',
          status: 'in_progress',
          demandeId: '0001-102'
        },
        {
          id: 'REC-003',
          date: '2023-07-05',
          subject: 'Erreur de facturation',
          description: 'Le montant facturé ne correspond pas au devis initial.',
          status: 'resolved',
          demandeId: '0001-101'
        },
        {
          id: 'REC-004',
          date: '2023-07-12',
          subject: 'Échantillon perdu',
          description: 'Mon échantillon semble avoir été perdu, aucune nouvelle depuis 3 semaines.',
          status: 'in_progress',
          demandeId: '0001-103'
        },
        {
          id: 'REC-005',
          date: '2023-07-20',
          subject: 'Rapport incomplet',
          description: 'Le rapport d\'analyse reçu ne contient pas toutes les informations demandées.',
          status: 'pending',
          demandeId: '0001-104'
        }
      ];

      this.filteredReclamations = [...this.reclamations];
      this.isLoading = false;
    }, 1000);
  }

  // Toggle filter section visibility
  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  // Apply filters
  applyFilters(): void {
    this.currentPage = 1; // Reset to first page when filters are applied

    this.filteredReclamations = this.reclamations.filter(reclamation => {
      // Filter by search term (ID or subject)
      const matchesSearch = this.searchTerm ?
        (reclamation.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
         reclamation.subject.toLowerCase().includes(this.searchTerm.toLowerCase())) :
        true;

      // Filter by status
      const matchesStatus = this.selectedStatus ?
        reclamation.status === this.selectedStatus :
        true;

      // Filter by date
      let matchesDate = true;
      if (this.selectedDate) {
        const reclamationDate = new Date(reclamation.date).toISOString().split('T')[0];
        matchesDate = reclamationDate === this.selectedDate;
      }

      return matchesSearch && matchesStatus && matchesDate;
    });
  }

  // Reset filters
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedDate = '';
    this.filteredReclamations = [...this.reclamations];
    this.currentPage = 1;
  }

  // Get status label for display
  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending': return 'En attente';
      case 'in_progress': return 'En cours';
      case 'resolved': return 'Résolu';
      default: return status;
    }
  }

  // Get status class for styling
  getStatusClass(status: string): string {
    switch (status) {
      case 'pending': return 'pending';
      case 'in_progress': return 'in-progress';
      case 'resolved': return 'resolved';
      default: return '';
    }
  }

  // Get status icon
  getStatusIcon(status: string): any {
    switch (status) {
      case 'pending': return this.faCircleDot;
      case 'in_progress': return this.faSpinner;
      case 'resolved': return this.faCheckCircle;
      default: return this.faCircleDot;
    }
  }

  // Check if status is in progress for spin animation
  isInProgress(status: string): boolean {
    return status === 'in_progress';
  }

  // Navigate to reclamation details
  viewReclamationDetails(id: string): void {
    this.router.navigate(['/client/reclamation-details', id]);
  }

  // Navigate to create new reclamation
  createNewReclamation(): void {
    this.router.navigate(['/client/reclamation']);
  }
}
