import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root' // ✅ Assure-toi que ce service est accessible globalement
})
export class ReportService {
  private readonly storageKey = 'adminReports';

  constructor() {}

  saveReport(report: any): void {
    let storedReports = localStorage.getItem(this.storageKey);
    let reports = storedReports ? JSON.parse(storedReports) : [];
    reports.push(report);
    localStorage.setItem(this.storageKey, JSON.stringify(reports));
  }

  getReports(): any[] {
    let storedReports = localStorage.getItem(this.storageKey);
    return storedReports ? JSON.parse(storedReports) : [];
  }

  updateReports(reports: any[]): void {
    localStorage.setItem(this.storageKey, JSON.stringify(reports));
  }
}
