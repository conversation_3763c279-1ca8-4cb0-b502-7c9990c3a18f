import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

/* Interface des demandes de dérogation */
interface DerogationRequest {
  id: number;
  clientName: string;
  sampleType: string;
  requestedMass: number;
  minimumMass: number;
  status: 'pending' | 'approved' | 'rejected' | 'derogation_required';
  requestDate: string;
}

@Component({
  selector: 'app-manage-requests',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './manage-requests.component.html',
  styleUrl: './manage-requests.component.css'
})
export class ManageRequestsComponent {

  /* 🔹 Liste des demandes avec types d'échantillons */
  derogationRequests: DerogationRequest[] = [
    {
      id: 1,
      clientName: 'Naifar Mahdi',
      sampleType: 'Poissons',
      requestedMass: 35,
      minimumMass: 50,
      status: 'pending',
      requestDate: '2025-02-17'
    },
    {
      id: 2,
      clientName: 'Bendaya Yessin',
      sampleType: 'Crustacés',
      requestedMass: 40,
      minimumMass: 50,
      status: 'pending',
      requestDate: '2025-02-16'
    },
    {
      id: 3,
      clientName: 'Samir Aqua',
      sampleType: 'Algues',
      requestedMass: 100,
      minimumMass: 100,
      status: 'pending',
      requestDate: '2025-02-15'
    }
  ];

  constructor(private router: Router) {}

  /**
   * ✅ Vérifie si la masse demandée respecte le critère minimal
   * - Si oui, la demande est validée.
   * - Sinon, une **dérogation** est requise.
   */
  validateRequest(request: DerogationRequest): void {
    if (request.requestedMass >= request.minimumMass) {
      request.status = 'approved';
      alert(`✅ Demande acceptée ! Quantité requise respectée pour ${request.clientName}.`);
      this.notifyReceptionistAndClient(request);
    } else {
      request.status = 'derogation_required';
      alert(`⚠️ Masse insuffisante pour ${request.clientName}. Dérogation requise.`);
      this.viewDerogationDetails(request);
    }
  }
  getUnit(sampleType: string): string {
    switch (sampleType) {
      case 'Poissons':
      case 'Crustacés':
      case 'Mollusques':
      case 'Algues':
        return 'g MF'; // Grammes de matière fraîche
      case 'Coquillage':
        return 'ind'; // Nombre d'individus
      case 'Matrice séchée produit de la mer':
        return 'g MS'; // Grammes de matière sèche
      case 'Produit de la mer en conserve':
        return '≥g'; // Quantité minimale en grammes
      default:
        return 'g'; // Par défaut en grammes
    }
  }

  /**
   * ❌ Rejette la demande et notifie le client.
   */
  rejectRequest(request: DerogationRequest): void {
    request.status = 'rejected';
    alert(`❌ Demande rejetée pour ${request.clientName}. Masse trop faible.`);
    this.notifyClient(request);
  }

  /**
   * 📄 Redirige vers la fiche de dérogation si la masse demandée est insuffisante.
   */
  viewDerogationDetails(request: DerogationRequest): void {
    this.router.navigate(['/admin/requests-derogation'], {
      queryParams: { id: request.id, sampleType: request.sampleType }
    });
  }

  /**
   * 📩 Notification envoyée au réceptionniste et au client après validation.
   */
  notifyReceptionistAndClient(request: DerogationRequest): void {
    console.log(`📩 Notification envoyée au réceptionniste et au client : Demande ${request.id} validée.`);
  }

  /**
   * 📩 Notification envoyée au client après un rejet.
   */
  notifyClient(request: DerogationRequest): void {
    console.log(`📩 Notification envoyée au client : Demande ${request.id} rejetée.`);
  }
}
