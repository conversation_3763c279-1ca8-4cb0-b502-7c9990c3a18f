{"version": 3, "sources": ["../../../../../../node_modules/dompurify/dist/purify.es.mjs"], "sourcesContent": ["/*! @license DOMPurify 3.2.4 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */\n\nconst {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor\n} = Object;\nlet {\n  freeze,\n  seal,\n  create\n} = Object; // eslint-disable-line import/no-mutable-exports\nlet {\n  apply,\n  construct\n} = typeof Reflect !== 'undefined' && Reflect;\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\nif (!construct) {\n  construct = function construct(Func, args) {\n    return new Func(...args);\n  };\n}\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySplice = unapply(Array.prototype.splice);\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\nconst regExpTest = unapply(RegExp.prototype.test);\nconst typeErrorCreate = unconstruct(TypeError);\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply(func) {\n  return function (thisArg) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return apply(func, thisArg, args);\n  };\n}\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return construct(func, args);\n  };\n}\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(set, array) {\n  let transformCaseFunc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n        element = lcElement;\n      }\n    }\n    set[element] = true;\n  }\n  return set;\n}\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray(array) {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n  return array;\n}\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone(object) {\n  const newObject = create(null);\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (value && typeof value === 'object' && value.constructor === Object) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n  return newObject;\n}\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n    object = getPrototypeOf(object);\n  }\n  function fallbackValue() {\n    return null;\n  }\n  return fallbackValue;\n}\nconst html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\nconst svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nconst svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nconst svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nconst mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']);\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nconst mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nconst text = freeze(['#text']);\nconst html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'popover', 'popovertarget', 'popovertargetaction', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'wrap', 'xmlns', 'slot']);\nconst svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'amplitude', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'exponent', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'slope', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'tablevalues', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nconst mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nconst xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n// eslint-disable-next-line unicorn/better-regex\nconst MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nconst ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nconst TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nconst DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nconst ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nconst IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nconst IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nconst ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nconst DOCTYPE_NAME = seal(/^html$/i);\nconst CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\nvar EXPRESSIONS = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ARIA_ATTR: ARIA_ATTR,\n  ATTR_WHITESPACE: ATTR_WHITESPACE,\n  CUSTOM_ELEMENT: CUSTOM_ELEMENT,\n  DATA_ATTR: DATA_ATTR,\n  DOCTYPE_NAME: DOCTYPE_NAME,\n  ERB_EXPR: ERB_EXPR,\n  IS_ALLOWED_URI: IS_ALLOWED_URI,\n  IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n  MUSTACHE_EXPR: MUSTACHE_EXPR,\n  TMPLIT_EXPR: TMPLIT_EXPR\n});\n\n/* eslint-disable @typescript-eslint/indent */\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5,\n  // Deprecated\n  entityNode: 6,\n  // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12 // Deprecated\n};\nconst getGlobal = function getGlobal() {\n  return typeof window === 'undefined' ? null : window;\n};\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n  if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  }\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\nconst _createHooksMap = function _createHooksMap() {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: []\n  };\n};\nfunction createDOMPurify() {\n  let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n  const DOMPurify = root => createDOMPurify(root);\n  DOMPurify.version = '3.2.4';\n  DOMPurify.removed = [];\n  if (!window || !window.document || window.document.nodeType !== NODE_TYPE.document || !window.Element) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n  let {\n    document\n  } = window;\n  const originalDocument = document;\n  const currentScript = originalDocument.currentScript;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes\n  } = window;\n  const ElementPrototype = Element.prototype;\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName\n  } = document;\n  const {\n    importNode\n  } = originalDocument;\n  let hooks = _createHooksMap();\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT\n  } = EXPRESSIONS;\n  let {\n    IS_ALLOWED_URI: IS_ALLOWED_URI$1\n  } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc = null;\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n  const formElement = document.createElement('form');\n  const isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function _parseConfig() {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE =\n    // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS') ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR') ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES') ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR') ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR, transformCaseFunc) : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS') ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS, transformCaseFunc) : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS') ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS') ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR') ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES') ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS = cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS = cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n      }\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n      }\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n      }\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n    CONFIG = cfg;\n  };\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);\n  const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function _checkValidNamespace(element) {\n    let parent = getParentNode(element);\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      }\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    }\n    // For XHTML and XML documents that support custom namespaces\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    }\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function _removeAttribute(name, element) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element\n      });\n    }\n    element.removeAttribute(name);\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n    const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n    const body = doc.body || doc.documentElement;\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function _createNodeIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root,\n    // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null);\n  };\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function _isClobbered(element) {\n    return element instanceof HTMLFormElement && (typeof element.nodeName !== 'string' || typeof element.textContent !== 'string' || typeof element.removeChild !== 'function' || !(element.attributes instanceof NamedNodeMap) || typeof element.removeAttribute !== 'function' || typeof element.setAttribute !== 'function' || typeof element.namespaceURI !== 'string' || typeof element.insertBefore !== 'function' || typeof element.hasChildNodes !== 'function');\n  };\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function _isNode(value) {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n  function _executeHooks(hooks, currentNode, data) {\n    arrayForEach(hooks, hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function _sanitizeElements(currentNode) {\n    let content = null;\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any kind of possibly harmful comments */\n    if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\\w]/g, currentNode.data)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {\n          return false;\n        }\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {\n          return false;\n        }\n      }\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        content = stringReplace(content, expr, ' ');\n      });\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n    return false;\n  };\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ;else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ;else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n      // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n      // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ;else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ;else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ;else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ;else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ;else if (value) {\n      return false;\n    } else ;\n    return true;\n  };\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function _isBasicCustomElement(tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n    const {\n      attributes\n    } = currentNode;\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined\n    };\n    let l = attributes.length;\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const {\n        name,\n        namespaceURI,\n        value: attrValue\n      } = attr;\n      const lcName = transformCaseFunc(name);\n      let value = name === 'value' ? attrValue : stringTrim(attrValue);\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n      /* Handle attributes that require Trusted Types */\n      if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ;else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty) {\n    let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n      // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n    /* Now start iterating over the created document */\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n      return returnNode;\n    }\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n    /* Serialize doctype if allowed */\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n  DOMPurify.setConfig = function () {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n      return index === -1 ? undefined : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n    return arrayPop(hooks[entryPoint]);\n  };\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n  return DOMPurify;\n}\nvar purify = createDOMPurify();\nexport { purify as default };\n"], "mappings": ";;;AAEA,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AACJ,IAAI;AAAA,EACF;AAAA,EACA;AAAA,EACA;AACF,IAAI;AACJ,IAAI;AAAA,EACF;AAAA,EACA;AACF,IAAI,OAAO,YAAY,eAAe;AACtC,IAAI,CAAC,QAAQ;AACX,WAAS,SAASA,QAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AACF;AACA,IAAI,CAAC,MAAM;AACT,SAAO,SAASC,MAAK,GAAG;AACtB,WAAO;AAAA,EACT;AACF;AACA,IAAI,CAAC,OAAO;AACV,UAAQ,SAASC,OAAM,KAAK,WAAW,MAAM;AAC3C,WAAO,IAAI,MAAM,WAAW,IAAI;AAAA,EAClC;AACF;AACA,IAAI,CAAC,WAAW;AACd,cAAY,SAASC,WAAU,MAAM,MAAM;AACzC,WAAO,IAAI,KAAK,GAAG,IAAI;AAAA,EACzB;AACF;AACA,IAAM,eAAe,QAAQ,MAAM,UAAU,OAAO;AACpD,IAAM,mBAAmB,QAAQ,MAAM,UAAU,WAAW;AAC5D,IAAM,WAAW,QAAQ,MAAM,UAAU,GAAG;AAC5C,IAAM,YAAY,QAAQ,MAAM,UAAU,IAAI;AAC9C,IAAM,cAAc,QAAQ,MAAM,UAAU,MAAM;AAClD,IAAM,oBAAoB,QAAQ,OAAO,UAAU,WAAW;AAC9D,IAAM,iBAAiB,QAAQ,OAAO,UAAU,QAAQ;AACxD,IAAM,cAAc,QAAQ,OAAO,UAAU,KAAK;AAClD,IAAM,gBAAgB,QAAQ,OAAO,UAAU,OAAO;AACtD,IAAM,gBAAgB,QAAQ,OAAO,UAAU,OAAO;AACtD,IAAM,aAAa,QAAQ,OAAO,UAAU,IAAI;AAChD,IAAM,uBAAuB,QAAQ,OAAO,UAAU,cAAc;AACpE,IAAM,aAAa,QAAQ,OAAO,UAAU,IAAI;AAChD,IAAM,kBAAkB,YAAY,SAAS;AAO7C,SAAS,QAAQ,MAAM;AACrB,SAAO,SAAU,SAAS;AACxB,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AACA,WAAO,MAAM,MAAM,SAAS,IAAI;AAAA,EAClC;AACF;AAOA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAY;AACjB,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AACA,WAAO,UAAU,MAAM,IAAI;AAAA,EAC7B;AACF;AASA,SAAS,SAAS,KAAK,OAAO;AAC5B,MAAI,oBAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC5F,MAAI,gBAAgB;AAIlB,mBAAe,KAAK,IAAI;AAAA,EAC1B;AACA,MAAI,IAAI,MAAM;AACd,SAAO,KAAK;AACV,QAAI,UAAU,MAAM,CAAC;AACrB,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,YAAY,kBAAkB,OAAO;AAC3C,UAAI,cAAc,SAAS;AAEzB,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,gBAAM,CAAC,IAAI;AAAA,QACb;AACA,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,QAAI,OAAO,IAAI;AAAA,EACjB;AACA,SAAO;AACT;AAOA,SAAS,WAAW,OAAO;AACzB,WAAS,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;AACjD,UAAM,kBAAkB,qBAAqB,OAAO,KAAK;AACzD,QAAI,CAAC,iBAAiB;AACpB,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,MAAM,QAAQ;AACrB,QAAM,YAAY,OAAO,IAAI;AAC7B,aAAW,CAAC,UAAU,KAAK,KAAK,QAAQ,MAAM,GAAG;AAC/C,UAAM,kBAAkB,qBAAqB,QAAQ,QAAQ;AAC7D,QAAI,iBAAiB;AACnB,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,kBAAU,QAAQ,IAAI,WAAW,KAAK;AAAA,MACxC,WAAW,SAAS,OAAO,UAAU,YAAY,MAAM,gBAAgB,QAAQ;AAC7E,kBAAU,QAAQ,IAAI,MAAM,KAAK;AAAA,MACnC,OAAO;AACL,kBAAU,QAAQ,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,aAAa,QAAQ,MAAM;AAClC,SAAO,WAAW,MAAM;AACtB,UAAM,OAAO,yBAAyB,QAAQ,IAAI;AAClD,QAAI,MAAM;AACR,UAAI,KAAK,KAAK;AACZ,eAAO,QAAQ,KAAK,GAAG;AAAA,MACzB;AACA,UAAI,OAAO,KAAK,UAAU,YAAY;AACpC,eAAO,QAAQ,KAAK,KAAK;AAAA,MAC3B;AAAA,IACF;AACA,aAAS,eAAe,MAAM;AAAA,EAChC;AACA,WAAS,gBAAgB;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,SAAS,OAAO,CAAC,KAAK,QAAQ,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,KAAK,OAAO,OAAO,OAAO,SAAS,cAAc,QAAQ,MAAM,UAAU,UAAU,WAAW,UAAU,QAAQ,QAAQ,OAAO,YAAY,WAAW,QAAQ,YAAY,MAAM,aAAa,OAAO,WAAW,OAAO,UAAU,OAAO,OAAO,MAAM,MAAM,WAAW,MAAM,YAAY,cAAc,UAAU,QAAQ,UAAU,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAAQ,UAAU,UAAU,MAAM,QAAQ,KAAK,OAAO,SAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,OAAO,QAAQ,WAAW,QAAQ,YAAY,SAAS,OAAO,QAAQ,MAAM,YAAY,UAAU,UAAU,KAAK,WAAW,OAAO,YAAY,KAAK,MAAM,MAAM,QAAQ,KAAK,QAAQ,WAAW,UAAU,UAAU,SAAS,UAAU,UAAU,QAAQ,UAAU,UAAU,SAAS,OAAO,WAAW,OAAO,SAAS,SAAS,MAAM,YAAY,YAAY,SAAS,MAAM,SAAS,QAAQ,MAAM,SAAS,MAAM,KAAK,MAAM,OAAO,SAAS,KAAK,CAAC;AAC/+B,IAAM,QAAQ,OAAO,CAAC,OAAO,KAAK,YAAY,eAAe,gBAAgB,gBAAgB,iBAAiB,oBAAoB,UAAU,YAAY,QAAQ,QAAQ,WAAW,UAAU,QAAQ,KAAK,SAAS,YAAY,SAAS,SAAS,QAAQ,kBAAkB,UAAU,QAAQ,YAAY,SAAS,QAAQ,WAAW,WAAW,YAAY,kBAAkB,QAAQ,QAAQ,SAAS,UAAU,UAAU,QAAQ,YAAY,SAAS,QAAQ,SAAS,QAAQ,OAAO,CAAC;AACzd,IAAM,aAAa,OAAO,CAAC,WAAW,iBAAiB,uBAAuB,eAAe,oBAAoB,qBAAqB,qBAAqB,kBAAkB,gBAAgB,WAAW,WAAW,WAAW,WAAW,WAAW,kBAAkB,WAAW,WAAW,eAAe,gBAAgB,YAAY,gBAAgB,sBAAsB,eAAe,UAAU,cAAc,CAAC;AAKrZ,IAAM,gBAAgB,OAAO,CAAC,WAAW,iBAAiB,UAAU,WAAW,aAAa,oBAAoB,kBAAkB,iBAAiB,iBAAiB,iBAAiB,SAAS,aAAa,QAAQ,gBAAgB,aAAa,WAAW,iBAAiB,UAAU,OAAO,cAAc,WAAW,KAAK,CAAC;AAC5T,IAAM,WAAW,OAAO,CAAC,QAAQ,YAAY,UAAU,WAAW,SAAS,UAAU,MAAM,cAAc,iBAAiB,MAAM,MAAM,SAAS,WAAW,YAAY,SAAS,QAAQ,MAAM,UAAU,SAAS,UAAU,QAAQ,QAAQ,WAAW,UAAU,OAAO,SAAS,OAAO,UAAU,cAAc,aAAa,CAAC;AAG5T,IAAM,mBAAmB,OAAO,CAAC,WAAW,eAAe,cAAc,YAAY,aAAa,WAAW,WAAW,UAAU,UAAU,SAAS,aAAa,cAAc,kBAAkB,eAAe,MAAM,CAAC;AACxN,IAAM,OAAO,OAAO,CAAC,OAAO,CAAC;AAC7B,IAAM,OAAO,OAAO,CAAC,UAAU,UAAU,SAAS,OAAO,kBAAkB,gBAAgB,wBAAwB,YAAY,cAAc,WAAW,UAAU,WAAW,eAAe,eAAe,WAAW,QAAQ,SAAS,SAAS,SAAS,QAAQ,WAAW,YAAY,gBAAgB,UAAU,eAAe,YAAY,YAAY,WAAW,OAAO,YAAY,2BAA2B,yBAAyB,YAAY,aAAa,WAAW,gBAAgB,QAAQ,OAAO,WAAW,UAAU,UAAU,QAAQ,QAAQ,YAAY,MAAM,aAAa,aAAa,SAAS,QAAQ,SAAS,QAAQ,QAAQ,WAAW,QAAQ,OAAO,OAAO,aAAa,SAAS,UAAU,OAAO,aAAa,YAAY,SAAS,QAAQ,SAAS,WAAW,cAAc,UAAU,QAAQ,WAAW,WAAW,eAAe,eAAe,WAAW,iBAAiB,uBAAuB,UAAU,WAAW,WAAW,cAAc,YAAY,OAAO,YAAY,OAAO,YAAY,QAAQ,QAAQ,WAAW,cAAc,SAAS,YAAY,SAAS,QAAQ,SAAS,QAAQ,WAAW,SAAS,OAAO,UAAU,QAAQ,SAAS,WAAW,YAAY,SAAS,aAAa,QAAQ,UAAU,UAAU,SAAS,SAAS,QAAQ,SAAS,MAAM,CAAC;AACzuC,IAAM,MAAM,OAAO,CAAC,iBAAiB,cAAc,YAAY,sBAAsB,aAAa,UAAU,iBAAiB,iBAAiB,WAAW,iBAAiB,kBAAkB,SAAS,QAAQ,MAAM,SAAS,QAAQ,iBAAiB,aAAa,aAAa,SAAS,uBAAuB,+BAA+B,iBAAiB,mBAAmB,MAAM,MAAM,KAAK,MAAM,MAAM,mBAAmB,aAAa,WAAW,WAAW,OAAO,YAAY,aAAa,OAAO,YAAY,QAAQ,gBAAgB,aAAa,UAAU,eAAe,eAAe,iBAAiB,eAAe,aAAa,oBAAoB,gBAAgB,cAAc,gBAAgB,eAAe,MAAM,MAAM,MAAM,MAAM,cAAc,YAAY,iBAAiB,qBAAqB,UAAU,QAAQ,MAAM,mBAAmB,MAAM,OAAO,aAAa,KAAK,MAAM,MAAM,MAAM,MAAM,WAAW,aAAa,cAAc,YAAY,QAAQ,gBAAgB,kBAAkB,gBAAgB,oBAAoB,kBAAkB,SAAS,cAAc,cAAc,gBAAgB,gBAAgB,eAAe,eAAe,oBAAoB,aAAa,OAAO,QAAQ,SAAS,UAAU,QAAQ,OAAO,QAAQ,cAAc,UAAU,YAAY,WAAW,SAAS,UAAU,eAAe,UAAU,YAAY,eAAe,QAAQ,cAAc,uBAAuB,oBAAoB,gBAAgB,UAAU,iBAAiB,uBAAuB,kBAAkB,KAAK,MAAM,MAAM,UAAU,QAAQ,QAAQ,eAAe,aAAa,WAAW,UAAU,UAAU,SAAS,QAAQ,mBAAmB,SAAS,oBAAoB,oBAAoB,gBAAgB,eAAe,gBAAgB,eAAe,cAAc,gBAAgB,oBAAoB,qBAAqB,kBAAkB,mBAAmB,qBAAqB,kBAAkB,UAAU,gBAAgB,SAAS,gBAAgB,kBAAkB,YAAY,eAAe,WAAW,WAAW,aAAa,oBAAoB,eAAe,mBAAmB,kBAAkB,cAAc,QAAQ,MAAM,MAAM,WAAW,UAAU,WAAW,cAAc,WAAW,cAAc,iBAAiB,iBAAiB,SAAS,gBAAgB,QAAQ,gBAAgB,oBAAoB,oBAAoB,KAAK,MAAM,MAAM,SAAS,KAAK,MAAM,MAAM,KAAK,YAAY,CAAC;AAC/0E,IAAM,SAAS,OAAO,CAAC,UAAU,eAAe,SAAS,YAAY,SAAS,gBAAgB,eAAe,cAAc,cAAc,SAAS,OAAO,WAAW,gBAAgB,YAAY,SAAS,SAAS,UAAU,QAAQ,MAAM,WAAW,UAAU,iBAAiB,UAAU,UAAU,kBAAkB,aAAa,YAAY,eAAe,WAAW,WAAW,iBAAiB,YAAY,YAAY,QAAQ,YAAY,YAAY,cAAc,WAAW,UAAU,UAAU,eAAe,iBAAiB,wBAAwB,aAAa,aAAa,cAAc,YAAY,kBAAkB,kBAAkB,aAAa,WAAW,SAAS,OAAO,CAAC;AACnqB,IAAM,MAAM,OAAO,CAAC,cAAc,UAAU,eAAe,aAAa,aAAa,CAAC;AAGtF,IAAM,gBAAgB,KAAK,2BAA2B;AACtD,IAAM,WAAW,KAAK,uBAAuB;AAC7C,IAAM,cAAc,KAAK,eAAe;AACxC,IAAM,YAAY,KAAK,8BAA8B;AACrD,IAAM,YAAY,KAAK,gBAAgB;AACvC,IAAM,iBAAiB;AAAA,EAAK;AAAA;AAC5B;AACA,IAAM,oBAAoB,KAAK,uBAAuB;AACtD,IAAM,kBAAkB;AAAA,EAAK;AAAA;AAC7B;AACA,IAAM,eAAe,KAAK,SAAS;AACnC,IAAM,iBAAiB,KAAK,0BAA0B;AACtD,IAAI,cAA2B,OAAO,OAAO;AAAA,EAC3C,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAID,IAAM,YAAY;AAAA,EAChB,SAAS;AAAA,EACT,WAAW;AAAA,EACX,MAAM;AAAA,EACN,cAAc;AAAA,EACd,iBAAiB;AAAA;AAAA,EAEjB,YAAY;AAAA;AAAA,EAEZ,wBAAwB;AAAA,EACxB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,UAAU;AAAA;AACZ;AACA,IAAM,YAAY,SAASC,aAAY;AACrC,SAAO,OAAO,WAAW,cAAc,OAAO;AAChD;AASA,IAAM,4BAA4B,SAASC,2BAA0B,cAAc,mBAAmB;AACpG,MAAI,OAAO,iBAAiB,YAAY,OAAO,aAAa,iBAAiB,YAAY;AACvF,WAAO;AAAA,EACT;AAIA,MAAI,SAAS;AACb,QAAM,YAAY;AAClB,MAAI,qBAAqB,kBAAkB,aAAa,SAAS,GAAG;AAClE,aAAS,kBAAkB,aAAa,SAAS;AAAA,EACnD;AACA,QAAM,aAAa,eAAe,SAAS,MAAM,SAAS;AAC1D,MAAI;AACF,WAAO,aAAa,aAAa,YAAY;AAAA,MAC3C,WAAWC,OAAM;AACf,eAAOA;AAAA,MACT;AAAA,MACA,gBAAgB,WAAW;AACzB,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,SAAS,GAAG;AAIV,YAAQ,KAAK,yBAAyB,aAAa,wBAAwB;AAC3E,WAAO;AAAA,EACT;AACF;AACA,IAAM,kBAAkB,SAASC,mBAAkB;AACjD,SAAO;AAAA,IACL,yBAAyB,CAAC;AAAA,IAC1B,uBAAuB,CAAC;AAAA,IACxB,wBAAwB,CAAC;AAAA,IACzB,0BAA0B,CAAC;AAAA,IAC3B,wBAAwB,CAAC;AAAA,IACzB,yBAAyB,CAAC;AAAA,IAC1B,uBAAuB,CAAC;AAAA,IACxB,qBAAqB,CAAC;AAAA,IACtB,wBAAwB,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,kBAAkB;AACzB,MAAIC,UAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,UAAU;AAC3F,QAAM,YAAY,UAAQ,gBAAgB,IAAI;AAC9C,YAAU,UAAU;AACpB,YAAU,UAAU,CAAC;AACrB,MAAI,CAACA,WAAU,CAACA,QAAO,YAAYA,QAAO,SAAS,aAAa,UAAU,YAAY,CAACA,QAAO,SAAS;AAGrG,cAAU,cAAc;AACxB,WAAO;AAAA,EACT;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAIA;AACJ,QAAM,mBAAmB;AACzB,QAAM,gBAAgB,iBAAiB;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAeA,QAAO,gBAAgBA,QAAO;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIA;AACJ,QAAM,mBAAmB,QAAQ;AACjC,QAAM,YAAY,aAAa,kBAAkB,WAAW;AAC5D,QAAM,SAAS,aAAa,kBAAkB,QAAQ;AACtD,QAAM,iBAAiB,aAAa,kBAAkB,aAAa;AACnE,QAAM,gBAAgB,aAAa,kBAAkB,YAAY;AACjE,QAAM,gBAAgB,aAAa,kBAAkB,YAAY;AAOjE,MAAI,OAAO,wBAAwB,YAAY;AAC7C,UAAM,WAAW,SAAS,cAAc,UAAU;AAClD,QAAI,SAAS,WAAW,SAAS,QAAQ,eAAe;AACtD,iBAAW,SAAS,QAAQ;AAAA,IAC9B;AAAA,EACF;AACA,MAAI;AACJ,MAAI,YAAY;AAChB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,QAAQ,gBAAgB;AAI5B,YAAU,cAAc,OAAO,YAAY,cAAc,OAAO,kBAAkB,cAAc,kBAAkB,eAAe,uBAAuB;AACxJ,QAAM;AAAA,IACJ,eAAAC;AAAA,IACA,UAAAC;AAAA,IACA,aAAAC;AAAA,IACA,WAAAC;AAAA,IACA,WAAAC;AAAA,IACA,mBAAAC;AAAA,IACA,iBAAAC;AAAA,IACA,gBAAAC;AAAA,EACF,IAAI;AACJ,MAAI;AAAA,IACF,gBAAgB;AAAA,EAClB,IAAI;AAMJ,MAAI,eAAe;AACnB,QAAM,uBAAuB,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,OAAO,GAAG,YAAY,GAAG,UAAU,GAAG,IAAI,CAAC;AAEpG,MAAI,eAAe;AACnB,QAAM,uBAAuB,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAG,GAAG,CAAC;AAO9E,MAAI,0BAA0B,OAAO,KAAK,OAAO,MAAM;AAAA,IACrD,cAAc;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,UAAU;AAAA,MACV,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,gCAAgC;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AAEF,MAAI,cAAc;AAElB,MAAI,cAAc;AAElB,MAAI,kBAAkB;AAEtB,MAAI,kBAAkB;AAEtB,MAAI,0BAA0B;AAG9B,MAAI,2BAA2B;AAI/B,MAAI,qBAAqB;AAIzB,MAAI,eAAe;AAEnB,MAAI,iBAAiB;AAErB,MAAI,aAAa;AAGjB,MAAI,aAAa;AAKjB,MAAI,aAAa;AAGjB,MAAI,sBAAsB;AAG1B,MAAI,sBAAsB;AAI1B,MAAI,eAAe;AAcnB,MAAI,uBAAuB;AAC3B,QAAM,8BAA8B;AAEpC,MAAI,eAAe;AAGnB,MAAI,WAAW;AAEf,MAAI,eAAe,CAAC;AAEpB,MAAI,kBAAkB;AACtB,QAAM,0BAA0B,SAAS,CAAC,GAAG,CAAC,kBAAkB,SAAS,YAAY,QAAQ,iBAAiB,QAAQ,UAAU,QAAQ,MAAM,MAAM,MAAM,MAAM,SAAS,WAAW,YAAY,YAAY,aAAa,UAAU,SAAS,OAAO,YAAY,SAAS,SAAS,SAAS,KAAK,CAAC;AAEhS,MAAI,gBAAgB;AACpB,QAAM,wBAAwB,SAAS,CAAC,GAAG,CAAC,SAAS,SAAS,OAAO,UAAU,SAAS,OAAO,CAAC;AAEhG,MAAI,sBAAsB;AAC1B,QAAM,8BAA8B,SAAS,CAAC,GAAG,CAAC,OAAO,SAAS,OAAO,MAAM,SAAS,QAAQ,WAAW,eAAe,QAAQ,WAAW,SAAS,SAAS,SAAS,OAAO,CAAC;AAChL,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,iBAAiB;AAEvB,MAAI,YAAY;AAChB,MAAI,iBAAiB;AAErB,MAAI,qBAAqB;AACzB,QAAM,6BAA6B,SAAS,CAAC,GAAG,CAAC,kBAAkB,eAAe,cAAc,GAAG,cAAc;AACjH,MAAI,iCAAiC,SAAS,CAAC,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,OAAO,CAAC;AACnF,MAAI,0BAA0B,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAK7D,QAAM,+BAA+B,SAAS,CAAC,GAAG,CAAC,SAAS,SAAS,QAAQ,KAAK,QAAQ,CAAC;AAE3F,MAAI,oBAAoB;AACxB,QAAM,+BAA+B,CAAC,yBAAyB,WAAW;AAC1E,QAAM,4BAA4B;AAClC,MAAI,oBAAoB;AAExB,MAAI,SAAS;AAGb,QAAM,cAAc,SAAS,cAAc,MAAM;AACjD,QAAM,oBAAoB,SAASC,mBAAkB,WAAW;AAC9D,WAAO,qBAAqB,UAAU,qBAAqB;AAAA,EAC7D;AAOA,QAAM,eAAe,SAASC,gBAAe;AAC3C,QAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,QAAI,UAAU,WAAW,KAAK;AAC5B;AAAA,IACF;AAEA,QAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,YAAM,CAAC;AAAA,IACT;AAEA,UAAM,MAAM,GAAG;AACf;AAAA,IAEA,6BAA6B,QAAQ,IAAI,iBAAiB,MAAM,KAAK,4BAA4B,IAAI;AAErG,wBAAoB,sBAAsB,0BAA0B,iBAAiB;AAErF,mBAAe,qBAAqB,KAAK,cAAc,IAAI,SAAS,CAAC,GAAG,IAAI,cAAc,iBAAiB,IAAI;AAC/G,mBAAe,qBAAqB,KAAK,cAAc,IAAI,SAAS,CAAC,GAAG,IAAI,cAAc,iBAAiB,IAAI;AAC/G,yBAAqB,qBAAqB,KAAK,oBAAoB,IAAI,SAAS,CAAC,GAAG,IAAI,oBAAoB,cAAc,IAAI;AAC9H,0BAAsB,qBAAqB,KAAK,mBAAmB,IAAI,SAAS,MAAM,2BAA2B,GAAG,IAAI,mBAAmB,iBAAiB,IAAI;AAChK,oBAAgB,qBAAqB,KAAK,mBAAmB,IAAI,SAAS,MAAM,qBAAqB,GAAG,IAAI,mBAAmB,iBAAiB,IAAI;AACpJ,sBAAkB,qBAAqB,KAAK,iBAAiB,IAAI,SAAS,CAAC,GAAG,IAAI,iBAAiB,iBAAiB,IAAI;AACxH,kBAAc,qBAAqB,KAAK,aAAa,IAAI,SAAS,CAAC,GAAG,IAAI,aAAa,iBAAiB,IAAI,CAAC;AAC7G,kBAAc,qBAAqB,KAAK,aAAa,IAAI,SAAS,CAAC,GAAG,IAAI,aAAa,iBAAiB,IAAI,CAAC;AAC7G,mBAAe,qBAAqB,KAAK,cAAc,IAAI,IAAI,eAAe;AAC9E,sBAAkB,IAAI,oBAAoB;AAC1C,sBAAkB,IAAI,oBAAoB;AAC1C,8BAA0B,IAAI,2BAA2B;AACzD,+BAA2B,IAAI,6BAA6B;AAC5D,yBAAqB,IAAI,sBAAsB;AAC/C,mBAAe,IAAI,iBAAiB;AACpC,qBAAiB,IAAI,kBAAkB;AACvC,iBAAa,IAAI,cAAc;AAC/B,0BAAsB,IAAI,uBAAuB;AACjD,0BAAsB,IAAI,uBAAuB;AACjD,iBAAa,IAAI,cAAc;AAC/B,mBAAe,IAAI,iBAAiB;AACpC,2BAAuB,IAAI,wBAAwB;AACnD,mBAAe,IAAI,iBAAiB;AACpC,eAAW,IAAI,YAAY;AAC3B,uBAAmB,IAAI,sBAAsB;AAC7C,gBAAY,IAAI,aAAa;AAC7B,qCAAiC,IAAI,kCAAkC;AACvE,8BAA0B,IAAI,2BAA2B;AACzD,8BAA0B,IAAI,2BAA2B,CAAC;AAC1D,QAAI,IAAI,2BAA2B,kBAAkB,IAAI,wBAAwB,YAAY,GAAG;AAC9F,8BAAwB,eAAe,IAAI,wBAAwB;AAAA,IACrE;AACA,QAAI,IAAI,2BAA2B,kBAAkB,IAAI,wBAAwB,kBAAkB,GAAG;AACpG,8BAAwB,qBAAqB,IAAI,wBAAwB;AAAA,IAC3E;AACA,QAAI,IAAI,2BAA2B,OAAO,IAAI,wBAAwB,mCAAmC,WAAW;AAClH,8BAAwB,iCAAiC,IAAI,wBAAwB;AAAA,IACvF;AACA,QAAI,oBAAoB;AACtB,wBAAkB;AAAA,IACpB;AACA,QAAI,qBAAqB;AACvB,mBAAa;AAAA,IACf;AAEA,QAAI,cAAc;AAChB,qBAAe,SAAS,CAAC,GAAG,IAAI;AAChC,qBAAe,CAAC;AAChB,UAAI,aAAa,SAAS,MAAM;AAC9B,iBAAS,cAAc,MAAM;AAC7B,iBAAS,cAAc,IAAI;AAAA,MAC7B;AACA,UAAI,aAAa,QAAQ,MAAM;AAC7B,iBAAS,cAAc,KAAK;AAC5B,iBAAS,cAAc,GAAG;AAC1B,iBAAS,cAAc,GAAG;AAAA,MAC5B;AACA,UAAI,aAAa,eAAe,MAAM;AACpC,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,GAAG;AAC1B,iBAAS,cAAc,GAAG;AAAA,MAC5B;AACA,UAAI,aAAa,WAAW,MAAM;AAChC,iBAAS,cAAc,QAAQ;AAC/B,iBAAS,cAAc,MAAM;AAC7B,iBAAS,cAAc,GAAG;AAAA,MAC5B;AAAA,IACF;AAEA,QAAI,IAAI,UAAU;AAChB,UAAI,iBAAiB,sBAAsB;AACzC,uBAAe,MAAM,YAAY;AAAA,MACnC;AACA,eAAS,cAAc,IAAI,UAAU,iBAAiB;AAAA,IACxD;AACA,QAAI,IAAI,UAAU;AAChB,UAAI,iBAAiB,sBAAsB;AACzC,uBAAe,MAAM,YAAY;AAAA,MACnC;AACA,eAAS,cAAc,IAAI,UAAU,iBAAiB;AAAA,IACxD;AACA,QAAI,IAAI,mBAAmB;AACzB,eAAS,qBAAqB,IAAI,mBAAmB,iBAAiB;AAAA,IACxE;AACA,QAAI,IAAI,iBAAiB;AACvB,UAAI,oBAAoB,yBAAyB;AAC/C,0BAAkB,MAAM,eAAe;AAAA,MACzC;AACA,eAAS,iBAAiB,IAAI,iBAAiB,iBAAiB;AAAA,IAClE;AAEA,QAAI,cAAc;AAChB,mBAAa,OAAO,IAAI;AAAA,IAC1B;AAEA,QAAI,gBAAgB;AAClB,eAAS,cAAc,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,IACjD;AAEA,QAAI,aAAa,OAAO;AACtB,eAAS,cAAc,CAAC,OAAO,CAAC;AAChC,aAAO,YAAY;AAAA,IACrB;AACA,QAAI,IAAI,sBAAsB;AAC5B,UAAI,OAAO,IAAI,qBAAqB,eAAe,YAAY;AAC7D,cAAM,gBAAgB,6EAA6E;AAAA,MACrG;AACA,UAAI,OAAO,IAAI,qBAAqB,oBAAoB,YAAY;AAClE,cAAM,gBAAgB,kFAAkF;AAAA,MAC1G;AAEA,2BAAqB,IAAI;AAEzB,kBAAY,mBAAmB,WAAW,EAAE;AAAA,IAC9C,OAAO;AAEL,UAAI,uBAAuB,QAAW;AACpC,6BAAqB,0BAA0B,cAAc,aAAa;AAAA,MAC5E;AAEA,UAAI,uBAAuB,QAAQ,OAAO,cAAc,UAAU;AAChE,oBAAY,mBAAmB,WAAW,EAAE;AAAA,MAC9C;AAAA,IACF;AAGA,QAAI,QAAQ;AACV,aAAO,GAAG;AAAA,IACZ;AACA,aAAS;AAAA,EACX;AAIA,QAAM,eAAe,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,YAAY,GAAG,aAAa,CAAC;AAC7E,QAAM,kBAAkB,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,gBAAgB,CAAC;AAOvE,QAAM,uBAAuB,SAASC,sBAAqB,SAAS;AAClE,QAAI,SAAS,cAAc,OAAO;AAGlC,QAAI,CAAC,UAAU,CAAC,OAAO,SAAS;AAC9B,eAAS;AAAA,QACP,cAAc;AAAA,QACd,SAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM,UAAU,kBAAkB,QAAQ,OAAO;AACjD,UAAM,gBAAgB,kBAAkB,OAAO,OAAO;AACtD,QAAI,CAAC,mBAAmB,QAAQ,YAAY,GAAG;AAC7C,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,iBAAiB,eAAe;AAI1C,UAAI,OAAO,iBAAiB,gBAAgB;AAC1C,eAAO,YAAY;AAAA,MACrB;AAIA,UAAI,OAAO,iBAAiB,kBAAkB;AAC5C,eAAO,YAAY,UAAU,kBAAkB,oBAAoB,+BAA+B,aAAa;AAAA,MACjH;AAGA,aAAO,QAAQ,aAAa,OAAO,CAAC;AAAA,IACtC;AACA,QAAI,QAAQ,iBAAiB,kBAAkB;AAI7C,UAAI,OAAO,iBAAiB,gBAAgB;AAC1C,eAAO,YAAY;AAAA,MACrB;AAGA,UAAI,OAAO,iBAAiB,eAAe;AACzC,eAAO,YAAY,UAAU,wBAAwB,aAAa;AAAA,MACpE;AAGA,aAAO,QAAQ,gBAAgB,OAAO,CAAC;AAAA,IACzC;AACA,QAAI,QAAQ,iBAAiB,gBAAgB;AAI3C,UAAI,OAAO,iBAAiB,iBAAiB,CAAC,wBAAwB,aAAa,GAAG;AACpF,eAAO;AAAA,MACT;AACA,UAAI,OAAO,iBAAiB,oBAAoB,CAAC,+BAA+B,aAAa,GAAG;AAC9F,eAAO;AAAA,MACT;AAGA,aAAO,CAAC,gBAAgB,OAAO,MAAM,6BAA6B,OAAO,KAAK,CAAC,aAAa,OAAO;AAAA,IACrG;AAEA,QAAI,sBAAsB,2BAA2B,mBAAmB,QAAQ,YAAY,GAAG;AAC7F,aAAO;AAAA,IACT;AAKA,WAAO;AAAA,EACT;AAMA,QAAM,eAAe,SAASC,cAAa,MAAM;AAC/C,cAAU,UAAU,SAAS;AAAA,MAC3B,SAAS;AAAA,IACX,CAAC;AACD,QAAI;AAEF,oBAAc,IAAI,EAAE,YAAY,IAAI;AAAA,IACtC,SAAS,GAAG;AACV,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AAOA,QAAM,mBAAmB,SAASC,kBAAiB,MAAM,SAAS;AAChE,QAAI;AACF,gBAAU,UAAU,SAAS;AAAA,QAC3B,WAAW,QAAQ,iBAAiB,IAAI;AAAA,QACxC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,SAAS,GAAG;AACV,gBAAU,UAAU,SAAS;AAAA,QAC3B,WAAW;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,YAAQ,gBAAgB,IAAI;AAE5B,QAAI,SAAS,MAAM;AACjB,UAAI,cAAc,qBAAqB;AACrC,YAAI;AACF,uBAAa,OAAO;AAAA,QACtB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf,OAAO;AACL,YAAI;AACF,kBAAQ,aAAa,MAAM,EAAE;AAAA,QAC/B,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAOA,QAAM,gBAAgB,SAASC,eAAc,OAAO;AAElD,QAAI,MAAM;AACV,QAAI,oBAAoB;AACxB,QAAI,YAAY;AACd,cAAQ,sBAAsB;AAAA,IAChC,OAAO;AAEL,YAAM,UAAU,YAAY,OAAO,aAAa;AAChD,0BAAoB,WAAW,QAAQ,CAAC;AAAA,IAC1C;AACA,QAAI,sBAAsB,2BAA2B,cAAc,gBAAgB;AAEjF,cAAQ,mEAAmE,QAAQ;AAAA,IACrF;AACA,UAAM,eAAe,qBAAqB,mBAAmB,WAAW,KAAK,IAAI;AAKjF,QAAI,cAAc,gBAAgB;AAChC,UAAI;AACF,cAAM,IAAI,UAAU,EAAE,gBAAgB,cAAc,iBAAiB;AAAA,MACvE,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAEA,QAAI,CAAC,OAAO,CAAC,IAAI,iBAAiB;AAChC,YAAM,eAAe,eAAe,WAAW,YAAY,IAAI;AAC/D,UAAI;AACF,YAAI,gBAAgB,YAAY,iBAAiB,YAAY;AAAA,MAC/D,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AACA,UAAM,OAAO,IAAI,QAAQ,IAAI;AAC7B,QAAI,SAAS,mBAAmB;AAC9B,WAAK,aAAa,SAAS,eAAe,iBAAiB,GAAG,KAAK,WAAW,CAAC,KAAK,IAAI;AAAA,IAC1F;AAEA,QAAI,cAAc,gBAAgB;AAChC,aAAO,qBAAqB,KAAK,KAAK,iBAAiB,SAAS,MAAM,EAAE,CAAC;AAAA,IAC3E;AACA,WAAO,iBAAiB,IAAI,kBAAkB;AAAA,EAChD;AAOA,QAAM,sBAAsB,SAASC,qBAAoB,MAAM;AAC7D,WAAO,mBAAmB;AAAA,MAAK,KAAK,iBAAiB;AAAA,MAAM;AAAA;AAAA,MAE3D,WAAW,eAAe,WAAW,eAAe,WAAW,YAAY,WAAW,8BAA8B,WAAW;AAAA,MAAoB;AAAA,IAAI;AAAA,EACzJ;AAOA,QAAM,eAAe,SAASC,cAAa,SAAS;AAClD,WAAO,mBAAmB,oBAAoB,OAAO,QAAQ,aAAa,YAAY,OAAO,QAAQ,gBAAgB,YAAY,OAAO,QAAQ,gBAAgB,cAAc,EAAE,QAAQ,sBAAsB,iBAAiB,OAAO,QAAQ,oBAAoB,cAAc,OAAO,QAAQ,iBAAiB,cAAc,OAAO,QAAQ,iBAAiB,YAAY,OAAO,QAAQ,iBAAiB,cAAc,OAAO,QAAQ,kBAAkB;AAAA,EAC3b;AAOA,QAAM,UAAU,SAASC,SAAQ,OAAO;AACtC,WAAO,OAAO,SAAS,cAAc,iBAAiB;AAAA,EACxD;AACA,WAAS,cAAcC,QAAO,aAAa,MAAM;AAC/C,iBAAaA,QAAO,UAAQ;AAC1B,WAAK,KAAK,WAAW,aAAa,MAAM,MAAM;AAAA,IAChD,CAAC;AAAA,EACH;AAUA,QAAM,oBAAoB,SAASC,mBAAkB,aAAa;AAChE,QAAI,UAAU;AAEd,kBAAc,MAAM,wBAAwB,aAAa,IAAI;AAE7D,QAAI,aAAa,WAAW,GAAG;AAC7B,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,kBAAkB,YAAY,QAAQ;AAEtD,kBAAc,MAAM,qBAAqB,aAAa;AAAA,MACpD;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAED,QAAI,YAAY,cAAc,KAAK,CAAC,QAAQ,YAAY,iBAAiB,KAAK,WAAW,WAAW,YAAY,SAAS,KAAK,WAAW,WAAW,YAAY,WAAW,GAAG;AAC5K,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,YAAY,aAAa,UAAU,wBAAwB;AAC7D,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,gBAAgB,YAAY,aAAa,UAAU,WAAW,WAAW,WAAW,YAAY,IAAI,GAAG;AACzG,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,aAAa,OAAO,KAAK,YAAY,OAAO,GAAG;AAElD,UAAI,CAAC,YAAY,OAAO,KAAK,sBAAsB,OAAO,GAAG;AAC3D,YAAI,wBAAwB,wBAAwB,UAAU,WAAW,wBAAwB,cAAc,OAAO,GAAG;AACvH,iBAAO;AAAA,QACT;AACA,YAAI,wBAAwB,wBAAwB,YAAY,wBAAwB,aAAa,OAAO,GAAG;AAC7G,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,gBAAgB,CAAC,gBAAgB,OAAO,GAAG;AAC7C,cAAM,aAAa,cAAc,WAAW,KAAK,YAAY;AAC7D,cAAM,aAAa,cAAc,WAAW,KAAK,YAAY;AAC7D,YAAI,cAAc,YAAY;AAC5B,gBAAM,aAAa,WAAW;AAC9B,mBAAS,IAAI,aAAa,GAAG,KAAK,GAAG,EAAE,GAAG;AACxC,kBAAM,aAAa,UAAU,WAAW,CAAC,GAAG,IAAI;AAChD,uBAAW,kBAAkB,YAAY,kBAAkB,KAAK;AAChE,uBAAW,aAAa,YAAY,eAAe,WAAW,CAAC;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AACA,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,uBAAuB,WAAW,CAAC,qBAAqB,WAAW,GAAG;AACxE,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,SAAK,YAAY,cAAc,YAAY,aAAa,YAAY,eAAe,WAAW,+BAA+B,YAAY,SAAS,GAAG;AACnJ,mBAAa,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,sBAAsB,YAAY,aAAa,UAAU,MAAM;AAEjE,gBAAU,YAAY;AACtB,mBAAa,CAAClB,gBAAeC,WAAUC,YAAW,GAAG,UAAQ;AAC3D,kBAAU,cAAc,SAAS,MAAM,GAAG;AAAA,MAC5C,CAAC;AACD,UAAI,YAAY,gBAAgB,SAAS;AACvC,kBAAU,UAAU,SAAS;AAAA,UAC3B,SAAS,YAAY,UAAU;AAAA,QACjC,CAAC;AACD,oBAAY,cAAc;AAAA,MAC5B;AAAA,IACF;AAEA,kBAAc,MAAM,uBAAuB,aAAa,IAAI;AAC5D,WAAO;AAAA,EACT;AAUA,QAAM,oBAAoB,SAASiB,mBAAkB,OAAO,QAAQ,OAAO;AAEzE,QAAI,iBAAiB,WAAW,QAAQ,WAAW,YAAY,SAAS,YAAY,SAAS,cAAc;AACzG,aAAO;AAAA,IACT;AAKA,QAAI,mBAAmB,CAAC,YAAY,MAAM,KAAK,WAAWhB,YAAW,MAAM,EAAG;AAAA,aAAU,mBAAmB,WAAWC,YAAW,MAAM,EAAG;AAAA,aAAU,CAAC,aAAa,MAAM,KAAK,YAAY,MAAM,GAAG;AAChM;AAAA;AAAA;AAAA;AAAA,QAIA,sBAAsB,KAAK,MAAM,wBAAwB,wBAAwB,UAAU,WAAW,wBAAwB,cAAc,KAAK,KAAK,wBAAwB,wBAAwB,YAAY,wBAAwB,aAAa,KAAK,OAAO,wBAAwB,8BAA8B,UAAU,WAAW,wBAAwB,oBAAoB,MAAM,KAAK,wBAAwB,8BAA8B,YAAY,wBAAwB,mBAAmB,MAAM;AAAA;AAAA,QAGxf,WAAW,QAAQ,wBAAwB,mCAAmC,wBAAwB,wBAAwB,UAAU,WAAW,wBAAwB,cAAc,KAAK,KAAK,wBAAwB,wBAAwB,YAAY,wBAAwB,aAAa,KAAK;AAAA,OAAI;AAAA,WAAM;AACjT,eAAO;AAAA,MACT;AAAA,IAEF,WAAW,oBAAoB,MAAM,EAAG;AAAA,aAAU,WAAW,kBAAkB,cAAc,OAAOE,kBAAiB,EAAE,CAAC,EAAG;AAAA,cAAW,WAAW,SAAS,WAAW,gBAAgB,WAAW,WAAW,UAAU,YAAY,cAAc,OAAO,OAAO,MAAM,KAAK,cAAc,KAAK,EAAG;AAAA,aAAU,2BAA2B,CAAC,WAAWD,oBAAmB,cAAc,OAAOC,kBAAiB,EAAE,CAAC,EAAG;AAAA,aAAU,OAAO;AAC7Z,aAAO;AAAA,IACT,MAAO;AACP,WAAO;AAAA,EACT;AASA,QAAM,wBAAwB,SAASc,uBAAsB,SAAS;AACpE,WAAO,YAAY,oBAAoB,YAAY,SAASb,eAAc;AAAA,EAC5E;AAWA,QAAM,sBAAsB,SAASc,qBAAoB,aAAa;AAEpE,kBAAc,MAAM,0BAA0B,aAAa,IAAI;AAC/D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,QAAI,CAAC,cAAc,aAAa,WAAW,GAAG;AAC5C;AAAA,IACF;AACA,UAAM,YAAY;AAAA,MAChB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,eAAe;AAAA,IACjB;AACA,QAAI,IAAI,WAAW;AAEnB,WAAO,KAAK;AACV,YAAM,OAAO,WAAW,CAAC;AACzB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,OAAO;AAAA,MACT,IAAI;AACJ,YAAM,SAAS,kBAAkB,IAAI;AACrC,UAAI,QAAQ,SAAS,UAAU,YAAY,WAAW,SAAS;AAE/D,gBAAU,WAAW;AACrB,gBAAU,YAAY;AACtB,gBAAU,WAAW;AACrB,gBAAU,gBAAgB;AAC1B,oBAAc,MAAM,uBAAuB,aAAa,SAAS;AACjE,cAAQ,UAAU;AAIlB,UAAI,yBAAyB,WAAW,QAAQ,WAAW,SAAS;AAElE,yBAAiB,MAAM,WAAW;AAElC,gBAAQ,8BAA8B;AAAA,MACxC;AAEA,UAAI,gBAAgB,WAAW,iCAAiC,KAAK,GAAG;AACtE,yBAAiB,MAAM,WAAW;AAClC;AAAA,MACF;AAEA,UAAI,UAAU,eAAe;AAC3B;AAAA,MACF;AAEA,uBAAiB,MAAM,WAAW;AAElC,UAAI,CAAC,UAAU,UAAU;AACvB;AAAA,MACF;AAEA,UAAI,CAAC,4BAA4B,WAAW,QAAQ,KAAK,GAAG;AAC1D,yBAAiB,MAAM,WAAW;AAClC;AAAA,MACF;AAEA,UAAI,oBAAoB;AACtB,qBAAa,CAACrB,gBAAeC,WAAUC,YAAW,GAAG,UAAQ;AAC3D,kBAAQ,cAAc,OAAO,MAAM,GAAG;AAAA,QACxC,CAAC;AAAA,MACH;AAEA,YAAM,QAAQ,kBAAkB,YAAY,QAAQ;AACpD,UAAI,CAAC,kBAAkB,OAAO,QAAQ,KAAK,GAAG;AAC5C;AAAA,MACF;AAEA,UAAI,sBAAsB,OAAO,iBAAiB,YAAY,OAAO,aAAa,qBAAqB,YAAY;AACjH,YAAI,aAAc;AAAA,aAAM;AACtB,kBAAQ,aAAa,iBAAiB,OAAO,MAAM,GAAG;AAAA,YACpD,KAAK,eACH;AACE,sBAAQ,mBAAmB,WAAW,KAAK;AAC3C;AAAA,YACF;AAAA,YACF,KAAK,oBACH;AACE,sBAAQ,mBAAmB,gBAAgB,KAAK;AAChD;AAAA,YACF;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAEA,UAAI;AACF,YAAI,cAAc;AAChB,sBAAY,eAAe,cAAc,MAAM,KAAK;AAAA,QACtD,OAAO;AAEL,sBAAY,aAAa,MAAM,KAAK;AAAA,QACtC;AACA,YAAI,aAAa,WAAW,GAAG;AAC7B,uBAAa,WAAW;AAAA,QAC1B,OAAO;AACL,mBAAS,UAAU,OAAO;AAAA,QAC5B;AAAA,MACF,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAEA,kBAAc,MAAM,yBAAyB,aAAa,IAAI;AAAA,EAChE;AAMA,QAAM,qBAAqB,SAASoB,oBAAmB,UAAU;AAC/D,QAAI,aAAa;AACjB,UAAM,iBAAiB,oBAAoB,QAAQ;AAEnD,kBAAc,MAAM,yBAAyB,UAAU,IAAI;AAC3D,WAAO,aAAa,eAAe,SAAS,GAAG;AAE7C,oBAAc,MAAM,wBAAwB,YAAY,IAAI;AAE5D,wBAAkB,UAAU;AAE5B,0BAAoB,UAAU;AAE9B,UAAI,WAAW,mBAAmB,kBAAkB;AAClD,QAAAA,oBAAmB,WAAW,OAAO;AAAA,MACvC;AAAA,IACF;AAEA,kBAAc,MAAM,wBAAwB,UAAU,IAAI;AAAA,EAC5D;AAEA,YAAU,WAAW,SAAU,OAAO;AACpC,QAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,QAAI,OAAO;AACX,QAAI,eAAe;AACnB,QAAI,cAAc;AAClB,QAAI,aAAa;AAIjB,qBAAiB,CAAC;AAClB,QAAI,gBAAgB;AAClB,cAAQ;AAAA,IACV;AAEA,QAAI,OAAO,UAAU,YAAY,CAAC,QAAQ,KAAK,GAAG;AAChD,UAAI,OAAO,MAAM,aAAa,YAAY;AACxC,gBAAQ,MAAM,SAAS;AACvB,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,gBAAgB,iCAAiC;AAAA,QACzD;AAAA,MACF,OAAO;AACL,cAAM,gBAAgB,4BAA4B;AAAA,MACpD;AAAA,IACF;AAEA,QAAI,CAAC,UAAU,aAAa;AAC1B,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,YAAY;AACf,mBAAa,GAAG;AAAA,IAClB;AAEA,cAAU,UAAU,CAAC;AAErB,QAAI,OAAO,UAAU,UAAU;AAC7B,iBAAW;AAAA,IACb;AACA,QAAI,UAAU;AAEZ,UAAI,MAAM,UAAU;AAClB,cAAM,UAAU,kBAAkB,MAAM,QAAQ;AAChD,YAAI,CAAC,aAAa,OAAO,KAAK,YAAY,OAAO,GAAG;AAClD,gBAAM,gBAAgB,yDAAyD;AAAA,QACjF;AAAA,MACF;AAAA,IACF,WAAW,iBAAiB,MAAM;AAGhC,aAAO,cAAc,SAAS;AAC9B,qBAAe,KAAK,cAAc,WAAW,OAAO,IAAI;AACxD,UAAI,aAAa,aAAa,UAAU,WAAW,aAAa,aAAa,QAAQ;AAEnF,eAAO;AAAA,MACT,WAAW,aAAa,aAAa,QAAQ;AAC3C,eAAO;AAAA,MACT,OAAO;AAEL,aAAK,YAAY,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AAEL,UAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC;AAAA,MAE3C,MAAM,QAAQ,GAAG,MAAM,IAAI;AACzB,eAAO,sBAAsB,sBAAsB,mBAAmB,WAAW,KAAK,IAAI;AAAA,MAC5F;AAEA,aAAO,cAAc,KAAK;AAE1B,UAAI,CAAC,MAAM;AACT,eAAO,aAAa,OAAO,sBAAsB,YAAY;AAAA,MAC/D;AAAA,IACF;AAEA,QAAI,QAAQ,YAAY;AACtB,mBAAa,KAAK,UAAU;AAAA,IAC9B;AAEA,UAAM,eAAe,oBAAoB,WAAW,QAAQ,IAAI;AAEhE,WAAO,cAAc,aAAa,SAAS,GAAG;AAE5C,wBAAkB,WAAW;AAE7B,0BAAoB,WAAW;AAE/B,UAAI,YAAY,mBAAmB,kBAAkB;AACnD,2BAAmB,YAAY,OAAO;AAAA,MACxC;AAAA,IACF;AAEA,QAAI,UAAU;AACZ,aAAO;AAAA,IACT;AAEA,QAAI,YAAY;AACd,UAAI,qBAAqB;AACvB,qBAAa,uBAAuB,KAAK,KAAK,aAAa;AAC3D,eAAO,KAAK,YAAY;AAEtB,qBAAW,YAAY,KAAK,UAAU;AAAA,QACxC;AAAA,MACF,OAAO;AACL,qBAAa;AAAA,MACf;AACA,UAAI,aAAa,cAAc,aAAa,gBAAgB;AAQ1D,qBAAa,WAAW,KAAK,kBAAkB,YAAY,IAAI;AAAA,MACjE;AACA,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,iBAAiB,KAAK,YAAY,KAAK;AAE5D,QAAI,kBAAkB,aAAa,UAAU,KAAK,KAAK,iBAAiB,KAAK,cAAc,WAAW,KAAK,cAAc,QAAQ,QAAQ,WAAW,cAAc,KAAK,cAAc,QAAQ,IAAI,GAAG;AAClM,uBAAiB,eAAe,KAAK,cAAc,QAAQ,OAAO,QAAQ;AAAA,IAC5E;AAEA,QAAI,oBAAoB;AACtB,mBAAa,CAACtB,gBAAeC,WAAUC,YAAW,GAAG,UAAQ;AAC3D,yBAAiB,cAAc,gBAAgB,MAAM,GAAG;AAAA,MAC1D,CAAC;AAAA,IACH;AACA,WAAO,sBAAsB,sBAAsB,mBAAmB,WAAW,cAAc,IAAI;AAAA,EACrG;AACA,YAAU,YAAY,WAAY;AAChC,QAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,iBAAa,GAAG;AAChB,iBAAa;AAAA,EACf;AACA,YAAU,cAAc,WAAY;AAClC,aAAS;AACT,iBAAa;AAAA,EACf;AACA,YAAU,mBAAmB,SAAU,KAAK,MAAM,OAAO;AAEvD,QAAI,CAAC,QAAQ;AACX,mBAAa,CAAC,CAAC;AAAA,IACjB;AACA,UAAM,QAAQ,kBAAkB,GAAG;AACnC,UAAM,SAAS,kBAAkB,IAAI;AACrC,WAAO,kBAAkB,OAAO,QAAQ,KAAK;AAAA,EAC/C;AACA,YAAU,UAAU,SAAU,YAAY,cAAc;AACtD,QAAI,OAAO,iBAAiB,YAAY;AACtC;AAAA,IACF;AACA,cAAU,MAAM,UAAU,GAAG,YAAY;AAAA,EAC3C;AACA,YAAU,aAAa,SAAU,YAAY,cAAc;AACzD,QAAI,iBAAiB,QAAW;AAC9B,YAAM,QAAQ,iBAAiB,MAAM,UAAU,GAAG,YAAY;AAC9D,aAAO,UAAU,KAAK,SAAY,YAAY,MAAM,UAAU,GAAG,OAAO,CAAC,EAAE,CAAC;AAAA,IAC9E;AACA,WAAO,SAAS,MAAM,UAAU,CAAC;AAAA,EACnC;AACA,YAAU,cAAc,SAAU,YAAY;AAC5C,UAAM,UAAU,IAAI,CAAC;AAAA,EACvB;AACA,YAAU,iBAAiB,WAAY;AACrC,YAAQ,gBAAgB;AAAA,EAC1B;AACA,SAAO;AACT;AACA,IAAI,SAAS,gBAAgB;", "names": ["freeze", "seal", "apply", "construct", "getGlobal", "_createTrustedTypesPolicy", "html", "_createHooksMap", "window", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "CUSTOM_ELEMENT", "isRegexOrFunction", "_parseConfig", "_checkValidNamespace", "_forceRemove", "_removeAttribute", "_initDocument", "_createNodeIterator", "_isClobbered", "_isNode", "hooks", "_sanitizeElements", "_isValidAttribute", "_isBasicCustomElement", "_sanitizeAttributes", "_sanitizeShadowDOM"]}