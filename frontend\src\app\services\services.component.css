/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ SECTION PRINCIPALE */
.services-section {
    
    text-align: center;
    padding: 60px 5%;
    background: white;
    color: black;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* ✅ TITRE PRINCIPAL AVEC ANIMATION */
.services-title {
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 30px;
  letter-spacing: 3px;
 
  border-bottom: 4px solid #2496d3;
  padding-bottom: 10px;
  display: inline-block;
 
}

/* ✅ Effet lumineux sur le titre */


/* ✅ Conteneur des cartes de services */
.services-container {
    display: flex;
    justify-content: center;
    align-items: stretch;
    flex-wrap: wrap;
    gap: 40px;
    width: 100%;
}

/* ✅ Style des cartes de service */
.service-card {
    flex: 1;
    min-width: 300px;
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: left;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: auto;
}

/* ✅ Effet au survol */
.service-card:hover {
    transform: scale(1.01);
    cursor: pointer;
    box-shadow: 5px 5px 5px #93a0ad;
}

/* ✅ TITRE DU SERVICE */
.service-title {
    font-size: 18px;
    font-weight: bold;
    color:#fba939;
    margin-bottom: 20px;
    font-family: 'Poppins', sans-serif;
    display: flex;
    align-items: center;
    gap: 15px;
}

/* ✅ LISTE DES SERVICES */
.service-list {
    font-size: 17px;
    line-height: 1.8;
    font-family: 'Poppins', sans-serif;
}

/* ✅ Effet d’interactivité sur les items */
.service-list li {
    list-style: none;
    padding: 12px 0;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.service-list li::before {
    content: "✔";
    margin-right: 10px;
    font-size: 18px;
    transition: transform 0.3s ease, color 0.3s ease;
}

/* ✅ Effet au survol */
.service-list li:hover {
    transform: translateX(10px);
    cursor: pointer;
}

.service-list li:hover::before {
    transform: scale(1.2);
    color: #00cc66;
}

/* ✅ IMAGE INTERACTIVE AVEC ANIMATION DE ROTATION FLUIDE */
.image-container {
    margin-top: 60px;
    display: flex;
    justify-content: center;
    width: 100%;
    animation: fadeIn 1.5s ease-in-out forwards;
}

.image-container img {
    width: 350px;
    max-width: 100%;
    border-radius: 15px;
    opacity: 0;
    animation: fadeIn 1.5s ease-in-out forwards, rotateFull 10s infinite linear;
}

/* ✅ Effet de rotation fluide */
@keyframes rotateFull {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ✅ Animations générales */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 1024px) {
    .services-container {
        flex-direction: column;
        align-items: center;
    }

    .service-card {
        max-width: 90%;
        height: auto;
    }
}

@media (max-width: 768px) {
    .services-title {
        font-size: 20px;
    }

    .service-card {
        max-width: 100%;
    }

    .service-list li {
        font-size: 16px;
    }
}
