<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resultat_client', function (Blueprint $table) {
            $table->id();
            $table->string('demande_id');
            $table->foreign('demande_id')->references('demande_id')->on('demandes')->onDelete('cascade');
            $table->string('rapport_file')->nullable(); // Path to the rapport PDF file
            $table->string('facture_file')->nullable(); // Path to the facture PDF file
            $table->enum('status', ['pending', 'sent'])->default('pending');
            $table->timestamp('sent_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resultat_client');
    }
};
