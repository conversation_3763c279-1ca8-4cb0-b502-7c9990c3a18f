import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, FormsModule, ReactiveFormsModule, FormControl, AbstractControl } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DemandeService } from './demande.service';
import { AnalysesService } from '../../services/analyses.service';
import { Analyse } from '../../../models/analyse.model';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPlus, faTrash, faPaperPlane, faCalendarAlt, faUndo, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@Component({
  selector: 'app-demandes',
  standalone: true,
  templateUrl: './demandes.component.html',
  styleUrls: ['./demandes.component.css'],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, FontAwesomeModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DemandesComponent implements OnInit {
  // Font Awesome icons
  faPlus = faPlus;
  faTrash = faTrash;
  faPaperPlane = faPaperPlane;
  faCalendarAlt = faCalendarAlt;
  faUndo = faUndo;
  faSpinner = faSpinner;
  demandeForm: FormGroup;
  successMessage = '';
  errorMessage = '';
  showNotification = false;
  modeReglementValue: string = '';
  formSubmitted = false; // Flag to track form submission
  isLoading = false; // Flag to track loading state
  loadingAnalyses = false; // Flag to track analyses loading state

  // User Information
  user: any = null;
  name: string | null = null;
  nickname: string | null = null;
  email: string | null = null;
  phone: string | null = null;
  adress: string | null = null;
  fax: string | null = null;

  // Available analyses - will be populated from API
  analysesDisponibles: { name: string; price: number; accredited: boolean }[] = [];

  dropdownOpen: boolean[] = [];

  constructor(
    private readonly fb: FormBuilder,
    private readonly demandeService: DemandeService,
    private readonly analysesService: AnalysesService
  ) {
    this.demandeForm = this.fb.group({
      enregistrements: this.fb.array([this.createEnregistrement()]),
      mode_reglement: ['', Validators.required],
      delai_souhaite: ['']
    });
  }

  ngOnInit() {
    this.loadUserInfo();
    this.loadAnalyses();
  }

  /**
   * Loads analyses from the backend API
   */
  loadAnalyses() {
    this.loadingAnalyses = true;
    this.analysesService.getAllAnalyses().subscribe({
      next: (analyses) => {
        // Map the analyses from the API to the format expected by the template
        this.analysesDisponibles = analyses.map(analysis => ({
          name: analysis.analyse,
          price: analysis.price,
          accredited: analysis.is_accredited === 'Yes'
        }));
        this.loadingAnalyses = false;
      },
      error: (error) => {
        console.error('Error loading analyses:', error);
        this.loadingAnalyses = false;
        // If there's an error, we'll show a default set of analyses
        this.analysesDisponibles = [
          { name: 'Biotoxine (DSP)', price: 350, accredited: true },
          { name: 'Biotoxine (PSP)', price: 350, accredited: true },
          { name: 'Humidité', price: 20, accredited: true },
        ];
      }
    });
  }

  // Load user info from localStorage
  loadUserInfo() {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        this.user = JSON.parse(userData);
        this.name = this.user?.name || null;
        this.nickname = this.user?.nickname || null;
        this.email = this.user?.email || null;
        this.phone = this.user?.phone || null;
        this.adress = this.user?.adress || null;
        this.fax = this.user?.fax || null;
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }

  get enregistrements(): FormArray {
    return this.demandeForm.get('enregistrements') as FormArray;
  }

  createEnregistrement(): FormGroup {
    return this.fb.group({

      reference: ['', Validators.required],
      analyse_souhaite: [''],
      date_prelevement: [''],
      origine_prelevement: [''],
      nom_preleveur: [''],
      site:[''],
      nature_echantillon: ['', Validators.required],
      provenance: ['', Validators.required],
      masse_echantillon: ['', [Validators.required, Validators.pattern(/^\d+(\.\d{1,2})?$/)]],
      etat: ['', Validators.required],
      analyses_demandees: this.fb.control([], Validators.required),
      lot: ['', Validators.required],
      observation: [''], // New optional observation field
    });
  }

  getEchantillonFormGroup(index: number): FormGroup {
    return this.enregistrements.at(index) as FormGroup;
  }

  addEnregistrement() {
    this.enregistrements.push(this.createEnregistrement());
    this.dropdownOpen.push(false);
  }

  removeEnregistrement(index: number) {
    if (this.enregistrements.length > 1) {
      this.enregistrements.removeAt(index);
      this.dropdownOpen.splice(index, 1);
    }
  }

  toggleDropdown(index: number) {
    this.dropdownOpen[index] = !this.dropdownOpen[index];
  }

  toggleAnalysisSelection(index: number, analysisName: string, event: Event) {
    const analysesControl = this.enregistrements.at(index).get('analyses_demandees');
    const selectedAnalyses = analysesControl?.value || [];
    const checked = (event.target as HTMLInputElement).checked;

    if (checked) {
      this.enregistrements.at(index).patchValue({
        analyses_demandees: [...selectedAnalyses, analysisName],
      });
    } else {
      this.enregistrements.at(index).patchValue({
        analyses_demandees: selectedAnalyses.filter((a: string) => a !== analysisName),
      });
    }

    // Mark as touched to trigger validation
    analysesControl?.markAsTouched();
    analysesControl?.updateValueAndValidity();
  }

  updateModeReglement(event: Event) {
    this.modeReglementValue = (event.target as HTMLSelectElement).value;
    this.demandeForm.patchValue({ mode_reglement: this.modeReglementValue });
    console.log("Mode de règlement sélectionné:", this.modeReglementValue);
  }

  onSubmit() {
    console.log("🔄 Submitting the form...");

    // Set form as submitted to trigger validation display
    this.formSubmitted = true;

    // Mark all controls as touched to trigger validation messages
    this.markFormGroupTouched(this.demandeForm);

    // Log validation state for debugging
    console.log('Form valid:', this.demandeForm.valid);
    console.log('Mode règlement valid:', this.demandeForm.get('mode_reglement')?.valid);

    // Check first enregistrement validation
    if (this.enregistrements.length > 0) {
      const firstItem = this.enregistrements.at(0);
      console.log('First enregistrement valid:', firstItem.valid);
      console.log('Reference valid:', firstItem.get('reference')?.valid);
      console.log('Provenance valid:', firstItem.get('provenance')?.valid);
      console.log('Analyses valid:', firstItem.get('analyses_demandees')?.valid);
    }

    if (this.demandeForm.invalid) {
      console.error("❌ Form validation failed:", this.demandeForm.errors);
      this.errorMessage = 'Veuillez remplir correctement tous les champs obligatoires !';
      return;
    }

    const modeReglement = this.modeReglementValue;

    if (!modeReglement) {
      console.error("❌ Mode de règlement is missing!");
      this.errorMessage = "Le mode de règlement est obligatoire !";
      return;
    }

    // Show loading spinner
    this.isLoading = true;
    this.errorMessage = '';

    const formValue = this.demandeForm.value;
    console.log("📋 Form Value before submission:", formValue);
    const requestData = {
      mode_reglement: modeReglement,
      delai_souhaite: formValue.delai_souhaite || null,
      enregistrements: this.enregistrements.value.map((echantillon: any) => ({

        reference: echantillon.reference || null,
        date_prelevement: echantillon. date_prelevement || null,
        nom_preleveur: echantillon. nom_preleveur || null,
        origine_prelevement: echantillon.origine_prelevement || null,
        site: echantillon.site || null,
        analyse_souhaite: echantillon.analyse_souhaite || null,
        nature_echantillon: echantillon.nature_echantillon || null,
        provenance: echantillon.provenance || null,
        masse_echantillon: echantillon.masse_echantillon ? parseFloat(echantillon.masse_echantillon) : null,
        etat: echantillon.etat || null,
        analyses_demandees: Array.isArray(echantillon.analyses_demandees) ? echantillon.analyses_demandees : [],
        lot: echantillon.lot || null,
        observation: echantillon.observation || null
      }))
    };

    console.log("📤 Final requestData being sent to API:", JSON.stringify(requestData, null, 2));

    this.demandeService.submitDemande(requestData).subscribe({
      next: (response) => {
        console.log("✅ Response from API:", response);

        // Hide loading spinner
        this.isLoading = false;

        // Show success message
        this.successMessage = 'Demande envoyée avec succès !';
        this.showNotification = true;
        this.errorMessage = '';

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);

        // Reset the form and all state
        this.resetForm();
      },
      error: (error) => {
        console.error("❌ Error submitting demande:", error);

        // Hide loading spinner
        this.isLoading = false;

        if (error.status === 422) {
          this.errorMessage = error.error?.message || "Erreur de validation des données !";
        } else if (error.status === 500) {
          this.errorMessage = "Erreur interne du serveur. Veuillez réessayer plus tard.";
        } else {
          this.errorMessage = "Une erreur inattendue s'est produite. Veuillez réessayer.";
        }

        this.successMessage = '';
      }
    });
  }

  /**
   * Recursively marks all controls in a form group or form array as touched and dirty
   * This ensures validation errors are displayed
   * @param formGroup - The form group or form array to process
   */
  private markFormGroupTouched(formGroup: AbstractControl): void {
    if (formGroup instanceof FormGroup) {
      Object.keys(formGroup.controls).forEach(key => {
        const control = formGroup.get(key);
        if (control) {
          if (control instanceof FormGroup || control instanceof FormArray) {
            this.markFormGroupTouched(control);
          } else {
            control.markAsTouched();
            control.markAsDirty();
            control.updateValueAndValidity();
          }
        }
      });
    } else if (formGroup instanceof FormArray) {
      formGroup.controls.forEach(control => {
        if (control instanceof FormGroup || control instanceof FormArray) {
          this.markFormGroupTouched(control);
        } else {
          control.markAsTouched();
          control.markAsDirty();
          control.updateValueAndValidity();
        }
      });
    } else if (formGroup instanceof FormControl) {
      formGroup.markAsTouched();
      formGroup.markAsDirty();
      formGroup.updateValueAndValidity();
    }
  }

  /**
   * Helper method to check if a form control has errors and should display them
   * @param controlName - The name of the control to check
   * @param form - The form group containing the control
   * @returns True if the control has errors and should display them
   */
  shouldShowError(controlName: string, form: FormGroup): boolean {
    const control = form.get(controlName);
    return control ? ((control.touched || this.formSubmitted) && control.invalid) : false;
  }

  /**
   * Resets the form and all state variables to their initial values
   */
  resetForm(): void {
    // Reset form state flags
    this.formSubmitted = false;
    this.modeReglementValue = '';
    this.dropdownOpen = [false];

    // Reset the form completely
    this.demandeForm.reset();

    // Create a new enregistrement with default values
    this.demandeForm.setControl('enregistrements', this.fb.array([this.createEnregistrement()]));

    // Reset the mode_reglement select element
    const modeReglementSelect = document.getElementById('modeReglement') as HTMLSelectElement;
    if (modeReglementSelect) {
      modeReglementSelect.selectedIndex = 0;
    }
  }

  /**
   * Closes the success notification and resets the form
   */
  closeNotification(): void {
    this.showNotification = false;
  }
}
