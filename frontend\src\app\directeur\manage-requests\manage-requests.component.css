@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.requests-container {
  max-width: 1100px;
  margin: 40px auto;
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0px 10px 25px rgba(36, 150, 211, 0.25);
  text-align: center;
  font-family: 'Poppins', sans-serif;
  transition: all 0.4s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  animation: fadeIn 0.5s ease-in-out;
}

/* ✅ TITRE PRINCIPAL */
.requests-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 25px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  color: transparent;
}

/* ✅ Tableau des demandes */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0px 6px 15px rgba(36, 150, 211, 0.3);
}

/* ✅ En-tête du tableau */
th {
  background-color: #2496d3;
  color: white;
  padding: 14px;
  text-align: center;
  font-weight: bold;
  border: 1px solid #ddd;
}

/* ✅ Cellules du tableau */
td {
  padding: 12px;
  border: 1px solid #ddd;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

/* ✅ Masse en rouge si inférieure au minimum */
.low-mass {
  color: red;
  font-weight: bold;
}

/* ✅ Conteneur des boutons d'action */
.actions-container {
  display: flex;
  flex-direction: column; /* Les boutons restent en colonne */
  gap: 8px; /* Espacement uniforme */
  align-items: center; /* Alignement centré */
}

/* ✅ Style des boutons */
button {
  padding: 12px 20px;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-size: 15px;
  font-weight: bold;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: bold;
  width: 150px; /* Assurer une largeur uniforme */
  text-transform: uppercase;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

/* ✅ Bouton "Voir détails" */
.details-btn {
  background: #1e90ff;
  color: white;
  box-shadow: 0px 4px 12px rgba(30, 144, 255, 0.3);
}

.details-btn:hover {
  background: #0c7cd5;
  transform: scale(1.05);
}

/* ✅ Bouton "Valider" */
.validate-btn {
  background: #28a745;
  color: white;
  box-shadow: 0px 4px 12px rgba(40, 167, 69, 0.3);
}

.validate-btn:hover {
  background: #218838;
  transform: scale(1.05);
}

/* ✅ Bouton "Rejeter" */
.reject-btn {
  background: #dc3545;
  color: white;
  box-shadow: 0px 4px 12px rgba(220, 53, 69, 0.3);
}

.reject-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}
/* ✅ Animation Glow sur le titre */
@keyframes glowText {
  0% {
    text-shadow: 0px 0px 5px rgba(36, 150, 211, 0.5);
  }
  100% {
    text-shadow: 0px 0px 15px rgba(36, 150, 211, 0.9);
  }
}

/* ✅ Animation d'apparition */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/* ✅ Ajustement Responsive */
@media (max-width: 768px) {
  .actions-container {
    flex-direction: column;
    width: 100%;
  }

  button {
    width: 100%;
    font-size: 14px;
    padding: 10px 15px;
  }
}
/* ✅ Adaptation Responsive */
@media (max-width: 768px) {
  .requests-container {
    width: 95%;
    padding: 20px;
  }

  table {
    font-size: 14px;
  }

  button {
    font-size: 14px;
    padding: 10px 15px;
  }

  .actions-container {
    flex-direction: row;
    gap: 5px;
  }
}
