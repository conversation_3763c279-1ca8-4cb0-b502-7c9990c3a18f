/* ---------------------------------
   1) ANIMATIONS
---------------------------------- */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* ---------------------------------
   2) CONTENEUR PRINCIPAL
---------------------------------- */
.facture-table-container {
  /* Mise en page */
  margin: 40px auto;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  /* Animation d'apparition */
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden; /* Pas de scroll horizontal */
  text-align: center; /* Centre le texte si besoin */
  transition: all 0.4s ease-in-out;
}

/* ---------------------------------
   3) TITRE PRINCIPAL
---------------------------------- */
.facture-table-container h2 {
  /* Police Orbitron */
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ---------------------------------
   4) BARRE DE FILTRAGE
---------------------------------- */
.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: center; /* Vertically centers all items */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 60%; /* Limits the width as in your original design */
  margin: 0 auto; /* Centers the filter bar within its parent */
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 250px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ---------------------------------
   5) TABLEAU DES FACTURES
---------------------------------- */
.facture-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* En-tête */
.facture-table thead tr th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
  text-align: center;
}

/* Cellules */
.facture-table tbody tr td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
  background-color: white;
}

/* Survol d'une ligne */
.facture-table tbody tr:hover td {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* ---------------------------------
   6) STATUT DE PAIEMENT
---------------------------------- */
.status {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
  display: inline-block;
  margin: 0 auto;
}

/* Statut: En attente */
.status.en_attente {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
  box-shadow: 0px 3px 8px rgba(255, 193, 7, 0.4);
}

/* Statut: Payé */
.status.paye {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  box-shadow: 0px 3px 8px rgba(40, 167, 69, 0.4);
}

/* Statut: Paiement rejeté */
.status.paiement_rejete {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  box-shadow: 0px 3px 8px rgba(220, 53, 69, 0.4);
}

/* Style for rejection reason */
.rejection-reason {
  font-size: 12px;
  color: #dc3545;
  margin-top: 5px;
  font-style: italic;
}

/* ---------------------------------
   7) BOUTON DÉTAILS
---------------------------------- */
button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px auto;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
}

/* Bouton Détails */
.btn-details {
  background: #2496d3;
  color: white;
  box-shadow: 0px 4px 10px rgba(36, 150, 211, 0.3);
}

.btn-details:hover {
  background: #1e78b5;
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
}

/* ---------------------------------
   8) LOADING SPINNER & MESSAGE "AUCUNE FACTURE"
---------------------------------- */
/* Styles pour l'indicateur de chargement et les messages vides */
.loading-row, .empty-row {
  height: 100px;
}

.loading-row td, .empty-row td {
  text-align: center;
  vertical-align: middle;
  font-size: 18px;
  color: #6c757d;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.text-center {
  text-align: center;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ---------------------------------
   9) PAGINATION
---------------------------------- */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* ---------------------------------
   10) RESPONSIVE
---------------------------------- */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    max-width: 95%;
    height: auto;
    padding: 1rem;
  }

  .filter-group {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .filter-group input,
  .filter-group select {
    max-width: 100%;
  }

  .btn-clear {
    margin-top: 0.5rem;
    margin-left: 0;
    width: 100%;
  }

  .facture-table-container {
    padding: 20px;
  }

  .facture-table thead tr th,
  .facture-table tbody tr td {
    padding: 10px;
    font-size: 13px;
  }

  .facture-table-container button {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 15px;
  }
}
