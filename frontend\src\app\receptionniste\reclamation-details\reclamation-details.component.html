<div class="reclamation-details-container">
  <div class="header-with-back">
    <button class="btn-back" (click)="goBack()">
      <fa-icon [icon]="faArrowLeft"></fa-icon>
    </button>
    <h2>Détails de la Réclamation</h2>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner"></div>
    <p>Chargement en cours...</p>
  </div>

  <!-- Error Message -->
  <div *ngIf="!isLoading && errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <!-- Success Message -->
  <div *ngIf="showSuccessMessage" class="success-message">
    <div class="success-icon">
      <fa-icon [icon]="faCheckCircle"></fa-icon>
    </div>
    <p>{{ successMessage }}</p>
    <button class="btn-close" (click)="closeSuccessMessage()">
      <fa-icon [icon]="faTimes"></fa-icon>
    </button>
  </div>

  <!-- Reclamation Details -->
  <div *ngIf="!isLoading && reclamation && !showSuccessMessage" class="details-content">
    <!-- Reclamation Header -->
    <div class="reclamation-header">
      <div class="reclamation-id">
        <strong>ID:</strong> {{ reclamation.id }} |
        <strong>Date:</strong> {{ reclamation.date | date:'dd/MM/yyyy' }} |
        <strong>Statut:</strong>
        <span class="status-tag" [ngClass]="getStatusClass(reclamation.status)">
          <fa-icon [icon]="getStatusIcon(reclamation.status)"></fa-icon>
          {{ getStatusLabel(reclamation.status) }}
        </span>
      </div>
    </div>

    <!-- Client Information -->
    <div class="client-info-card">
      <h3>
        <fa-icon [icon]="faUser"></fa-icon>
        Informations du Client
      </h3>
      <div class="client-info-table-container">
        <table class="client-info-table">
          <tbody>
            <tr>
              <td class="label">Nom:</td>
              <td class="value">{{ reclamation.clientName }} {{ reclamation.clientNickname }}</td>
            </tr>
            <tr *ngIf="reclamation.clientEmail">
              <td class="label">Email:</td>
              <td class="value">{{ reclamation.clientEmail }}</td>
            </tr>
            <tr *ngIf="reclamation.clientPhone">
              <td class="label">Téléphone:</td>
              <td class="value">{{ reclamation.clientPhone }}</td>
            </tr>
            <tr *ngIf="reclamation.demandeId">
              <td class="label">Demande associée:</td>
              <td class="value">
                {{ reclamation.demandeId }}
                <button class="btn-link" (click)="viewDemande(reclamation.demandeId)">
                  <fa-icon [icon]="faExternalLink"></fa-icon> Voir la demande
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Reclamation Content -->
    <div class="reclamation-content-card">
      <h3>
        <fa-icon [icon]="faClipboard"></fa-icon>
        Contenu de la Réclamation
      </h3>
      <div class="reclamation-content">
        <div class="info-row">
          <span class="label">Sujet:</span>
          <span class="value">{{ reclamation.subject }}</span>
        </div>
        <div class="info-row">
          <span class="label">Description:</span>
          <div class="description-text">{{ reclamation.description }}</div>
        </div>
        <div class="info-row" *ngIf="reclamation.attachmentUrl">
          <span class="label">Pièce jointe:</span>
          <div class="attachment-preview">
            <div class="file-card">
              <div class="file-icon">
                <fa-icon [icon]="getFileIcon(reclamation.attachmentUrl)"></fa-icon>
              </div>
              <div class="file-info">
                <span class="file-name">{{ getFileName(reclamation.attachmentUrl) }}</span>
                <div class="file-actions">
                  <button class="btn-file-action" title="Voir">
                    <fa-icon [icon]="faEye"></fa-icon>
                  </button>
                  <button class="btn-file-action" title="Télécharger">
                    <fa-icon [icon]="faDownload"></fa-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Combined Status Change and Response Form -->
    <div class="response-form-card">
      <div class="card-header-flex">
        <h3>
          <fa-icon [icon]="faReply"></fa-icon>
          Répondre au Client
        </h3>
        <div class="status-options">
          <button
            class="btn-status pending"
            [class.active]="reclamation.status === 'pending'"
            (click)="changeStatus('pending')"
          >
            <fa-icon [icon]="faCircleDot"></fa-icon> En attente
          </button>
          <button
            class="btn-status in-progress"
            [class.active]="reclamation.status === 'in_progress'"
            (click)="changeStatus('in_progress')"
          >
            <fa-icon [icon]="faSpinner"></fa-icon> En cours
          </button>
          <button
            class="btn-status resolved"
            [class.active]="reclamation.status === 'resolved'"
            (click)="changeStatus('resolved')"
          >
            <fa-icon [icon]="faCheckCircle"></fa-icon> Résolu
          </button>
        </div>
      </div>
      <form [formGroup]="responseForm" (ngSubmit)="submitResponse()">
        <div class="form-group">
          <label for="response">Votre réponse</label>
          <textarea
            id="response"
            formControlName="response"
            rows="3"
            placeholder="Entrez votre réponse à la réclamation..."
            [ngClass]="{'is-invalid': submitted && f['response'].errors}"
          ></textarea>
          <div *ngIf="submitted && f['response'].errors" class="error-message">
            <div *ngIf="f['response'].errors['required']">La réponse est requise</div>
          </div>
        </div>
        <div class="form-actions">
          <button type="submit" class="btn-submit" [disabled]="isSubmitting">
            <span *ngIf="!isSubmitting">
              <fa-icon [icon]="faPaperPlane"></fa-icon> Envoyer la réponse
            </span>
            <span *ngIf="isSubmitting">
              <fa-icon [icon]="faSpinner" [spin]="true"></fa-icon> Envoi en cours...
            </span>
          </button>
        </div>
      </form>
    </div>

    <!-- Previous Responses -->
    <div class="previous-responses-card" *ngIf="reclamation.responses && reclamation.responses.length > 0">
      <h3>
        <fa-icon [icon]="faComments"></fa-icon>
        Réponses Précédentes
      </h3>
      <div class="responses-list">
        <div class="response-item" *ngFor="let response of reclamation.responses">
          <div class="response-header">
            <span class="response-author">{{ response.author }}</span>
            <span class="response-date">{{ response.date | date:'dd/MM/yyyy HH:mm' }}</span>
          </div>
          <div class="response-content">
            {{ response.content }}
          </div>
        </div>
      </div>
    </div>


  </div>
</div>
