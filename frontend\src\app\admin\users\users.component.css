@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ CONTAINER PRINCIPAL */
.user-management {
  text-align: center;
  padding: 50px 5%;
  font-family: 'Montserrat', sans-serif;
  background: white;
  color: black;
  animation: fadeInBg 2s ease-in-out;
}

/* ✅ TITRE PRINCIPAL */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 28px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 10px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.error {
  padding-left: 10px;
color: #d40000;
border-radius: 8px;


font-size: 13px;
animation: fadeIn 0.5s ease-in-out;
}

/* ✅ BOUTON AJOUTER UTILISATEUR */
.add-user-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 20px 0;
}

.add-user-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-weight: bold;
  background: #2496d3;
  color: white;
  padding: 14px 25px;
  font-size: 18px;
  border-radius: 30px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 8px 15px rgba(36, 150, 211, 0.5);
}

.add-user-btn:hover {
  background: black;
  transform: scale(1.1);
  box-shadow: 0px 12px 35px rgba(0, 0, 0, 0.8);
}

/* ✅ TABLEAU DES UTILISATEURS */
.user-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
}

.user-table th {
  background: #2496d3;
  color: white;
  padding: 14px;
  text-transform: uppercase;
  font-weight: bold;
}

.user-table td {
  border: 1px solid #ddd;
  padding: 14px;
  text-align: center;
  font-size: 16px;
}

/* ✅ BOUTONS D'ACTION */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.edit-btn, .delete-btn {
  padding: 10px;
  border: none;
  cursor: pointer;
  font-size: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}

/* ✅ MODIFIER */
.edit-btn {
  background: #f0ad4e;
  color: white;
}

.edit-btn:hover {
  background: #d6963c;
  transform: scale(1.1);
}

/* ✅ SUPPRIMER */
.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #b22234;
  transform: scale(1.1);
}

/* ✅ MODALE - Correction et visibilité améliorée */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
  padding: 20px;
  animation: fadeIn 0.3s ease-in-out;
}





/* ✅ CONTENU DE LA MODALE */
.modal-content {
  background: white;
  padding: 35px;
  border-radius: 12px;
  width: 520px;
  max-height: 90vh;
  text-align: center;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  animation: fadeInUp 0.4s ease-in-out;
}
.filter-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  background: #f8f9fa; /* Light background */
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  max-width: 350px; /* Adjust width */
  margin: 20px auto; /* Centering */
}

.filter-container label {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.filter-container input,
.filter-container select {
  width: 100%;
  padding: 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background: #fff;
  outline: none;
  transition: all 0.3s ease-in-out;
}

/* Improve dropdown list appearance */
.filter-container select {
  appearance: none;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><path fill="%23333" d="M7 10l5 5 5-5H7z"/></svg>') no-repeat right 12px center;
  background-size: 14px;
  padding-right: 30px;
  cursor: pointer;
}

/* Hover & Focus Effects */
.filter-container input:focus,
.filter-container select:focus {
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

/* Responsive design for smaller screens */
@media (max-width: 400px) {
  .filter-container {
      max-width: 100%;
      padding: 15px;
  }
}

/* ✅ TITRE DE LA MODALE */
.modal-content h3 {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
  padding: 50px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* ✅ CHAMPS DU FORMULAIRE */
.modal-content label {
  display: block;
  font-weight: bold;
  margin-top: 12px;
  font-size: 16px;
}

.modal-content input, .modal-content select {
  width: 100%;
  padding: 12px;
  margin-top: 6px;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 16px;
  background: #f8f8f8;
}

/* ✅ ESPACEMENT ET LISIBILITÉ */
.modal-content input {
  height: 50px;
}

/* ✅ BOUTONS DE LA MODALE */
.modal-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.save-btn, .cancel-btn {
  padding: 14px 20px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: background 0.3s ease, transform 0.2s;
  width: 48%;
}

.save-btn {
  background: #2496d3;
  color: white;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.save-btn:hover, .cancel-btn:hover {
  transform: scale(1.05);
}

/* ✅ ANIMATIONS */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.25);
  }
  100% {
    transform: scale(1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  }
}

.edit-btn:hover, .delete-btn:hover {
  animation: pulse 0.4s ease-in-out;
}
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
}
