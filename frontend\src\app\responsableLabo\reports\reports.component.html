<div class="admin-panel">
  <h2>Les Rapports</h2>

  <!-- Affichage d'un message si aucun rapport n'est disponible -->
  <p *ngIf="!reports || reports.length === 0">Aucun rapport disponible.</p>

  <table class="reports-table" *ngIf="reports && reports.length > 0">
    <thead>
      <tr>
        <th>ID</th>
        <th>Demande Numéro</th>
        <th>Date de Création</th>
        <th>Statut</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- On parcourt les rapports provenant du TS : this.reports -->
      <tr *ngFor="let report of reports">
        <td>{{ report.id }}</td>
        <td>{{ report.demande_id }}</td>
        <td>{{ report.creation_date | date:'dd/MM/yyyy' }}</td>
        <td>{{ report.status }}</td>
        <td class="action-buttons">
          <!-- Bouton "Envoyer" si le rapport est en attente -->
          <!-- <button
            *ngIf="report.status === 'Non envoyé'"
            class="approve-btn"
            (click)="envoyerRapport(report.id)"
          >
            Envoyer
          </button> -->

          <!-- Bouton "Voir Détails" -->
          <button class="details-btn" (click)="viewReportDetails(report.id)">
            <fa-icon [icon]="faEye" style="margin-right: 10px;"></fa-icon>Voir détails
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
