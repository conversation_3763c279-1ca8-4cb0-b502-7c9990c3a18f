<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up() {
        Schema::create('registres', function (Blueprint $table) {
            $table->id();
            $table->foreignId('registre_suivi_id')->constrained('registre_suivis')->onDelete('cascade');
            $table->string('code_client');
            $table->string('code_labo');
            $table->integer('quantite');
            $table->string('nature_echantillon');
            $table->string('echantillon_envoye_par')->nullable();
            $table->string('echantillon_recu_par')->nullable();
            $table->string('lieu_conservation')->nullable();
            $table->string('numero_demande');
            $table->json('parametres_a_analyser');
            $table->date('date_prevue_analyse');
            $table->date('date_effective_analyse')->nullable();
            $table->date('date_emission_rapport')->nullable();
            $table->timestamps();
        });
    }

    public function down() {
        Schema::dropIfExists('registres');
    }
};
