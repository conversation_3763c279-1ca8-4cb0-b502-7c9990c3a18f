import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ResultatsService {
  private apiUrl = 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) { }

  // Get results by demande ID
  getResultsByDemandeId(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/results/demande/${demandeId}`);
  }

  // Get file URL for viewing
  getFileUrl(filePath: string): string {
    if (!filePath) return '';
    const url = `${this.apiUrl}/direct-file/${encodeURIComponent(filePath)}`;
    console.log('Generated file URL:', url, 'from path:', filePath);
    return url;
  }

  // Download file
  downloadFile(filePath: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/direct-file/${encodeURIComponent(filePath)}`, {
      responseType: 'blob'
    });
  }

  // Download results file by result ID
  downloadResults(resultId: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/results/download/${resultId}`, {
      responseType: 'blob'
    });
  }

  // Get rapport details
  getRapportDetails(rapportId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/rapports/${rapportId}`);
  }

  // Get facture details
  getFactureDetails(factureId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/factures/${factureId}`);
  }

  // Get facture file
  getFactureFile(factureId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/factures/${factureId}/file`);
  }

  // Get payment details for a demande
  getPaymentsByDemandeId(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/payments/demande/${demandeId}`);
  }

  // Get user details
  getUserDetails(userId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/userdetails/${userId}`);
  }

  // Get demande details
  getDemandeDetails(demandeId: number | string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/demandes/${demandeId}`).pipe(
      map(response => {
        if (!response || !response.demande) {
          throw new Error('Invalid API response structure');
        }
        console.log('Processed API response:', response.demande);
        return response.demande;
      }),
      catchError(error => {
        console.error('API error:', error);
        return throwError(() => new Error('Failed to fetch demande details.'));
      })
    );
  }

  // Get rapport by demande ID
  getRapportByDemandeId(demandeId: number | string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/rapports/demande/${demandeId}`);
  }
}
