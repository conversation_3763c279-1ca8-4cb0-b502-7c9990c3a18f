<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Registre extends Model {
    use HasFactory;

    protected $fillable = [
        'registre_suivi_id', 'code_client', 'code_labo', 'quantite', 'nature_echantillon',
        'echantillon_envoye_par', 'echantillon_recu_par', 'lieu_conservation', 'numero_demande',
        'parametres_a_analyser', 'date_prevue_analyse', 'date_effective_analyse', 'date_emission_rapport'
    ];

    protected $casts = [
        'parametres_a_analyser' => 'array', // Automatically decode JSON field
    ];

    public function registreSuivi() {
        return $this->belongsTo(RegistreSuivi::class);
    }
}
