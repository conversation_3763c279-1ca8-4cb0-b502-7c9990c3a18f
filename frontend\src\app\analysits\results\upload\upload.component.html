<div class="upload-container">
  <div class="upload-header">
    <h2>
      <fa-icon [icon]="faUpload" style="color: #000000; -webkit-text-fill-color: #2496d3;"></fa-icon>
      Dépôt des Résultats d’Analyse
    </h2>
    <button class="back-btn" (click)="goBack()">
      <fa-icon [icon]="faArrowLeft" style="color: white;"></fa-icon> Retour au Résultats d'Analyses
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-message">
    <fa-icon [icon]="faSpinner" [spin]="true"></fa-icon>
    Chargement des détails de la demande...
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message">
    <fa-icon [icon]="faExclamationTriangle"></fa-icon>
    {{ errorMessage }}
  </div>

  <!-- Success message -->
  <div *ngIf="successMessage" class="success-message">
    <fa-icon [icon]="faCheckCircle"></fa-icon>
    {{ successMessage }}
  </div>

  <!-- Demande details -->
  <div *ngIf="!isLoading && demandeDetails" class="demande-details">
    <h3>Détails de la Demande</h3>
    <div class="details-grid">
      <div class="detail-item">
        <span class="detail-label">Numéro de Demande:</span>
        <span class="detail-value">{{ demandeDetails.demande_id }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">Date de Demande:</span>
        <span class="detail-value">{{ demandeDetails.demande_date }}</span>
      </div>
      <div class="detail-item">
        <span class="detail-label">Total Échantillons:</span>
        <span class="detail-value">{{ demandeDetails.total_enregistrements || (demandeDetails.samples ? demandeDetails.samples.length : 'Non disponible') }}</span>
      </div>
    </div>
  </div>

  <!-- Upload form -->
  <div *ngIf="!isLoading && !successMessage" class="upload-form">
    <div class="file-upload-container">
      <div class="file-upload-area" [class.has-file]="fileName">
        <input
          type="file"
          id="file-upload"
          class="file-upload-input"
          (change)="onFileSelected($event)"
          accept=".xls,.xlsx"
          [disabled]="isUploading"
        >
        <label for="file-upload" class="file-upload-label">
          <fa-icon [icon]="faFileExcel" class="icon-excel"></fa-icon>
          <span *ngIf="!fileName">Sélectionner un fichier Excel</span>
          <span *ngIf="fileName">{{ fileName }}</span>
        </label>
      </div>
      <div class="file-upload-info" *ngIf="fileName">
        <p>Fichier sélectionné: <strong>{{ fileName }}</strong></p>
      </div>
    </div>

    <div class="upload-actions">
      <button
        class="upload-btn"
        [disabled]="!selectedFile || isUploading"
        (click)="uploadFile()"
      >
        <fa-icon *ngIf="!isUploading" [icon]="faUpload" style="color: white;"></fa-icon>
        <fa-icon *ngIf="isUploading" [icon]="faSpinner" [spin]="true" style="color: white;"></fa-icon>
        {{ isUploading ? 'Téléchargement en cours...' : 'Télécharger les Résultats' }}
      </button>
    </div>
  </div>

  <!-- Instructions -->
  <div class="upload-instructions">
    <h4>Instructions:</h4>
    <ul>
      <li>Le fichier doit être au format Excel <code>.xls</code> ou <code>.xlsx</code></li>
      <li>Assurez-vous que le fichier contient tous les résultats d'analyse pour cette demande</li>
      <li>Le format du fichier doit correspondre au modèle standard du laboratoire</li>
      <li>Taille maximale du fichier: 10 MB</li>
    </ul>
  </div>
</div>
