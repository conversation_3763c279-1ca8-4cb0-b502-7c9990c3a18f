<?php

namespace Tests\Feature;

use App\Models\Rapport;
use App\Models\Demande;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RapportNotesTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_update_rapport_notes()
    {
        // Create a user
        $user = User::factory()->create();

        // Create a demande
        $demande = Demande::factory()->create([
            'user_id' => $user->id,
            'demande_id' => 'TEST-001'
        ]);

        // Create a rapport
        $rapport = Rapport::create([
            'demande_id' => $demande->id,
            'creation_date' => now(),
            'status' => 'not_sent',
            'status_director' => 'not_sent',
            'demande_numero' => $demande->demande_id,
            'notes' => null
        ]);

        // Test updating notes
        $response = $this->actingAs($user, 'sanctum')
            ->put<PERSON><PERSON>("/api/rapport/notes/{$rapport->id}", [
                'notes' => 'This is a test note for the rapport.'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Notes updated successfully',
                'rapport' => [
                    'id' => $rapport->id,
                    'notes' => 'This is a test note for the rapport.'
                ]
            ]);

        // Verify the notes were saved in the database
        $this->assertDatabaseHas('rapports', [
            'id' => $rapport->id,
            'notes' => 'This is a test note for the rapport.'
        ]);
    }

    public function test_can_clear_rapport_notes()
    {
        // Create a user
        $user = User::factory()->create();

        // Create a demande
        $demande = Demande::factory()->create([
            'user_id' => $user->id,
            'demande_id' => 'TEST-002'
        ]);

        // Create a rapport with existing notes
        $rapport = Rapport::create([
            'demande_id' => $demande->id,
            'creation_date' => now(),
            'status' => 'not_sent',
            'status_director' => 'not_sent',
            'demande_numero' => $demande->demande_id,
            'notes' => 'Existing notes'
        ]);

        // Test clearing notes
        $response = $this->actingAs($user, 'sanctum')
            ->putJson("/api/rapport/notes/{$rapport->id}", [
                'notes' => null
            ]);

        $response->assertStatus(200);

        // Verify the notes were cleared in the database
        $this->assertDatabaseHas('rapports', [
            'id' => $rapport->id,
            'notes' => null
        ]);
    }

    public function test_notes_included_in_rapport_details()
    {
        // Create a user
        $user = User::factory()->create();

        // Create a demande
        $demande = Demande::factory()->create([
            'user_id' => $user->id,
            'demande_id' => 'TEST-003'
        ]);

        // Create a rapport with notes
        $rapport = Rapport::create([
            'demande_id' => $demande->id,
            'creation_date' => now(),
            'status' => 'not_sent',
            'status_director' => 'not_sent',
            'demande_numero' => $demande->demande_id,
            'notes' => 'Test notes for details endpoint'
        ]);

        // Test getting rapport details
        $response = $this->actingAs($user, 'sanctum')
            ->getJson("/api/rapports/{$rapport->id}");

        $response->assertStatus(200)
            ->assertJsonFragment([
                'notes' => 'Test notes for details endpoint'
            ]);
    }
}
