import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ResultatsService } from './resultats.service';
import { FileViewerModule } from '../../shared/file-viewer/file-viewer.module';

@Component({
  selector: 'app-resultats',
  standalone: true,
  imports: [CommonModule, FileViewerModule],
  templateUrl: './resultats.component.html',
  styleUrl: './resultats.component.css'
})
export class ResultatsComponent implements OnInit {

  // Component properties
  demandeId: string = '';
  resultsData: any = null;
  demande: any = null;
  rapport: any = null;
  facture: any = null;

  // UI state
  isLoading: boolean = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resultatsService: ResultatsService
  ) {}

  ngOnInit(): void {
    // Get demande ID from route params
    this.route.params.subscribe(params => {
      this.demandeId = params['id'];

      if (this.demandeId) {
        this.loadResultsData();
        this.loadDemandeDetails(this.demandeId);
      } else {
        this.error = 'Identifiant de demande non valide.';
        this.isLoading = false;
      }
    });
  }

  loadResultsData(): void {
    this.isLoading = true;
    this.error = null;

    this.resultatsService.getResultsByDemandeId(this.demandeId).subscribe({
      next: (response) => {
        if (response && response.status === 'success' && response.data) {
          this.resultsData = response.data;
          console.log('resultsData', this.resultsData);
          console.log('Results data loaded:', this.resultsData);

          // Debug file paths
          if (this.resultsData.rapport_file) {
            console.log('Rapport file found:', this.resultsData.rapport_file);
          } else {
            console.warn('No rapport_file in resultsData');
          }

          if (this.resultsData.facture_file) {
            console.log('Facture file found:', this.resultsData.facture_file);
          } else {
            console.warn('No facture_file in resultsData');
          }
        } else {
          console.warn('No results data found or invalid response format');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading results data:', error);
        // Don't set error here as we might still have rapport or facture data
        this.isLoading = false;
      }
    });
  }

  loadDemandeDetails(id: string | number): void {
    this.resultatsService.getDemandeDetails(id).subscribe({
      next: (data) => {
        this.demande = data;
        console.log('Demande details:', data);

        // Load rapport if rapport_created is available in demande
        if (data.rapport_created) {
          console.log('Found rapport_created in demande:', data.rapport_created);
          this.loadRapportDetails(data.rapport_created);
        } else {
          console.log('No rapport_created found in demande, trying to fetch by demande ID');
          // If rapport_created is not directly available, try to fetch it from the backend
          this.resultatsService.getRapportByDemandeId(id).subscribe({
            next: (rapportData: any) => {
              if (rapportData?.id) {
                console.log('Found rapport by demande ID:', rapportData.id);
                this.loadRapportDetails(rapportData.id);
              } else {
                console.warn('No rapport found for demande');
                this.checkLoadingComplete();
              }
            },
            error: (error: any) => {
              console.error('Error fetching rapport by demande ID:', error);
              this.checkLoadingComplete();
            }
          });
        }

        // Load facture if facture_id is available in demande
        if (data.facture_id) {
          console.log('Found facture_id in demande:', data.facture_id);
          this.loadFactureDetails(data.facture_id);
        } else {
          console.warn('No facture_id found in demande');
          if (!data.rapport_created) {
            this.checkLoadingComplete();
          }
        }
      },
      error: (error) => {
        console.error('Error loading demande details:', error);
        this.error = 'Erreur lors du chargement des détails de la demande';
        this.isLoading = false;
      }
    });
  }

  loadRapportDetails(rapportId: number): void {
    this.resultatsService.getRapportDetails(rapportId).subscribe({
      next: (data) => {
        this.rapport = data;
        console.log('Rapport details:', data);
        this.checkLoadingComplete();
      },
      error: (error) => {
        console.error('Error loading rapport details:', error);
        this.checkLoadingComplete();
      }
    });
  }

  loadFactureDetails(factureId: number): void {
    this.resultatsService.getFactureDetails(factureId).subscribe({
      next: (data) => {
        this.facture = data.facture;
        console.log('Facture details:', data);

        // We no longer automatically generate a file_path
        // Only use the file_path if it actually exists in the data

        this.checkLoadingComplete();
      },
      error: (error) => {
        console.error('Error loading facture details:', error);
        this.checkLoadingComplete();
      }
    });
  }

  checkLoadingComplete(): void {
    // Check if we have loaded all the data we need
    if (this.isLoading) {
      const hasRapport = !!this.rapport;
      const hasFacture = !!this.facture;
      const hasResults = !!this.resultsData;

      if (hasRapport || hasFacture || hasResults) {
        this.isLoading = false;
      }
    }
  }

  // File handling methods
  hasReportFile(): boolean {
    // Only show report file if rapport_file exists and is not null
    if (this.resultsData?.rapport_file && this.resultsData.rapport_file !== null && this.resultsData.rapport_file !== '') {
      return true;
    }

    // Only show report if file_path exists and is not null
    if (this.rapport?.file_path && this.rapport.file_path !== null && this.rapport.file_path !== '') {
      return true;
    }

    return false;
  }

  hasInvoiceFile(): boolean {
    // Only show invoice file if facture_file exists and is not null
    if (this.resultsData?.facture_file && this.resultsData.facture_file !== null && this.resultsData.facture_file !== '') {
      return true;
    }

    // Only show invoice if file_path exists and is not null
    if (this.facture?.file_path && this.facture.file_path !== null && this.facture.file_path !== '') {
      return true;
    }

    // We no longer show facture just because facture.id exists
    // Only show if there's an actual file
    return false;
  }

  hasAnyFiles(): boolean {
    return this.hasReportFile() || this.hasInvoiceFile();
  }

  getReportFileName(): string {
    // Try to get filename from results data
    if (this.resultsData?.rapport_file && this.resultsData.rapport_file !== null && this.resultsData.rapport_file !== '') {
      const path = this.resultsData.rapport_file;
      return path.split('/').pop() ?? 'rapport.pdf';
    }

    // Try to get filename from rapport
    if (this.rapport?.file_path && this.rapport.file_path !== null && this.rapport.file_path !== '') {
      const path = this.rapport.file_path;
      return path.split('/').pop() ?? 'rapport.pdf';
    }

    return 'rapport.pdf';
  }

  getInvoiceFileName(): string {
    // Try to get filename from results data
    if (this.resultsData?.facture_file && this.resultsData.facture_file !== null && this.resultsData.facture_file !== '') {
      const path = this.resultsData.facture_file;
      return path.split('/').pop() ?? 'facture.pdf';
    }

    // Try to get filename from facture
    if (this.facture?.file_path && this.facture.file_path !== null && this.facture.file_path !== '') {
      const path = this.facture.file_path;
      return path.split('/').pop() ?? 'facture.pdf';
    }

    return 'facture.pdf';
  }

  // We're now using only the download function for both viewing and downloading

  // Downloading files (used for both report and invoice)
  downloadFile(type: 'report' | 'invoice'): void {
    let filePath = '';
    let fileName = '';

    // Show visual feedback that download is starting
    const downloadButton = document.querySelector(`.file-card[ng-reflect-ng-if="${type === 'report' ? 'hasReportFile()' : 'hasInvoiceFile()'}"] .download-button`);
    if (downloadButton) {
      downloadButton.classList.add('downloading');
      setTimeout(() => {
        downloadButton.classList.remove('downloading');
      }, 1000);
    }

    if (type === 'report') {
      // Try to get file path from results data first
      if (this.resultsData?.rapport_file && this.resultsData.rapport_file !== null && this.resultsData.rapport_file !== '') {
        filePath = this.resultsData.rapport_file;
      }
      // Then try to get file path from rapport
      else if (this.rapport?.file_path && this.rapport.file_path !== null && this.rapport.file_path !== '') {
        filePath = this.rapport.file_path;
      }
      fileName = this.getReportFileName();
    } else { // invoice
      // Try to get file path from results data first
      if (this.resultsData?.facture_file && this.resultsData.facture_file !== null && this.resultsData.facture_file !== '') {
        filePath = this.resultsData.facture_file;
      }
      // Then try to get file path from facture
      else if (this.facture?.file_path && this.facture.file_path !== null && this.facture.file_path !== '') {
        filePath = this.facture.file_path;
      }
      // We no longer generate a file path based on facture ID
      fileName = this.getInvoiceFileName();
    }

    if (!filePath) {
      console.error(`No file path found for ${type}`);
      return;
    }

    // Get the file URL and create a download link
    const url = this.resultatsService.getFileUrl(filePath);
    console.log(`Downloading ${type} file from: ${url}`);

    // Create a link element to trigger the download
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.target = '_blank'; // Open in new tab if download doesn't start automatically
    document.body.appendChild(a);
    a.click();

    // Clean up the DOM
    setTimeout(() => {
      document.body.removeChild(a);
    }, 100);
  }

  goBack(): void {
    this.router.navigate(['/client/dashboard']);
  }
}
