import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG } from '../app.config';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-change-password',
  standalone: true,
  imports: [CommonModule,ReactiveFormsModule],
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.css']
})
export class ChangePasswordComponent implements OnInit {
  changePasswordForm!: FormGroup;
  token: string | null = null;
  email: string | null = null;
  errorMessage: string | null = null;
  successMessage: string | null = null;
  submitted = false;
  private changePasswordUrl = `${APP_CONFIG.apiBase}/reset-password`;
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private http: HttpClient,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.token = params['token'] || null;
      this.email = params['email'] || null;
    });
  
    this.changePasswordForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(8)]],
      password_confirmation: ['', [Validators.required, Validators.minLength(8)]]
    }, {
      validators: this.passwordMatchValidator});
  }
  passwordMatchValidator(formGroup: FormGroup) {
    const password = formGroup.get('password')?.value;
    const confirmPassword = formGroup.get('password_confirmation')?.value;

    if (password !== confirmPassword) {
      formGroup.get('password_confirmation')?.setErrors({ mismatch: true });
    } else {
      // Remove the mismatch error if passwords match (keeping any other errors intact)
      const errors = formGroup.get('password_confirmation')?.errors;
      if (errors) {
        delete errors['mismatch'];
        if (!Object.keys(errors).length) {
          formGroup.get('password_confirmation')?.setErrors(null);
        }
      }
    }
    return null;
  }

 

  onSubmit(): void {
    this.errorMessage = null;
    if (this.changePasswordForm.invalid) {
      this.changePasswordForm.markAllAsTouched();
      return;
    }

    const data = {
      token: this.token,
      email: this.email,
      password: this.changePasswordForm.value.password,
      password_confirmation: this.changePasswordForm.value.password_confirmation,
    };

    this.http.post<any>(this.changePasswordUrl, data).subscribe({
        next: (res) => {
          console.log('Password reset successful:', res);
          this.successMessage = "Success! Check your email for the password reset link and follow the instructions to update your password.";
          this.router.navigate(['/login'],{ state: { message: "Success! Check your email for the password reset link and follow the instructions to update your password." } });
        },
        error: (err) => {
          console.error('Password reset failed:', err);
          this.errorMessage = err.error?.message || 'Impossible de réinitialiser votre mot de passe en ce moment. Veuillez vérifier que l’adresse courriel est correcte et réessayer sous peu. Si le problème persiste, contactez notre équipe de support.';
        }
      });
  }
}
