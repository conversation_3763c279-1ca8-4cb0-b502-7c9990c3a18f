<?php

return [

    /*
    |--------------------------------------------------------------------------
    | <PERSON><PERSON> CORS
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for CORS. This determines what
    | cross-origin operations may execute in web browsers. You are free
    | to adjust these settings as needed.
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie','storage/*'],
    'allowed_methods' => ['*'],
    'allowed_origins' => ['*'],
    'allowed_headers' => ['*'],
    'supports_credentials' => true,
];
