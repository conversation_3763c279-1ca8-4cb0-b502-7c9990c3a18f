<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('fiches_transmission', function (Blueprint $table) {
            $table->id();
            $table->string('demande_id'); // ✅ Store demande_id as string
            $table->foreign('demande_id')->references('demande_id')->on('demandes')->onDelete('cascade'); // ✅ Proper FK reference
            $table->string('client_name');
            $table->string('client_nickname');
            $table->date('date_transmission');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('fiches_transmission');
    }
};
