<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResultatClient extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'resultat_client';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'demande_id',
        'rapport_file',
        'facture_file',
        'status',
        'sent_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'sent_at' => 'datetime',
    ];

    /**
     * Get the demande that owns the resultat.
     */
    public function demande()
    {
        return $this->belongsTo(Demande::class, 'demande_id', 'demande_id');
    }
}
