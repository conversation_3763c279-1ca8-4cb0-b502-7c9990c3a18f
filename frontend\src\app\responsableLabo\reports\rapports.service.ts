import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RapportsService {
  private apiUrl = 'http://127.0.0.1:8000/api'; // Replace with your backend URL

  constructor(private http: HttpClient) {}

  createRapport(demandeId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/rapports/${demandeId}/create`, {});
  }

  getRapportDetails(rapportId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/rapports/${rapportId}`);
  }
  updateAnalysis(id: number, updatedData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/update-rapport-analysis/${id}`, updatedData);
  }

    // Fetch all Rapports
    getAllRapports(): Observable<any[]> {
      return this.http.get<any[]>(`${this.apiUrl}/rapports`);
    }

    // Get count of reports with status 'not_sent'
    getNotSentReportsCount(): Observable<number> {
      return this.getAllRapports().pipe(
        map(reports => {
          const notSentReports = reports.filter(report => report.status === 'not_sent');
          return notSentReports.length;
        })
      );
    }



    // Send a Rapport
    sendRapport(rapportId: number): Observable<any> {
      return this.http.put(`http://127.0.0.1:8000/api/rapport/send/${rapportId}`, {}); // ✅ API call to send the report
    }
}
