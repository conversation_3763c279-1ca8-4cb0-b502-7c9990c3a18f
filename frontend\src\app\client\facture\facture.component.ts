import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { DevisFactureService } from '../../services/devis-facture.service';
import { Facture } from '../../../models/facture';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

@Component({
  selector: 'app-facture',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './facture.component.html',
  styleUrls: ['./facture.component.css'],
  providers: [DatePipe] // ✅ Ajout du DatePipe pour la date automatique
})
export class FactureComponent implements OnInit {
  facture: Facture | null = null;
  isLoading: boolean = true;
  hasError: boolean = false;
  currentDate: string = '';

  @ViewChild('factureContent', { static: false }) factureContent!: ElementRef;

  constructor(private factureService: DevisFactureService, private datePipe: DatePipe) {}

  ngOnInit() {
    this.loadFacture();
    this.currentDate = this.datePipe.transform(new Date(), 'dd/MM/yyyy') || ''; // ✅ Ajout de la date automatique
  }

  /**
   * ✅ Récupérer la facture depuis l'API
   */
  private loadFacture() {
    const clientId = 1; // Remplacez avec l'ID réel du client connecté
    this.factureService.getFacture(clientId).subscribe({
      next: (data) => {
        this.facture = data;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('❌ Erreur lors de la récupération de la facture', err);
        this.hasError = true;
        this.isLoading = false;
      }
    });
  }

  downloadFacture() {
    if (!this.factureContent) return;

    const pdf = new jsPDF('p', 'mm', 'a4');
    const element = this.factureContent.nativeElement;

    html2canvas(element, {
      scale: 2.5,          // qualité optimale (bonne clarté sans surcharge)
      useCORS: true,
      allowTaint: true,
    }).then(canvas => {
      const imgData = canvas.toDataURL('image/png');

      // Dimensions A4 en mm
      const pdfWidth = 210;
      const pdfHeight = 297;

      // Calcul dynamique en respectant le ratio image
      const imgProps = pdf.getImageProperties(imgData);
      const imgWidth = pdfWidth - 20; // Marge horizontale de 10mm à gauche/droite
      const imgHeight = (imgProps.height * imgWidth) / imgProps.width;

      // Vérification hauteur pour gérer l'affichage vertical sur une ou plusieurs pages
      let positionY = 10; // Marge verticale supérieure
      pdf.addImage(imgData, 'PNG', 10, positionY, imgWidth, imgHeight);

      pdf.save(`Facture_${this.facture?.id}.pdf`);
    });
  }

}
