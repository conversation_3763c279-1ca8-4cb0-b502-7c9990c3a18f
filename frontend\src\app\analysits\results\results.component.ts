import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSearch, faEraser, faEye, faSpinner, faFileAlt, faUpload, faCheckCircle, faFilter, faThumbtack, faTrash, faArrowLeft, faCalendarAlt, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import { ExcelViewerComponent } from './excel-viewer/excel-viewer.component';

import { ResultsService } from './results.service';
import { DemandeService } from '../../receptionniste/demandes/demande.service';
import { FicheTransmissionService } from '../../responsableLabo/fiche-transmission/fiche.service';

@Component({
  selector: 'app-results',
  standalone: true,
  imports: [CommonModule, FormsModule, NgxPaginationModule, FontAwesomeModule, ExcelViewerComponent],
  templateUrl: './results.component.html',
  styleUrls: ['./results.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // For [spin] attribute on fa-icon
})
export class ResultsComponent implements OnInit {
  // Font Awesome icons
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faSpinner = faSpinner;
  faFileAlt = faFileAlt;
  faUpload = faUpload;
  faCheckCircle = faCheckCircle;
  faFilter = faFilter;
  faThumbtack = faThumbtack;
  faTrash = faTrash;
  faArrowLeft = faArrowLeft;
  faCalendarAlt = faCalendarAlt;
  faExclamationTriangle = faExclamationTriangle;

  // Data properties
  demandes: any[] = [];
  filteredDemandes: any[] = [];
  isLoading = true;
  errorMessage: string | null = null;
  userCache: { [key: number]: { name: string; nickname: string } } = {};

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Filter properties
  searchTerm: string = '';
  selectedStatus: string = '';
  selectedDate: string = '';

  // Excel viewer properties
  showExcelViewer: boolean = false;
  currentExcelData: Blob | null = null;
  currentFileName: string = '';

  // Delete confirmation
  showDeleteConfirmation: boolean = false;
  resultToDelete: any = null;
  isDeleting: boolean = false;

  constructor(
    private readonly demandeService: DemandeService,
    private readonly resultsService: ResultsService,
    private readonly ficheService: FicheTransmissionService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.fetchFichesAndDemandes();
  }

  fetchFichesAndDemandes(): void {
    this.isLoading = true;
    this.demandes = [];

    // First fetch all fiches
    this.ficheService.getAllFiches().subscribe({
      next: (fiches: any) => {
        if (fiches && fiches.length > 0) {
          // Process each fiche to extract demande information
          fiches.forEach((fiche: any) => {
            if (fiche.demande) {
              // Create a demande object with necessary properties
              const demande = {
                ...fiche.demande,
                fiche_id: fiche.id // Store the fiche_id for navigation
              };

              // Add to our demandes array
              this.demandes.push(demande);

              // Fetch user details for this demande
              if (demande.user_id) {
                this.fetchUserName(demande);
              }
            }
          });

          // Update filtered demandes
          this.filteredDemandes = [...this.demandes];
          console.log('Demandes from fiches:', this.demandes);

          if (this.demandes.length === 0) {
            this.errorMessage = 'Aucune demande trouvée.';
          }
        } else {
          this.errorMessage = 'Aucune fiche trouvée.';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching fiches:', error);
        this.errorMessage = 'Échec du chargement des fiches.';
        this.isLoading = false;
      }
    });
  }

  fetchUserName(demande: any): void {
    if (this.userCache[demande.user_id]) {
      demande.userName = this.userCache[demande.user_id].name;
      demande.userNick = this.userCache[demande.user_id].nickname;
      return;
    }

    this.demandeService.getUserDetails(demande.user_id).subscribe({
      next: (user) => {
        this.userCache[demande.user_id] = {
          name: user.name || 'Inconnu',
          nickname: user.nickname || ''
        };
        demande.userName = user.name;
        demande.userNick = user.nickname;
      },
      error: (error) => {
        console.error(`Error fetching user for demande ${demande.demande_id}:`, error);
        demande.userName = 'Inconnu';
        demande.userNick = '';
      }
    });
  }

  // Filter methods
  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page when filter changes
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.demandes];

    // Filter by search term (demande number or client name)
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      const searchTermLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(demande => {
        const demandeIdMatch = demande.demande_id?.toString().toLowerCase().includes(searchTermLower);
        const clientNameMatch = (demande.userName || '').toLowerCase().includes(searchTermLower);
        return demandeIdMatch || clientNameMatch;
      });
    }

    // Filter by status
    if (this.selectedStatus && this.selectedStatus !== '') {
      if (this.selectedStatus === 'with_results') {
        filtered = filtered.filter(demande => demande.result_id);
      } else if (this.selectedStatus === 'without_results') {
        filtered = filtered.filter(demande => !demande.result_id);
      }
    }

    // Filter by reception date
    if (this.selectedDate && this.selectedDate !== '') {
      filtered = filtered.filter(demande => {
        if (!demande.demande_date) return false;

        // Convert both dates to YYYY-MM-DD format for comparison
        const filterDate = new Date(this.selectedDate).toISOString().split('T')[0];
        const demandeDate = new Date(demande.demande_date).toISOString().split('T')[0];

        return demandeDate === filterDate;
      });
    }

    this.filteredDemandes = filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedDate = '';
    this.currentPage = 1;
    this.filteredDemandes = [...this.demandes];
  }

  navigateToDemandeDetails(demande: any): void {
    if (demande.demande_id) {
      this.router.navigate(['/analysits/fichedetails/', demande.fiche_id]);
    } else {
      console.error('Demande ID is missing:', demande);
    }
  }

  uploadResults(demande: any): void {
    if (demande.id) {
      this.router.navigate(['/analysits/results/upload', demande.id]);
    } else {
      console.error('Demande ID is missing:', demande);
    }
  }

  downloadResults(demande: any): void {
    if (demande.result_id) {
      this.resultsService.downloadResults(demande.result_id).subscribe({
        next: (blob) => {
          // Create a URL for the blob
          const url = window.URL.createObjectURL(blob);

          // Create a link element
          const a = document.createElement('a');
          a.href = url;
          a.download = `results_${demande.demande_id}.xlsx`;

          // Append to the document, click it, and remove it
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        },
        error: (error) => {
          console.error('Error downloading results:', error);
        }
      });
    } else {
      console.error('Result ID is missing:', demande);
    }
  }

  // Show delete confirmation dialog
  confirmDeleteResult(demande: any, event: Event): void {
    event.stopPropagation(); // Prevent event bubbling

    if (demande.result_id) {
      this.resultToDelete = demande;
      this.showDeleteConfirmation = true;
    } else {
      console.error('Result ID is missing:', demande);
    }
  }

  // Cancel delete operation
  cancelDelete(): void {
    this.showDeleteConfirmation = false;
    this.resultToDelete = null;
  }

  // Delete result
  deleteResult(): void {
    if (!this.resultToDelete?.result_id) {
      console.error('No result selected for deletion');
      return;
    }

    this.isDeleting = true;

    this.resultsService.deleteResult(this.resultToDelete.result_id).subscribe({
      next: () => {
        // Update the demande to reflect that the result has been deleted
        this.resultToDelete.result_id = null;

        // Update the filtered demandes list
        this.filteredDemandes = this.filteredDemandes.map(demande => {
          if (demande.demande_id === this.resultToDelete?.demande_id) {
            return { ...demande, result_id: null };
          }
          return demande;
        });

        // Update the original demandes list
        this.demandes = this.demandes.map(demande => {
          if (demande.demande_id === this.resultToDelete?.demande_id) {
            return { ...demande, result_id: null };
          }
          return demande;
        });

        // Reset state
        this.showDeleteConfirmation = false;
        this.resultToDelete = null;
        this.isDeleting = false;
      },
      error: (error) => {
        console.error('Error deleting result:', error);
        this.isDeleting = false;
      }
    });
  }

  // View Excel file in the interface
  viewExcelFile(demande: any): void {
    if (demande.result_id) {
      // Create a modal or dialog to display the Excel file
      this.resultsService.downloadResults(demande.result_id).subscribe({
        next: (response) => {
          // Check if the response is a JSON object with a file URL
          if (response instanceof Blob) {
            // Direct blob response
            this.currentExcelData = response;
            this.currentFileName = `Résultats - Demande ${demande.demande_id}`;
            this.showExcelViewer = true;
          } else {
            // Try to parse as JSON if it's not a blob
            try {
              // Convert blob to text and parse as JSON
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const jsonResponse = JSON.parse(reader.result as string);
                  if (jsonResponse.file_url) {
                    // Fetch the actual file from the URL
                    this.fetchExcelFromUrl(jsonResponse.file_url, demande.demande_id);
                  } else {
                    console.error('No file URL found in response:', jsonResponse);
                  }
                } catch (parseError) {
                  console.error('Error parsing JSON response:', parseError);
                }
              };
              reader.readAsText(response);
            } catch (error) {
              console.error('Error processing response:', error);
            }
          }
        },
        error: (error) => {
          console.error('Error viewing Excel file:', error);
        }
      });
    } else {
      console.error('Result ID is missing:', demande);
    }
  }

  // Fetch Excel file from URL
  fetchExcelFromUrl(fileUrl: string, demandeId: string): void {
    // Make sure the URL is absolute
    const url = fileUrl.startsWith('http') ? fileUrl : `${this.resultsService.getBaseUrl()}${fileUrl}`;

    // Fetch the file
    fetch(url)
      .then(response => response.blob())
      .then(blob => {
        this.currentExcelData = blob;
        this.currentFileName = `Résultats - Demande ${demandeId}`;
        this.showExcelViewer = true;
      })
      .catch(error => {
        console.error('Error fetching Excel file from URL:', error);
      });
  }

  // Close Excel viewer
  closeExcelViewer(): void {
    this.showExcelViewer = false;
    this.currentExcelData = null;
  }

  getResultStatus(demande: any): string {
    return demande.result_id ? 'Résultats disponibles' : 'Résultats non disponibles';
  }

  goToDashboard(): void {
    this.router.navigate(['/analysits/dashboard']);
  }
}
