.profile-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.back-btn {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.back-btn:hover {
  background-color: #e9ecef;
}

.profile-header {
  margin-bottom: 30px;
  text-align: center;
}

.profile-title-container {
  display: inline-block;
  position: relative;
}

.profile-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
  padding: 10px 0;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px rgba(46, 204, 113, 0.3), 0 0 10px rgba(46, 204, 113, 0.2);
  }
  to {
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.5), 0 0 20px rgba(52, 152, 219, 0.3);
  }
}

.profile-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-avatar {
  margin-bottom: 20px;
}

.avatar-circle {
  width: 120px;
  height: 120px;
  background-color: #3498db;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
}

.profile-info {
  width: 100%;
  text-align: center;
}

.profile-name {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 5px;
  color: #2c3e50;
}

.profile-role {
  display: inline-block;
  background-color: #2ecc71;
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
}

.info-icon {
  color: #3498db;
  margin-right: 15px;
  font-size: 1.2rem;
  width: 20px;
}

.info-label {
  font-weight: bold;
  color: #7f8c8d;
  width: 120px;
}

.info-value {
  flex: 1;
  color: #2c3e50;
}

.profile-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-password-btn {
  background-color: #2ecc71;
  color: white;
}

.change-password-btn:hover {
  background-color: #27ae60;
}

.notification-modal {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 5px;
  color: white;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification-icon {
  font-size: 1.5rem;
  margin-right: 10px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-card {
    padding: 20px;
  }
  
  .avatar-circle {
    width: 100px;
    height: 100px;
    font-size: 2rem;
  }
  
  .profile-name {
    font-size: 1.5rem;
  }
  
  .info-section {
    gap: 10px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    width: 100%;
    margin-bottom: 5px;
  }
  
  .info-icon {
    margin-bottom: 5px;
  }
}
