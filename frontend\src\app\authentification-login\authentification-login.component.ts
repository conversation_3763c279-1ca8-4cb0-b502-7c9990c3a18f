import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG } from '../app.config';

@Component({
    selector: 'app-authentification-login',standalone: true,
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './authentification-login.component.html',
    styleUrls: ['./authentification-login.component.css']
})
export class AuthentificationLoginComponent implements OnInit {
  loginForm!: FormGroup;
  errorMessage: string | null = null;
  isLoading: boolean = false;

  private loginUrl = `${APP_CONFIG.apiBase}/login`; // Adjust if needed

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required,  Validators.pattern('[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}')]],
      password: ['', [Validators.required, Validators.minLength(8)]]
    });

    const navigation = this.router.getCurrentNavigation();

  }

  navigateTo(path: string): void {
    this.router.navigate([path]);
  }

  loginFormSubmit(): void {
    this.errorMessage = null; // Reset any previous error

    // Stop if form is invalid
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    // Show loading spinner
    this.isLoading = true;

    this.http.post<{
      message: string;
      Token: string;
      role: string;
      user:Array<string>;
    }>(this.loginUrl, this.loginForm.value).subscribe({
      next: (response) => {
        console.log('Login success:', response);
        // Hide loading spinner
        this.isLoading = false;

        // Store token and role in localStorage (or a service)
        localStorage.setItem('token', response.Token);
        localStorage.setItem('user_role', response.role);
        localStorage.setItem('user', JSON.stringify(response.user));
        console.log('Stored user:', response.user);
        // Check the role from the response and navigate
        if (response.role === 'admin') {
          this.router.navigate(['/admin/dashboard']);
        } else if (response.role === 'client') {
          this.router.navigate(['/client/dashboard']);
        }else if (response.role === 'receptionist') {
          this.router.navigate(['/receptionist/dashboard']);
        }else if(response.role === 'director') {
          this.router.navigate(['/director/dashboard']);
        }
        else if (response.role ==='responsable'){
          this.router.navigate(['analysits/dashboard']);
        }

        // Optional: Inform all tabs of the successful login (to sync role)
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'user_role',
          newValue: response.role
        }));
      },
      error: (error) => {
        console.error('Login error:', error);
        // Hide loading spinner
        this.isLoading = false;

        if (error.status === 401) {
          this.errorMessage = 'Email ou mot de passe incorrect.';
        } else {
          this.errorMessage = 'Une erreur est survenue. Veuillez réessayer plus tard.';
        }
      }
    });
  }

}
