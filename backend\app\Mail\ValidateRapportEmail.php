<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ValidateRapportEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $notification;
    public $demande;
    public $user;

    public function __construct($notification, $demande, $user)
    {
        $this->notification = $notification;
        $this->demande = $demande;
        $this->user = $user;
        
        
    }

    public function build()
    {
        return $this->view('emails.validaterapport')
                    ->with([
                        'notification' => $this->notification,
                        'demande' => $this->demande,
                    ]);
    }
    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Validation du Rapport',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.validaterapport',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
