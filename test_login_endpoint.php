<?php
/**
 * Manual Test Script for Login Endpoint
 * 
 * This script demonstrates how to test the login endpoint manually
 * Run this script from the backend directory: php test_login_endpoint.php
 */

// Configuration
$baseUrl = 'http://localhost:8000/api'; // Adjust this to your Laravel server URL
$loginEndpoint = $baseUrl . '/login';

// Test data
$testCases = [
    'valid_login' => [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ],
    'invalid_email' => [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ],
    'invalid_password' => [
        'email' => '<EMAIL>',
        'password' => 'wrongpassword'
    ],
    'missing_email' => [
        'password' => 'password123'
    ],
    'missing_password' => [
        'email' => '<EMAIL>'
    ],
    'invalid_email_format' => [
        'email' => 'invalid-email',
        'password' => 'password123'
    ],
    'short_password' => [
        'email' => '<EMAIL>',
        'password' => '1234567' // 7 characters, less than required 8
    ]
];

/**
 * Function to make HTTP POST request
 */
function makeRequest($url, $data) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status_code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response
    ];
}

/**
 * Function to create a test user (you need to run this first)
 */
function createTestUser() {
    global $baseUrl;
    
    $registerData = [
        'name' => 'Test User',
        'nickname' => 'testuser',
        'email' => '<EMAIL>',
        'phone' => '1234567890',
        'adress' => '123 Test Street',
        'fax' => '0987654321',
        'password' => 'password123',
        'password_confirmation' => 'password123'
    ];
    
    echo "Creating test user...\n";
    $result = makeRequest($baseUrl . '/register', $registerData);
    
    if ($result['status_code'] === 201) {
        echo "✅ Test user created successfully\n\n";
    } else {
        echo "❌ Failed to create test user: " . $result['raw_response'] . "\n\n";
    }
}

/**
 * Function to test login endpoint
 */
function testLoginEndpoint() {
    global $loginEndpoint, $testCases;
    
    echo "=== TESTING LOGIN ENDPOINT ===\n\n";
    
    foreach ($testCases as $testName => $testData) {
        echo "Testing: " . str_replace('_', ' ', ucfirst($testName)) . "\n";
        echo "Data: " . json_encode($testData) . "\n";
        
        $result = makeRequest($loginEndpoint, $testData);
        
        echo "Status Code: " . $result['status_code'] . "\n";
        echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n";
        
        // Validate expected results
        switch ($testName) {
            case 'valid_login':
                if ($result['status_code'] === 201 && isset($result['response']['Token'])) {
                    echo "✅ PASS: Valid login successful\n";
                } else {
                    echo "❌ FAIL: Valid login should return 201 with token\n";
                }
                break;
                
            case 'invalid_email':
            case 'invalid_password':
                if ($result['status_code'] === 401) {
                    echo "✅ PASS: Invalid credentials rejected\n";
                } else {
                    echo "❌ FAIL: Invalid credentials should return 401\n";
                }
                break;
                
            case 'missing_email':
            case 'missing_password':
            case 'invalid_email_format':
            case 'short_password':
                if ($result['status_code'] === 422) {
                    echo "✅ PASS: Validation error returned\n";
                } else {
                    echo "❌ FAIL: Validation should return 422\n";
                }
                break;
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }
}

/**
 * Function to test authenticated endpoint with token
 */
function testAuthenticatedEndpoint($token) {
    global $baseUrl;
    
    echo "=== TESTING AUTHENTICATED ENDPOINT ===\n\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/user');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Testing /api/user endpoint with token\n";
    echo "Status Code: " . $httpCode . "\n";
    echo "Response: " . json_encode(json_decode($response, true), JSON_PRETTY_PRINT) . "\n";
    
    if ($httpCode === 200) {
        echo "✅ PASS: Token authentication successful\n";
    } else {
        echo "❌ FAIL: Token authentication failed\n";
    }
}

// Main execution
echo "Login Endpoint Test Script\n";
echo "========================\n\n";

// Check if server is running
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 0) {
    echo "❌ ERROR: Cannot connect to server at $baseUrl\n";
    echo "Make sure your Laravel server is running: php artisan serve\n";
    exit(1);
}

echo "✅ Server is running at $baseUrl\n\n";

// Create test user first
createTestUser();

// Test login endpoint
testLoginEndpoint();

// Test with valid token
echo "Getting token for authenticated endpoint test...\n";
$validLoginResult = makeRequest($loginEndpoint, $testCases['valid_login']);
if ($validLoginResult['status_code'] === 201 && isset($validLoginResult['response']['Token'])) {
    $token = $validLoginResult['response']['Token'];
    testAuthenticatedEndpoint($token);
} else {
    echo "❌ Could not get valid token for authenticated endpoint test\n";
}

echo "\n=== TEST COMPLETE ===\n";
