@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ---------------------------------
   1) ANIMATIONS
---------------------------------- */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glowText {
  from {
    text-shadow: 0 0 5px rgba(36, 150, 211, 0.5);
  }
  to {
    text-shadow: 0 0 15px rgba(36, 150, 211, 0.8);
  }
}

/* ---------------------------------
   2) CONTENEUR PRINCIPAL
---------------------------------- */
.user-table-container {
  /* Mise en page */
  margin: 40px auto;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  /* Animation d'apparition */
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden; /* Pas de scroll horizontal */
  text-align: center; /* Centre le texte si besoin */
  transition: all 0.4s ease-in-out;
}

/* ---------------------------------
   3) TITRE PRINCIPAL
---------------------------------- */
.user-table-container h2 {
  /* Police Orbitron */
  font-family: 'Orbitron', sans-serif;
  font-size: 24px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  display: inline-block;
  border-bottom: 3px solid #2496d3;

  /* Gradient text (optionnel) */
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: glowText 1.5s infinite alternate;
}

/* ---------------------------------
   4) BARRE DE FILTRAGE
---------------------------------- */
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  max-width: 75%;
  margin-left: auto;
  margin-right: auto;
}

.filter-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-right: 15px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#nameFilter {
  width: 250px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ---------------------------------
   5) BOUTON AJOUTER UTILISATEUR
---------------------------------- */
.add-user-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 20px;
  transition: background-color 0.3s, transform 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-user-btn:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

/* ---------------------------------
   6) TABLEAU DES UTILISATEURS
---------------------------------- */
.user-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* En-tête */
.user-table thead tr th {
  background-color: #2496d3;
  color: #fff;
  padding: 15px;
  text-align: center; /* Center all headers */
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
}

/* Cellules */
.user-table tbody tr td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #f2f2f2;
  transition: background-color 0.3s ease;
}

/* Centrer le contenu des cellules spécifiques */
.user-table tbody tr td.centered {
  text-align: center;
}

/* Survol d'une ligne */
.user-table tbody tr:hover td {
  background-color: #f1f1f1;
}

/* ---------------------------------
   7) BOUTONS D'ACTION
---------------------------------- */
.btn-edit, .btn-delete {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  margin: 0 3px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-width: 90px;
}

.btn-edit {
  background-color: #2496d3;
  color: white;
}

.btn-edit:hover {
  background-color: #1a7bb9;
  transform: translateY(-2px);
}

.btn-delete {
  background-color: #dc3545;
  color: white;
}

.btn-delete:hover {
  background-color: #c82333;
  transform: translateY(-2px);
}

/* ---------------------------------
   8) MESSAGE "AUCUN UTILISATEUR"
---------------------------------- */
.no-users {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 5px;
  color: #6c757d;
  font-size: 16px;
  font-style: italic;
}

/* ---------------------------------
   9) PAGINATION
---------------------------------- */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* ---------------------------------
   10) RESPONSIVE
---------------------------------- */
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    max-width: 95%;
    height: auto;
    padding: 1rem;
  }

  .filter-group {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .filter-group input,
  .filter-group select {
    max-width: 100%;
  }

  .btn-clear {
    margin-top: 0.5rem;
    margin-left: 0;
    width: 100%;
  }

  .user-table-container {
    padding: 20px;
  }

  .user-table thead tr th,
  .user-table tbody tr td {
    padding: 10px;
    font-size: 13px;
  }

  .user-table-container button {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 15px;
  }
}

/* ✅ MODAL */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal h3 {
  margin-top: 0;
  color: #2496d3;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.modal form {
  display: flex;
  flex-direction: column;
}

.modal label {
  margin-top: 15px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal input,
.modal select {
  padding: 10px;
  margin-top: 5px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 14px;
}

.modal input:focus,
.modal select:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

.error {
  color: #dc3545;
  font-size: 12px;
  margin-left: 10px;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
}

.cancel-btn,
.save-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background-color: #5a6268;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

.save-btn:hover {
  background-color: #218838;
}

/* ---------------------------------
   11) LOADING SPINNER
---------------------------------- */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #6c757d;
}

.loading-spinner {
  border: 4px solid rgba(36, 150, 211, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
    width: 90%;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
    margin-top: 10px;
  }

  .user-table {
    font-size: 14px;
  }

  .user-table th, .user-table td {
    padding: 10px;
  }

  .modal-content {
    width: 95%;
    padding: 20px;
  }
}
