<nav class="navbar">
    <div class="navbar-container">
  
      <!-- ✅ Logo + Nom de la plateforme -->
      <a class="navbar-brand" routerLink="/">
        <img src="/logob3sansbackground.png" alt="B3 Aqua Logo" class="navbar-logo"> 
      </a>
  
      <!-- ✅ Navigation Links -->
      <ul class="nav-links">
        <!-- Show these links if role is null (unauthenticated) -->
        <li *ngIf="role === null"><a (click)="navigateTo('/')" class="nav-links-text">Accueil</a></li>
        <li *ngIf="role === null"><a (click)="navigateTo('/')" class="nav-links-text">Services</a></li>
        <li *ngIf="role === null"><a (click)="navigateTo('/')" class="nav-links-text">Analyses</a></li>
        <li *ngIf="role === null"><a (click)="navigateTo('/')" class="nav-links-text">À propos</a></li>
        <li *ngIf="role === null"><a (click)="navigateTo('/')" class="nav-links-text">Contact</a></li>
  
        <!-- Show these links if the user is logged in -->
        
  </ul>
  <ul class="nav-links">
        <!-- Admin specific navbar items -->
        <li *ngIf="role === 'admin'">
          <a (click)="navigateTo('/admin/dashboard')" class="nav-links-text">Admin Dashboard</a>
        </li>
        <li *ngIf="role === 'admin'">
          <a (click)="navigateTo('/admin/users')" class="nav-links-text">Manage Users</a>
        </li>
  
        <!-- Client specific navbar items -->
        <li *ngIf="role === 'client'">
          <a (click)="navigateTo('/client/dashboard')" class="nav-links-text">Client Dashboard</a>
        </li>
        <li *ngIf="role === 'client'">
          <a (click)="navigateTo('/client/orders')" class="nav-links-text">My Orders</a>
        </li>
        <li *ngIf="role !== null"><a (click)="navigateTo('/profile')" class="nav-links-text">Profile</a></li>
  
        <!-- Show the logout button if user is logged in -->
        <li *ngIf="role !== null"><a href="#" (click)="logout()" class="nav-links-text">Logout</a></li>
      </ul>
  
      <!-- ✅ Connexion Button (only for unauthenticated users) -->
      <button (click)="navigateTo('/signup')" class="nav-button" *ngIf="role === null">Connexion</button>
  
    </div>
  </nav>
  