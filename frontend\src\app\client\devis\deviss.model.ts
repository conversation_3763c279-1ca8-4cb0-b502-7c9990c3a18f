export interface Devis {
  id: number;
  analyse: string;
  methode: string;
  prix_unitaire: string;
  quantite: number;
  prix_total: string;
}

export interface Payment {
  id: number;
  demande_id: number;
  user_id: number;
  amount: number;
  payment_proof: string;
  payment_date: string;
  status: string; // 'pending', 'approved', or 'rejected'
  notes?: string;
  created_at: string;
  updated_at: string;
  direct_url?: string;
}

export interface Demande {
  devis_sent?: string;
  demande_id: string;
  demande_date: string;
  total_enregistrements: number;
  status: string;
  devis: Devis[];
  payment_id?: number | null;
  payments?: Payment[];
}