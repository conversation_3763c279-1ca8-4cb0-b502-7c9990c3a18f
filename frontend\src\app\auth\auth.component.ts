
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-auth',standalone: true,
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './auth.component.html',
    styleUrl: './auth.component.css'
})
export class AuthComponent {
  loginForm: FormGroup;
  registerForm: FormGroup;
  isRegisterMode = false;
  errorMessage: string = '';

  constructor(private fb: FormBuilder, private router: Router) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    this.registerForm = this.fb.group({
      nom: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  toggleMode() {
    this.isRegisterMode = !this.isRegisterMode;
  }

  loginFormSubmit() {
    if (this.loginForm.valid) {
      const formData = this.loginForm.value;
      // Simuler une authentification (à remplacer par une API)
      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
        this.router.navigate(['/admin/dashboard']);
      } else {
        this.errorMessage = 'Email ou mot de passe incorrect.';
      }
    }
  }

  registerFormSubmit() {
    if (this.registerForm.valid) {
      // Simuler une inscription (à remplacer par une API)
      console.log('Inscription réussie:', this.registerForm.value);
      this.toggleMode(); // Revenir à la page de connexion
    }
  }
}
