import { Component, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-analyst',
  standalone: true,
  imports: [CommonModule, RouterOutlet],
  templateUrl: './analyst.component.html',
  styleUrl: './analyst.component.css'
})
export class AnalystComponent implements OnInit {
 role: string | null = null;
  name: string | null = null;
  user: [] | null = null; 
  userEmail: string | null = null; 
  constructor(

    private router: Router
  ) {}

  ngOnInit() {
    this.userEmail = this.getUserProperty('email')
    this.name=this.getUserProperty('name')
  }

  getUserProperty(key: string): any {
    const userData = localStorage.getItem('user');
    
    if (userData) {
      try {
        const user = JSON.parse(userData);
        return user[key] ?? null; // Return the value if exists, otherwise null
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    }

    return null;
  }
 
  manageUsers() {
    this.router.navigate(['/analyst/analyse']);
  }
  manageReports() {
    
  }
  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user_role');
    localStorage.removeItem('user');
    this.role = null;
    this.user=[];
    this.router.navigate(['/login']);
 }
}
