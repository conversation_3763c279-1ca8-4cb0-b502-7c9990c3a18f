// src/app/services/derogation.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Derogation,DerogationResponse } from './derogation.model';


@Injectable({
  providedIn: 'root'
})
export class DerogationService {
  private apiUrl = 'http://127.0.0.1:8000/api/derogations'; 
  private demandeURL = 'http://127.0.0.1:8000/api/demandes'; 
  constructor(private http: HttpClient) {}

  // Fetch derogation details by demande_id
  getDerogationDetails(demandeId: string): Observable<DerogationResponse> {
    const url = `${this.apiUrl}/${demandeId}`; // Matches your route: /derogations/{demande_id}

    return this.http.get<DerogationResponse>(url);
  }

  getAllDerogations(): Observable<Derogation[]> {
    return this.http.get<{ data: Derogation[] }>(this.apiUrl).pipe(
      map(response => {
        console.log('API Response:', response); // Debug log
        if (response && response.data) {
          return response.data;
        } else {
          throw new Error('Invalid API response format: No data property found.');
        }
      }),
      catchError(error => {
        console.error('Error fetching derogations:', error);
        return throwError(() => new Error('Erreur lors du chargement des dérogations. Veuillez réessayer.'));
      })
    );
  }
  getDemandeById(identifier: number | string): Observable<any> {
    return this.http.get<any>(`${this.demandeURL}/${identifier}`);
  }
}