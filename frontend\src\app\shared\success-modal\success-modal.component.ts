import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-success-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './success-modal.component.html',
  styleUrls: ['./success-modal.component.css']
})
export class SuccessModalComponent {
  @Input() isVisible: boolean = false;
  @Input() title: string = 'Succès!';
  @Input() message: string = 'Votre compte a été créé avec succès.';
  @Input() buttonText: string = 'Se connecter';
  @Input() redirectPath: string = '/login';
  
  @Output() onClose = new EventEmitter<void>();
  @Output() onConfirm = new EventEmitter<void>();

  constructor(private router: Router) {}

  closeModal(): void {
    this.onClose.emit();
  }

  confirmAction(): void {
    this.onConfirm.emit();
    this.router.navigate([this.redirectPath]);
  }

  // Close modal when clicking on backdrop
  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.closeModal();
    }
  }
}
