/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* Container Styles */
.fiche-details-container {
  font-family: 'Montserrat', sans-serif;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 1rem auto;
  max-width: 1200px;
}

/* 2. HEADER */
.fiches-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.fiches-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 4px solid #2496d3;
  padding-bottom: 0.25rem;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, #000, #888);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* Style for the icon inside the title to make it visible */
.fiches-title fa-icon {
  color: #000000; /* Blue color to match the border */

}

.back-btn, .print-btn {
  color: #fff;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  transition: transform 0.3s, box-shadow 0.3s;
  font-weight: bold;
}

.back-btn {
  background: #007bff;
}

.print-btn {
  background: #28a745;
}

.back-btn:hover, .print-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.print-btn:hover {
  background: #218838;
}

.back-btn:hover {
  background: #0069d9;
}

/* Glow animation */
@keyframes glowText {
  from { text-shadow: 0 0 8px rgba(36,150,211,0.4); }
  to   { text-shadow: 0 0 16px rgba(36,150,211,0.8); }
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

thead {
  background: linear-gradient(to right, #2496d3, #0a6aa1);
  color: white;
}

th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

tbody tr {
  background-color: white;
  transition: background-color 0.3s;
}

tbody tr:hover {
  background-color: #f1f3f5;
}

tbody tr:last-child td {
  border-bottom: none;
}

/* List Styles */
ul {
  margin: 0;
  padding-left: 1.2rem;
}

li {
  margin-bottom: 0.3rem;
}

li:last-child {
  margin-bottom: 0;
}

/* Message Styles */
.loading-message, .error, .no-data {
  text-align: center;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.loading-message {
  background-color: #e9f5fe;
  color: #2496d3;
}

.error {
  background-color: #ffe9e9;
  color: #dc3545;
}

.no-data {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fiche-details-container {
    padding: 1.5rem;
  }

  .fiches-header {
    flex-direction: column;
    gap: 1rem;
  }

  table {
    display: block;
    overflow-x: auto;
  }

  th, td {
    padding: 0.8rem;
  }
}

/* =======================
   RAPPORT NOTES SECTION
======================= */
.rapport-notes-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #fff5f5;
  border: 2px solid #f56565;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(245, 101, 101, 0.15);
  animation: fadeIn 0.5s ease-in-out;
}

.rapport-notes-title {
  color: #e53e3e;
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 2px solid #fed7d7;
  padding-bottom: 0.5rem;
}

.notes-icon {
  color: #e53e3e;
  font-size: 22px;
}

.rapport-notes-container {
  background-color: #ffffff;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.rapport-info {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.rapport-info p {
  margin: 0.5rem 0;
  font-size: 14px;
  color: #2d3748;
}

.rapport-info strong {
  color: #1a202c;
  font-weight: 600;
}

.status-approved {
  color: #28a745;
  font-weight: bold;
  background-color: rgba(40, 167, 69, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.status-rejected {
  color: #dc3545;
  font-weight: bold;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.status-pending {
  color: #ffc107;
  font-weight: bold;
  background-color: rgba(255, 193, 7, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.rapport-notes-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
}

.rapport-notes-content h4 {
  margin: 0 0 0.5rem 0;
  color: #e53e3e;
  font-size: 16px;
  font-weight: 600;
}

.rapport-notes-content p {
  margin: 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.no-notes {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 1rem;
}

.no-notes p {
  margin: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments for rapport notes */
@media (max-width: 768px) {
  .rapport-notes-section {
    padding: 1rem;
    margin: 1.5rem 0;
  }

  .rapport-notes-title {
    font-size: 18px;
  }

  .rapport-notes-container {
    padding: 1rem;
  }
}
