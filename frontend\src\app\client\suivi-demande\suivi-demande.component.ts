import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClientModule, HttpErrorResponse } from '@angular/common/http';
import { finalize } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Demande } from '../devis/deviss.model';
import { DemandeService } from '../devis/demande.service';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faEye, faCircleCheck, faCircleXmark, faCircleExclamation, faSpinner, faCircleDot, faFilter, faSearch, faCalendarAlt, faEraser } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-suivi-demande',
  standalone: true,
  imports: [CommonModule, HttpClientModule, FontAwesomeModule, FormsModule, NgxPaginationModule],
  templateUrl: './suivi-demande.component.html',
  styleUrl: './suivi-demande.component.css'
})
export class SuiviDemandeComponent implements OnInit {
  // Font Awesome icons
  faEye = faEye;
  faCircleCheck = faCircleCheck;
  faCircleXmark = faCircleXmark;
  faCircleExclamation = faCircleExclamation;
  faSpinner = faSpinner;
  faCircleDot = faCircleDot;
  faFilter = faFilter;
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faEraser = faEraser;

  // Data properties
  isLoading: boolean = true;
  hasError: boolean = false;
  demandes: Demande[] = [];
  filteredDemandes: Demande[] = [];

  // Filtering properties
  searchTerm: string = '';
  selectedDate: string = '';
  selectedStatus: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Status translations
  statusTranslations: { [key: string]: string } = {
    pending: 'En attente',
    ongoing_validation: 'Validation en cours',
    valid: 'Valide',
    derogation: 'Déroger',
    rejected: 'Rejeté'
  };

  constructor(private devisService: DemandeService, private router: Router) {}

  ngOnInit() {
    this.fetchDevis();
  }

  fetchDevis() {
    console.log('Fetching devis data...');
    this.isLoading = true;
    this.hasError = false;

    this.devisService.getUserDevis().pipe(
      finalize(() => {
        console.log('Request completed.');
      })
    ).subscribe({
      next: (data) => {
        console.debug('Received data:', data);
        console.log('Data type:', typeof data);
        if (Array.isArray(data)) {
          console.log('Data length:', data.length);
          console.log('First item:', data[0]);
          this.demandes = data;
          this.filteredDemandes = [...this.demandes]; // Initialize filtered demandes
        } else {
          this.demandes = [];
          this.filteredDemandes = [];
        }
        this.isLoading = false;
        console.log('Fetch complete. isLoading:', this.isLoading);
      },
      error: (error: HttpErrorResponse) => {
        console.error('Error fetching devis:', error);
        console.error('Error status:', error.status);
        console.error('Error message:', error.message);
        console.error('Full error object:', error);
        if (error.error instanceof ErrorEvent) {
          console.error('Client-side error:', error.error.message);
        } else {
          console.error('Server-side error:', error.error);
        }
        this.hasError = true;
        this.isLoading = false;
        console.log('Fetch failed. hasError:', this.hasError, 'isLoading:', this.isLoading);
      }
    });
  }
  getTotalPrice(demande: any): number {
    if (demande.devis && Array.isArray(demande.devis)) {
      return demande.devis.reduce((sum: number, item: any) => sum + parseFloat(item.prix_total), 0);
    }
    return 0;
  }
  navigateToDetails(demandeId: string, event?: Event) {
    // If event exists, stop propagation to prevent row click
    if (event) {
      event.stopPropagation();
    }
    this.router.navigate(['/demandeClient', demandeId]);
  }

  // Filtering methods
  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page when filter changes
    const term = this.searchTerm.toLowerCase();
    const status = this.selectedStatus;
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredDemandes = this.demandes.filter((demande) => {
      const matchesSearch = demande.demande_id.toLowerCase().includes(term);
      const matchesStatus = status ? demande.status === status : true;
      const demandeDate = new Date(demande.demande_date);
      const matchesDate = filterDate ? demandeDate.toDateString() === filterDate.toDateString() : true;

      return matchesSearch && matchesStatus && matchesDate;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.selectedStatus = '';
    this.filteredDemandes = [...this.demandes];
    this.currentPage = 1;
  }

  getStatusTranslation(status: string): string {
    return this.statusTranslations[status] || status;
  }
}
