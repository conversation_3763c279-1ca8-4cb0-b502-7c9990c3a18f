<?php
// This is a simple test script to verify the file naming convention
// You can run this script to see the file names that would be generated

$demandeId = '0001-101';
$fileType = 'report'; // or 'invoice'

// Old naming convention
$oldFileName = $fileType . '_' . $demandeId . '_' . time() . '.pdf';
echo "Old file name: " . $oldFileName . "\n";

// New naming convention
$filePrefix = $fileType === 'report' ? 'rapport' : 'facture';
$newFileName = $filePrefix . '_' . $demandeId . '.pdf';
echo "New file name: " . $newFileName . "\n";
