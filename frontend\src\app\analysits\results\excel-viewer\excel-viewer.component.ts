import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import * as XLSX from 'xlsx';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faTimes, faDownload } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-excel-viewer',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './excel-viewer.component.html',
  styleUrls: ['./excel-viewer.component.css']
})
export class ExcelViewerComponent implements OnInit {
  @Input() excelData: Blob | null = null;
  @Input() fileName: string = 'Excel File';
  @Output() close = new EventEmitter<void>();

  faTimes = faTimes;
  faDownload = faDownload;
  isLoading: boolean = true;
  error: string | null = null;
  headers: string[] = [];
  data: any[][] = [];
  sheets: string[] = [];
  activeSheet: string = '';
  fileUrl: string | null = null;

  constructor() {}

  ngOnInit(): void {
    this.loadExcelData();
  }

  loadExcelData(): void {
    if (!this.excelData) {
      this.error = 'No Excel data provided';
      this.isLoading = false;
      return;
    }

    // First check if the blob is actually JSON
    this.checkIfJson(this.excelData, (isJson, jsonData) => {
      if (isJson && jsonData) {
        // Handle JSON response
        this.handleJsonResponse(jsonData);
      } else {
        // Handle as Excel file
        this.parseExcelFile(this.excelData as Blob);
      }
    });
  }

  checkIfJson(blob: Blob, callback: (isJson: boolean, data?: any) => void): void {
    const reader = new FileReader();
    reader.onload = () => {
      try {
        const text = reader.result as string;
        // Try to parse as JSON
        const json = JSON.parse(text);
        callback(true, json);
      } catch (e) {
        // Not JSON, probably an Excel file
        callback(false);
      }
    };
    reader.onerror = () => callback(false);
    reader.readAsText(blob);
  }

  handleJsonResponse(jsonData: any): void {
    // Check if there's a file URL in the JSON
    if (jsonData.file_url) {
      const fileUrl = jsonData.file_url as string;
      this.fileUrl = fileUrl;

      // Show loading message while fetching the file
      this.isLoading = true;
      this.error = null;

      // Fetch the Excel file from the URL
      this.fetchExcelFile(fileUrl);
    } else {
      // If no file URL, just display the JSON data
      this.displayJsonData(jsonData);
      this.isLoading = false;
    }
  }

  displayJsonData(jsonData: any): void {
    // Display JSON data in a readable format
    this.headers = ['Key', 'Value'];

    // Format the data for better display
    this.data = Object.entries(jsonData).map(([key, value]) => {
      let displayValue = value;

      // Format the value based on its type
      if (typeof value === 'object' && value !== null) {
        try {
          displayValue = JSON.stringify(value, null, 2);
        } catch (e) {
          displayValue = String(value);
        }
      } else if (typeof value === 'string' && value.startsWith('http')) {
        // If it's a URL, make it clickable
        displayValue = `<a href="${value}" target="_blank">${value}</a>`;
      }

      return [key, displayValue];
    });
  }

  fetchExcelFile(url: string): void {
    // Make sure the URL is absolute
    const absoluteUrl = url.startsWith('http') ? url : `http://127.0.0.1:8000${url}`;

    // Try to fetch using fetch API first (might fail due to CORS)
    this.fetchWithFetch(absoluteUrl).catch(() => {
      // If fetch fails, try using XMLHttpRequest which might handle CORS differently
      this.fetchWithXHR(absoluteUrl);
    });
  }

  fetchWithFetch(url: string): Promise<void> {
    return fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
        }
        return response.blob();
      })
      .then(blob => {
        // Process the Excel file
        this.excelData = blob;
        this.parseExcelFile(blob);
      })
      .catch(error => {
        console.error('Error fetching Excel file with fetch API:', error);
        throw error; // Rethrow to try the next method
      });
  }

  fetchWithXHR(url: string): void {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';

    xhr.onload = () => {
      if (xhr.status === 200) {
        const blob = xhr.response;
        this.excelData = blob;
        this.parseExcelFile(blob);
      } else {
        this.handleFetchError(new Error(`Failed to fetch file: ${xhr.status} ${xhr.statusText}`), url);
      }
    };

    xhr.onerror = () => {
      this.handleFetchError(new Error('Network error occurred'), url);
    };

    xhr.send();
  }

  handleFetchError(error: Error, url: string): void {
    console.error('Error fetching Excel file:', error);
    this.error = `Erreur lors du chargement du fichier Excel: ${error.message}`;
    this.isLoading = false;

    // Fall back to displaying JSON if we can't fetch the Excel file
    if (this.data.length === 0) {
      this.displayJsonData({ file_url: url, error: error.message });
    }
  }

  parseExcelFile(blob: Blob): void {
    const reader = new FileReader();

    reader.onload = (e: any) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        // Get all sheet names
        this.sheets = workbook.SheetNames;

        if (this.sheets.length === 0) {
          this.error = 'No sheets found in the Excel file';
          this.isLoading = false;
          return;
        }

        // Set the first sheet as active by default
        this.activeSheet = this.sheets[0];
        this.loadSheet(this.activeSheet);

        this.isLoading = false;
      } catch (error) {
        console.error('Error parsing Excel file:', error);
        this.error = 'Failed to parse Excel file. The file might be corrupted or in an unsupported format.';
        this.isLoading = false;
      }
    };

    reader.onerror = () => {
      this.error = 'Failed to read the Excel file';
      this.isLoading = false;
    };

    reader.readAsArrayBuffer(blob);
  }

  loadSheet(sheetName: string): void {
    if (!this.excelData) {
      this.error = 'No Excel data provided';
      return;
    }

    try {
      const reader = new FileReader();

      reader.onload = (e: any) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });

          // Get the worksheet
          const worksheet = workbook.Sheets[sheetName];

          // Convert the worksheet to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          if (jsonData.length === 0) {
            this.headers = [];
            this.data = [];
            return;
          }

          // The first row contains the headers
          this.headers = jsonData[0] as string[];

          // The rest of the rows contain the data
          this.data = jsonData.slice(1) as any[][];

          this.activeSheet = sheetName;
        } catch (error) {
          console.error('Error processing sheet:', error);
          this.error = `Failed to process sheet "${sheetName}"`;
        }
      };

      reader.onerror = () => {
        this.error = 'Failed to read the Excel file';
      };

      // We've already checked that excelData is not null above
      reader.readAsArrayBuffer(this.excelData);
    } catch (error) {
      console.error('Error loading sheet:', error);
      this.error = `Failed to load sheet "${sheetName}"`;
    }
  }

  closeViewer(): void {
    this.close.emit();
  }

  downloadFile(): void {
    if (!this.fileUrl) return;

    // Create a link element
    const a = document.createElement('a');
    a.href = this.fileUrl;
    a.download = this.fileName.replace('Résultats - ', 'Results_') + '.xlsx';

    // Append to the document, click it, and remove it
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }
}
