<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    /**
     * Display a listing of all users.
     */
    public function index()
    {
        $users = User::where('id', '!=', Auth::id())->get();
        return response()->json($users, 200);
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone'=>['required', 'string','max:255'],
            'adress'=> ['required', 'string','max:255'],
            'fax'=> ['nullable', 'string','max:255'],
            'role' => 'required|in:admin,client,receptionist,responsable',
        ]);
        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone'=> $request->phone,
            'adress'=> $request->adress,
            'fax'=> $request->fax,
            'role' => $request->role,
        ]);
        return response()->json('account created successfully',201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        return response()->json($user, 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        // Validate the request
        $validatedData = $request->validate([
            'role' => 'required|in:admin,client,receptionist,responsable',
        ]);
    
        // Fetch the user
        $user = User::find($id);
    
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }
    
        // Update the role and force save
        $user->role = $validatedData['role'];
        $saved = $user->save();
    
        if (!$saved) {
            return response()->json(['message' => 'Failed to update user'], 500);
        }
    
        return response()->json(['message' => 'User updated successfully'], 200);
    }
    
    

    
    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = User::find($id);
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->delete();
        return response()->json(['message' => 'User deleted successfully'], 200);
    }
    
}
