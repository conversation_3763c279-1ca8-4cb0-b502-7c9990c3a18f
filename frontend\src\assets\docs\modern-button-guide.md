# Modern Button Styling Guide

This guide explains how to implement the new modern button styling system in your application.

## Overview

The new button system uses:
- CSS variables for consistent theming
- Modern design principles (clean, minimal, purposeful)
- Accessibility-focused contrast ratios
- Subtle animations and micro-interactions
- Consistent sizing and spacing

## Implementation Steps

### 1. Import the Button System CSS

Add this import to your `angular.json` file in the styles array:

```json
"styles": [
  "src/styles.css",
  "src/assets/css/button-system.css",
  // other styles...
]
```

Or import it directly in your global styles.css:

```css
@import './assets/css/button-system.css';
```

### 2. Update Button Classes

Replace your current button classes with the new system classes:

#### Basic Buttons

| Purpose | Old Class | New Class |
|---------|-----------|-----------|
| Primary action | `btn-view-details` | `btn btn-primary` |
| Secondary action | `btn-detail` | `btn btn-secondary` |
| Success/Create | `btn-create`, `btn-add` | `btn btn-success` |
| Danger/Delete | - | `btn btn-danger` |
| Warning | - | `btn btn-warning` |

#### Alternative Styles

| Style | Class |
|-------|-------|
| Outline buttons | `btn btn-outline btn-outline-primary` |
| Ghost buttons | `btn btn-ghost btn-ghost-primary` |
| Glass effect | `btn btn-glass` |
| Soft UI effect | `btn btn-soft` |

#### Button Sizes

| Size | Class |
|------|-------|
| Small | `btn-sm` |
| Default | - |
| Large | `btn-lg` |

### 3. Loading State

For buttons with loading states, use:

```html
<button class="btn btn-primary" [class.btn-loading]="isLoading" [disabled]="isLoading">
  <span class="icon"><fa-icon [icon]="faIcon"></fa-icon></span>
  <span class="text">Button Text</span>
</button>
```

### 4. Example Button Implementations

#### View Details Button
```html
<button class="btn btn-primary">
  <span class="icon"><fa-icon [icon]="faEye"></fa-icon></span>
  <span class="text">Voir détails</span>
</button>
```

#### Create Button
```html
<button class="btn btn-success">
  <span class="icon"><fa-icon [icon]="faPlus"></fa-icon></span>
  <span class="text">Créer</span>
</button>
```

#### View Document Button
```html
<button class="btn btn-secondary">
  <span class="icon"><fa-icon [icon]="faFileAlt"></fa-icon></span>
  <span class="text">Voir document</span>
</button>
```

#### Delete Button
```html
<button class="btn btn-danger">
  <span class="icon"><fa-icon [icon]="faTrash"></fa-icon></span>
  <span class="text">Supprimer</span>
</button>
```

### 5. Conditional Styling

For buttons that change appearance based on state:

```html
<button 
  class="btn" 
  [ngClass]="{
    'btn-success': !item.id,
    'btn-secondary': item.id,
    'btn-loading': isCreating
  }"
  [disabled]="isCreating"
>
  <span class="icon">
    <fa-icon [icon]="faIcon" [spin]="isCreating"></fa-icon>
  </span>
  <span class="text">
    {{ isCreating ? 'Création en cours...' : (item.id ? 'Voir' : 'Créer') }}
  </span>
</button>
```

## Customization

### Changing Colors

To customize the color scheme, modify the CSS variables in the `:root` section of the `button-system.css` file:

```css
:root {
  --primary-500: #2196f3;  /* Change this to your primary color */
  --success-500: #4caf50;  /* Change this to your success color */
  /* etc. */
}
```

### Adding New Button Types

To add a new button type:

1. Add new CSS variables for your color palette
2. Create new button variant classes following the existing pattern

## Best Practices

1. **Consistency**: Use the same button style for the same action across the application
2. **Hierarchy**: Use button styles to indicate importance (primary actions vs secondary actions)
3. **Feedback**: Always provide visual feedback for button interactions
4. **Accessibility**: Ensure buttons have sufficient contrast and clear labels
5. **Responsiveness**: Test buttons on different screen sizes

## Example Implementation for validation.component.html

Here's how to update the buttons in the validation component:

```html
<!-- Bouton : Voir les détails -->
<button class="btn btn-primary" (click)="navigateToDemandeDetails(demande)">
  <span class="icon"><fa-icon [icon]="faEye"></fa-icon></span>
  <span class="text">Voir détails</span>
</button>

<!-- Bouton : Voir/Créer la fiche -->
<button
  class="btn"
  [ngClass]="{
    'btn-loading': demande.isCreatingFiche,
    'btn-secondary': demande.fiche_id,
    'btn-success': !demande.fiche_id
  }"
  (click)="viewFicheTransmission(demande)"
  [disabled]="demande.isCreatingFiche"
>
  <span class="icon">
    <fa-icon [icon]="faFileAlt" [spin]="demande.isCreatingFiche"></fa-icon>
  </span>
  <span class="text">
    {{ demande.isCreatingFiche ? 'Création en cours...' : (demande.fiche_id ? 'Voir fiche' : 'Créer fiche') }}
  </span>
</button>
```
