import { Component } from '@angular/core';
import { AboutComponent } from '../about/about.component';
import { Router, RouterOutlet } from '@angular/router';
@Component({
    selector: 'app-presentation',
    imports: [AboutComponent,RouterOutlet],
    standalone: true,
    templateUrl: './presentation.component.html',
    styleUrl: './presentation.component.css'
})
export class PresentationComponent {
    constructor(private router: Router) {}
    navigateTo(path: string): void {
        this.router.navigate([path]);
      }
}
