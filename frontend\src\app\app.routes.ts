import { Routes } from '@angular/router';

// 📌 Pages principales
import { HomeComponent } from './home/<USER>';
import { ServicesComponent } from './services/services.component';
import { PresentationComponent } from './presentation/presentation.component';
import { ExpertiseComponent } from './expertise/expertise.component';
import { TarifComponent } from './tarif/tarif.component';
import { ContactComponent } from './contact/contact.component';
import { AboutComponent } from './about/about.component';
import { AcceptTableComponent } from './accept-table/accept-table.component';


// 📌 Authentification
import { AuthentificationSingupComponent } from './authentification-singup/authentification-singup.component';
import { AuthentificationLoginComponent } from './authentification-login/authentification-login.component';
import { AnalystComponent } from './analyst/analyst.component';
// 📌 Interface Administrateur
import { AdminComponent } from './admin/admin.component';
import { UsersComponent } from './admin/users/users.component';


// 📌 Interface Analyse
import { AnalyseComponent } from './analyse/analyse.component';

// 📌 Interface Client
import { DemandesComponent as ClientDemandesComponent } from './client/demandes/demandes.component';
import { FacturationComponent } from './receptionniste/facturation/facturation.component';
import { DevisComponent } from './client/devis/devis.component';
import { FactureComponent } from './client/facture/facture.component';
import { PaiementComponent } from './client/paiement/paiement.component';


// 📌 Interface Réceptionniste
import { DashboardComponent as ReceptionnisteDashboardComponent } from './receptionniste/dashboard/dashboard.component';
import { DemandesComponent as ReceptionnisteDemandesComponent } from './receptionniste/demandes/demandes.component';

import { ValidationComponent } from './receptionniste/validation/validation.component';
import { AnalysesComponent } from './analyst/analyses/analyses.component';
import { ClientDashboardComponent } from './client/client-dashboard/client-dashboard.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { FicheDerogationComponent } from './receptionniste/fiche-derogation/fiche-derogation.component';
import { FicheTransmissionComponent } from './receptionniste/fiche-transmission/fiche-transmission.component';
import { NotificationsComponent } from './receptionniste/notifications/notification/notification.component';
import { DemandeDetailsComponent } from './receptionniste/demande-details/demande-details.component';
import { NotificationClientComponent } from './client/notification-client/notification-client.component';
import { DemandeDetailsClientComponent } from './client/demande-details/demande-details.component';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { DashboardDirectorComponent } from './directeur/dashboard/dashboard.component';
import { NotificationsDirectorComponent } from './directeur/notifications/notification/notification.component';
import { DerogationComponent } from './directeur/derogations/derogation.component';
import { DerogationListComponent } from './directeur/derogation-list/derogation-list.component';
import { RapportDanalyseComponent } from './responsableLabo/rapport-danalyse/rapport-danalyse.component';
import { ReportsComponent } from './directeur/reports/reports.component';
import { ManageRequestsComponent } from './directeur/manage-requests/manage-requests.component';
import { FicheListComponent } from './receptionniste/fiche-transmission-list/fiche-list/fiche-list.component';
import { RegistreSuiviListComponent } from './receptionniste/registres-list/registres.component';
import { RegistreSuiviDetailComponent } from './receptionniste/registre/registre.component';

import { RapportsListComponent } from './responsableLabo/reports/reports.component';
import { FicheTransmissionDetailsComponent } from './responsableLabo/fiche-transmission/fiche-transmission.component';
import { FichesListComponent } from './responsableLabo/fiche-transmission-list/fiche-list/fiche-list.component';
import { RapportsReceptionistComponent } from './receptionniste/rapports/rapports.component';
import { RapportDanalyseDetailsComponent } from './receptionniste/rapport-details/rapport-details.component';
import { RapportsListDirectorComponent } from './directeur/rapports-list/rapports-list.component';
import { RapportsDetailsDirectorComponent } from './directeur/rapports-details/rapports-details.component';
import { FactureListComponent } from './receptionniste/facture-list/facture-list.component';
import { SuiviDemandeComponent } from './client/suivi-demande/suivi-demande.component';
import { NotificationsResponsableComponent } from './responsableLabo/notifications/notification/notification.component';
import { UsersComponent as ReceptionnisteUsersComponent } from './receptionniste/users/users.component';
import { ProfileComponent as ReceptionnisteProfileComponent } from './receptionniste/profile/profile.component';
import { ResultsListComponent } from './client/results-list/results-list.component';
import { ResultsComponent as ReceptionnisteResultsComponent } from './receptionniste/results/results.component';
import { SendResultsComponent } from './receptionniste/results/send-results/send-results.component';
import { ResultatsComponent } from './client/resultats/resultats.component';
import { ReclamationComponent } from './client/reclamation/reclamation.component';
import { ReclamationsComponent } from './client/reclamations/reclamations.component';
import { ReclamationsListComponent } from './receptionniste/reclamations-list/reclamations-list.component';
import { ReclamationDetailsComponent } from './receptionniste/reclamation-details/reclamation-details.component';

// 📌 Interface Analyste
import { AnalystDashboardComponent } from './analysits/dashboard/dashboard.component';
import { AnalystFicheTransmissionComponent } from './analysits/fiche-transmission/fiche-transmission.component';
import { AnalystFicheDetailsComponent } from './analysits/fiche-details/fiche-details.component';
import { ResultsComponent } from './analysits/results/results.component';
import { UploadResultsComponent } from './analysits/results/upload/upload.component';

/** ✅ Routes Structurées */
export const routes: Routes = [
  // 📌 Routes Générales
  { path: '', component: HomeComponent },
  { path: 'services', component: ServicesComponent },
  { path: 'presentation', component: PresentationComponent },
  { path: 'expertise', component: ExpertiseComponent },
  { path: 'tarif', component: TarifComponent },
  { path: 'contact', component: ContactComponent },
  { path: 'about', component: AboutComponent },
  {path:'resetPassword',component:ResetPasswordComponent},
  // 📌 Authentification
  { path: 'signup', component: AuthentificationSingupComponent },
  { path: 'login', component: AuthentificationLoginComponent },

  // 📌 Administration
  { path: 'admin/dashboard', component: AdminComponent },
  { path: 'admin/users', component: UsersComponent },

  { path: 'accept-table', component: AcceptTableComponent },
  { path: 'analysta', component: AnalysesComponent },

  // 📌 Analyses
  { path: 'analyse', component: AnalyseComponent },
  { path: 'analyse/tarif', component: AnalyseComponent },
  // 📌 Client
  { path: 'client/dashboard', component: ClientDashboardComponent },
  { path: 'client/demandes', component: ClientDemandesComponent },
  { path: 'client/facturation', component: FacturationComponent },
  { path: 'client/devis', component: DevisComponent },
  { path: 'client/suivi', component: SuiviDemandeComponent },
  { path: 'client/results', component: ResultsListComponent },
  { path: 'client/reclamation', component: ReclamationComponent },
  { path: 'client/reclamations', component: ReclamationsComponent },
  { path: 'client/reclamation-details/:id', component: ReclamationDetailsComponent },
  { path: 'resultats/:id', component: ResultatsComponent },
  { path: 'paiement/:demande_id', component: PaiementComponent },

  { path: 'receptionist/facture/:id', component: FactureComponent },
  // 📌 Réceptionniste
  { path: 'receptionist/dashboard', component: ReceptionnisteDashboardComponent },
  { path: 'receptionist/demandes', component: ReceptionnisteDemandesComponent },

  { path: 'receptionist/validation', component: ValidationComponent },
  { path: 'receptionist/registre', component: RegistreSuiviListComponent },
  { path: 'receptionist/registres/:id', component: RegistreSuiviDetailComponent },
  { path: 'receptionist/fiche-transmission', component: FicheListComponent },
  { path: 'receptionist/fichedetails/:id', component: FicheTransmissionComponent },
  { path: 'fiche-transmission', component: FicheListComponent },
  {path:'receptionist/notifications',component:NotificationsComponent},
  { path: 'derogation-details/:demande_id', component: FicheDerogationComponent },


  { path: 'responsable/rapportslists', component: RapportsListComponent },
  { path: 'analyst/analyse', component: AnalysesComponent },
  { path: 'responsable/notifications', component: NotificationsResponsableComponent },
  { path: 'change-password', component: ChangePasswordComponent },
  { path: 'demande/:demande_id', component: DemandeDetailsComponent },
  { path: 'derogation/:demande_id', component: DerogationComponent },
  { path: 'demandeClient/:demande_id', component: DemandeDetailsClientComponent },
  {path:'notifications',component:NotificationsComponent},
  {
    path: 'client',
    children: [
      {
        path: 'BringSample',
        children: [
          { path: 'notifications', component: NotificationClientComponent },
        ],
      },
    ],
  },
  { path: 'director/dashboard', component: DashboardDirectorComponent },
  {
    path: 'director',
    children: [
      {
        path: 'Derogated',
        children: [
          { path: 'notifications', component: NotificationsDirectorComponent },
        ],
      },
    ],
  },
  { path: 'derogation', component: DerogationListComponent },
  { path: 'demandess', component: NotificationClientComponent },
  { path: 'derogation-details/:demande_id', component: FicheDerogationComponent },
  { path: 'responsable/rapports/:id', component: RapportDanalyseComponent },

  { path: 'responsable/rapportslists', component: RapportsListComponent },
  { path: 'director/reports', component: RapportsListDirectorComponent },
  { path: 'director/manage-requests', component: ManageRequestsComponent },
  { path: 'fiche-transmission/:id', component: FicheTransmissionDetailsComponent },
  { path: 'responsable/fiche-transmission', component: FichesListComponent },
  {path:'receptionist/rapports',component:RapportsReceptionistComponent},
  {path:'director/rapportsDetails/:id',component:RapportsDetailsDirectorComponent},
  {path:'receptionist/rapports/:id',component:RapportDanalyseDetailsComponent},

  {path:'receptionist/facture',component:FactureListComponent},
  {path:'facture/:id',component:FacturationComponent},
  {path:'receptionist/users',component:ReceptionnisteUsersComponent},
  {path:'receptionist/profile',component:ReceptionnisteProfileComponent},
  {path:'receptionist/results',component:ReceptionnisteResultsComponent},
  {path:'receptionist/results/send/:id',component:SendResultsComponent},
  {path:'receptionist/reclamations',component:ReclamationsListComponent},
  {path:'receptionist/reclamation-details/:id',component:ReclamationDetailsComponent},

  // 📌 Analyste
  { path: 'analysits/dashboard', component: AnalystDashboardComponent },
  { path: 'analysits/fiche-transmission', component: AnalystFicheTransmissionComponent },
  { path: 'analysits/fichedetails/:id', component: AnalystFicheDetailsComponent },
  { path: 'analysits/results', component: ResultsComponent },
  { path: 'analysits/results/upload/:id', component: UploadResultsComponent },

  // 📌 Gestion des Analyses
  {
    path: 'gestion-analyse',
    loadChildren: () => import('./gestion_analyse/gestion-analyse.routes').then(m => m.GESTION_ANALYSE_ROUTES)
  }

];
