import { Component, OnInit } from '@angular/core';
import { Sample } from '../../../models/sample.model';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormControl, Validators, FormsModule, FormBuilder } from '@angular/forms';

import { AnalysesService } from './analyses.service';

@Component({
  standalone :true,
  templateUrl: './analyses.component.html',
 imports: [CommonModule, ReactiveFormsModule, FormsModule],
  styleUrls: ['./analyses.component.css']
})
export class AnalysesComponent implements OnInit {
  samples: Sample[] = [];
  sampleForm: FormGroup;
  selectedSample: Sample | null = null;
  isModalOpen = false;  // State for modal visibility

  constructor(private analysesService: AnalysesService, private fb: FormBuilder) {
    this.sampleForm = this.fb.group({
      id: [null],
      sample_type: ['', Validators.required],
      sub_type: [''],
      quantite_minimal: ['', Validators.required],
      condition: ['', Validators.required],
      analysis: ['', Validators.required],
      parametre: ['', Validators.required],
      price: [0, Validators.required]
    });
  }

  ngOnInit(): void {
    this.getSamples();
  }

  getSamples(): void {
    this.analysesService.getSamples().subscribe(
      (data) => {
        this.samples = data;
      },
      (error) => {
        console.error('Error fetching samples:', error);
      }
    );
  }

  saveSample(): void {
    if (this.sampleForm.valid) {
      const sampleData: Sample = this.sampleForm.value;

      if (sampleData.id) {
        this.analysesService.updateSample(sampleData.id, sampleData).subscribe(() => {
          this.getSamples();
          this.closeModal();
        });
      } else {
        this.analysesService.createSample(sampleData).subscribe(() => {
          this.getSamples();
          this.closeModal();
        });
      }
    }
  }

  editSample(sample: Sample): void {
    this.selectedSample = sample;
    this.sampleForm.patchValue(sample);
    this.isModalOpen = true;
  }

  deleteSample(id: number): void {
    if (confirm('Are you sure you want to delete this sample?')) {
      this.analysesService.deleteSample(id).subscribe(() => {
        this.getSamples();
      });
    }
  }

  openModal(): void {
    this.isModalOpen = true;
    this.sampleForm.reset();
    this.selectedSample = null;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.sampleForm.reset();
    this.selectedSample = null;
  }
}
