import { Component, AfterViewInit, OnInit } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';

@Component({
    selector: 'app-about',
    standalone: true,
    imports: [RouterModule],
    templateUrl: './about.component.html',
    styleUrl: './about.component.css'
})
export class AboutComponent implements OnInit, AfterViewInit {

    constructor(private readonly route: ActivatedRoute) {}

    ngOnInit() {
        // Check for fragment in URL and scroll to that section
        this.route.fragment.subscribe(fragment => {
            if (fragment) {
                // Wait for the DOM to be fully loaded
                setTimeout(() => {
                    console.log('About Component: Looking for element with ID:', fragment);
                    const element = document.getElementById(fragment);
                    if (element) {
                        console.log('About Component: Element found, scrolling to it');
                        element.scrollIntoView({ behavior: 'smooth' });
                    } else {
                        console.log('About Component: Element not found with ID:', fragment);
                    }
                }, 500);
            }
        });

        // Make sure the about-section ID is accessible from the parent component
        setTimeout(() => {
            // Find the about section with the B3Aqua description
            const aboutSection = document.querySelector('.about-description-section');
            if (aboutSection) {
                // Ensure the ID is set at the component level for easier access
                aboutSection.setAttribute('id', 'about-section');
                // Add scroll-margin-top to ensure proper scrolling position
                aboutSection.setAttribute('style', aboutSection.getAttribute('style') + '; scroll-margin-top: 80px;');
                console.log('About section found and ID set');
            } else {
                console.log('About section not found in component');
            }
        }, 300);
    }

    ngAfterViewInit() {
        // Initialize the counter animation when the view is ready
        this.initCounterAnimation();
    }

    private initCounterAnimation() {
        const statsNumbers = document.querySelectorAll('.stats-number');

        // Create an Intersection Observer to detect when stats are in view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target as HTMLElement;
                    const targetValue = parseInt(element.getAttribute('data-count') ?? '0');
                    this.animateCounter(element, 0, targetValue, 2000); // 2 seconds duration
                    observer.unobserve(element); // Only animate once
                }
            });
        }, { threshold: 0.5 });

        // Observe all stat number elements
        statsNumbers.forEach(number => {
            observer.observe(number);
        });
    }

    private animateCounter(element: HTMLElement, start: number, end: number, duration: number) {
        let startTimestamp: number | null = null;
        const step = (timestamp: number) => {
            startTimestamp ??= timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            const currentValue = Math.floor(progress * (end - start) + start);

            // Update the element text, preserving the + sign if it exists
            const hasPlus = element.innerHTML.includes('<span class="plus-sign">+</span>');
            element.innerHTML = hasPlus
                ? currentValue + '<span class="plus-sign">+</span>'
                : currentValue.toString();

            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };

        window.requestAnimationFrame(step);
    }
}
