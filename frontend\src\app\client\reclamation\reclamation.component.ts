import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { DemandeService } from '../demande-details/demande.service';

@Component({
  selector: 'app-reclamation',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  templateUrl: './reclamation.component.html',
  styleUrl: './reclamation.component.css'
})
export class ReclamationComponent implements OnInit {
  // Form properties
  reclamationForm!: FormGroup;
  submitted = false;

  // Loading states
  isLoading = true;
  isSubmitting = false;

  // File upload properties
  selectedFile: File | null = null;
  fileName = '';
  fileError: string | null = null;

  // Success modal properties
  showSuccessModal = false;
  modalMessage = '';

  // User demandes for dropdown
  userDemandes: any[] = [];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private demandeService: DemandeService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadUserDemandes();
  }

  // Initialize the form
  initForm(): void {
    this.reclamationForm = this.fb.group({
      subject: ['', [Validators.required]],
      description: ['', [Validators.required]],
      demandeId: ['']
    });
  }

  // Getter for easy access to form fields
  get f() { return this.reclamationForm.controls; }

  // Load user demandes for the dropdown
  loadUserDemandes(): void {
    this.demandeService.getUserDemandes().subscribe({
      next: (demandes) => {
        // Check if response is an array
        if (Array.isArray(demandes)) {
          this.userDemandes = demandes;
        }
        // Check if response has a data property that is an array
        else if (demandes && demandes.data && Array.isArray(demandes.data)) {
          this.userDemandes = demandes.data;
        }
        // Check if response has a demandes property that is an array
        else if (demandes && demandes.demandes && Array.isArray(demandes.demandes)) {
          this.userDemandes = demandes.demandes;
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading user demandes:', error);
        this.isLoading = false;
      }
    });
  }

  // Handle file selection
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      const validTypes = [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png'
      ];

      // Check file size (max 25MB)
      const maxSize = 25 * 1024 * 1024; // 25MB in bytes

      if (!validTypes.includes(file.type)) {
        this.fileError = 'Veuillez sélectionner un fichier PDF ou une image (JPG, PNG).';
        this.selectedFile = null;
        this.fileName = '';
        event.target.value = '';
      } else if (file.size > maxSize) {
        this.fileError = 'La taille du fichier ne doit pas dépasser 25 MB.';
        this.selectedFile = null;
        this.fileName = '';
        event.target.value = '';
      } else {
        this.selectedFile = file;
        this.fileName = file.name;
        this.fileError = null;
      }
    }
  }

  // Form submission
  onSubmit(): void {
    this.submitted = true;

    // Stop if form is invalid
    if (this.reclamationForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    // In a real application, you would send the form data to the server here
    // For this static implementation, we'll simulate a successful submission
    setTimeout(() => {
      this.isSubmitting = false;
      this.showSuccessModal = true;
      this.modalMessage = 'Votre réclamation a été soumise avec succès. Nous vous contacterons dans les plus brefs délais.';

      // Reset form
      this.submitted = false;
      this.reclamationForm.reset();
      this.selectedFile = null;
      this.fileName = '';
    }, 1500);
  }

  // Close success modal
  closeSuccessModal(): void {
    this.showSuccessModal = false;
    this.router.navigate(['/client/dashboard']);
  }

  // Navigate to reclamations list
  goToReclamations(): void {
    this.router.navigate(['/client/reclamations']);
  }
}
