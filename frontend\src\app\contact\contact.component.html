<div class="contact-container">
  <div class="contact-header">
    <h2 class="contact-title">Contactez-Nous</h2>
    <div class="title-underline"></div>
  </div>

  <div class="contact-content">
    <!-- Left side: Contact form -->
    <div class="contact-form-container">
      <div class="form-header">
        <div class="form-icon">
          <i class="form-icon-svg"></i>
        </div>
        <h3>Envoyez-nous un message</h3>
        <p class="contact-description">
          Vous souhaitez obtenir plus d'informations sur nos analyses ou nos services ?
          Notre équipe vous répondra dans les plus brefs délais.
        </p>
      </div>

      <form class="contact-form" (ngSubmit)="onSubmit()" #contactForm="ngForm">
        <!-- Informations personnelles -->
        <div class="form-group">
          <div class="input-wrapper">
            <div class="input-icon">
              <i class="user-icon"></i>
            </div>
            <input type="text" id="name" name="name" [(ngModel)]="contactData.name" #nameInput="ngModel" placeholder="Nom et Prénom" required minlength="4">
          </div>
          <div *ngIf="nameInput.invalid && (nameInput.dirty || nameInput.touched)" class="validation-error">
            <span *ngIf="nameInput.errors?.['required']">Le nom est requis.</span>
            <span *ngIf="nameInput.errors?.['minlength']">Le nom doit contenir au moins 4 caractères.</span>
          </div>
        </div>

        <div class="form-group">
          <div class="input-wrapper">
            <div class="input-icon">
              <i class="email-icon"></i>
            </div>
            <input type="email" id="email" name="email" [(ngModel)]="contactData.email" #emailInput="ngModel" placeholder="Adresse Email" required pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$">
          </div>
          <div *ngIf="emailInput.invalid && (emailInput.dirty || emailInput.touched)" class="validation-error">
            <span *ngIf="emailInput.errors?.['required']">L'email est requis.</span>
            <span *ngIf="emailInput.errors?.['pattern']">Veuillez entrer une adresse email valide.</span>
          </div>
        </div>

        <div class="form-group">
          <div class="input-wrapper">
            <div class="input-icon">
              <i class="phone-icon"></i>
            </div>
            <input type="text" id="phone" name="phone" [(ngModel)]="contactData.phone" #phoneInput="ngModel" placeholder="Numéro de Téléphone (ex: 50123456)" required pattern="^[2579][0-9]{7}$">
          </div>
          <div *ngIf="phoneInput.invalid && (phoneInput.dirty || phoneInput.touched)" class="validation-error">
            <span *ngIf="phoneInput.errors?.['required']">Le numéro de téléphone est requis.</span>
            <span *ngIf="phoneInput.errors?.['pattern']">Veuillez entrer un numéro de téléphone tunisien valide (8 chiffres commençant par 2, 5, 7 ou 9).</span>
          </div>
        </div>

        <!-- Message du visiteur -->
        <div class="form-group">
          <div class="input-wrapper textarea-container">
            <div class="input-icon">
              <i class="message-icon"></i>
            </div>
            <textarea id="message" name="message" [(ngModel)]="contactData.message" #messageInput="ngModel" rows="5" placeholder="Décrivez votre question ou votre demande..." required minlength="10"></textarea>
          </div>
          <div *ngIf="messageInput.invalid && (messageInput.dirty || messageInput.touched)" class="validation-error">
            <span *ngIf="messageInput.errors?.['required']">Le message est requis.</span>
            <span *ngIf="messageInput.errors?.['minlength']">Le message doit contenir au moins 10 caractères.</span>
          </div>
        </div>

        <div *ngIf="showSuccessMessage" class="success-message">
          <div class="success-icon">✓</div>
          <p>Votre message a été envoyé avec succès! Nous vous répondrons dans les plus brefs délais. <span class="countdown">La page va se recharger dans {{countdownSeconds}} secondes...</span></p>
        </div>

        <button type="submit" class="submit-button" [disabled]="!contactForm.form.valid || isSubmitting">
          <span *ngIf="!isSubmitting">Envoyer</span>
          <span *ngIf="isSubmitting">Envoi en cours...</span>
          <i class="send-icon" *ngIf="!isSubmitting"></i>
          <div class="spinner" *ngIf="isSubmitting"></div>
        </button>
      </form>
    </div>

    <!-- Right side: Contact information -->
    <div class="contact-info-container">
      <div class="info-header">
        <h3>Informations de Contact</h3>
        <div class="info-underline"></div>
      </div>

      <div class="info-content">
        <div class="info-item">
          <div class="info-icon location-icon"></div>
          <div class="info-text">
            <h4>Adresse</h4>
            <p>INSTM Port de Pêche La Goulette</p>
          </div>
        </div>

        <div class="info-item">
          <div class="info-icon phone-info-icon"></div>
          <div class="info-text">
            <h4>Téléphone</h4>
            <p>(+216) 71 735 448</p>
          </div>
        </div>

        <div class="info-item">
          <div class="info-icon email-info-icon"></div>
          <div class="info-text">
            <h4>Email</h4>
            <p>B3Aqua.agrinet.tn</p>
          </div>
        </div>

        <div class="info-item">
          <div class="info-icon hours-icon"></div>
          <div class="info-text">
            <h4>Heures d'ouverture</h4>
            <p>Lun - Ven: 8h00 - 17h00</p>
          </div>
        </div>
      </div>

      <div class="map-container">
        <iframe
          title="Localisation INSTM Port de Pêche La Goulette"
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3193.9553768175397!2d10.304704876550372!3d36.8078204!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzbCsDQ4JzI4LjIiTiAxMMKwMTgnMjQuOCJF!5e0!3m2!1sen!2sus!4v1652345678901!5m2!1sen!2sus"
          width="100%"
          height="100%"
          style="border:0;"
          allowfullscreen=""
          loading="lazy"
          referrerpolicy="no-referrer-when-downgrade">
        </iframe>
      </div>
    </div>
  </div>
</div>
