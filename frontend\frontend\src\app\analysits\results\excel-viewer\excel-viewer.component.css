.excel-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.excel-viewer-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.excel-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.excel-viewer-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #dc3545;
}

.excel-viewer-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  padding: 20px;
  background-color: #f8d7da;
  border-radius: 4px;
  color: #721c24;
  text-align: center;
}

.excel-data-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sheet-tabs {
  display: flex;
  overflow-x: auto;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 15px;
}

.sheet-tabs button {
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  color: #495057;
  transition: all 0.2s;
}

.sheet-tabs button:hover {
  background-color: #f8f9fa;
}

.sheet-tabs button.active {
  border-bottom-color: #007bff;
  color: #007bff;
  font-weight: 500;
}

.excel-table-container {
  overflow: auto;
  max-height: 60vh;
}

.excel-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.excel-table th, .excel-table td {
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  text-align: left;
}

.excel-table th {
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
  font-weight: 600;
}

.excel-table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.excel-table tr:hover {
  background-color: #e9ecef;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}
