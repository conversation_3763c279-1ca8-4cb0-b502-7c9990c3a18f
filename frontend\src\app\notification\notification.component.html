<div class="notification-container" *ngIf="role">
    <!-- Bell Icon -->
    <div class="bell-icon" (click)="toggleNotifications($event)">
        <svg class="bell-svg" viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
        </svg>
        <span class="badge" *ngIf="getUnreadCount() > 0">{{ getUnreadCount() }}</span>
    </div>

    <!-- Notification Dropdown -->
    <div class="notification-dropdown" *ngIf="showNotifications">
        <div class="notification-header">
            <span class="title">Notifications</span>
           
        </div>

        <!-- Notification Table -->
        <div class="table-container">
            <table class="notification-table">
                <thead>
                    <tr>
                        <th>Catégorie</th>
                        <th>Message</th>
                        <th>Date</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let notification of notifications | paginate: { itemsPerPage: itemsPerPage, currentPage: page }"
                        [class.unread]="!notification.is_read"
                        (click)="handleNotificationClick(notification)">
                        <td>{{ notification.title }}</td>
                        <td>{{ notification.message }}</td>
                        <td>{{ notification.created_at | date:'yyyy-MM-dd HH:mm' }}</td>
                        <td>
                            <span class="status-tag" [class.unread-tag]="!notification.is_read">
                                {{ notification.is_read ? 'Lue' : 'Nouvelle' }}
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- No Notifications Message -->
        <div *ngIf="notifications.length === 0" class="no-notifications">
            <svg class="empty-icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
            </svg>
            No notifications available
        </div>

        <!-- Pagination and Button -->
        <div class="footer-actions">
            <pagination-controls 
                (pageChange)="page = $event" 
                previousLabel="Previous" 
                nextLabel="Next"
                class="pagination-custom">
            </pagination-controls>
            <button class="voir-plus-btn" (click)="navigateToNotifications()">Voir plus</button>
        </div>
    </div>
</div>