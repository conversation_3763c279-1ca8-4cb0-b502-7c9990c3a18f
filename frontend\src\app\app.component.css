html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-x: hidden; /* Empêche les bandes blanches horizontales */
}
:host {
  /* Updated to match actual navbar height */
  display: block;
  padding-top: 75px; /* push content down by 75px to match navbar height */
}
/* Example if navbar has 'position: fixed' and is 60px tall */
.page-content {             
  flex: 1;
  overflow-y: auto;
  margin:0;
  padding:0;
  /* Removed padding-top to eliminate extra space */
}
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;  
  margin:0;
  padding:0;       /* Make body at least full viewport height */
}
app-navbar, app-footer {
  flex-shrink: 0;
  width: 100%;
}
#back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  opacity: 0;
  pointer-events: none; /* Prevents interaction when hidden */
  background: linear-gradient(135deg, #6a11cb, #2575fc);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  font-size: 24px;
  box-shadow: 0 4px 10px rgba(0,0,0,0.3);
  transition: opacity 0.4s, transform 0.4s;
  transform: translateY(20px); /* Slightly below its final position when hidden */
  text-decoration: none;
}

#back-to-top.show {
  opacity: 1;
  pointer-events: auto; /* Enables interaction when visible */
  transform: translateY(0); /* Moves to its final position */
}

#back-to-top:hover {
  transform: scale(1.1); /* Slight zoom on hover */
  box-shadow: 0 6px 15px rgba(0,0,0,0.4);
}
