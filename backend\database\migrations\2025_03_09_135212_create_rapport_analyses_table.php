<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rapport_analyses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('rapport_id');
            $table->string('code_echantillon');
            $table->string('parametre');
            $table->string('mesurande')->nullable();
            $table->string('unite')->nullable();
            $table->string('limite_acceptabilite')->nullable();
            $table->string('methode_analyse_utilisee');
            $table->date('date_analyse')->nullable();
            $table->foreign('rapport_id')->references('id')->on('rapports')->onDelete('cascade');
            $table->timestamps();
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rapport_analyses');
    }
};
