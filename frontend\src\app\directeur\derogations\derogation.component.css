@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Main Container */
.demandes-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}

/* ✅ Title Styling */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Derogation Section Title */
.derogation-details h3 {
  font-family: 'Orbitron', sans-serif;
  font-size: 20px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  margin-top: 30px;
  margin-bottom: 15px;
  border-bottom: 4px solid #1e7bbd; /* Darker blue instead of orange */
  display: inline-block;
  padding-bottom: 6px;
  background: linear-gradient(90deg, #2496d3, #1e7bbd); /* Blue gradient */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Loading and Error Messages */
.error {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}

/* =========================
   Loading Spinner Overlay
   ========================= */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

/* Content hidden when loading */
.content-hidden {
  opacity: 0.3;
  pointer-events: none;
}

.loading-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-popup h3 {
  margin-top: 20px;
  color: #333;
  font-size: 18px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ✅ Table Styling (Applies to both Demande and Derogation tables) */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ Table Header */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Derogation Table Header (Override for Derogation Section) */
.derogation-details th {
  background-color: #1e7bbd; /* Darker blue instead of orange */
}

/* ✅ Table Rows */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Hover Effects */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* ✅ Derogation Row Hover */
.derogation-details tr:hover {
  background: rgba(30, 123, 189, 0.1); /* Lighter shade of darker blue */
}

/* ✅ Status Styling */
span {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
}

/* ✅ Status Colors */
.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  box-shadow: 0px 3px 8px rgba(255, 193, 7, 0.4);
}

.ongoing {
  background: rgba(36, 150, 211, 0.1);
  color: #2496d3;
  box-shadow: 0px 3px 8px rgba(36, 150, 211, 0.4);
}

.derogation {
  background: rgba(30, 123, 189, 0.1); /* Darker blue instead of orange */
  color: #1e7bbd;
  box-shadow: 0px 3px 8px rgba(30, 123, 189, 0.4);
}

.valid {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  box-shadow: 0px 3px 8px rgba(40, 167, 69, 0.4);
}

/* ✅ Button Styling */
button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px;
  transition: all 0.3s ease-in-out;
}

/* ✅ Pre-validation Button */
.btn-prevalider {
  background: #2496d3;
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  border-radius: 20px;
  margin-top: 20px;
  box-shadow: 0px 4px 10px rgba(36, 150, 211, 0.3);
}

.btn-prevalider:hover {
  background: #1e7bbd;
  transform: scale(1.03);
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
}

/* ✅ Validation Button */
.btn-validation {
  background-color: #1e78b5; /* Perfect blue */
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-validation:hover {
  background-color: #1a6a9e; /* Darker blue on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(30, 120, 181, 0.3);
}

/* ✅ Derogation Button */
.btn-derogation {
  background-color: #bd2222; /* Gray */
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-derogation:hover {
  background-color: #666666; /* Darker gray on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(128, 128, 128, 0.3);
}

/* ✅ Glow Animation */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =========================
   Notification Overlay
   ========================= */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.notification {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
  animation: fadeIn 0.5s ease-in-out;
}

.notification-icon {
  font-size: 50px;
  margin-bottom: 20px;
}

.notification-message {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

/* ✅ Validation Buttons Container */
.validation-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

/* ✅ Derogation Details */
.derogation-details {
  margin-top: 30px;
  background: rgba(36, 150, 211, 0.05); /* Light blue background */
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.2);
}

/* ✅ New Grid Layout for Derogation */
.derogation-grid {
  display: grid;
  gap: 10px;
  font-family: 'Montserrat', sans-serif;
}

.derogation-header {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  background-color: #1e7bbd; /* Darker blue instead of orange */
  color: white;
  padding: 15px;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  text-transform: uppercase;
}

.derogation-item {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  padding: 12px;
  background: white;
  border-radius: 5px;
  border: 1px solid rgba(36, 150, 211, 0.2);
  transition: all 0.3s ease-in-out;
}

.derogation-item:hover {
  background: rgba(36, 150, 211, 0.1); /* Consistent blue hover */
  box-shadow: 0px 4px 12px rgba(36, 150, 211, 0.3);
}

.derogation-item span {
  padding: 0;
  font-size: 16px;
  text-align: center;
}