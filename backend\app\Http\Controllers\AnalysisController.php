<?php

namespace App\Http\Controllers;

use App\Models\Analysis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AnalysisController extends Controller
{
    /**
     * Display a listing of the analyses.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $analyses = Analysis::all();

        return response()->json([
            'status' => 'success',
            'data' => $analyses
        ]);
    }

    /**
     * Store a newly created analysis in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'analyse' => 'required|string|unique:analyses,analyse|max:255',
            'parameter' => 'required|string',
            'price' => 'required|numeric|min:0',
            'is_accredited' => 'sometimes|required|in:Yes,No',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $analysis = Analysis::create($request->all());

        return response()->json([
            'status' => 'success',
            'message' => 'Analysis created successfully',
            'data' => $analysis
        ], 201);
    }

    /**
     * Display the specified analysis.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $analysis = Analysis::find($id);

        if (!$analysis) {
            return response()->json([
                'status' => 'error',
                'message' => 'Analysis not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => $analysis
        ]);
    }

    /**
     * Update the specified analysis in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $analysis = Analysis::find($id);

        if (!$analysis) {
            return response()->json([
                'status' => 'error',
                'message' => 'Analysis not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'analyse' => 'sometimes|required|string|max:255|unique:analyses,analyse,' . $id,
            'parameter' => 'sometimes|required|string',
            'price' => 'sometimes|required|numeric|min:0',
            'is_accredited' => 'sometimes|required|in:Yes,No',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $analysis->update($request->all());

        return response()->json([
            'status' => 'success',
            'message' => 'Analysis updated successfully',
            'data' => $analysis
        ]);
    }

    /**
     * Remove the specified analysis from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $analysis = Analysis::find($id);

        if (!$analysis) {
            return response()->json([
                'status' => 'error',
                'message' => 'Analysis not found'
            ], 404);
        }

        $analysis->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Analysis deleted successfully'
        ]);
    }
}
