/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ SECTION PRINCIPALE */
.expertise-section {
   
    padding: 100px 5%;
    background: white;
    color: black;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* ✅ TITRE PRINCIPAL AVEC ANIMATION */
.expertise-title {
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 30px;
  letter-spacing: 3px;
  
  border-bottom: 4px solid #2496d3;
  padding-bottom: 10px;
  display: inline-block;
  
}
/* ✅ Effet Glow sur le titre */
@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.5); }
    to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.9); }
}

/* ✅ CONTENEUR PRINCIPAL */
.expertise-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    align-items: center;
    gap: 70px;
    text-align: center;
}

/* ✅ IMAGE AVEC ANIMATION */
.expertise-image img {
    width: 100%;
    border-radius: 15px;
    opacity: 1;
   
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 0 20px rgba(36, 150, 211, 0.3); /* ✅ Ombre bleu ciel */
}



/* ✅ LISTE DES DOMAINES D'EXPERTISE */
.expertise-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* ✅ ÉLÉMENT INDIVIDUEL */
.expertise-item {
    display: flex;
    align-items: center;
    gap: 25px;
    padding: 20px;
    border-radius: 12px;
    border-left: 5px solid #2496d3;
    box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.3);
    background: rgba(255, 255, 255, 0.9);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 1;
    animation: fadeInUp 1s ease-in-out forwards;
}

/* ✅ Effet au survol */
.expertise-item:hover {
    transform: scale(1.05);
    cursor: pointer;
    box-shadow: 0px 12px 25px rgba(36, 150, 211, 0.5);
    background: rgba(36, 150, 211, 0.1);
}

/* ✅ NUMÉRO */
.expertise-number {
    font-size: 22px;
    font-weight: bold;
    color: orange;
    text-shadow: 2px 2px 10px rgba(255, 165, 0, 0.7); /* ✅ Ombre orange */
}

/* ✅ CONTENU */
.expertise-content {
    display: flex;
    flex-direction: column;
}

.expertise-heading {
    font-size: 22px;
    font-weight: bold;
    color: orange;
    margin-bottom: 10px;
    text-align: center;
}

.expertise-item p {
    font-size: 18px;
    color: rgba(0, 0, 0, 0.7);
    line-height: 1.6;
}

/* ✅ ANIMATIONS */
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes floatImage {
    0% { transform: translateY(0); }
    100% { transform: translateY(-10px); }
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 1200px) {
    .expertise-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .expertise-list {
        align-items: center;
    }

    .expertise-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .expertise-title {
        font-size: 22px;
    }

    .expertise-number {
        font-size: 20px;
    }

    .expertise-heading {
        font-size: 20px;
    }

    .expertise-item p {
        font-size: 16px;
    }
}
