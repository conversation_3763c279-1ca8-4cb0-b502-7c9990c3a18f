<?php

namespace App\Console\Commands;

use App\Models\Demande;
use Illuminate\Console\Command;

class ResetEnregistrementCount extends Command
{
    protected $signature = 'reset:enregistrements';
    protected $description = 'Reset the total enregistrement count at midnight';

    public function handle()
    {
        Demande::where('demande_date', '<', now()->format('Y-m-d'))->delete();
        $this->info('Enregistrement count reset successfully.');
    }
}
