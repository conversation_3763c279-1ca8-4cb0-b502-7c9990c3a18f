<div class="reclamations-container">
  <h2>Liste des Réclamations</h2>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro ou client:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="applyFilters()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="applyFilters()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="pending">En attente</option>
        <option value="in_progress">En cours</option>
        <option value="resolved">Résolu</option>
      </select>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faCalendarAlt" class="input-icon"></fa-icon>
        <input
          type="date"
          id="date"
          [(ngModel)]="selectedDate"
          (change)="applyFilters()"
          class="filter-input"
        />
      </div>
    </div>
    <button class="btn-reset" (click)="resetFilters()">
      <fa-icon [icon]="faEraser"></fa-icon> Effacer les filtres
    </button>
  </div>

  <!-- Tableau des réclamations -->
  <div class="table-container">
    <table>
      <thead>
        <tr>
          <th>ID Réclamation</th>
          <th>Date</th>
          <th>Client</th>
          <th>Sujet</th>
          <th>Statut</th>
          <th class="actions-col">Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Indicateur de chargement -->
        <tr *ngIf="isLoading" class="loading-row">
          <td colspan="6" class="text-center">
            <div class="spinner-container">
              <div class="spinner"></div>
              <span>Chargement...</span>
            </div>
          </td>
        </tr>

        <!-- Message quand aucune réclamation n'est trouvée -->
        <tr *ngIf="!isLoading && filteredReclamations.length === 0" class="empty-row">
          <td colspan="6" class="text-center">
            Aucune réclamation trouvée.
          </td>
        </tr>

        <!-- Affichage des réclamations -->
        <ng-container *ngIf="!isLoading && filteredReclamations.length > 0">
          <tr *ngFor="let reclamation of filteredReclamations | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }">
            <td>{{ reclamation.id }}</td>
            <td>{{ reclamation.date | date:'dd/MM/yyyy' }}</td>
            <td>{{ reclamation.clientName }} {{ reclamation.clientNickname }}</td>
            <td>{{ reclamation.subject }}</td>
            <td>
              <!-- Badges de statut -->
              <span class="status-tag pending" *ngIf="reclamation.status === 'pending'">
                <fa-icon [icon]="faCircleDot"></fa-icon> En attente
              </span>
              <span class="status-tag in-progress" *ngIf="reclamation.status === 'in_progress'">
                <fa-icon [icon]="faSpinner"></fa-icon> En cours
              </span>
              <span class="status-tag resolved" *ngIf="reclamation.status === 'resolved'">
                <fa-icon [icon]="faCheckCircle"></fa-icon> Résolu
              </span>
            </td>
            <td class="actions-col">
              <button class="btn-details" (click)="viewReclamationDetails(reclamation.id)">
                <fa-icon [icon]="faEye"></fa-icon> Voir détails
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="!isLoading && filteredReclamations.length > 0">
    <pagination-controls
      (pageChange)="currentPage = $event"
      previousLabel="Précédent"
      nextLabel="Suivant"
      screenReaderPaginationLabel="Pagination"
      screenReaderPageLabel="Page"
      screenReaderCurrentLabel="Vous êtes sur la page">
    </pagination-controls>
  </div>
</div>
