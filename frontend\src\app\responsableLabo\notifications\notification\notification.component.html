<div class="notifications-container">
    <h2>Notifications</h2>

    <!-- ✅ Check if there are notifications -->
    <div *ngIf="notifications.length > 0; else noNotifications">
        <table class="notification-table">
            <thead>
                <tr>
                    <th>Catégorie</th>
                    <th>Message</th>
                    <th>Date</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let notification of notifications | paginate: { itemsPerPage: itemsPerPage, currentPage: page }"
                    [class.unread]="!notification.is_read"
                    (click)="handleNotificationClick(notification)">
                    <td>{{ notification.title }}</td>
                    <td>{{ notification.message }}</td>
                    <td>{{ notification.created_at | date:'yyyy-MM-dd HH:mm' }}</td>
                    <td>
                        <span *ngIf="!notification.is_read" class="new-tag">Nouvelle</span>
                        <span *ngIf="notification.is_read" class="read-tag">Lue</span>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- ✅ Pagination Controls -->
        <pagination-controls (pageChange)="page = $event" previousLabel="Précédent"
        nextLabel="Suivant"></pagination-controls>
    </div>

    <!-- ✅ No Notifications Message -->
    <ng-template #noNotifications>
        <p class="no-notifications">Aucune notification disponible.</p>
    </ng-template>
</div>
