<!-- Main Loading Spinner Overlay -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="spinner"></div>
</div>

<!-- Main Content Container -->
<div class="main-content" [class.content-hidden]="isLoading">
  <div class="payment-history-container">

    <!-- Error message -->
    <div *ngIf="errorMessage" class="error">
      {{ errorMessage }}
    </div>

    <!-- Payment History Section -->
    <div *ngIf="!errorMessage">
      <div class="header-actions">
        <div class="title-print-container">

        </div>
      </div>

      <!-- No payments message -->
      <div *ngIf="!isLoading && payments.length === 0" class="no-payments">
        <p>Aucun paiement n'a été soumis pour cette demande.</p>
      </div>

      <!-- Rejection Reason Section -->
      <div *ngIf="!isLoading && hasRejectedPayments()" class="rejection-reason-container" style="width: 100%; margin: 20px auto; padding: 15px; background-color: #fff8f8; border-radius: 8px; border-left: 4px solid #dc3545; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 15px; color: #dc3545; font-size: 18px;">Raison de rejet de paiement</h3>
        <div *ngFor="let payment of getRejectedPayments()" style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #f1d1d1;">

          <div style="display: flex; align-items: flex-start; margin-top: 5px;">
            <div style="flex: 0 0 150px; font-weight: bold;">Raison:</div>
            <div style="color: #721c24;">{{ payment.notes || 'Aucune raison spécifiée' }}</div>
          </div>
        </div>
      </div>

      <!-- Payment history table -->
      <div *ngIf="!isLoading && payments.length > 0" class="payment-table-container" style="width: 100%; margin: 20px auto; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
    <table class="payment-table" style="width: 100%; border-collapse: collapse; font-size: 16px;">
      <thead>
        <tr style="background-color: #f5f5f5; height: 50px;">
          <th style="padding: 12px 15px; font-weight: bold; text-align: left; border-bottom: 2px solid #ddd;">Date paiement</th>
          <th style="padding: 12px 15px; font-weight: bold; text-align: left; border-bottom: 2px solid #ddd;">Montant</th>
          <th style="padding: 12px 15px; font-weight: bold; text-align: left; border-bottom: 2px solid #ddd;">Statut</th>
          <th style="padding: 12px 15px; font-weight: bold; text-align: left; border-bottom: 2px solid #ddd;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let payment of payments">
          <tr style="border-bottom: 1px solid #ddd; transition: background-color 0.3s ease;">
            <td style="padding: 12px 15px;">{{ formatDate(payment.payment_date) }}</td>
            <td style="padding: 12px 15px; font-weight: bold;">{{ payment.amount }} DT</td>
            <td style="padding: 12px 15px;">
              <span class="payment-status" [ngClass]="getStatusClass(payment.status)" style="padding: 5px 10px; border-radius: 4px; display: inline-block;">
                <fa-icon [icon]="getStatusIcon(payment.status)"></fa-icon>
                {{ getStatusText(payment.status) }}
              </span>
            </td>
            <td class="actions-cell" style="padding: 12px 15px; text-align: center;">
              <button
                class="btn-action view"
                [class.active]="(showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id)"
                (click)="viewPaymentProof(payment.payment_proof, payment.id)"
                title="Voir le justificatif"
                style="margin: 0 5px; padding: 8px; border-radius: 4px; background-color: #f0f0f0; border: none; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#e0e0e0'" onmouseout="this.style.backgroundColor='#f0f0f0'"
              >
                <fa-icon [icon]="(showPreview && previewPaymentId === payment.id) || (directImagePreview && currentPayment?.id === payment.id) ? faEyeSlash : faEye"></fa-icon>
              </button>
              <button class="btn-action download" (click)="downloadPaymentProof(payment.payment_proof)" title="Télécharger le justificatif" style="margin: 0 5px; padding: 8px; border-radius: 4px; background-color: #f0f0f0; border: none; cursor: pointer; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#e0e0e0'" onmouseout="this.style.backgroundColor='#f0f0f0'">
                <fa-icon [icon]="faDownload"></fa-icon>
              </button>
              <button
                *ngIf="canDeletePayment(payment.status)"
                class="btn-action delete"
                (click)="deletePayment(payment.id)"
                title="Supprimer le justificatif"
                style="margin: 0 5px; padding: 8px; border-radius: 4px; background-color: #f0f0f0; border: none; cursor: pointer; transition: background-color 0.3s ease;"
                onmouseover="this.style.backgroundColor='#e0e0e0'"
                onmouseout="this.style.backgroundColor='#f0f0f0'"
              >
                <fa-icon [icon]="faTrash"></fa-icon>
              </button>
            </td>
          </tr>

          <!-- Preview row that appears under the payment row -->
          <tr *ngIf="showPreview && previewPaymentId === payment.id" class="preview-row">
            <td colspan="4" class="preview-cell">
              <div class="preview-container">
                <!-- Loading spinner -->
                <div *ngIf="isLoadingPreview" class="loading-spinner">
                  <div class="spinner"></div>
                  <p>Chargement de l'aperçu...</p>
                </div>

                <!-- Error message -->
                <div *ngIf="!isLoadingPreview && previewError" class="error-message">
                  <p>{{ previewError }}</p>
                  <button class="btn-retry" (click)="retryLoadPreview()">
                    <fa-icon [icon]="faSync"></fa-icon> Réessayer
                  </button>
                </div>

                <!-- Preview content -->
                <div *ngIf="!isLoadingPreview && !previewError" class="preview-content">
                  <!-- File preview -->
                  <div class="file-preview">
                    <h4>Aperçu du fichier</h4>

                    <!-- Image preview -->
                    <div *ngIf="fileType === 'image'" class="image-container">
                      <img
                        [src]="currentPayment?.payment_proof_url || currentPayment?.direct_url || currentPreviewUrl"
                        alt="Aperçu du justificatif"
                        class="preview-image"
                        (error)="handleImageError($event)"
                      >
                      <p class="file-name">{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : '' }}</p>
                    </div>

                    <!-- PDF preview -->
                    <div *ngIf="fileType === 'pdf'" class="pdf-preview">
                      <div class="pdf-icon">
                        <fa-icon [icon]="faFilePdf" size="3x"></fa-icon>
                      </div>
                      <p>Fichier PDF sélectionné</p>
                      <p>{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : 'Fichier PDF' }}</p>
                      <button class="btn-open-pdf" (click)="openPdfInNewTab()">
                        <fa-icon [icon]="faExternalLinkAlt"></fa-icon> Ouvrir le PDF
                      </button>
                    </div>

                    <!-- Unknown file type -->
                    <div *ngIf="fileType === 'other'" class="unknown-file">
                      <div class="file-icon">
                        <fa-icon [icon]="faFile" size="3x"></fa-icon>
                      </div>
                      <p>Type de fichier non reconnu</p>
                      <p>{{ currentPreviewPath ? getFileNameFromPath(currentPreviewPath) : 'Fichier inconnu' }}</p>

                      <!-- Direct download button -->
                      <button class="btn-download" (click)="downloadCurrentFile()">
                        <fa-icon [icon]="faDownload"></fa-icon> Télécharger le fichier
                      </button>

                      <!-- Direct open button -->
                      <button class="btn-open-direct" (click)="openDirectUrl()">
                        <fa-icon [icon]="faExternalLinkAlt"></fa-icon> Ouvrir directement
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
      </div>

      <!-- Direct Image Preview Section -->
      <div *ngIf="directImagePreview" class="direct-image-preview">
        <h3>Aperçu du justificatif</h3>
        <div style="position: relative; display: inline-block; max-width: 100%;">
          <!-- Loading indicator while image is loading -->
          <div *ngIf="!directImageUrl" class="loading-spinner" style="padding: 20px; text-align: center;">
            <div class="spinner"></div>
            <p>Chargement de l'image...</p>
          </div>

          <img
            *ngIf="directImageUrl"
            [src]="directImageUrl"
            alt="Aperçu du justificatif"
            (error)="handleDirectImageError($event)"
            style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
            crossorigin="anonymous"
            onerror="this.onerror=null; console.log('HTML onerror handler triggered');"
          >

          <!-- Fallback content if image fails to load -->
          <div *ngIf="directImageRetryCount >= 3 && !directImageUrl" style="text-align: center; padding: 20px; background-color: #f8f9fa; border-radius: 8px; margin-top: 10px;">
            <fa-icon [icon]="faImage" size="3x" style="color: #6c757d; margin-bottom: 10px;"></fa-icon>
            <p>Impossible de charger l'image. Veuillez essayer de télécharger le fichier.</p>
          </div>
          <button
            (click)="closeDirectImagePreview()"
            style="position: absolute; top: 10px; right: 10px; background-color: rgba(255,255,255,0.8); border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: background-color 0.3s ease;"
            onmouseover="this.style.backgroundColor='rgba(240,240,240,0.9)'"
            onmouseout="this.style.backgroundColor='rgba(255,255,255,0.8)'"
            title="Fermer l'aperçu"
          >
            <fa-icon [icon]="faTimes"></fa-icon>
          </button>
        </div>
        <p *ngIf="currentImageFileName" style="margin-top: 10px; font-style: italic;">{{ currentImageFileName }}</p>

        <!-- Download button for the current image -->
        <button
          *ngIf="currentPayment"
          class="btn-download"
          (click)="downloadPaymentProof(currentPayment.payment_proof)"
          style="margin-top: 10px; padding: 8px 15px; background-color: #f0f0f0; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.3s ease;"
          onmouseover="this.style.backgroundColor='#e0e0e0'"
          onmouseout="this.style.backgroundColor='#f0f0f0'"
        >
          <fa-icon [icon]="faDownload"></fa-icon> Télécharger
        </button>

        <!-- Open in new tab button -->
        <button
          *ngIf="directImageUrl"
          class="btn-open-direct"
          (click)="openDirectUrl()"
          style="margin-top: 10px; margin-left: 10px; padding: 8px 15px; background-color: #f0f0f0; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.3s ease;"
          onmouseover="this.style.backgroundColor='#e0e0e0'"
          onmouseout="this.style.backgroundColor='#f0f0f0'"
        >
          <fa-icon [icon]="faExternalLinkAlt"></fa-icon> Ouvrir dans un nouvel onglet
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Notification de soumission -->
<div *ngIf="showNotification" class="notification-overlay">
  <div class="notification">
    <div class="notification-icon" [style.color]="notificationColor">{{ notificationIcon }}</div>
    <div class="notification-message">{{ notificationMessage }}</div>
  </div>
</div>
