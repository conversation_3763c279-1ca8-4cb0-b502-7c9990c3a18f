/*─────────────────────────────────────────────
  0. Polices & Thème
─────────────────────────────────────────────*/
@import url("https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap");

:root{
  /* Couleurs */
  --c-primary: #2496d3;
  --c-primary-dark: #1e7cb9;
  --c-accent: #17a2b8;
  --c-success: #28a745;
  --c-danger: #dc3545;
  --c-muted:  #6c757d;

  /* Surfaces */
  --bg-light: #f5f5f5;
  --bg-card : #fff;

  --radius : 8px;
  --shadow : 0 6px 24px rgba(0,0,0,.12);
}

.upload-container {
  max-width: 70%;
  margin: 0 auto;
  padding: 20px;
  padding-top:40px;

  padding-bottom:40px;

  background-color: var(--bg-light);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

/*─────────────────────────────────────────────
  3. Header
─────────────────────────────────────────────*/
.upload-header{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.75rem;
}

h2 {
  font-family: "Orbitron", sans-serif;
  font-size: 22px;
  text-transform: uppercase;
  border-bottom: 4px solid var(--c-primary);
  padding-bottom: .25rem;
  background: linear-gradient(90deg, #000, #888);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: glowText 1.6s infinite alternate;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

@keyframes glowText{
  from { text-shadow: 0 0 8px rgba(36,150,211,.4); }
  to   { text-shadow: 0 0 16px rgba(36,150,211,.8); }
}

.back-btn{
  display: inline-flex;
  align-items: center;
  gap: .4rem;
  background: #007bff;
  color: #fff;
  border: none;
  padding: .6rem 1.2rem;
  border-radius: 20px;
  cursor: pointer;
  transition: transform .3s, box-shadow .3s;
}

.back-btn:hover{
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,.15);
}

.loading-message, .error-message, .success-message {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-message {
  background-color: #e9ecef;
  color: #495057;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
}

.demande-details {
  background-color: white;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.demande-details h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 5px;
}

.detail-value {
  font-weight: 500;
  color: #333;
}

.upload-form{
  background: var(--bg-card);
  padding: 1.35rem;
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(0,0,0,.05);
  margin-bottom: 1.75rem;

}

.file-upload-container {
  margin-bottom: 20px;
}

.file-upload-area{
  position: relative;
  cursor: pointer;
  border: 2px dashed var(--c-primary);
  border-radius: var(--radius);
  padding: 3.5rem 2rem;
  text-align: center;
  background: repeating-linear-gradient(-45deg, transparent 0 10px, rgba(36,150,211,.06) 10px 20px);
  animation: dash 4s linear infinite;
  transition: all .3s ease;
}

@keyframes dash{
  from { background-position: 0 0; }
  to { background-position: 200px 0; }
}

.file-upload-area:hover,
.file-upload-area:focus-within{
  box-shadow: 0 0 0 3px rgba(36,150,211,.25), inset 0 0 30px rgba(36,150,211,.05);
  transform: translateY(-2px);
}

.file-upload-area.has-file{
  border-color: var(--c-success);
  background: linear-gradient(135deg, rgba(40,167,69,.03) 0%, rgba(40,167,69,.08) 100%);
  box-shadow: 0 0 0 1px rgba(40,167,69,.1), inset 0 0 20px rgba(40,167,69,.05);
  animation: none;
}

.file-upload-input{
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.file-upload-label{
  pointer-events: none;
  display: flex;
  flex-direction: column;
  gap: .55rem;
  color: var(--c-muted);
  font-size: .95rem;
}

.icon-excel{
  color: var(--c-success);
  font-size: 2.3rem;
  animation: pulseExcel 2s ease-in-out infinite;
}

@keyframes pulseExcel{
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.12); }
}

.file-upload-area:hover .icon-excel,
.file-upload-area:focus-within .icon-excel{
  animation: rotateExcel .8s ease-out;
}

@keyframes rotateExcel{
  50% { transform: rotate(12deg) scale(1.1); }
}

.file-upload-info{
  margin-top: .6rem;
  font-size: .9rem;
  padding: 10px;
  background-color: rgba(36,150,211,.06);
  border-radius: var(--radius);
  text-align: center;
}

.upload-actions {
  display: flex;
  justify-content: center;
}

.upload-btn {
  background-color: #28a745; /* Green */
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(40, 167, 69, 0.2);
}

.upload-btn:hover:not(:disabled) {
  background-color: #5cb85c; /* Light green */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
}

.upload-btn:disabled {
  background-color: var(--c-muted);
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

.upload-instructions {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #17a2b8;
}

.upload-instructions h4 {
  margin-top: 0;
  color: #17a2b8;
}

.upload-instructions ul {
  margin-bottom: 0;
  padding-left: 20px;
}

.upload-instructions li {
  margin-bottom: 5px;
  color: #495057;
}

/*─────────────────────────────────────────────
  9. Responsive
─────────────────────────────────────────────*/
@media(max-width: 768px){
  .upload-header{
    flex-direction: column;
    gap: 1rem;
  }
}
