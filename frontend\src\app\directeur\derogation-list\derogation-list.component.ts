// src/app/components/derogation-list/derogation-list.component.ts
import { Component, OnInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { DerogationService } from '../derogations/derogation.service';
import { DemandeService } from '../derogations/demande.service';
import { Derogation } from '../derogations/derogation.model';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSearch, faEraser, faEye, faFilter, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-derogation-list',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
  templateUrl: './derogation-list.component.html',
  styleUrls: ['./derogation-list.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DerogationListComponent implements OnInit {
  derogations: Derogation[] = [];
  filteredDerogations: Derogation[] = [];
  isLoading = true;
  errorMessage: string | null = null;
  demandeIdMap: { [key: number]: string } = {}; // Map number ID to string ID
  demandeStatusMap: { [key: number]: string } = {}; // Map demande ID to status

  // Filtering properties
  searchTerm: string = '';
  selectedDate: string = '';
  selectedStatus: string = '';

  // Font Awesome icons
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;

  constructor(
    private derogationService: DerogationService,
    private demandeService: DemandeService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.fetchAllDerogations();
  }

  fetchAllDerogations(): void {
    this.isLoading = true;
    this.errorMessage = null;

    this.derogationService.getAllDerogations().subscribe({
      next: (data: Derogation[]) => {
        this.derogations = data || [];
        this.filteredDerogations = [...this.derogations]; // Initialize filtered derogations
        this.isLoading = false;

        // Fetch demande_id string for each derogation
        this.derogations.forEach(derogation => {
          this.fetchDemandeIdString(derogation.demande_id);
        });

        this.cdr.detectChanges(); // Detect changes to update UI
      },
      error: (error) => {
        console.error('Subscription error:', error);
        this.errorMessage = error.message || 'Erreur lors du chargement des dérogations.';
        this.isLoading = false;
        this.derogations = [];
        this.filteredDerogations = [];
        this.cdr.detectChanges(); // Detect changes to update UI
      }
    });
  }

  fetchDemandeIdString(demandeId: number): void {
    this.derogationService.getDemandeById(demandeId).subscribe({
      next: (response) => {
        if (response?.demande?.demande_id) {
          this.demandeIdMap[demandeId] = response.demande.demande_id;

          // Fetch the demande details to get the status
          this.fetchDemandeStatus(response.demande.demande_id, demandeId);
        }
      },
      error: (error) => {
        console.error(`Error fetching demande for ID ${demandeId}:`, error);
      }
    });
  }

  fetchDemandeStatus(demandeIdString: string, numericId: number): void {
    this.demandeService.getDemandeBy(demandeIdString).subscribe({
      next: (demande) => {
        if (demande && demande.status) {
          this.demandeStatusMap[numericId] = this.translateStatus(demande.status);
          this.cdr.detectChanges(); // Update the UI
        }
      },
      error: (error) => {
        console.error(`Error fetching demande status for ID ${demandeIdString}:`, error);
      }
    });
  }

  translateStatus(status: string): string {
    switch (status) {
      case 'pending': return 'En attente';
      case 'ongoing': return 'En cours';
      case 'validated': return 'Validée';
      case 'rejected': return 'Rejetée';
      case 'derogation': return 'Dérogation';
      case 'valid': return 'Validée avec dérogation';
      default: return status;
    }
  }

  viewDemandeDetails(demandeId: number): void {
    const demandeIdString = this.demandeIdMap[demandeId];
    if (demandeIdString) {
      console.log('Navigating to demande_id:', demandeIdString);
      this.router.navigate(['/derogation', demandeIdString]);
    } else {
      console.error('Demande ID string not available yet.');
    }
  }

  // Filtering methods
  onFilterChange(): void {
    const term = this.searchTerm.toLowerCase();
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredDerogations = this.derogations.filter((derogation) => {
      // Filter by search term (demande ID or client name)
      const demandeIdString = this.demandeIdMap[derogation.demande_id] || '';
      const matchesSearch =
        demandeIdString.toLowerCase().includes(term) ||
        (derogation.demandeur && derogation.demandeur.toLowerCase().includes(term));

      // Filter by date
      const derogationDate = derogation.date_demande_client ? new Date(derogation.date_demande_client) : null;
      const matchesDate = filterDate && derogationDate ?
        derogationDate.toDateString() === filterDate.toDateString() :
        !filterDate;

      // Filter by demande status
      let matchesStatus = true;
      if (this.selectedStatus) {
        const status = this.demandeStatusMap[derogation.demande_id];
        if (this.selectedStatus === 'approved') {
          matchesStatus = status === 'Validée' || status === 'Validée avec dérogation';
        } else if (this.selectedStatus === 'rejected') {
          matchesStatus = status === 'Rejetée';
        } else if (this.selectedStatus === 'pending') {
          matchesStatus = !status || status === 'En attente' || status === 'En cours';
        } else if (this.selectedStatus === 'derogation') {
          matchesStatus = status === 'Dérogation';
        }
      }

      return matchesSearch && matchesDate && matchesStatus;
    });
  }

  // Clear all filters
  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.selectedStatus = '';
    this.filteredDerogations = [...this.derogations];
  }
}
