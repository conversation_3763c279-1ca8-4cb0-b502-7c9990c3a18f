import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { FicheTransmissionService } from '../../responsableLabo/fiche-transmission/fiche.service';
import { DemandeService } from '../../receptionniste/demandes/demande.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faEye,
  faFileAlt,
  faFilePdf,
  faFilter,
  faSearch,
  faTimes,
  faSpinner,
  faExclamationTriangle,
  faInfoCircle,
  faCalendarAlt,faEraser,faArrowLeft
} from '@fortawesome/free-solid-svg-icons';

// Interface for type safety
interface Fiche {
  id: number;
  status: string;
  created_at?: string;
  date_transmission?: string | null;
  date_remise_resultats?: string | null;
  demande?: {
    demande_id: string;
    user_id: number;
    result_id?: number | null;
  };
}

@Component({
  selector: 'app-analyst-fiche-transmission',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
  templateUrl: './fiche-transmission.component.html',
  styleUrls: ['./fiche-transmission.component.css']
})
export class AnalystFicheTransmissionComponent implements OnInit {
  // Font Awesome icons
  faArrowLeft=faArrowLeft;
  faEye = faEye;
  faFileAlt = faFileAlt;
  faFilePdf = faFilePdf;
  faFilter = faFilter;
  faSearch = faSearch;
  faTimes = faTimes;
  faSpinner = faSpinner;
  faExclamationTriangle = faExclamationTriangle;
  faInfoCircle = faInfoCircle;
  faCalendarAlt = faCalendarAlt;
  faEraser=faEraser;
  fiches: Fiche[] = [];
  filteredFiches: Fiche[] = [];
  loading: boolean = true;
  errorMessage: string = '';
  userCache: { [key: number]: { name: string; nickname: string } } = {};

  // Filter variables
  searchDate: string = '';
  combinedSearch: string = '';
  filterDateFrom: string = '';
  filterDateTo: string = '';

  constructor(
    private ficheService: FicheTransmissionService,
    private demandeService: DemandeService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadFiches();
  }

  // Load all fiches without results
  loadFiches() {
    this.ficheService.getAllFiches().subscribe({
      next: (data) => {
        // Filter fiches with status 'sented' but without results
        this.fiches = data.filter((fiche: Fiche) =>
          fiche.status === 'sented' &&
          (!fiche.date_remise_resultats || fiche.date_remise_resultats === null) &&
          (!fiche.demande?.result_id) // Also check if result_id is null or undefined
        );
        this.filteredFiches = [...this.fiches];
        this.loading = false;

        // Fetch user details for each fiche
        const userIds = [
          ...new Set(
            this.fiches
              .map(fiche => fiche.demande?.user_id)
              .filter((id): id is number => !!id) // Type guard to ensure id is a number
          )
        ];

        userIds.forEach(userId => {
          this.fetchUserDetails(userId);
        });
      },
      error: (error) => {
        this.errorMessage = 'Échec du chargement des fiches';
        console.error('Error fetching fiches:', error);
        this.loading = false;
      }
    });
  }

  // Fetch user details by ID
  fetchUserDetails(userId: number | undefined) {
    if (!userId || this.userCache[userId]) {
      return; // Skip undefined IDs or already fetched
    }

    this.demandeService.getUserDetails(userId).subscribe({
      next: (user:any) => {
        this.userCache[userId] = {
          name: user.name ?? 'Inconnu',
          nickname: user.nickname ?? ''
        };
      },
      error: (error) => {
        console.error(`Error fetching user ${userId}:`, error);
        this.userCache[userId] = { name: 'Inconnu', nickname: '' };
      }
    });
  }

  // Get user name from cache
  fetchUserName(userId: number | undefined): string {
    if (!userId) return 'Inconnu';

    const user = this.userCache[userId];
    if (!user) return 'Chargement...';

    return `${user.name} ${user.nickname}`;
  }

  // Apply filters
  onFilterChange() {
    const dt = this.searchDate;
    const combined = this.combinedSearch.toLowerCase();

    this.filteredFiches = this.fiches.filter(fiche => {
      // Combined search for demande ID and client name
      const demandeId = fiche.demande?.demande_id?.toString().toLowerCase() ?? '';
      const userName = this.fetchUserName(fiche.demande?.user_id ?? 0).toLowerCase();

      // Match if combined search term is found in either demande ID or client name
      const matchCombined = !combined || demandeId.includes(combined) || userName.includes(combined);

      // Date filter
      const matchDate = !dt || fiche.date_transmission === dt;

      // Date range filter
      let matchDateRange = true;
      if (this.filterDateFrom || this.filterDateTo) {
        // Use a safe default date if neither is available
        const dateStr = fiche.created_at ?? fiche.date_transmission ?? new Date().toISOString();
        const creationDate = new Date(dateStr);

        if (this.filterDateFrom) {
          const fromDate = new Date(this.filterDateFrom);
          matchDateRange = matchDateRange && creationDate >= fromDate;
        }

        if (this.filterDateTo) {
          const toDate = new Date(this.filterDateTo);
          // Set time to end of day for the to date
          toDate.setHours(23, 59, 59, 999);
          matchDateRange = matchDateRange && creationDate <= toDate;
        }
      }

      return matchCombined && matchDate && matchDateRange;
    });
  }

  // View fiche details
  goToFicheTransmission(ficheId: number) {
    this.router.navigate(['analysits/fichedetails', ficheId]);
  }

  // Go back to dashboard
  goToDashboard() {
    this.router.navigate(['analysits/dashboard']);
  }

  // Clear all filters
  resetFilters() {
    this.searchDate = '';
    this.combinedSearch = '';
    this.filterDateFrom = '';
    this.filterDateTo = '';
    this.filteredFiches = [...this.fiches];
  }
}
