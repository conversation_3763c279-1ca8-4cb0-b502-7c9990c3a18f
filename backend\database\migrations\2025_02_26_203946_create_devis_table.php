<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('devis', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('demande_id'); // Foreign key reference
            $table->string('analyse'); // Analysis name
            $table->string('methode')->nullable(); // Analysis method
            $table->decimal('prix_unitaire', 10, 2); // Price per unit
            $table->integer('quantite'); // Quantity
            $table->decimal('prix_total', 10, 2); // Total price
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('demande_id')->references('id')->on('demandes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('devis');
    }
};

