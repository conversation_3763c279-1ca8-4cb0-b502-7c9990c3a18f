<!-- src/app/components/derogation-list/derogation-list.component.html -->
<div class="derogations-container">
  <div *ngIf="errorMessage && !isLoading" class="error">{{ errorMessage }}</div>

  <div>
    <h2>Liste des Dérogations</h2>

    <!-- Barr<PERSON> de filtrage -->
    <div class="filter-bar">
      <div class="filter-group">
        <label for="search">Rechercher par N° ou client:</label>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          class="filter-input"
        />
      </div>
      <div class="filter-group">
        <label for="date">Date:</label>
        <input type="date" id="date" [(ngModel)]="selectedDate" />
      </div>
      <div class="filter-group">
        <label for="status">Statut:</label>
        <select id="status" [(ngModel)]="selectedStatus" class="status-select">
          <option value="">Tous</option>
          <option value="approved">Validées</option>
          <option value="rejected">Rejetées</option>
          <option value="pending">En attente</option>
          <option value="derogation">Dérogation</option>
        </select>
      </div>
      <div class="filter-buttons">
        <button class="btn-filter-apply" (click)="onFilterChange()">
          <fa-icon [icon]="faFilter"></fa-icon> Appliquer
        </button>
        <button class="btn-clear" (click)="clearFilters()">
          <fa-icon [icon]="faEraser"></fa-icon> Effacer
        </button>
      </div>
    </div>

    <!-- Table with loading state -->
    <table class="derogations-table">
      <thead>
        <tr>
          <th>Demande N°</th>
         
          <th>Date de réception</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>

      <!-- Loading indicator -->
      <tbody *ngIf="isLoading" class="loading-tbody">
        <tr>
          <td colspan="5" class="loading-cell">
            <div class="loading-spinner">
              <div class="spinner"></div>
              <p>Chargement des dérogations...</p>
            </div>
          </td>
        </tr>
      </tbody>

      <!-- No derogations message -->
      <tbody *ngIf="!isLoading && (!filteredDerogations || filteredDerogations.length === 0)" class="empty-tbody">
        <tr>
          <td colspan="5" class="empty-message">
            Aucune dérogation disponible.
          </td>
        </tr>
      </tbody>

      <!-- Derogations data -->
      <tbody *ngIf="!isLoading && filteredDerogations && filteredDerogations.length > 0">
        <tr *ngFor="let derogation of filteredDerogations" class="derogation-row">
          <!-- Display demande_id string if available, otherwise show loading text -->
          <td>
            {{ demandeIdMap[derogation.demande_id] || 'Chargement...' }}
          </td>
          
          <td>{{ derogation.updated_at | date:'dd/MM/yyyy' }}</td>
          <td>
            <span [ngClass]="{
              'status-approved': demandeStatusMap[derogation.demande_id] === 'Validée' || demandeStatusMap[derogation.demande_id] === 'Validée avec dérogation',
              'status-rejected': demandeStatusMap[derogation.demande_id] === 'Rejetée',
              'status-pending': !demandeStatusMap[derogation.demande_id] || demandeStatusMap[derogation.demande_id] === 'En attente' || demandeStatusMap[derogation.demande_id] === 'En cours',
              'status-derogation': demandeStatusMap[derogation.demande_id] === 'Dérogation'
            }">
              {{ demandeStatusMap[derogation.demande_id] || 'Chargement...' }}
            </span>
          </td>
          <td class="action-buttons">
            <button class="details-btn" (click)="viewDemandeDetails(derogation.demande_id)">
              <fa-icon [icon]="faEye"></fa-icon> Voir détails
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>