import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-modal-accreditation',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './modal-accreditation.component.html',
  styleUrl: './modal-accreditation.component.css'
})
export class ModalAccreditationComponent {
  accreditationData: any[] = []; // Stockage des données modifiées
  isFormValid: boolean = true;   // Vérification de la validité du formulaire

  constructor(
    public dialogRef: MatDialogRef<ModalAccreditationComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // Cloner les données pour éviter de modifier l'original avant confirmation
    this.accreditationData = JSON.parse(JSON.stringify(data.analysisResults));
  }

  /**
   * Vérifie si toutes les options sont bien sélectionnées
   */
  validateForm(): void {
    this.isFormValid = this.accreditationData.every(item => item.accreditationStatus !== undefined);
  }

  /**
   * Confirme la sélection et ferme la modale
   */
  saveAccreditationStatus(): void {
    this.validateForm();

    if (!this.isFormValid) {
      alert("Veuillez sélectionner une méthode accréditée pour chaque paramètre.");
      return;
    }

    this.dialogRef.close(this.accreditationData); // Retourne les nouvelles données au composant parent
  }
  /**
   * Demande une confirmation avant la fermeture sans sauvegarde
   */
  closeModal(): void {
    const confirmClose = confirm("Voulez-vous vraiment annuler sans enregistrer ?");
    if (confirmClose) {
      this.dialogRef.close(null); // Retourne null si l'utilisateur annule
    }
  }
}
