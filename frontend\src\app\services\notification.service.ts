import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Notification {
  id: number;
  codeClient: string;
  nomClient: string;
  dateReception: Date;
  type: 'demande client' | 'rapport de résultat';
  message: string;
  date: Date;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notifications: Notification[] = [];
  private notificationsSubject = new BehaviorSubject<Notification[]>(this.notifications);

  constructor() {}

  // ✅ Récupérer les notifications en temps réel
  getNotifications(): Observable<Notification[]> {
    return this.notificationsSubject.asObservable();
  }

  // ✅ Ajouter une nouvelle notification
  addNotification(notification: Notification): void {
    this.notifications.push(notification);
    this.notificationsSubject.next(this.notifications); // Mettre à jour les abonnés (réceptionniste)
  }

  // ✅ Supprimer une notification (après traitement par le réceptionniste)
  removeNotification(id: number): void {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.notificationsSubject.next(this.notifications);
  }
}
