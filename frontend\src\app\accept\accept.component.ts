import { Component, OnInit } from '@angular/core';
import { RouterModule, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-accept',
  standalone: true,
  imports: [RouterModule],
  templateUrl: './accept.component.html',
  styleUrls: ['./accept.component.css']
})
export class AcceptComponent implements OnInit {
  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit() {
    // Check if this component was navigated to with a fragment
    this.route.fragment.subscribe(fragment => {
      if (fragment === 'accept') {
        // Scroll to the accept section with a delay to ensure DOM is loaded
        setTimeout(() => {
          console.log('AcceptComponent: Looking for element with ID accept');
          const element = document.getElementById('accept');
          if (element) {
            console.log('AcceptComponent: Element found, scrolling to it');
            element.scrollIntoView({ behavior: 'smooth' });
          } else {
            console.log('AcceptComponent: Element not found with ID accept');
          }
        }, 500);
      }
    });
  }
}
