import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';

import { Demande, Payment } from './demande.model';
import { CommonModule } from '@angular/common';
import { DemandeService } from '../../client/demande-details/demande.service';
import { Devis } from '../../../models/devis';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ScrollService } from '../validation/scroll-service.service';
import { PaymentService } from '../services/payment.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faPrint,
  faDownload,
  faEye,
  faEyeSlash,
  faSync,
  faCheck,
  faTimes,
  faSpinner,
  faTrash,
  faFilePdf,
  faFile,
  faExternalLinkAlt,
  faImage
} from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-demande-details',
  standalone: true,
  templateUrl: './demande-details.component.html',
  imports:[CommonModule, ReactiveFormsModule, FormsModule, FontAwesomeModule],
  styleUrls: ['./demande-details.component.css'],

})
export class DemandeDetailsComponent implements OnInit {
  // Font Awesome icons
  faPrint = faPrint;
  faDownload = faDownload;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faSync = faSync;
  faCheck = faCheck;
  faTimes = faTimes;
  faSpinner = faSpinner;
  faTrash = faTrash;
  faFilePdf = faFilePdf;
  faFile = faFile;
  faExternalLinkAlt = faExternalLinkAlt;
  faImage = faImage;

  demande: Demande | null = null;
  deviss: any = null;
  isLoading = true;
  groupedDevis: any[] = [];
  devisForm: FormGroup = this.fb.group({});
  analysisForm!: FormGroup;
  isEditing: { [key: string]: boolean } = {};
  errorMessage: string | null = null;
  devisId: number | null = null; // Will be set from query params
  id: number | null = null;
  devis: Devis[] = [];
  isDevisLoading = false; // Added for devis loading state

  // Payment related properties
  payments: Payment[] = [];
  isLoadingPayments = false;
  paymentError: string | null = null;
  showPreview = false;
  previewPaymentId: number | null = null;
  currentPayment: Payment | null = null;
  isLoadingPreview = false;
  previewError: string | null = null;
  currentPreviewUrl: string = '';
  currentPreviewPath: string = '';
  fileType: 'image' | 'pdf' | 'unknown' = 'unknown';

  // Direct image preview properties
  directImagePreview = false;
  directImageUrl: string | null = null;
  currentImageFileName: string | null = null;
  directImageRetryCount = 0;

  // Rejection reason property
  rejectionReason: string = '';
  showRejectionInput: boolean = false;
  paymentToReject: number | null = null;

  // Loading and notification properties
  showLoadingModal = false;
  loadingMessage = 'Traitement en cours...';
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';

  // Results date property
  calculatedResultsDate: string | null = null;
  constructor(
    private route: ActivatedRoute,
    private demandeService: DemandeService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private fb: FormBuilder,
    private scrollService: ScrollService,
    private paymentService: PaymentService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {

    this.fetchDemandeDetails();
  }
  ngAfterViewInit(): void {
    this.scrollService.getScrollTarget().subscribe(target => {
      if (target) {
        const element = document.getElementById(target);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
        this.scrollService.setScrollTarget(null); // Reset to prevent repeated scrolls
      }
    });
  }
  updateGroupedDevis(): void {
    const grouped = new Map<string, any>();
    this.devis.forEach((item: any) => {
      if (grouped.has(item.analyse)) {
        let existing = grouped.get(item.analyse);
        existing.quantite += item.quantite;
        existing.prix_total += item.prix_unitaire * item.quantite;
      } else {
        grouped.set(item.analyse, {
          ...item,
          prix_total: item.prix_unitaire * item.quantite
        });
      }
    });
    this.groupedDevis = Array.from(grouped.values());
  }
  initializeForm(): void {
    const formControls: any = {};
    this.groupedDevis.forEach((devis: any) => {
      formControls[`prix_${devis.analyse}`] = this.fb.control({
        value: devis.prix_unitaire || '',
        disabled: true // Initially disabled
      });
      this.isEditing[devis.analyse] = false; // Initially not editing
    });
    this.devisForm = this.fb.group(formControls);
  }

toggleEdit(analyse: string): void {
  // Don't allow editing if devis has been sent
  if (this.demande?.devis_sent === 'yes') {
    console.log('Cannot edit devis that has been sent to client');
    return;
  }

  this.isEditing[analyse] = !this.isEditing[analyse];
  const controlName = `prix_${analyse}`;
  const control = this.devisForm.get(controlName);

  if (control) {
    if (this.isEditing[analyse]) {
      control.enable();
      this.cdr.detectChanges(); // 🔥 Force change detection
    } else {
      control.disable();
    }
  } else {
    console.error(`Control ${controlName} not found`);
  }
}


  updateRegistre(analyse: string): void {
    // Don't allow updating if devis has been sent
    if (this.demande?.devis_sent === 'yes') {
      console.log('Cannot update devis that has been sent to client');
      alert('Impossible de modifier un devis déjà envoyé au client.');
      return;
    }

    const demandeId = this.route.snapshot.paramMap.get('demande_id');
    if (!demandeId) {
      alert('No demande ID available!');
      return;
    }

    const controlName = `prix_${analyse}`;
    const newPrice = this.devisForm.get(controlName)?.value;
    const originalDevis = this.groupedDevis.find(d => d.analyse === analyse);

    if (!newPrice || newPrice === originalDevis?.prix_unitaire) {
      alert('Aucune modification détectée.');
      return;
    }

    this.demandeService.updateAnalysePrice(demandeId, analyse, newPrice).subscribe({
      next: () => {
        alert('Prix mis à jour avec succès !');
        this.isEditing[analyse] = false; // Exit edit mode
        this.toggleEdit(analyse); // Disable the control
        this.fetchDevisDetails(demandeId); // Refresh devis data
      },
      error: (err) => {
        console.error('Erreur de mise à jour du prix:', err);
        alert('Échec de la mise à jour.');
      }
    });
  }
  fetchDemandeDetails(): void {
    const demandeId = this.route.snapshot.paramMap.get('demande_id'); // ✅ Match route param
    if (!demandeId) {
      this.errorMessage = 'No demande ID provided!';
      this.isLoading = false;
      return;
    }

    this.demandeService.getDemandeDetails(demandeId).subscribe({
      next: (response) => {
        // Convert null payment_id to undefined to match the Demande interface
        const { payment_id, ...rest } = response;
        this.demande = {
          ...rest,
          payment_id: payment_id || undefined, // Convert null to undefined
          samples: response.samples || []
        }; // ✅ Ensure samples is an array

        // Process the demande data
        console.log('rapport_created:', this.demande?.rapport_created);
        console.log('facture_id:', this.demande?.facture_id);
        console.log('demande details:', this.demande);
        console.log('🔍 DEBUG - devis_sent:', this.demande?.devis_sent);
        console.log('🔍 DEBUG - status:', this.demande?.status);
        console.log('🔍 DEBUG - Button should show:', (this.demande?.devis_sent === null || this.demande?.devis_sent === 'no') && this.demande?.status === 'valid');
        // Turn off the loading spinner immediately when demande is loaded
        this.isLoading = false;

        // Fetch devis if the demande is valid
        if (this.demande?.status === 'valid') {
          this.fetchDevisDetails(demandeId);
        }
console.log("demande details ",this.demande);
        // Fetch payment information for the demande
        this.fetchPaymentDetails(demandeId);

        // Force change detection to update the view
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching demande:', error);
        this.errorMessage = 'Failed to load demande details.';
        this.isLoading = false;
      }
    });
  }
  fetchDevisDetails(demandeId: string): void {
    // Set devis loading state to true
    this.isDevisLoading = true;

    this.demandeService.getDevis(demandeId).subscribe({
      next: (response) => {
        this.devis = response;
        this.updateGroupedDevis(); // Compute grouped data once
        this.initializeForm(); // Initialize form after updating grouped data

        // Set devis loading state to false
        this.isDevisLoading = false;
        this.cdr.detectChanges(); // Ensure UI updates
      },
      error: (error) => {
        console.error('Error fetching devis:', error);
        this.errorMessage = 'Failed to load devis details.';

        // Set devis loading state to false even on error
        this.isDevisLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Fetch payment information for a demande
   * @param demandeId The demande ID to fetch payments for
   */
  fetchPaymentDetails(demandeId: string): void {
    this.isLoadingPayments = true;
    this.paymentError = null;

    this.paymentService.getPaymentsByDemandeId(demandeId).subscribe({
      next: (response) => {
        console.log('Payment response:', response);
        if (response && response.status === 'success') {
          this.payments = response.data || [];
        } else {
          this.payments = [];
          this.paymentError = 'Unexpected response format';
        }
        this.isLoadingPayments = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching payments:', error);
        this.paymentError = 'Failed to load payment information';
        this.isLoadingPayments = false;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Get the status text for a payment status
   * @param status The payment status
   * @returns The status text in French
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'pending': return 'En attente';
      case 'approved': return 'Approuvé';
      case 'rejected': return 'Rejeté';
      default: return 'Inconnu';
    }
  }

  /**
   * Get the CSS class for a payment status
   * @param status The payment status
   * @returns The CSS class for the status
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'pending': return 'status-pending';
      case 'approved': return 'status-approved';
      case 'rejected': return 'status-rejected';
      default: return '';
    }
  }

  /**
   * Get the icon for a payment status
   * @param status The payment status
   * @returns The Font Awesome icon for the status
   */
  getStatusIcon(status: string): any {
    switch (status) {
      case 'pending': return this.faSpinner;
      case 'approved': return this.faCheck;
      case 'rejected': return this.faTimes;
      default: return this.faSpinner;
    }
  }

  /**
   * View a payment proof file
   * @param paymentProofPath The path to the payment proof file
   * @param paymentId The ID of the payment
   */
  viewPaymentProof(paymentProofPath: string, paymentId?: number): void {
    console.log('View payment proof called with path:', paymentProofPath);

    if (!paymentProofPath) {
      console.error('Payment proof path is empty');
      this.paymentError = 'Aucun justificatif de paiement disponible';
      return;
    }

    // Find the current payment in the payments array
    if (paymentId) {
      this.currentPayment = this.payments.find(p => p.id === paymentId) ?? null;
      console.log('Current payment object:', this.currentPayment);
    }

    // Check if we're already showing this payment's preview (either type)
    const isDirectImageActive = this.directImagePreview && this.currentPayment?.id === paymentId;

    // If we're already showing this payment's preview, toggle it off
    if (isDirectImageActive) {
      this.closeDirectImagePreview();
      return;
    } else if (this.showPreview && this.previewPaymentId === paymentId) {
      this.closePreview();
      return;
    }

    // Determine file type based on file extension
    const fileName = this.getFileNameFromPath(paymentProofPath).toLowerCase();
    let isImage = false;

    if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') ||
        fileName.endsWith('.gif') || fileName.endsWith('.bmp')) {
      console.log('File type detected from filename: Image');
      isImage = true;
    }

    // For images, show directly under the payment history
    if (isImage) {
      // Close any existing preview
      this.closePreview();

      // Reset retry counter
      this.directImageRetryCount = 0;

      // Try to use the direct URLs from the payment object first if available
      if (this.currentPayment?.payment_proof_url) {
        console.log('Using payment_proof_url for direct image preview:', this.currentPayment.payment_proof_url);
        this.directImageUrl = this.currentPayment.payment_proof_url;
      } else if (this.currentPayment?.direct_url) {
        console.log('Using direct_url for direct image preview:', this.currentPayment.direct_url);
        this.directImageUrl = this.currentPayment.direct_url;
      } else {
        // Try with different URL constructions
        // First try with the full path
        const fullPathUrl = this.paymentService.getPaymentProofUrl(paymentProofPath);
        console.log('Using full path URL for direct image preview:', fullPathUrl);
        this.directImageUrl = fullPathUrl;
      }

      // Set direct image preview properties
      this.currentImageFileName = this.getFileNameFromPath(paymentProofPath);
      this.directImagePreview = true;

      // Store the current payment ID for reference
      this.previewPaymentId = paymentId ?? null;

      return;
    }

    // For non-image files, continue with the existing preview logic
    // Reset preview state
    this.isLoadingPreview = true;
    this.previewError = null;
    this.currentPreviewPath = paymentProofPath;
    this.currentPreviewUrl = '';
    this.fileType = 'pdf'; // Default to PDF
    this.showPreview = true;
    this.previewPaymentId = paymentId ?? null;

    try {
      // Determine file type based on file extension
      const fileName = this.getFileNameFromPath(paymentProofPath).toLowerCase();
      if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') ||
          fileName.endsWith('.gif') || fileName.endsWith('.bmp')) {
        console.log('File type detected from filename: Image');
        this.fileType = 'image';
      } else if (fileName.endsWith('.pdf')) {
        console.log('File type detected from filename: PDF');
        this.fileType = 'pdf';
      } else {
        console.log('File type: Unknown (not recognized)');
        this.fileType = 'unknown';
      }

      // Get the URL from the service
      const url = this.paymentService.getPaymentProofUrl(paymentProofPath);
      console.log('Preview URL:', url);

      // Set the URL directly for all file types
      this.currentPreviewUrl = url;

      // Mark loading as complete
      this.isLoadingPreview = false;

      // Set a timeout to hide the loading spinner if it's still showing after 3 seconds
      setTimeout(() => {
        if (this.isLoadingPreview) {
          console.log('Loading timeout reached, hiding spinner');
          this.isLoadingPreview = false;
        }
      }, 3000);
    } catch (error) {
      console.error('Error preparing preview:', error);
      this.isLoadingPreview = false;
      this.previewError = 'Impossible de préparer l\'aperçu du justificatif';
    }
  }

  /**
   * Close the preview
   */
  closePreview(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.currentPreviewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.currentPreviewUrl);
    }

    this.showPreview = false;
    this.currentPreviewUrl = '';
    this.currentPreviewPath = '';
    this.previewError = null;
    this.previewPaymentId = null;

    // Also close direct image preview if it's open
    this.closeDirectImagePreview();
  }

  /**
   * Close the direct image preview
   */
  closeDirectImagePreview(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.directImageUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.directImageUrl);
    }

    this.directImagePreview = false;
    this.directImageUrl = null;
    this.currentImageFileName = null;
    this.directImageRetryCount = 0; // Reset retry counter
  }

  /**
   * Handle direct image loading errors
   * @param event The error event
   */
  handleDirectImageError(event: Event): void {
    console.error('Direct image failed to load:', event);

    // Log additional information for debugging
    console.log('Current direct image URL:', this.directImageUrl);
    console.log('Current payment object:', this.currentPayment);

    // Log the actual payment proof path from the payment object
    if (this.currentPayment?.payment_proof) {
      console.log('Original payment_proof path:', this.currentPayment.payment_proof);
    }

    // Create a counter to track attempts
    if (!this.directImageRetryCount) {
      this.directImageRetryCount = 1;
    } else {
      this.directImageRetryCount++;
    }

    console.log(`Image load retry attempt: ${this.directImageRetryCount}`);

    // After 3 attempts, try a different approach
    if (this.directImageRetryCount >= 3) {
      console.log('Multiple retries failed, trying alternative approach');

      // Try to open the file in a new tab instead
      if (this.currentPayment?.payment_proof) {
        // Try to download the file instead of viewing it
        this.downloadPaymentProof(this.currentPayment.payment_proof);

        // Show an error notification
        this.showNotification = true;
        this.notificationMessage = 'Impossible de charger l\'image. Le téléchargement a été lancé à la place.';
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';

        // Hide notification after 3 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 3000);

        // Close the preview
        this.closeDirectImagePreview();
        return;
      }

      // Show an error notification
      this.showNotification = true;
      this.notificationMessage = 'Impossible de charger l\'image. Essayez de télécharger le fichier.';
      this.notificationIcon = '❌';
      this.notificationColor = '#dc3545';

      // Hide notification after 3 seconds
      setTimeout(() => {
        this.showNotification = false;
      }, 3000);

      // Close the preview
      this.closeDirectImagePreview();
      return;
    }

    // Try alternative URLs if available
    if (this.currentPayment?.payment_proof_url && this.directImageUrl !== this.currentPayment.payment_proof_url) {
      console.log('Trying payment_proof_url after image error:', this.currentPayment.payment_proof_url);
      this.directImageUrl = this.currentPayment.payment_proof_url;
      return; // Let the image try to load with the new URL
    }

    if (this.currentPayment?.direct_url && this.directImageUrl !== this.currentPayment.direct_url) {
      console.log('Trying direct_url after image error:', this.currentPayment.direct_url);
      this.directImageUrl = this.currentPayment.direct_url;
      return; // Let the image try to load with the new URL
    }

    // Try constructing a URL directly from the payment_proof field if available
    if (this.currentPayment?.payment_proof) {
      // Try with payments/ prefix
      if (!this.directImageUrl?.includes('payments/')) {
        const prefixedUrl = this.paymentService.getPaymentProofUrl(`payments/${this.getFileNameFromPath(this.currentPayment.payment_proof)}`);
        console.log('Trying with payments/ prefix:', prefixedUrl);
        this.directImageUrl = prefixedUrl;
        return;
      }

      // Try without any prefix
      const rawFileName = this.getFileNameFromPath(this.currentPayment.payment_proof);
      const directUrl = this.paymentService.getPaymentProofUrl(rawFileName);
      console.log('Trying with raw filename:', directUrl);
      this.directImageUrl = directUrl;
      return;
    }

    // If we get here, all immediate options failed
    // We'll let the retry counter handle further attempts
    console.error('All immediate URL options failed, will retry with different approach next time');
  }

  /**
   * Handle keyboard events for the preview
   * @param event The keyboard event
   */
  handleKeydown(event: KeyboardEvent): void {
    // Close the preview when Escape key is pressed
    if (event.key === 'Escape') {
      this.closePreview();
    }
  }

  /**
   * Retry loading the preview
   */
  retryLoadPreview(): void {
    if (this.currentPreviewPath) {
      this.viewPaymentProof(this.currentPreviewPath, this.previewPaymentId ?? undefined);
    }
  }

  /**
   * Open PDF in a new tab
   */
  openPdfInNewTab(): void {
    if (this.currentPayment?.payment_proof_url) {
      window.open(this.currentPayment.payment_proof_url, '_blank');
    } else if (this.currentPayment?.direct_url) {
      window.open(this.currentPayment.direct_url, '_blank');
    } else if (this.currentPreviewUrl) {
      window.open(this.currentPreviewUrl, '_blank');
    }
  }

  /**
   * Open the file directly in a new tab
   */
  openDirectUrl(): void {
    // Try to use the current payment's URLs if available
    if (this.currentPayment?.payment_proof_url) {
      console.log('Opening signed URL:', this.currentPayment.payment_proof_url);
      window.open(this.currentPayment.payment_proof_url, '_blank');
      return;
    }

    if (this.currentPayment?.direct_url) {
      console.log('Opening direct URL from payment:', this.currentPayment.direct_url);
      window.open(this.currentPayment.direct_url, '_blank');
      return;
    }

    if (this.currentPreviewUrl) {
      console.log('Opening preview URL:', this.currentPreviewUrl);
      window.open(this.currentPreviewUrl, '_blank');
    } else if (this.currentPreviewPath) {
      // Try to get a direct URL from the service
      const fileName = this.getFileNameFromPath(this.currentPreviewPath);
      const directUrl = this.paymentService.getPaymentProofUrl(fileName);
      console.log('Opening constructed direct URL:', directUrl);
      window.open(directUrl, '_blank');
    }
  }

  /**
   * Handle image loading errors
   * @param event The error event
   */
  handleImageError(event: Event): void {
    console.error('Image failed to load in the DOM:', event);

    // Log the URL that failed
    console.log('Failed URL:', this.currentPreviewUrl);
    console.log('Current file type:', this.fileType);
    console.log('Current preview path:', this.currentPreviewPath);

    // Change the file type to PDF as fallback
    this.fileType = 'pdf';

    // Try using the signed URL if available
    if (this.currentPayment?.payment_proof_url && this.currentPreviewUrl !== this.currentPayment.payment_proof_url) {
      console.log('Trying signed URL after image error:', this.currentPayment.payment_proof_url);
      this.currentPreviewUrl = this.currentPayment.payment_proof_url;
      return; // Let the image try to load with the new URL
    }

    // Try using the direct URL if available
    if (this.currentPayment?.direct_url && this.currentPreviewUrl !== this.currentPayment.direct_url) {
      console.log('Trying direct URL after image error:', this.currentPayment.direct_url);
      this.currentPreviewUrl = this.currentPayment.direct_url;
      return; // Let the image try to load with the new URL
    }
  }

  /**
   * Download a payment proof file
   * @param paymentProofPath The path to the payment proof file
   */
  downloadPaymentProof(paymentProofPath: string): void {
    console.log('Download payment proof called with path:', paymentProofPath);

    if (!paymentProofPath) {
      console.error('Payment proof path is empty');
      this.paymentError = 'Aucun justificatif de paiement disponible';
      return;
    }

    this.isLoadingPayments = true;
    this.paymentError = null;

    // Show notification
    this.showNotification = true;
    this.notificationMessage = 'Téléchargement en cours...';
    this.notificationIcon = '⏳';
    this.notificationColor = '#007bff';

    // Try to use the current payment's direct URL if available
    if (this.currentPayment?.id && this.previewPaymentId === this.currentPayment.id) {
      console.log('Using current payment for download');
      // Try signed URL first
      if (this.currentPayment.payment_proof_url) {
        console.log('Using signed URL for download:', this.currentPayment.payment_proof_url);
        const link = document.createElement('a');
        link.href = this.currentPayment.payment_proof_url;
        link.download = this.getFileNameFromPath(paymentProofPath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.isLoadingPayments = false;

        // Show success notification
        this.showNotification = true;
        this.notificationMessage = 'Téléchargement réussi';
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';

        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
        return;
      }

      // Try direct URL next
      if (this.currentPayment.direct_url) {
        console.log('Using direct URL for download:', this.currentPayment.direct_url);
        const link = document.createElement('a');
        link.href = this.currentPayment.direct_url;
        link.download = this.getFileNameFromPath(paymentProofPath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.isLoadingPayments = false;

        // Show success notification
        this.showNotification = true;
        this.notificationMessage = 'Téléchargement réussi';
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';

        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
        return;
      }
    }

    // Use the service method that handles authentication
    this.paymentService.downloadPaymentProof(paymentProofPath).subscribe({
      next: (blob) => {
        this.isLoadingPayments = false;

        // Check if the blob is valid (not empty and has the correct type)
        if (blob.size === 0) {
          console.error('Downloaded blob is empty');
          this.paymentError = 'Le fichier téléchargé est vide';

          // Show error notification
          this.showNotification = true;
          this.notificationMessage = 'Échec du téléchargement: fichier vide';
          this.notificationIcon = '❌';
          this.notificationColor = '#dc3545';

          setTimeout(() => {
            this.showNotification = false;
          }, 4000);

          return;
        }

        // Create a blob URL and trigger download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Extract filename from the path or use default
        const fileName = this.getFileNameFromPath(paymentProofPath);
        console.log('Using filename:', fileName);
        link.download = fileName;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        window.URL.revokeObjectURL(url);

        // Show success notification
        this.showNotification = true;
        this.notificationMessage = 'Téléchargement réussi';
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';

        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      },
      error: (error) => {
        this.isLoadingPayments = false;
        console.error('Error downloading payment proof:', error);
        this.paymentError = 'Impossible de télécharger le justificatif de paiement';

        // Show error notification
        this.showNotification = true;
        this.notificationMessage = 'Échec du téléchargement';
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';

        setTimeout(() => {
          this.showNotification = false;
        }, 4000);

        // Try direct URL as fallback
        try {
          const url = this.paymentService.getPaymentProofUrl(paymentProofPath);
          console.log('Trying fallback URL for download:', url);

          // Show fallback notification
          this.showNotification = true;
          this.notificationMessage = 'Tentative de téléchargement alternatif...';
          this.notificationIcon = '⏳';
          this.notificationColor = '#ffc107';

          const link = document.createElement('a');
          link.href = url;
          link.download = this.getFileNameFromPath(paymentProofPath);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Show success notification for fallback
          setTimeout(() => {
            this.showNotification = true;
            this.notificationMessage = 'Téléchargement alternatif réussi';
            this.notificationIcon = '✅';
            this.notificationColor = '#28a745';

            setTimeout(() => {
              this.showNotification = false;
            }, 4000);
          }, 1000);
        } catch (e) {
          console.error('Fallback download also failed:', e);

          // Show final error notification
          this.showNotification = true;
          this.notificationMessage = 'Toutes les tentatives de téléchargement ont échoué';
          this.notificationIcon = '❌';
          this.notificationColor = '#dc3545';

          setTimeout(() => {
            this.showNotification = false;
          }, 4000);
        }
      }
    });
  }

  /**
   * Download the current file being previewed
   */
  downloadCurrentFile(): void {
    if (this.currentPreviewPath) {
      this.downloadPaymentProof(this.currentPreviewPath);
    }
  }

  /**
   * Show the rejection reason input field
   * @param paymentId The ID of the payment to reject
   */
  showRejectPaymentForm(paymentId: number): void {
    this.paymentToReject = paymentId;
    this.showRejectionInput = true;
    this.rejectionReason = '';
  }

  /**
   * Cancel the rejection process
   */
  cancelRejection(): void {
    this.showRejectionInput = false;
    this.paymentToReject = null;
    this.rejectionReason = '';
  }

  /**
   * Submit the rejection with the reason
   */
  submitRejection(): void {
    if (!this.paymentToReject) {
      console.error('No payment selected for rejection');
      return;
    }

    if (!this.rejectionReason.trim()) {
      alert('La raison du rejet est requise. Opération annulée.');
      return;
    }

    // Call the updatePaymentStatus method with the rejection reason
    this.updatePaymentStatus(this.paymentToReject, 'rejected', this.rejectionReason);

    // Reset the rejection form
    this.showRejectionInput = false;
    this.paymentToReject = null;
    this.rejectionReason = '';
  }

  /**
   * Calculate results date based on payment approval date
   * @param startDate The date to calculate from (payment approval date)
   * @returns Formatted date string (DD/MM/YYYY)
   */
  calculateResultsDate(startDate: Date): string {
    let workingDaysToAdd = 7;
    let currentDate = new Date(startDate);

    // Skip weekends (Saturday and Sunday)
    while (workingDaysToAdd > 0) {
      currentDate.setDate(currentDate.getDate() + 1);

      // Skip weekends (0 = Sunday, 6 = Saturday)
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        workingDaysToAdd--;
      }
    }

    // Format the date as DD/MM/YYYY
    const day = String(currentDate.getDate()).padStart(2, '0');
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const year = currentDate.getFullYear();

    return `${day}/${month}/${year}`;
  }

  /**
   * Update the status of a payment
   * @param paymentId The ID of the payment to update
   * @param status The new status ('approved' or 'rejected')
   * @param rejectionReason Optional reason for rejection
   */
  updatePaymentStatus(paymentId: number, status: 'approved' | 'rejected', rejectionReason?: string): void {
    if (!paymentId) {
      console.error('Payment ID is required');
      return;
    }

    // Show loading notification
    this.showNotification = true;
    this.notificationMessage = 'Mise à jour du statut du paiement...';
    this.notificationIcon = '⏳';
    this.notificationColor = '#007bff';

    // If rejecting, ensure we have a reason
    if (status === 'rejected') {
      if (!rejectionReason || rejectionReason.trim() === '') {
        alert('La raison du rejet est requise. Opération annulée.');
        this.showNotification = false;
        return;
      }

      // Call the service to update the payment status with rejection reason
      this.paymentService.updatePaymentStatus(paymentId, status, rejectionReason).subscribe({
        next: (response) => {
          console.log('Payment status updated:', response);

          // Update the payment in the local array
          const index = this.payments.findIndex(p => p.id === paymentId);
          if (index !== -1) {
            this.payments[index].status = status;

            // If this is the current payment being previewed, update it
            if (this.currentPayment && this.currentPayment.id === paymentId) {
              this.currentPayment.status = status;
            }
          }

          // Show success notification
          this.showNotification = true;
          this.notificationMessage = 'Paiement rejeté avec succès';
          this.notificationIcon = '✅';
          this.notificationColor = '#28a745';

          setTimeout(() => {
            this.showNotification = false;
          }, 4000);
        },
        error: (error) => {
          console.error('Error updating payment status:', error);

          // Show error notification
          this.showNotification = true;
          this.notificationMessage = 'Erreur lors de la mise à jour du statut du paiement';
          this.notificationIcon = '❌';
          this.notificationColor = '#dc3545';

          setTimeout(() => {
            this.showNotification = false;
          }, 4000);
        }
      });
    } else {
      // For approved status, no rejection reason needed
      this.paymentService.updatePaymentStatus(paymentId, status).subscribe({
        next: (response) => {
          console.log('Payment status updated:', response);

          // Update the payment in the local array
          const index = this.payments.findIndex(p => p.id === paymentId);
          if (index !== -1) {
            this.payments[index].status = status;

            // If this is the current payment being previewed, update it
            if (this.currentPayment && this.currentPayment.id === paymentId) {
              this.currentPayment.status = status;
            }
          }

          // Calculate results date based on payment approval date (today)
          if (status === 'approved') {
            this.calculatedResultsDate = this.calculateResultsDate(new Date());
            console.log('Calculated results date after payment approval:', this.calculatedResultsDate);
          }

          // Show success notification with results date if approved
          this.showNotification = true;
          if (status === 'approved') {
            this.notificationMessage = `Paiement approuvé avec succès. Les résultats seront prêts d'ici le ${this.calculatedResultsDate}`;
          } else {
            this.notificationMessage = 'Paiement rejeté avec succès';
          }
          this.notificationIcon = '✅';
          this.notificationColor = '#28a745';

          setTimeout(() => {
            this.showNotification = false;
          }, 4000);
        },
        error: (error) => {
          console.error('Error updating payment status:', error);

          // Show error notification
          this.showNotification = true;
          this.notificationMessage = 'Erreur lors de la mise à jour du statut du paiement';
          this.notificationIcon = '❌';
          this.notificationColor = '#dc3545';

          setTimeout(() => {
            this.showNotification = false;
          }, 4000);
        }
      });
    }
  }

  /**
   * Get the file name from a path
   * @param path The file path
   * @returns The file name
   */
  getFileNameFromPath(path: string): string {
    if (!path) return 'file';

    // Extract just the filename if it contains a path
    let fileName = path;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() || fileName;
    }

    return fileName;
  }
  groupDevisData(): any[] {
    const grouped = new Map<string, any>();
    this.devis.forEach((item: any) => {
      if (grouped.has(item.analyse)) {
        let existing = grouped.get(item.analyse);
        existing.quantite += item.quantite;
        existing.prix_total += item.prix_unitaire * item.quantite;
      } else {
        grouped.set(item.analyse, {
          ...item,
          prix_total: item.prix_unitaire * item.quantite
        });
      }
    });
    return Array.from(grouped.values());
  }

  getTotal(): number {
    return this.groupedDevis.reduce((sum, d) => sum + d.prix_total, 0);
  }
  preValidateDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Pré-validation de la demande...';

    this.demandeService.updateStatusToOngoing(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Demande pré-validée!", response);

        // Show success notification
        this.notificationMessage = "La demande a été pré-validée. Le client est notifié d'apporter les échantillons.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);

        this.fetchDemandeDetails(); // Refresh demande details
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de la pré-validation de la demande:", error);

        // Show error notification
        this.notificationMessage = "Échec de la pré-validation de la demande. Veuillez réessayer.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }
  validateDemande(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Validation de la demande...';

    this.demandeService.updateStatusToValidated(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Demande validée!", response);

        // Show success notification
        this.notificationMessage = "La demande a été complètement validée.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);

        this.fetchDemandeDetails();
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de la validation de la demande:", error);

        // Show error notification
        this.notificationMessage = "Échec de la validation de la demande.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }
  envoyerDevis(): void {
    console.log('🚀 envoyerDevis() called');
    console.log('🔍 demande:', this.demande);
    console.log('🔍 demande_id:', this.demande?.demande_id);

    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Envoi du devis...';

    this.demandeService.envoyerDevis(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("devis envoyé!", response);

        // Show success notification
        this.notificationMessage = "Le devis a été envoyé au client.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);

        this.fetchDemandeDetails();
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de l'envoi du devis:", error);

        // Show error notification
        this.notificationMessage = "Échec de l'envoi du devis.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }
  createInvoice(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Création de la facture...';

    this.demandeService.createFacture(this.demande.demande_id).subscribe({
      next: (response) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("facture cree!", response);

        // Show success notification
        this.notificationMessage = "La facture a été créée avec succès.";
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);

        this.fetchDemandeDetails();
      },
      error: (error) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Erreur lors de la création de la facture:", error);

        // Show error notification
        this.notificationMessage = "Échec de création de la facture.";
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }

  // ✅ Function for validation with derogation - Modified to skip backend call
  validateWithDerogation(): void {
    if (!this.demande || !this.demande.demande_id) {
      console.error("No demande found!");
      return;
    }

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Validation avec dérogation...';

    // Simulate processing time (optional)
    setTimeout(() => {
      // Hide loading spinner
      this.showLoadingModal = false;

      console.log("Demande validée avec dérogation! (Redirection directe)");

      // Show success notification
      this.notificationMessage = "La demande a été validée avec dérogation.";
      this.notificationIcon = '✅';
      this.notificationColor = '#28a745';
      this.showNotification = true;

      // Hide notification after 2 seconds and redirect
      setTimeout(() => {
        this.showNotification = false;

        // ✅ Update status locally
        if (this.demande) {
          this.demande.status = 'validated_with_derogation';
        }

        // Navigate to derogation-details with the demande ID
        this.router.navigate(['/derogation-details', this.demande?.demande_id]);
      }, 2000);
    }, 1000); // Simulate 1 second of processing time
  }

  // Variable to track if we're in edit mode
  isEditingPdf: boolean = false;
  editablePrintWindow: Window | null = null;

  /**
   * Génère et imprime un PDF de la demande avec les détails du client et des échantillons
   */
  printDemande(): void {
    if (!this.demande) {
      console.error('Aucune demande à imprimer');
      return;
    }

    // Générer les lignes du tableau d'échantillons
    const sampleRows = this.demande.samples.map(sample => {
      // Convertir le tableau d'analyses en liste HTML
      const analysesList = sample.analyses_demandees?.length > 0
        ? `<ul style="margin: 0; padding-left: 20px;">${sample.analyses_demandees.map(analysis => `<li>${analysis}</li>`).join('')}</ul>`
        : '—';

      // Déterminer l'état de l'échantillon avec des cases à cocher
      const etatLiq = sample.etat === 'Liquide' ? '✓' : '';
      const etatSol = sample.etat === 'Solide' ? '✓' : '';
      const etatRig = sample.etat === 'Rigide' ? '✓' : '';
      const etatCgl = sample.etat === 'Congelé' ? '✓' : '';

      return `
        <tr>
          <td><div contenteditable="true" class="editable-field">${sample.identification_echantillon || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${sample.nature_echantillon || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${sample.provenance || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${sample.masse_echantillon || '—'}</div></td>
          <td>${etatLiq}</td>
          <td>${etatSol}</td>
          <td>${etatRig}</td>
          <td>${etatCgl}</td>
          <td><div contenteditable="true" class="editable-field">${analysesList}</div></td>
        </tr>
      `;
    }).join('');

    // Devis section removed as requested

    // Formater la date pour l'affichage
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    };

    // Calculate total pages based on samples
    const samplesPerPage = 5; // Assuming 5 samples can fit on a page
    const totalSamples = this.demande.samples.length;
    const samplePages = Math.ceil(totalSamples / samplesPerPage);
    const totalPages = Math.max(1, samplePages); // At least 1 page

    // Générer le HTML complet pour l'impression avec des champs éditables
    const printHtml = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire Client Complet</title>
    <style>
        /* Print Settings */
        @media print {
            @page {
                size: A4;
                margin: 5mm;
            }
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                counter-reset: pages 0;
            }
            .logo-cell img {
                width: 30mm;
                height: auto;
            }
            .header-table th, .header-table td {
                font-size: 10pt;
            }
            /* Page break after sample table if needed */
            .sample-table-container {
                page-break-after: auto;
            }
            /* Make header repeat on each page */
            thead {
                display: table-header-group;
            }
            tfoot {
                display: table-footer-group;
            }
            /* Ensure proper page breaks */
            tr {
                page-break-inside: avoid;
            }
            .header-table {
                page-break-after: avoid;
            }
            /* Add space at top of each page for the header */
            .content-wrapper {
                counter-increment: pages;
                page-break-before: always;
            }
            .content-wrapper.first-page {
                page-break-before: avoid;
            }
            .page-number {
                display: inline-block;
                min-width: 40px;
            }
            .page-number::before {
                content: counter(pages);
            }
            .page-number::after {
                content: " / " attr(data-total-pages);
            }
            .page-number span {
                display: none;
            }
        }

        /* Global Styles */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        /* Editable Fields Styles */
        .editable-field {
            min-height: 20px;
            border: 1px dashed #ccc;
            padding: 2px 5px;
            background-color: #f9f9f9;
        }
        .editable-field:focus {
            outline: none;
            border: 1px dashed #2496d3;
            background-color: #e9f7fe;
        }

        /* Label styles */
        .label {
            font-weight: bold;
        }

        @media print {
            .editable-field {
                border: none;
                background-color: transparent;
            }
        }



        /* Print Controls */
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #2496d3;
            color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            cursor: move;
            user-select: none;
        }
        .print-controls .drag-handle {
            padding: 5px 0;
            margin-bottom: 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            cursor: move;
        }
        .print-controls button {
            background-color: white;
            color: #2496d3;
            border: none;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            display: block;
            width: 100%;
        }
        .print-controls button:hover {
            background-color: #f0f0f0;
        }

        @media print {
            .print-controls {
                display: none;
            }
            .editable-field {
                border: none;
                background-color: transparent;
            }
        }

        /* Header Table */
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px auto;
            margin-bottom: 40px;
            page-break-inside: avoid;
            page-break-after: avoid;
        }
        /* Main table for page layout */
        body > table {
            width: 100%;
            border-collapse: collapse;
            border: none;
        }
        body > table > thead > tr > td,
        body > table > tbody > tr > td,
        body > table > tfoot > tr > td {
            padding: 0;
            border: none;
        }
        .header-table th, .header-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        /* Content wrapper to handle page breaks properly */
        .content-wrapper {
            page-break-before: always;
        }
        .first-page {
            page-break-before: avoid;
        }
        .logo-cell {
            vertical-align: middle;
            text-align: center;
        }
        .second-column {

            background-color: #e0e0e0;
            text-align: center;
            font-weight: bold;
            font-size: 24pt;
            vertical-align: middle;
            padding: 20px;
        }
        .third-column {
            text-align: right;
        }

        /* Client Info Section */
        .form-section {
            max-width: 1200px;
            margin: 40px auto;
            background: transparent;
            padding: 30px;
            border-radius: 8px;

        }
        .client-info-container {
            margin-bottom: 40px;
        }
        .header-info {
            display: flex;
            justify-content: space-between;
            border-bottom: 2px solidrgb(0, 0, 0);
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .header-label {
            font-size: 18px;
            color:rgb(0, 0, 0);
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .client-table {
            width: 100%;
            border-collapse: collapse;
        }
        .client-table td {
            border: 1px solid #000;
            padding: 12px;
            vertical-align: top;
        }
        .label-cell {
            font-weight: bold;
            width: 25%;
            background-color: transparent;
        }
        .data-cell {
            width: 75%;
            padding-left: 15px;
        }

        /* Sample Table Section */
        .sample-table-container {
            margin: 20px auto;
        }
        .sample-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }
        .sample-table th, .sample-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .sample-table thead th {
            font-weight: bold;
        }

        /* Payment Section */
        .payment-section {
            margin: 20px auto;
            width: 100%;
            max-width: 800px;
            padding: 20px;
        }
        .payment-section h3 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
        }
        .payment-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .option {
            display: flex;
            align-items: center;
        }
        .option input {
            margin-right: 5px;
        }
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-block-left, .signature-block-right {
            width: 45%;
        }
        .signature-block-left {
            text-align: left;
        }
        .signature-block-right {
            text-align: right;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            height: 30px;
            margin-top: 10px;
        }

        /* Footer Section */
        .footer-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 40px;
            box-sizing: border-box;
            padding-top: 50px;
        }
        .bottom-text {
            border-top: 1px solid #000;
            padding-top: 20px;
            font-size: 14px;
            text-align: center;
            font-style: italic;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <table>
        <thead>
            <!-- Document Header that will repeat on each page -->
            <tr>
                <td colspan="100%">
                    <table class="header-table">
                        <tr>
                            <td rowspan="4" class="logo-cell">
                                <img src="${window.location.origin}/kls.png" alt="Logo Left" style="width: 120px; height: auto;">
                            </td>
                            <td class="third-column" rowspan="4" style="vertical-align: middle; text-align:center;font-size:20px;font-weight:bold;" contenteditable="true">
                        DEMANDE D'ANALYSE
                      </td>
                            <td class="third-column"><span class="label">CODE:</span> <span contenteditable="true" class="editable-field">FE/03-PRT/08</span></td>
                        </tr>
                        <tr>
                            <td class="third-column"><span class="label">VERSION :</span> <span contenteditable="true" class="editable-field">01</span></td>
                        </tr>
                        <tr>
                            <td class="third-column"><span class="label">DATE :</span> <span contenteditable="true" class="editable-field">${formatDate(this.demande.demande_date)}</span></td>
                        </tr>
                        <tr>
                            <td class="third-column"><span class="label">PAGE :</span> <div class="page-number" data-total-pages="${totalPages}"></div></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan="100%">
                    <div class="content-wrapper first-page">

    <!-- Form Structure -->
    <div style="margin: 20px 0;">
      <style>
        .form-container {
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        }

        .form-row {
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
        }

        .form-label {
          font-weight: bold;
          margin-right: 10px;
        }

        .input-boxes {
          display: flex;
          gap: 2px;
        }

        .input-box {
          width: 30px;
          height: 30px;
          border: 1px solid black;
          display: inline-block;
          text-align: center;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .separator {
          margin: 0 5px;
        }

        .underlined-label {
          font-style: italic;
          text-decoration: underline;
          margin-bottom: 10px;
          display: block;
        }

        .dotted-line {
          border-bottom: 1px dotted black;
          flex-grow: 1;
          height: 1.2em;
          margin-right: 10px;
        }

        .amount-field {
          text-align: right;
          flex-grow: 1;
        }

        .date-group {
          display: flex;
          gap: 10px;
        }
      </style>

      <div class="form-container">
        <div class="form-row">
          <div style="flex: 1;">
            <div class="form-label">Demande N°:</div>
            <div class="input-boxes">
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(0, 1) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(1, 2) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(2, 3) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(3, 4) : ''}</div>
              <span class="separator"></span>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(4, 5) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(5, 6) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(6, 7) : ''}</div>
            </div>
          </div>

          <div style="flex: 1;">
            <div class="form-label">Date d'enregistrement :</div>
            <div class="date-group">
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(0, 1)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(1, 2)}</div>
              </div>
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(3, 4)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(4, 5)}</div>
              </div>
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(8, 9)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(9, 10)}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <span class="underlined-label">Référence document client :</span>
        </div>

        <div class="form-row">
          <div style="flex: 2;">
            <span  style="font-weight:bold;">Quittance</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:200px; display:inline-block; margin-left:10px;" contenteditable="true"></span>
          </div>
        </div>

        <div class="form-row">
          <div style="flex: 2;">
            <span style="font-weight:bold;">Bon de commande</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:200px; display:inline-block; margin-left:10px;" contenteditable="true"></span>
          </div>
          <div style="flex: 1;">
            <span  style="font-weight:bold;">Montant HT</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:100px; display:inline-block; margin-left:10px;" contenteditable="true">${this.getTotal()} DT</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Information Section -->
    <div class="form-section">
<table class="client-table">
    <tr>
        <td class="label-cell">Client</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.user_name}${this.demande.user_nickname ? ' (' + this.demande.user_nickname + ')' : ''}</span></td>
        <td class="label-cell">Tél.</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.telephone || ''}</span></td>
    </tr>
    <tr>
        <td rowspan="2" class="label-cell">Adresse</td>
        <td rowspan="2" class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.adresse || ''}</span></td>
        <td class="label-cell">Fax</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.fax || ''}</span></td>
    </tr>
    <tr>
        <td class="label-cell">Mail</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.user_email}</span></td>
    </tr>
</table>
    </div>

    <!-- Sample Table Section -->
    <div class="sample-table-container">
        <table class="sample-table">
            <thead>
                <tr>
                    <th rowspan="3">Identification échantillon</th>
                    <th rowspan="3">Nature échantillon</th>
                    <th rowspan="3">Provenance</th>
                    <th colspan="5">Description de l'échantillon</th>
                    <th rowspan="3">Analyses demandées*</th>
                </tr>
                <tr>
                    <th rowspan="2">Qté<br>/Echantillon</th>
                    <th colspan="4">Etat</th>
                </tr>
                <tr>
                    <th>Liq</th>
                    <th>Sol</th>
                    <th>Rig</th>
                    <th>Cgl</th>
                </tr>
            </thead>
            <tbody>
                ${sampleRows}
            </tbody>
        </table>
    </div>

    <!-- Mode de règlement Section -->
    <div style="margin: 30px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
        <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">Mode de règlement :</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="carte" ${this.demande.mode_reglement === 'Carte bancaire' ? 'checked' : ''}>
                <label for="carte" style="margin-left: 5px;">Sur présentation de facture</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="virement" ${this.demande.mode_reglement === 'Virement bancaire' ? 'checked' : ''}>
                <label for="virement" style="margin-left: 5px;">Virement bancaire</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="convention" ${this.demande.mode_reglement === 'Convention' ? 'checked' : ''}>
                <label for="convention" style="margin-left: 5px;">Convention</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="collaboration" ${this.demande.mode_reglement === 'Collaboration' ? 'checked' : ''}>
                <label for="collaboration" style="margin-left: 5px;">Collaboration</label>
            </div>
        </div>

        <!-- NB Section -->
        <div style="margin-top: 20px; font-size: 14px;">
            <p style="font-weight: bold; margin-bottom: 10px;">NB :</p>
            <p style="margin-bottom: 5px;">-La durée des analyses est d'une semaine ouvrable pour un échantillon standard.</p>
            <p style="margin-bottom: 5px;">-Si N≥6 contacter le DL pour définir le délai à communiquer au client.</p>
            <p style="margin-bottom: 5px;">**Délai d'exécution souhaité par le client, méthode d'analyse autre que celle utilisée par le laboratoire ; état de l'emballage de l'échantillon….</p>
        </div>
    </div>

    <!-- Signatures Section -->
    <div style="display: flex; justify-content: space-between; margin: 40px 0;">
        <div style="width: 45%; text-align: left;">
            <p style="font-weight: bold; margin-bottom: 10px;">Signature du Client</p>

        </div>
        <div style="width: 45%; text-align: right;">
            <p style="font-weight: bold; margin-bottom: 10px;">Signature du réceptionnaire</p>

        </div>
    </div>

    <!-- Footer Section -->
    <div class="footer-container">
        <div class="bottom-text">
            Ce document est strictement confidentiel et ne doit pas être reproduit sans autorisation
        </div>
    </div>
    </div> <!-- Close content-wrapper -->
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="100%">
                    <!-- Footer content if needed -->
                </td>
            </tr>
        </tfoot>
    </table>

    <!-- Print Controls (Draggable) -->
    <div class="print-controls" id="draggable-print-controls">
        <div class="drag-handle">⋮⋮ Déplacer</div>
        <button id="print-button">Imprimer</button>
        <button id="cancel-button">Annuler</button>
    </div>

    <script>
        // Add event listeners to buttons
        document.getElementById('print-button').addEventListener('click', function() {
            // Hide controls before printing
            document.querySelector('.print-controls').style.display = 'none';
            // Print the document
            window.requestAnimationFrame(function() {
                window.print();
                // Show controls again after printing
                document.querySelector('.print-controls').style.display = 'block';
            });
        });

        document.getElementById('cancel-button').addEventListener('click', function() {
            window.close();
        });

        // Make the print controls draggable
        (function() {
            const dragElement = document.getElementById('draggable-print-controls');
            if (!dragElement) return;

            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            const dragHandle = document.querySelector('.drag-handle');

            if (dragHandle) {
                // If drag handle exists, attach mousedown to it
                dragHandle.onmousedown = dragMouseDown;
            } else {
                // Otherwise, attach mousedown to the whole element
                dragElement.onmousedown = dragMouseDown;
            }

            function dragMouseDown(e) {
                e.preventDefault();
                // Get the mouse cursor position at startup
                pos3 = e.clientX;
                pos4 = e.clientY;
                document.onmouseup = closeDragElement;
                // Call a function whenever the cursor moves
                document.onmousemove = elementDrag;
            }

            function elementDrag(e) {
                e.preventDefault();
                // Calculate the new cursor position
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;
                // Set the element's new position
                dragElement.style.top = (dragElement.offsetTop - pos2) + 'px';
                dragElement.style.left = (dragElement.offsetLeft - pos1) + 'px';
                dragElement.style.right = 'auto';
                dragElement.style.bottom = 'auto';
            }

            function closeDragElement() {
                // Stop moving when mouse button is released
                document.onmouseup = null;
                document.onmousemove = null;
            }
        })();
    </script>
</body>
</html>
    `;

    // Ouvrir une nouvelle fenêtre pour l'édition et l'impression
    this.editablePrintWindow = window.open('', '_blank', 'width=800,height=600');
    if (this.editablePrintWindow) {
      this.editablePrintWindow.document.write(printHtml);
      this.editablePrintWindow.document.close();
      this.isEditingPdf = true;
    } else {
      console.error('Impossible d\'ouvrir la fenêtre d\'impression. Veuillez autoriser les pop-ups.');
    }
  }
}