<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Events\DemandeStatusUpdate;
use App\Events\DerogationNotification;
use App\Mail\BringSampleEmail;
use App\Models\Demande;
use App\Models\Derogation;
use App\Models\User;
use App\Models\Devis;
use App\Models\Notification;
use App\Models\Sample;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\DemandeNotificationEmail;
use App\Mail\ValidationEmail;
class ReceptionistController extends Controller
{
    public function updateStatusToOngoing($demande_id)
    {
        // ✅ Find the demande by `demande_id` instead of `id`
        $demande = Demande::where('demande_id', $demande_id)->firstOrFail();

        // ✅ Update status
        $demande->status = 'ongoing_validation';
        $demande->save();

        // ✅ Save notification for client
        $notification=Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'Demande Échantillon',
            'message' => 'Veuillez apporter les échantillons à la réceptionniste.',
            'type' => 'demande',
            'demande_id' => $demande->id,
            'demande'=>$demande_id // Ensure it uses demande_id
        ]);
        $user = User::find($demande->user_id);
        $demande = Demande::find($demande->id);

        // Send the email with error handling


            Mail::to($user->email)->send(new BringSampleEmail($notification, $demande, $user));


        // ✅ Broadcast status update
        broadcast(new DemandeStatusUpdate($demande, 'Bring échantillons to the receptionist'))->toOthers();

        return response()->json(['message' => 'Client notified to bring échantillons']);
    }
    public function updateStatusToRejected($demande_id)
    {
        // ✅ Find the demande by `demande_id` instead of `id`
        $demande = Demande::where('demande_id', $demande_id)->firstOrFail();

        // ✅ Update status
        $demande->status = 'rejected';
        $demande->save();

        // ✅ Save notification for client
        Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'Demande rejetée',
            'message' => 'Demande de '.$demande->user->name.' de numero '.$demande->demande_id.' a échoué.',
            'type' => 'demande',
            'demande_id' => $demande->id,
            'demande'=>$demande_id // Ensure it uses demande_id
        ]);
        Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'Rejet de la Demande',
            'message' => 'Votre demande  de numero '.$demande->demande_id.' a échoué. Veuillez contacter le service de réception pour plus de détails ou pour soumettre une nouvelle demande corrigée',
            'type' => 'demande',
            'demande_id' => $demande->id,
            'demande'=>$demande_id // Ensure it uses demande_id
        ]);

        // ✅ Broadcast status update
        broadcast(new DemandeStatusUpdate($demande, 'Bring échantillons to the receptionist'))->toOthers();

        return response()->json(['message' => 'Client notified ']);
    }
    public function updateStatusToValidatedWithDerogation($demande_id)
    {
        // ✅ Find the demande by `demande_id` instead of `id`
        $demande = Demande::where('demande_id', $demande_id)->firstOrFail();

        // ✅ Update status
        $demande->status = 'derogation';
        $demande->save();

        // ✅ Save notification for client
        Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'derogation created',
            'message' => 'Demande avec derogation.',
            'type' => 'demande',
            'demande_id' => $demande->id,
            'demande'=>$demande_id // Ensure it uses demande_id
        ]);

        // ✅ Broadcast status update
        broadcast(new DemandeStatusUpdate($demande, 'Bring échantillons to the receptionist'))->toOthers();

        return response()->json(['message' => 'Client notified to bring échantillons']);
    }

    public function getNewDemandeNotifications()
    {

            // Get all notifications with title "Nouvelle Demande"
            $notifications = Notification::where('title', 'Nouvelle Demande')->get();

            return response()->json([
                'notifications' => $notifications,
            ]);

    }
    public function validateDemandeFinale($demande_id)
    {
        $demande = Demande::where('demande_id', $demande_id)->firstOrFail();

        $demande->status = 'valid';
        $demande->save();

        // Retrieve the first Devis associated with the demande
        $devis = Devis::where('demande_id', $demande->id)->first();

        // Calculate the results date (7 working days after validation)
        $resultsDate = $this->calculateResultsDate(Carbon::now(), 7);

        return response()->json([
            'message' => 'Client notified and status updated',
            'results_date' => $resultsDate->format('Y-m-d'),
            'devis_id' => $devis ? $devis->id : null,
        ]);
    }
    public function sendDevis($demande_id)
    {
        $demande = Demande::where('demande_id', $demande_id)->firstOrFail();
        // Retrieve the first Devis associated with the demande
        $demande ->devis_sent='yes';
        $demande -> save();
        $devis = Devis::where('demande_id', $demande->id)->first();

        // Calculate the results date (7 working days after validation)
        $resultsDate = $this->calculateResultsDate(Carbon::now(), 7);

        // Notify the user
        $message = "Votre demande a été validée avec succès. Veuillez consulter le devis correspondant à votre demande..";
        $notification = Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'Validation de la Demande',
            'message' => $message . " Vos résultats seront prêts d'ici le " . $resultsDate->format('Y-m-d') . ".",
            'type' => 'demande',
            'demande_id' => $demande->id, // Use the numeric ID
            'demande' => $demande_id, // Store the string value here
            'devis_id' => $devis ? $devis->id : null, // Save devis_id if it exists
            'is_read' => 0
        ]);

        // Send email to the user
        try {
            $user = User::find($demande->user_id);
            Mail::to($user->email)
                ->send(new ValidationEmail(
                    $notification,
                    $demande,
                    $user,
                    $devis,
                    $resultsDate->format('Y-m-d')
                ));

            \Illuminate\Support\Facades\Log::info('Validation email sent successfully', [
                'user_email' => $user->email,
                'demande_id' => $demande->demande_id,
                'mail_driver' => config('mail.default')
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send validation email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $demande->user_id,
                'demande_id' => $demande->demande_id
            ]);
        }
        broadcast(new DemandeStatusUpdate($demande, $message))->toOthers();

        return response()->json([
            'message' => 'Client notified and status updated',
            'results_date' => $resultsDate->format('Y-m-d'),
            'devis_id' => $devis ? $devis->id : null,
        ]);
    }
    public function getDevisDetails($demandeID)
    {
        // Fetch the devis with its related demande
        $devis = Devis::with('demande')->
        where('demande', $demandeID)->get();

        // Check if the devis exists
        if (!$devis) {
            return response()->json([
                'message' => 'Devis not found.',
            ], 404);
        }

        // Return the devis details
        return response()->json([
            'message' => 'Devis details retrieved successfully.',
            'devis' => $devis
        ], 200);
    }
public function getDevisByUser($userId)
{


        // Get all Devis linked to Demandes for this user
        $devis = Devis::whereHas('demande', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->with('demande')->get();

        if ($devis->isEmpty()) {
            return response()->json(['message' => 'No Devis found for this user.'], 404);
        }

        return response()->json([
            'message' => 'Devis for user retrieved successfully.',
            'devis' => $devis
        ], 200);



}
public function getUserDevis()
{
    // ✅ Get the authenticated user ID
    $userId = auth('sanctum')->user()->id;

    if (!$userId) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    // ✅ Retrieve all demandes for the logged-in user
    $demandes = Demande::where('user_id', $userId)
        ->with('devis') // Eager-load related devis
        ->orderBy('demande_date', 'desc') // Order by latest demandes
        ->get();

    // ✅ Format the response
    $formattedData = $demandes->map(function ($demande) {
        return [
            'demande_id' => $demande->demande_id,
            'demande_date' => $demande->demande_date,
            'total_enregistrements' => $demande->total_enregistrements,
            'status' => $demande->status,
            'devis_sent'=>$demande->devis_sent,
            'devis' => $demande->devis->map(function ($devis) {
                return [
                    'id' => $devis->id,
                    'analyse' => $devis->analyse,
                    'methode' => $devis->methode,
                    'prix_unitaire' => $devis->prix_unitaire,
                    'quantite' => $devis->quantite,
                    'prix_total' => $devis->prix_total
                ];
            })
        ];
    });

    return response()->json($formattedData);
}

    /**
     * Function to calculate the results date (7 working days after validation, excluding weekends)
     */
    private function calculateResultsDate(Carbon $startDate, int $days)
    {
        $currentDate = clone $startDate;
        $count = 0;

        while ($count < $days) {
            $currentDate->addDay();
            if (!$currentDate->isWeekend()) { // Exclude Saturdays and Sundays
                $count++;
            }
        }

        return $currentDate;
    }
    public function getAllDevis()
    {
        try {
            // Retrieve all Devis with their related Demande
            $devis = Devis::with('demande')->get();

            if ($devis->isEmpty()) {
                return response()->json(['message' => 'No Devis found.'], 404);
            }

            return response()->json([
                'message' => 'All Devis retrieved successfully.',
                'devis' => $devis
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error retrieving Devis: ' . $e->getMessage()], 500);
        }
    }
    public function createDerogation(Request $request, $demande_id)
    {
        // Fetch the Demande using the demande_id, with better error handling
        $demande = Demande::byDemandeId($demande_id)->with('user')->first();
        if (!$demande) {
            return response()->json(['message' => "Demande with demande_id {$demande_id} not found"], 404);
        }
 // ✅ Update status
 $demande->status = 'derogation';
 $demande->save();

 // ✅ Save notification for client
 Notification::create([
     'user_id' => $demande->user_id,
     'title' => 'derogation created',
     'message' => 'Demande avec derogation.',
     'type' => 'demande',
     'demande_id' => $demande->id,
     'demande'=>$demande_id // Ensure it uses demande_id
 ]);
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'derogation.samples' => 'required|array|min:1',
            'derogation.samples.*.identification_echantillon' => 'required|string',
            'derogation.samples.*.masse_echantillon' => 'required|numeric',
            'derogation.samples.*.description_ecart' => 'required|string',
            'derogation.samples.*.reponse_du_client' => 'required|string',
            'derogation.samples.*.decision' => 'nullable|string', // Added for clarity
            'derogation.samples.*.date_et_visa_du_DL' => 'nullable|date',
            'derogation.samples.*.date_et_visa_du_demander' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $derogations = [];

        // Process each sample in the derogation request
        foreach ($request->input('derogation.samples', []) as $sampleData) {
            // Fetch the related sample using the relationship
            $sample = $demande->samples()
                ->where('identification_echantillon', $sampleData['identification_echantillon'])
                ->first();

            if (!$sample) {
                return response()->json([
                    'message' => "Sample {$sampleData['identification_echantillon']} not found for demande {$demande_id}",
                ], 404);
            }

            // Create the derogation record
            $derogation = Derogation::create([
                'demande_id' => $demande->id, // Use internal ID, not demande_id
                'identification_echantillon' => $sample->identification_echantillon,
                'nature_echantillon' => $sample->nature_echantillon,
                'reference' => $sample->reference,
                'provenance' => $sample->provenance,
                'demandeur' => $demande->user->name ?? 'Unknown', // Fallback if user is null
                'date_demande_client' => $demande->demande_date,
                'masse_echantillon' => $sampleData['masse_echantillon'],
                'analyses_demandees' => $sample->analyses_demandees,
                'description_ecart' => $sampleData['description_ecart'],
                'reponse_du_client' => $sampleData['reponse_du_client'],
                'decision' => $sampleData['decision'] ?? null,
                'date_et_visa_du_DL' => $sampleData['date_et_visa_du_DL'] ?? null,
                'date_et_visa_du_demander' => $sampleData['date_et_visa_du_demander'] ?? null,
            ]);

            $derogations[] = $derogation;
        }

        // Notify the director
        Notification::create([
            'user_id' => $demande->user_id,
            'title' => 'Demande de validation avec derogation',
            'message' => 'Une demande de validation avec dérogation est en attente de votre approbation.',
            'type' => 'demande',
           'demande_id' => $demande->id,
            'demande'=>$demande_id// Use the custom demande_id
        ]);

        return response()->json([
            'message' => 'Derogation created successfully.',
            'derogation' => $derogations,
        ], 201); // 201 Created status
    }

public function getDevisProforma($demandeId)
{
    // Retrieve all devis records linked to the given demande_id
    $devisProforma = Devis::where('id', $demandeId)->get();

    if ($devisProforma->isEmpty()) {
        return response()->json(['message' => 'No Devis Proforma found for this demande.'], 404);
    }

    return response()->json([
        'message' => 'Devis Proforma details retrieved successfully.',
        'devis_proforma' => $devisProforma
    ]);
}



}
