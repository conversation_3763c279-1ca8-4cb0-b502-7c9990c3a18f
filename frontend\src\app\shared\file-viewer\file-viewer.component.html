<div class="file-viewer-overlay" [class.visible]="isVisible" (click)="closeViewer()">
  <div class="file-viewer-container" (click)="$event.stopPropagation()">
    <div class="file-viewer-header">
      <h3>{{ fileName }}</h3>
      <div class="file-viewer-actions">
        <button class="action-btn download" (click)="downloadFile()" title="Télécharger">
          <i class="fas fa-download"></i>
        </button>
        <button class="action-btn close" (click)="closeViewer()" title="Fermer">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <div class="file-viewer-content">
      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="spinner">
          <i class="fas fa-spinner fa-spin fa-2x"></i>
        </div>
        <p>Chargement du fichier...</p>
      </div>

      <!-- Error message -->
      <div *ngIf="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button class="retry-btn" (click)="loadFile()">Réessayer</button>
      </div>

      <!-- File Viewer (works for both PDF and images) -->
      <div *ngIf="!isLoading && !error" class="file-container">
        <div *ngIf="fileType === 'pdf'" class="pdf-container">
          <object [data]="safeImageUrl" type="application/pdf" width="100%" height="100%">
            <p>Votre navigateur ne peut pas afficher ce PDF.
              <a [href]="fileUrl" target="_blank">Cliquez ici pour le télécharger</a>.</p>
          </object>
        </div>
        <div *ngIf="fileType === 'image'" class="image-container">
          <img [src]="safeImageUrl" alt="Image preview" class="image-preview">
        </div>
      </div>
    </div>
  </div>
</div>
