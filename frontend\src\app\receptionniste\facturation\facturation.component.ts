import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { FactureService } from './facture.service';
import { FontAwesomeModule, FaIconLibrary } from '@fortawesome/angular-fontawesome';
import {
  faCheck,
  faTimes,
  faSpinner,
  faDownload,
  faEye,
  faEyeSlash,
  faFilePdf,
  faFile,
  faExternalLinkAlt,
  faSync,
  faMoneyBill,
  faCalendar,
  faTag,
  faInfoCircle,
  faExclamationTriangle,
  faPrint
} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-facturation',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './facturation.component.html',
  styleUrl: './facturation.component.css'
})
export class FacturationComponent implements OnInit, OnDestroy {
  facture: any = null;
  devis: any[] = [];
  payments: any[] = [];
  isLoading: boolean = true; // Main loading state for the entire interface
  isLoadingPayments: boolean = false;
  paymentError: string | null = null;

  // Font Awesome icons
  faCheck = faCheck;
  faTimes = faTimes;
  faSpinner = faSpinner;
  faDownload = faDownload;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faFilePdf = faFilePdf;
  faFile = faFile;
  faExternalLinkAlt = faExternalLinkAlt;
  faSync = faSync;
  faMoneyBill = faMoneyBill;
  faCalendar = faCalendar;
  faTag = faTag;
  faInfoCircle = faInfoCircle;
  faExclamationTriangle = faExclamationTriangle;
  faPrint = faPrint;

  // Preview properties
  isLoadingPreview = false;
  previewError: string | null = null;
  currentPreviewUrl: string | null = null;
  currentPreviewPath: string | null = null;
  fileType: 'image' | 'pdf' | 'other' = 'other';
  showPreview = false;
  previewPaymentId: number | null = null;
  currentPayment: any = null;

  // Direct image preview properties
  directImagePreview = false;
  directImageUrl: string | null = null;
  currentImageFileName: string | null = null;

  // Notification properties
  showNotification = false;
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';

  constructor(
    private route: ActivatedRoute,
    private factureService: FactureService,
    private library: FaIconLibrary
  ) {
    // Add icons to the library for use with the fa-icon component
    this.library.addIcons(
      faCheck,
      faTimes,
      faSpinner,
      faDownload,
      faEye,
      faEyeSlash,
      faFilePdf,
      faFile,
      faExternalLinkAlt,
      faSync,
      faMoneyBill,
      faCalendar,
      faTag,
      faInfoCircle,
      faExclamationTriangle,
      faPrint
    );
  }

  ngOnInit(): void {
    this.isLoading = true; // Start loading
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.factureService.getFactureDetails(+id).subscribe({
        next: (data) => {
          this.facture = data.facture;
          this.devis = data.devis;

          // If facture has a demande property, fetch payments for that demande
          if (this.facture?.demande) {
            this.fetchPaymentsForDemande(this.facture.demande);
          } else {
            this.isLoading = false; // End loading if no payments to fetch
          }
        },
        error: (err) => {
          console.error('Error fetching facture details:', err);
          this.isLoading = false; // End loading on error
        }
      });
    } else {
      this.isLoading = false; // End loading if no ID
    }
  }

  /**
   * Fetch payments for a specific demande
   */
  fetchPaymentsForDemande(demandeId: string): void {
    this.isLoadingPayments = true;
    this.paymentError = null;

    this.factureService.getPaymentsByDemandeId(demandeId).subscribe({
      next: (response) => {
        this.payments = response.data ?? [];
        console.log('Payments loaded:', this.payments);
        this.isLoadingPayments = false;
        this.isLoading = false; // End main loading when payments are loaded
      },
      error: (err) => {
        console.error('Error fetching payments:', err);
        this.paymentError = 'Impossible de récupérer les paiements. Veuillez réessayer plus tard.';
        this.isLoadingPayments = false;
        this.isLoading = false; // End main loading on error
      }
    });
  }

  groupDevisData(): any[] {
    const grouped = new Map<string, any>();

    this.devis.forEach((item: any) => {
      if (grouped.has(item.analyse)) {
        let existing = grouped.get(item.analyse);
        existing.quantite += item.quantite;
        existing.prix_total += item.prix_unitaire * item.quantite;
      } else {
        grouped.set(item.analyse, {
          ...item,
          prix_total: item.prix_unitaire * item.quantite
        });
      }
    });

    return Array.from(grouped.values());
  }

  getTotal(): number {
    return this.groupDevisData().reduce((sum, d) => sum + d.prix_total, 0);
  }

  formatDate(date: string): string {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  /**
   * Get status class for styling
   */
  getStatusClass(status: string): { [key: string]: boolean } {
    return {
      'status-approved': status === 'approved',
      'status-rejected': status === 'rejected',
      'status-pending': status !== 'approved' && status !== 'rejected'
    };
  }

  /**
   * Get human-readable status text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'approved':
        return 'Payée';
      case 'rejected':
        return 'Paiement rejeté';
      default:
        return 'En attente de paiement';
    }
  }

  /**
   * Get status icon
   * @returns The icon object for the status
   */
  getStatusIcon(status: string) {
    switch (status) {
      case 'approved':
        return this.faCheck;
      case 'rejected':
        return this.faTimes;
      case 'pending':
        return this.faSpinner;
      default:
        return this.faSpinner;
    }
  }

  /**
   * Get status icon class for FontAwesome
   * @deprecated Use getStatusIcon instead
   */
  getStatusIconClass(status: string): string {
    switch (status) {
      case 'approved':
        return 'fa fa-check';
      case 'rejected':
        return 'fa fa-times';
      default:
        return 'fa fa-spinner fa-spin';
    }
  }

  /**
   * View a payment proof file
   */
  viewPaymentProof(paymentProofPath: string, paymentId?: number): void {
    console.log('View payment proof called with path:', paymentProofPath);

    if (!paymentProofPath) {
      console.error('Payment proof path is empty');
      this.paymentError = 'Aucun justificatif de paiement disponible';
      return;
    }

    // Find the current payment in the payments array
    if (paymentId) {
      this.currentPayment = this.payments.find(p => p.id === paymentId);
      console.log('Current payment:', this.currentPayment);
    }

    // Check if we're already showing this payment's preview (either type)
    const isDirectImageActive = this.directImagePreview && this.currentPayment?.id === paymentId;

    // If we're already showing this payment's preview, toggle it off
    if (isDirectImageActive) {
      this.closeDirectImagePreview();
      return;
    } else if (this.showPreview && this.previewPaymentId === paymentId) {
      this.closePreview();
      return;
    }

    // Determine file type based on file extension
    const fileName = this.getFileNameFromPath(paymentProofPath).toLowerCase();
    let isImage = false;

    if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') ||
        fileName.endsWith('.gif') || fileName.endsWith('.bmp')) {
      console.log('File type detected from filename: Image');
      isImage = true;
    }

    // For images, show directly under the payment history
    if (isImage) {
      // Close any existing preview
      this.closePreview();

      // Get the URL from the service
      const url = this.getPaymentProofUrl(paymentProofPath);
      console.log('Showing image directly under payment history:', url);

      // Set direct image preview properties
      this.directImageUrl = url;
      this.currentImageFileName = this.getFileNameFromPath(paymentProofPath);
      this.directImagePreview = true;

      return;
    }

    // Reset preview state for non-image files
    this.isLoadingPreview = true;
    this.previewError = null;
    this.currentPreviewPath = paymentProofPath;
    this.currentPreviewUrl = null;
    this.fileType = 'pdf'; // Default to PDF
    this.showPreview = true;
    this.previewPaymentId = paymentId ?? null;

    try {
      // Determine file type based on file extension
      const fileName = this.getFileNameFromPath(paymentProofPath).toLowerCase();
      if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') ||
          fileName.endsWith('.gif') || fileName.endsWith('.bmp')) {
        console.log('File type detected from filename: Image');
        this.fileType = 'image';
      } else if (fileName.endsWith('.pdf')) {
        console.log('File type detected from filename: PDF');
        this.fileType = 'pdf';
      } else {
        console.log('File type: Other (not recognized)');
        this.fileType = 'other';
      }

      // Get the URL from the service
      const url = this.getPaymentProofUrl(paymentProofPath);
      console.log('Preview URL:', url);

      // Set the URL directly for all file types
      this.currentPreviewUrl = url;

      // Mark loading as complete
      this.isLoadingPreview = false;

      // Set a timeout to hide the loading spinner if it's still showing after 3 seconds
      setTimeout(() => {
        if (this.isLoadingPreview) {
          console.log('Loading timeout reached, hiding spinner');
          this.isLoadingPreview = false;
        }
      }, 3000);
    } catch (error) {
      console.error('Error preparing preview:', error);
      this.isLoadingPreview = false;
      this.previewError = 'Impossible de préparer l\'aperçu du justificatif';
    }
  }

  /**
   * Close the preview
   */
  closePreview(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.currentPreviewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.currentPreviewUrl);
    }

    this.showPreview = false;
    this.currentPreviewUrl = null;
    this.currentPreviewPath = null;
    this.previewError = null;
    this.previewPaymentId = null;

    // Also close direct image preview if it's open
    this.closeDirectImagePreview();
  }

  /**
   * Close the direct image preview
   */
  closeDirectImagePreview(): void {
    this.directImagePreview = false;
    this.directImageUrl = null;
    this.currentImageFileName = null;
  }

  /**
   * Handle direct image loading errors
   */
  handleDirectImageError(event: Event): void {
    console.error('Direct image failed to load:', event);

    // Show an error notification
    this.showNotification = true;
    this.notificationMessage = 'Impossible de charger l\'image. Essayez de télécharger le fichier.';
    this.notificationIcon = '❌';
    this.notificationColor = '#dc3545';

    // Hide notification after 3 seconds
    setTimeout(() => {
      this.showNotification = false;
    }, 3000);

    // Close the preview
    this.closeDirectImagePreview();
  }

  /**
   * Get the file name from a path
   */
  getFileNameFromPath(path: string): string {
    if (!path) return 'payment_proof';

    // Extract the file name from the path
    const fileName = path.split('/').pop() ?? path.split('\\').pop() ?? 'payment_proof';
    return fileName;
  }

  /**
   * Get the URL for a payment proof file
   */
  getPaymentProofUrl(paymentProofPath: string): string {
    if (!paymentProofPath) return '';

    // Check if the URL already contains http:// or https://
    if (paymentProofPath.startsWith('http://') || paymentProofPath.startsWith('https://')) {
      return paymentProofPath;
    }

    // Extract just the filename if it contains a path
    let fileName = paymentProofPath;
    if (fileName.includes('/')) {
      fileName = fileName.split('/').pop() ?? fileName;
    }

    // Try the direct file route
    const apiUrl = 'http://localhost:8000/api';
    const directFileUrl = `${apiUrl}/direct-file/${encodeURIComponent(fileName)}`;
    return directFileUrl;
  }

  /**
   * Download a payment proof file
   */
  downloadPaymentProof(paymentProofPath: string): void {
    console.log('Download payment proof called with path:', paymentProofPath);

    if (!paymentProofPath) {
      console.error('Payment proof path is empty');
      this.paymentError = 'Aucun justificatif de paiement disponible';
      return;
    }

    // Try to use the current payment's direct URL if available
    if (this.currentPayment?.id && this.previewPaymentId === this.currentPayment.id) {
      // Try signed URL first
      if (this.currentPayment.payment_proof_url) {
        console.log('Using signed URL for download:', this.currentPayment.payment_proof_url);
        const link = document.createElement('a');
        link.href = this.currentPayment.payment_proof_url;
        link.download = this.getFileNameFromPath(paymentProofPath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return;
      }

      // Try direct URL next
      if (this.currentPayment.direct_url) {
        console.log('Using direct URL for download:', this.currentPayment.direct_url);
        const link = document.createElement('a');
        link.href = this.currentPayment.direct_url;
        link.download = this.getFileNameFromPath(paymentProofPath);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return;
      }
    }

    // Use direct URL as fallback
    const url = this.getPaymentProofUrl(paymentProofPath);
    console.log('Using fallback URL for download:', url);
    const link = document.createElement('a');
    link.href = url;
    link.download = this.getFileNameFromPath(paymentProofPath);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Open PDF in a new tab
   */
  openPdfInNewTab(): void {
    if (this.currentPayment?.payment_proof_url) {
      window.open(this.currentPayment.payment_proof_url, '_blank');
    } else if (this.currentPayment?.direct_url) {
      window.open(this.currentPayment.direct_url, '_blank');
    } else if (this.currentPreviewUrl) {
      window.open(this.currentPreviewUrl, '_blank');
    }
  }

  /**
   * Handle image loading errors
   */
  handleImageError(event: Event): void {
    console.error('Image failed to load in the DOM:', event);

    // Log the URL that failed
    console.log('Failed URL:', this.currentPreviewUrl);
    console.log('Current file type:', this.fileType);
    console.log('Current preview path:', this.currentPreviewPath);

    // Change the file type to PDF as fallback
    this.fileType = 'pdf';

    // Try using the signed URL if available
    if (this.currentPayment?.payment_proof_url && this.currentPreviewUrl !== this.currentPayment.payment_proof_url) {
      console.log('Trying signed URL after image error:', this.currentPayment.payment_proof_url);
      this.currentPreviewUrl = this.currentPayment.payment_proof_url;
      return; // Let the image try to load with the new URL
    }

    // Try using the direct URL if available
    if (this.currentPayment?.direct_url && this.currentPreviewUrl !== this.currentPayment.direct_url) {
      console.log('Trying direct URL after image error:', this.currentPayment.direct_url);
      this.currentPreviewUrl = this.currentPayment.direct_url;
    }
  }

  /**
   * Clean up resources when the component is destroyed
   */
  ngOnDestroy(): void {
    // Revoke any blob URLs to prevent memory leaks
    if (this.currentPreviewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(this.currentPreviewUrl);
    }
  }

  printFacture(): void {
    if (!this.facture) {
      console.error('No facture data available to print.');
      return;
    }

    const groupedDevis = this.groupDevisData();
    const total = this.getTotal();
    const formattedDate = this.formatDate(this.facture.facture_date);

    const printHtml = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Facture</title>
        <style>
          @media print {
            @page {
              size: A4;
              margin: 20mm 15mm;
            }

            body {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
          }

          body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            color: black;
            margin: 0;
            padding: 0;
          }

          .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 0;
            flex: 1;
          }

          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 120px;
            margin-bottom: 30px;
          }

          .header-left {
            flex: 1;
            display: flex;
            align-items: flex-start;
            text-align: center;
          }

          .header-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
          }

          .logo {
            height: 200px;
            width: 200px;
          }

          .table-container {
            margin: 10px 0;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }

          th, td {
            border: 0.4px solid black;
            padding: 8px;
            font-size: 11pt;
            text-align: center;
          }

          th {
            text-align: center;
            background-color: #f2f2f2 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            white-space: nowrap; /* Prevent wrapping in table headers */
          }

          .center { text-align: center; }
          .lefts { text-align: right; }
          .right { text-align: right; }
  .left strong {
       text-align: left;
      font-size: 12px;
  }
          .total-row td {
            font-weight: bold;
          }

          .facture-number {
            padding-top: 20px;
            text-align: center;
            margin: 30px 0;
            font-size: 18px;
          }

          .doit {
            margin: 20px 0;
          }

          .footer-text {
            margin: 30px 0;
            text-align: center;
          }

          .ccp1 {
            margin-top: 30px;
            text-align: center;
            padding-top: 200px;
          }
  .lot-label {
  margin-left: 10px; /* Adjust the space as needed */
}
          .ccp {
            padding-top: 40px;
            border-bottom: 1px solid black;
            padding-bottom: 10px;
            margin-bottom: 10px;
          }

          .bold {
            font-size: 10pt;
          }

          p {
            margin: 0;
            padding: 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="header-left">
              <p class="bold">
                Ministère de l'Agriculture<br>
                et des Ressources Hydrauliques et<br>
                de la pêche<br>
                Institution de la recherche et<br>
                de l'enseignement Supérieur Agricoles<br>
                ***<br>
                Institut national des sciences<br>
                et technologie de la mer
              </p>
            </div>
            <div class="header-right">
              <img src="${window.location.origin}/logoins.png" alt="Logo" class="logo">
              <p class="date">Date: le ${formattedDate}</p>
            </div>
          </div>

          <div class="facture-number">
            <h2>Facture N° ${this.facture.id}/2025</h2>
          </div>

          <div class="doit">
            <p><strong>Doit:</strong> Client/Tunis</p>
          </div>

          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th class="center">N</th>
                  <th class="lefts">OBJET</th>
                  <th class="center">Quantité</th>
                  <th class="right">Prix U. en DT</th> <!-- Removed <br> -->
                  <th class="right">Prix T en DT</th>
                </tr>
              </thead>
              <tbody>
                ${groupedDevis.map((devi, index) => `
                  <tr>
                    <td class="center">${(index + 1).toString().padStart(2, '0')}</td> <!-- Add leading zero -->
                    <td class="left"><strong>-Lot:${devi.lot ?? 'N/A'}</strong>  ${devi.analyse} </td> <!-- Append -Lot -->
                    <td class="center">${devi.quantite}</td>
                   <td class="right">${Math.floor(devi.prix_unitaire)}</td>
                    <td class="right">${devi.prix_total}</td>
                  </tr>
                `).join('')}
                <tr class="total-row">
                  <td colspan="4" class="right">TOTAL</td>
                  <td class="right">${total}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="footer-text">
            <p>Arrêté la présente facture à la somme de: ${total} DT</p>
          </div>

          <div class="ccp">
            <p>Montant à virer au CCP N°17001000000273374803</p>
          </div>
          <div class="ccp1">
            <p>28 Rue 2 mars 1934 - 2025 Salammbô - Tunisie TVA: 609519G/N/N/000</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      // Use a safer approach than document.write
      printWindow.document.open();
      printWindow.document.write(printHtml);
      printWindow.document.close();

      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
          // printWindow.close(); // Optional: close after printing
        }, 500);
      };
    } else {
      console.error('Failed to open print window. Please allow pop-ups.');
    }
  }
}