import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators,ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { APP_CONFIG } from '../app.config';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.css']  // Corrected from 'styleUrl' to 'styleUrls'
})
export class ResetPasswordComponent implements OnInit {
  forgotPasswordForm!: FormGroup;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private router: Router
  ) {}
  navigateTo(path: string): void {
    this.router.navigate([path]);
  }
  ngOnInit(): void {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  onSubmit(): void {
    if (this.forgotPasswordForm.invalid) {
      this.forgotPasswordForm.markAllAsTouched();
      return;
    }

    const emailValue = this.forgotPasswordForm.value.email;

    this.http.post<any>(`${APP_CONFIG.apiBase}/forgot-password`, { email: emailValue })
      .subscribe({
        next: (res) => {
          // Typically returns something like { "message": "We have emailed your password reset link!" }
          this.successMessage = res.message;
          // Optionally redirect after a success message
          this.navigateTo('/login');
        },
        error: (err) => {
          // Could be 422 with an error message
         
          if (err.status === 422) {
            this.errorMessage = 'Aucun utilisateur trouvé avec cette adresse de courriel.';
          } else {
            this.errorMessage = err?.error?.errors?.email ?? 'Impossible de réinitialiser votre mot de passe en ce moment. Veuillez vérifier que l’adresse courriel est correcte et réessayer sous peu. Si le problème persiste, contactez notre équipe de support.';
          }
        }
      });
  }
}
