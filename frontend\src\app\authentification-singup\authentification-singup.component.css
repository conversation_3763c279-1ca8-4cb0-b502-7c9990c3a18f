/* ✅ 1. Global Reset & Google Font Import */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
  text-decoration: none;
  list-style: none;
}

/* ✅ 2. Body Styles (Background Gradient) */
body {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90deg, #e2e2e2, #c9d6ff);
  animation: fadeIn 1s ease-in-out;
}

/* ✅ 3. Signup Container */
.signup-container {
  width: clamp(320px, 80%, 600px);
  margin: 2rem auto;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  padding: 2rem 1.5rem;
  text-align: left;
  animation: fadeIn 0.7s ease-in-out forwards;
  position: relative;
}

/* ✅ 4. Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-in-out;
}

.loading-popup {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-popup h3 {
  margin-top: 20px;
  color: #333;
  font-size: 18px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ✅ 5. Title */
.title {
  font-size: 30px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 30px;
  text-align: center;
  color: #333;
  
}

/* ✅ 5. Form Styling */
form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

/* ✅ 6. Input Groups */
.input-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.input-group.invalid label,
.input-group.invalid input {
  color: #d40000;
  border-color: #d40000 !important;
}

.input-group.invalid input {
  background-color: #ffe6e6;
}

/* Label styles */
.input-group label {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

/* ✅ 7. Input Fields */
input[type="text"],
input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 13px 20px;
  border-radius: 8px;
  border: 2px solid #eeeeee;
  background: #f9f9f9;
  font-size: 16px;
  color: #333;
  outline: none;
  transition: all 0.3s ease;
}

/* ✅ Placeholder styling */
input::placeholder {
  color: #888;
  font-weight: 400;
}

/* ✅ On Focus Effect */
input:focus {
  border-color: #2496d3;
  background-color: #f4f7ff;
  transform: scale(1.03);
}

/* ✅ Login Link */
.btn-login {
  font-size: 16px;
  color: #007BFF;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.btn-login:hover,
.btn-login:focus {
  color: #0056b3;
  text-decoration: underline;
}

/* ✅ 8. Primary Button */
.btn-primary {
  background: #2496d3;
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  padding: 15px 40px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 5px 15px rgba(0, 170, 255, 0.4); /* ✅ Ombre sur bouton principal */
  position: relative;
  overflow: hidden;
}

/* ✅ Hover Effect */
.btn-primary:hover {
  background: rgba(15, 151, 192, 0.8);
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(36, 150, 211, 0.5);
}

/* ✅ 9. Success & Error Messages */
.success {
  color: green;
  background: rgba(0, 255, 0, 0.1);
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  font-weight: bold;
  font-size: 15px;
  animation: fadeIn 0.5s ease-in-out;
}

.error {
  padding-left: 10px;
  color: #d40000;
  font-size: 13px;
  animation: fadeIn 0.5s ease-in-out;
}

/* ✅ Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes glowText {
  from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4); }
  to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8); }
}

/* ✅ Responsive Design */
@media (max-width: 1024px) {
  .signup-container {
    padding: 1.5rem;
  }

  .title {
    font-size: 24px;
  }

  .btn-primary {
    width: 150px;
    height: 45px;
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .signup-container {
    width: 90%;
    padding: 1rem;
  }

  .title {
    font-size: 22px;
  }

  .btn-primary {
    width: 140px;
    height: 42px;
    font-size: 14px;
  }
}
