<!-- Loading Spinner Overlay -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="spinner"></div>
</div>

<!-- Main Content -->
<div class="main-content" [class.content-hidden]="isLoading">
  <!-- Status Messages -->
  <p *ngIf="demande?.status === 'ongoing_validation'" class="results-date-message">
    <i class="fas fa-check-circle" style="color: #28a745; margin-right: 10px; font-size: 18px;"></i>
    <span style="font-weight: bold;">Votre demande a été pré-validée. Veuillez apporter vos échantillons au bureau de <strong>INSTM Port de Pêche La Goulette</strong> pour finaliser la validation de votre demande.</span>
  </p>

  <div class="results-date-message" *ngIf="demande?.status === 'valid'">
    <i class="fas fa-calendar-check"></i>
    <span>Vos résultats d'analyse seront disponibles à partir du <strong>{{ expectedResultsDate }}</strong> (7 jours ouvrables après l'approbation du paiement).</span>
  </div>

  <div class="demandes-container">
    <!-- Error Message -->
    <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>

    <!-- Demande Details -->
    <div *ngIf="demande && !isLoading">
      <div class="header-actions">
        <h2>Détails de la demande</h2>
      </div>

      <p><strong>Demande N°:</strong> {{ demande?.demande_id }}</p>
      <p><strong>Date de réception:</strong> {{ demande?.demande_date }}</p>
      <p><strong>Mode de règlement:</strong> {{ demande?.mode_reglement || 'Non spécifié' }}</p>

      <div class="status-container">
        <p><strong>Statut:</strong>
          <span class="status-tag pending" *ngIf="demande?.status === 'pending'">🟡 En attente</span>
          <span class="status-tag ongoing" *ngIf="demande?.status === 'ongoing_validation'">🔵 En cours de validation</span>
          <span class="status-tag derogation" *ngIf="demande?.status === 'derogation'">🟠 En cours de validation avec dérogation</span>
          <span class="status-tag valid" *ngIf="demande?.status === 'valid'">✅ Demande validée</span>
          <span class="status-tag rejected" *ngIf="demande?.status === 'rejected'">❌ Demande refusée</span>
        </p>
      </div>

      <!-- Rejection Reason Message - Only shown when demande is rejected -->
      <div *ngIf="demande?.status === 'rejected'" class="rejection-reason">
        <i class="fas fa-exclamation-circle" style="color: #dc3545; margin-right: 10px; font-size: 18px;"></i>
        <div class="rejection-content">
          <h4>Motif de refus:</h4>
          <p>Votre demande a été refusée pour la raison suivante :<br>
            Les échantillons fournis ne correspondent pas aux critères d'analyse requis par notre laboratoire. Veuillez consulter notre guide des critères d'acceptation d'échantillons et soumettre une nouvelle demande avec des échantillons conformes.</p>
        </div>
      </div>

      <!-- Samples Table -->
      <div *ngIf="demande.samples?.length; else noSamples">
        <table>
          <thead>
            <tr>
              <th>Identification de l'échantillon</th>
              <th>Référence</th>
              <th>Nature</th>
              <th>Provenance</th>
              <th>Masse (G)</th>
              <th>État</th>
              <th>Analyses Demandées</th>
              <th>Délai Souhaité</th>
              <th>Analyse Souhaitée</th>
              <th>Lot</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let sample of demande.samples">
              <td>{{ sample.identification_echantillon }}</td>
              <td>{{ sample.reference }}</td>
              <td>{{ sample.nature_echantillon }}</td>
              <td>{{ sample.provenance }}</td>
              <td>{{ sample.masse_echantillon }}</td>
              <td>{{ sample.etat }}</td>
              <td>{{ sample.analyses_demandees?.join(', ') }}</td>
              <td style="font-size: 16px;">{{ sample.delai_souhaite || 'Non spécifié'}}</td>
              <td style="font-size: 16px;">{{ sample.analyse_souhaite || 'Non spécifié'}}</td>
              <td>{{ sample.lot }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <ng-template #noSamples>
        <p>Aucun échantillon disponible pour cette demande.</p>
      </ng-template>

      <!-- Notification Section -->
      <div *ngIf="notification && demande?.status === 'valid'" class="notifi-section">
        <strong>{{ notification.message }}</strong>
      </div>

      <!-- Devis Section -->
      <div *ngIf="demande?.status === 'valid'" class="devis-section">
        <!-- Loading Spinner for Devis -->
        <div *ngIf="isDevisLoading" class="devis-loading">
          <div class="spinner"></div>
        </div>

        <!-- Devis Content when available -->
        <div *ngIf="!isDevisLoading && devis && devis.length > 0" class="devis-container">
          <h3 class="devis-label">Devis lié à la demande N° {{ demande?.demande_id }}</h3>
          <p class="devis-subtitle">Détails des analyses et coûts associés</p>

          <div class="payment-notice">
            <i class="fas fa-info-circle" style="color: #007bff; margin-right: 10px;"></i>
            <span><strong>Important :</strong> Vous devez effectuer le paiement de ce devis pour récupérer les résultats d'analyse.</span>
          </div>

          <table class="table table-bordered text-center devis-table">
            <thead class="table-light">
              <tr>
                <th>N°</th>
                <th>Analyse</th>
                <th>Méthode</th>
                <th>Prix Unitaire (DT)</th>
                <th>Quantité</th>
                <th>Prix Total (DT)</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let d of groupDevisData(); let i = index">
                <td>{{ i + 1 }}</td>
                <td>
                  <span [ngClass]="{'text-primary fw-bold': d.analyse.toLowerCase() === 'lipide'}">
                    {{ d.analyse }}
                  </span>
                </td>
                <td>{{ d.methode }}</td>
                <td class="text-primary fw-bold">{{ d.prix_unitaire }}</td>
                <td class="text-primary fw-bold">{{ d.quantite }}</td>
                <td class="text-primary fw-bold">{{ d.prix_total }}</td>
              </tr>
              <tr class="fw-bold total-row">
                <td colspan="5" class="text-end">Montant Total (DT)</td>
                <td class="text-primary">{{ getTotal() }}</td>
              </tr>
            </tbody>
          </table>

          <div class="devis-actions">
            <button class="btn-payment" (click)="navigateToDevis()">
              <i class="fas fa-file-invoice" style="margin-right: 8px;"></i>Aller au paiement du devis
            </button>
          </div>
        </div>

        <!-- No Devis Message -->
        <div *ngIf="!isDevisLoading && (!devis || devis.length === 0)" class="no-devis">
          <p>Aucun devis disponible pour cette demande validée.</p>
        </div>
      </div>
    </div>
  </div>
</div>