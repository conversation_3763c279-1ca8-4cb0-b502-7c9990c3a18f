import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Sample } from '../../../models/sample.model';

@Injectable({
  providedIn: 'root',
})
export class DemandeService {
  private apiUrl = 'http://localhost:8000/api'; // Updated API base URL

  constructor(private http: HttpClient) {}

  getSamples(): Observable<Sample[]> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    });

    return this.http.get<Sample[]>(`${this.apiUrl}/samples`, { headers });
  }

  submitDemande(demandeData: any): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
 // ✅ Debug payload before sending

    return this.http.post(`${this.apiUrl}/demandes`,JSON.stringify(demandeData), { headers });
  }

  // 🏆 Extracts analysis name & price from format "Protiene 155"
  private extractAnalysisDetails(analysisString: string): [string, string] {
    if (!analysisString) return ['', '0'];

    const match = analysisString.match(/^(.+?)\s+(\d+(?:\.\d+)?)$/);
    if (match) {
      return [match[1], match[2]]; // ✅ Returns ["Protiene", "155"]
    }

    return [analysisString, '0']; // Default case if format is incorrect
  }
}
