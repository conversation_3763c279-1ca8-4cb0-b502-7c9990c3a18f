<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PasswordResetController extends Controller
{
    // Handle request for password reset link
    public function sendResetLinkEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink($request->only('email'));

        return $status === Password::RESET_LINK_SENT
            ? response()->json(['message' => __($status)], 200)
            : response()->json(['errors' => ['email' => __($status)]], 422);
    }

    // Handle the actual password reset
    public function reset(Request $request)
    {
        $request->validate([
            'token'    => 'required',
            'email'    => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        // Log details BEFORE the password reset (so they always run)
        Log::info('Received token: ' . $request->token);
        Log::info('Received email: ' . $request->email);

        $record = DB::table('password_resets')->where('email', $request->email)->first();
        if ($record) {
            Log::info('Database token: ' . $record->token);
            Log::info('Database token matches received: ' . Hash::check($request->token, $record->token));
        } else {
            Log::info('No record found for email: ' . $request->email);
        }

        // Attempt the actual password reset
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->setRememberToken(Str::random(60));

                $user->save();
                event(new PasswordReset($user));
            }
        );

        // Return JSON response based on success or failure
        return $status === Password::PASSWORD_RESET
            ? response()->json(['message' => 'Your password has been reset!'], 200)
            : response()->json(['errors' => ['email' => [__($status)]]], 422);
    }
}
