import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faTrash, faEye } from '@fortawesome/free-solid-svg-icons';
import { NotificationService } from '../../notification/notification.service';
import { Notification } from '../../../models/notification.model';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-notification-client',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule, FontAwesomeModule],
  templateUrl: './notification-client.component.html',
  styleUrls: ['./notification-client.component.css']
})
export class NotificationClientComponent implements OnInit {
  notifications: Notification[] = [];
  page: number = 1;
  itemsPerPage: number = 7;

  // FontAwesome icons
  faTrash = faTrash;
  faEye = faEye;

  // Delete loading states
  isDeletingNotification: boolean = false;

  // Success/Error messages
  successMessage: string = '';
  errorMessage: string = '';

  constructor(
    private notificationService: NotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.fetchClientNotifications();
  }

  fetchClientNotifications(): void {
    forkJoin([
      this.notificationService.getValiatedDemandesNotifications(),
      this.notificationService.getBringSampleNotifications()
    ]).subscribe(
      ([validatedResponse, bringSampleResponse]) => {
        this.notifications = [
          ...validatedResponse.notifications,
          ...bringSampleResponse.notifications
        ];
        this.notifications.sort((a, b) =>
          Number(a.is_read) - Number(b.is_read) ||
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      },
      (error) => console.error('Error fetching client notifications:', error)
    );
  }

  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe(
        () => {
          notification.is_read = true;
          this.navigateToDemandeDetails(notification);
        },
        (error) => console.error('Error marking notification as read:', error)
      );
    } else {
      this.navigateToDemandeDetails(notification);
    }
  }

  navigateToDemandeDetails(notification: Notification): void {
    if (notification.demande) {
      console.log('Navigating to:', `/demandeClient/${notification.demande}`);
      // Prepare query params
      const queryParams: any = { fromNotification: notification.title };
      // Add devis_id if it exists in the notification (for "Demande Validated")
      if (notification.title === 'Demande Validated' && 'devis_id' in notification) {
        queryParams.devisId = notification.devis_id;
      }
      this.router.navigate(['/demandeClient', notification.demande], {
        queryParams
      });
    } else {
      console.error('Demande ID is missing in notification:', notification);
    }
  }

  // Remove a specific notification with confirmation
  removeNotification(notification: Notification): void {
    // Show confirmation dialog
    const confirmDelete = confirm(`Êtes-vous sûr de vouloir supprimer cette notification ?\n\n"${notification.title}"`);

    if (!confirmDelete) {
      return;
    }

    // Set loading state
    this.isDeletingNotification = true;
    this.clearMessages();

    // Call API to delete notification
    this.notificationService.deleteNotification(notification.id).subscribe({
      next: (response) => {
        if (response.success) {
          // Remove from local array
          this.notifications = this.notifications.filter(n => n.id !== notification.id);

          // Show success message
          this.successMessage = response.message || 'Notification supprimée avec succès';
          this.autoHideMessage();

          console.log('✅ Notification deleted successfully:', notification.id);
        } else {
          this.errorMessage = response.message || 'Erreur lors de la suppression de la notification';
          this.autoHideMessage();
        }
      },
      error: (error) => {
        console.error('❌ Error deleting notification:', error);
        this.errorMessage = 'Erreur lors de la suppression de la notification. Veuillez réessayer.';
        this.autoHideMessage();
      },
      complete: () => {
        this.isDeletingNotification = false;
        this.cdr.detectChanges();
      }
    });
  }

  // Helper method to clear success/error messages
  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = '';
  }

  // Helper method to auto-hide messages after 5 seconds
  autoHideMessage(): void {
    setTimeout(() => {
      this.clearMessages();
      this.cdr.detectChanges();
    }, 5000);
  }
}