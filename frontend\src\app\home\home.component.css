/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ Section principale */
.engagements-section {
    background: white;
    color: black;
    font-family: 'Poppins', sans-serif;
    
    text-align: center;
    padding: 80px 5%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Remove gap between navbar and home component */
:host {
 
}

/* ✅ Titre principal animé */
.engagements-title {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 30px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 10px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
/* ✅ Sous-titre en Poppins */
.subtitle {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    margin-bottom: 40px;
    max-width: 85%;
    opacity: 1;
    animation: fadeIn 1.2s ease-in forwards;
}

/* ✅ Tous les h2, h3 en orange glow */
h2, h3 {
    color: #ff6600; /* ✅ Orange lumineux */
    text-decoration: none;
    text-shadow: 0px 0px 10px rgba(255, 102, 0, 0.7);
}

/* ✅ Grille des engagements */
.engagements-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    
    justify-content: center;
    padding: 40px 0;
    text-align: center;
}

/* ✅ Cartes d'engagement interactives */
.engagement-card {
    background: white;
    padding: 30px;
    font-size: 16px;
    border-radius: 15px;
    box-shadow: 0px 12px 22px rgba(36, 150, 211, 0.4); /* ✅ Ombre bleu ciel renforcée */
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 1;
    transform: translateY(30px);
    animation: fadeInUp 1s ease-in-out forwards;
}

/* ✅ Effet au survol */
.engagement-card:hover {
    transform: scale(1.07);
    box-shadow: 0px 18px 30px rgba(36, 150, 211, 0.6); /* ✅ Ombre plus forte */
    background: rgba(36, 150, 211, 0.08);
}

/* ✅ Icônes */
.engagement-card i {
    font-size: 28px;
    margin-bottom: 15px;
    color: #ffcc00;
    transition: transform 0.3s ease;
}

/* ✅ Animation des icônes au survol */
.engagement-card:hover i {
    transform: rotate(15deg);
}

/* ✅ Image centrale avec animation */
.image-container {
    margin-top: 60px;
    display: flex;
    justify-content: center;
}

/* ✅ Animation de l'image */
.image-container img {
    width: 75%;
    border-radius: 15px;
    opacity: 1;
    animation: fadeIn 2s ease-in-out forwards, floatImage 5s infinite alternate;
}

/* ✅ Animation de flottement */
@keyframes floatImage {
    0% { transform: translateY(0); }
    100% { transform: translateY(-10px); }
}

/* ✅ Animations générales */
@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.5); }
    to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.9); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✅ Responsive Design */
@media (max-width: 1024px) {
    .engagements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 35px; /* ✅ Adaptation de l'espacement */
    }

    .image-container img {
        width: 85%;
    }
}

@media (max-width: 768px) {
    .engagements-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .engagement-card {
        padding: 25px;
        font-size: 15px;
    }

    .image-container img {
        width: 100%;
    }
}
