<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ValidationEmail extends Mailable
{
    use Queueable, SerializesModels;
    
    public $notification;
    public $demande;
    public $user;
    public $devis;
    public $resultsDate;

    /**
     * Create a new message instance.
     *
     * @param object $notification The notification object
     * @param object $demande The demande object
     * @param object $user The user object
     * @param object|null $devis The devis object
     * @param string|null $resultsDate The expected results date
     */
    public function __construct($notification, $demande, $user, $devis = null, $resultsDate = null)
    {
        $this->notification = $notification;
        $this->demande = $demande;
        $this->user = $user;
        $this->devis = $devis;
        $this->resultsDate = $resultsDate;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Demande Validée - ' . $this->demande->demande_id,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.validation',
            with: [
                'notification' => $this->notification,
                'demande' => $this->demande,
                'user' => $this->user,
                'devis' => $this->devis,
                'resultsDate' => $this->resultsDate
            ]
        );
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.validation')
                    ->with([
                        'notification' => $this->notification,
                        'demande' => $this->demande,
                        'user' => $this->user,
                        'devis' => $this->devis,
                        'resultsDate' => $this->resultsDate
                    ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
