<div class="container">
  <h2>Gestion des Analyses</h2>

  <!-- Success and Error Messages -->
  <div *ngIf="successMessage" class="success-message">
    {{ successMessage }}
  </div>
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <!-- Add Analysis Button -->
  <div class="add-analyse">
    <button (click)="openModal()" class="add-analyse-btn">
      <fa-icon [icon]="faPlus" class="icon-margin-right"></fa-icon>Ajouter une analyse
    </button>
  </div>

  <!-- Filter Section -->
  <div class="filter-bar" [formGroup]="filterForm">
    <div class="filter-group">
      <label for="searchTerm">Rechercher par nom ou paramètre:</label>
      <div class="input-with-icon">

        <input
          type="text"
          id="searchTerm"
          formControlName="searchTerm"
          placeholder="Rechercher..."
          class="filter-input"
        >
      </div>
    </div>

    <button (click)="filterForm.reset(); searchTerm = ''; applyFilters();" class="btn-clear">
      <fa-icon [icon]="faEraser" class="icon-margin-right"></fa-icon>Effacer les filtres
    </button>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-spinner">
    <div class="spinner"></div>
    <p>Chargement...</p>
  </div>

  <!-- Analyses List -->
  <div *ngIf="!isLoading">
    



    <table class="analyses-table">
      <thead>
        <tr>

          <th>Analyse</th>
          <th>Paramètre</th>
          <th>Prix</th>
          <th>Accrédité</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let analyse of filteredAnalyses | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }">

          <td>{{ analyse.analyse }}</td>
          <td>{{ analyse.parameter }}</td>
          <td>{{ formatPrice(analyse.price) }}</td>
          <td>
            <span [ngClass]="{'accredited': analyse.is_accredited === 'Yes', 'not-accredited': analyse.is_accredited === 'No'}">
              {{ analyse.is_accredited === 'Yes' ? 'Accrédité' : 'Non Accrédité' }}
            </span>
          </td>
          <td>
            <div class="action-btns">
              <button (click)="editAnalyse(analyse)" class="edit-btn">
                <fa-icon [icon]="faEdit" class="action-icon"></fa-icon> Modifier
              </button>
              <button (click)="deleteAnalyse(analyse.id ?? 0)" class="delete-btn">
                <fa-icon [icon]="faTrash" class="action-icon"></fa-icon> Supprimer
              </button>
            </div>
          </td>
        </tr>
        <tr *ngIf="filteredAnalyses.length === 0">
          <td colspan="5" class="no-data">Aucune analyse disponible</td>
        </tr>
      </tbody>
    </table>

    <!-- Pagination -->
    <pagination-controls
      *ngIf="filteredAnalyses.length > 0"
      (pageChange)="currentPage = $event"
      previousLabel="Précédent"
      nextLabel="Suivant"
      class="pagination-custom">
    </pagination-controls>
  </div>

  <!-- Modal for Add/Edit Analysis -->
  <div class="modal" [ngClass]="{ 'show': isModalOpen }">
    <div class="modal-content">
      <span class="close" (click)="closeModal()">&times;</span>
      <h3>{{ selectedAnalyse ? 'Modifier' : 'Ajouter' }} une Analyse</h3>

      <!-- Analysis Form -->
      <form [formGroup]="analyseForm" (ngSubmit)="saveAnalyse()">
        <input type="hidden" formControlName="id">

        <div class="form-group">
          <label for="analyse">Nom de l'analyse: <span class="required">*</span></label>
          <input
            type="text"
            id="analyse"
            formControlName="analyse"
            [ngClass]="{'invalid': analyseForm.get('analyse')?.invalid && analyseForm.get('analyse')?.touched}"
          >
          <div *ngIf="analyseForm.get('analyse')?.invalid && analyseForm.get('analyse')?.touched" class="error-text">
            Le nom de l'analyse est requis.
          </div>
        </div>

        <div class="form-group">
          <label for="parameter">Paramètre: <span class="required">*</span></label>
          <textarea
            id="parameter"
            formControlName="parameter"
            rows="4"
            [ngClass]="{'invalid': analyseForm.get('parameter')?.invalid && analyseForm.get('parameter')?.touched}"
          ></textarea>
          <div *ngIf="analyseForm.get('parameter')?.invalid && analyseForm.get('parameter')?.touched" class="error-text">
            Le paramètre est requis.
          </div>
        </div>

        <div class="form-group">
          <label for="price">Prix: <span class="required">*</span></label>
          <input
            type="number"
            id="price"
            formControlName="price"
            min="0"
            step="0.01"
            [ngClass]="{'invalid': analyseForm.get('price')?.invalid && analyseForm.get('price')?.touched}"
          >
          <div *ngIf="analyseForm.get('price')?.invalid && analyseForm.get('price')?.touched" class="error-text">
            <span *ngIf="analyseForm.get('price')?.errors?.['required']">Le prix est requis.</span>
            <span *ngIf="analyseForm.get('price')?.errors?.['min']">Le prix doit être positif.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <label for="is_accredited" class="checkbox-label">
            <input
              type="checkbox"
              id="is_accredited"
              formControlName="is_accredited"
            >
            Analyse accréditée
          </label>
        </div>

        <div class="action-btns-modal">
          <button type="submit" class="save-btn" [disabled]="analyseForm.invalid">
            {{ selectedAnalyse ? 'Mettre à jour' : 'Ajouter' }}
          </button>
          <button type="button" class="cancel-btn" (click)="closeModal()">Annuler</button>
        </div>
      </form>
    </div>
  </div>
</div>
