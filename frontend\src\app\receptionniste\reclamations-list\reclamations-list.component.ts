import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faSearch,
  faCalendarAlt,
  faEraser,
  faEye,
  faSpinner,
  faCircleDot,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';

// Define a Reclamation interface for type safety
interface Reclamation {
  id: string;
  date: string;
  clientName: string;
  clientNickname: string;
  subject: string;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved';
  demandeId?: string;
  attachmentUrl?: string;
}

@Component({
  selector: 'app-reclamations-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxPaginationModule,
    FontAwesomeModule
  ],
  templateUrl: './reclamations-list.component.html',
  styleUrl: './reclamations-list.component.css'
})
export class ReclamationsListComponent implements OnInit {
  // Font Awesome icons
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faEraser = faEraser;
  faEye = faEye;
  faSpinner = faSpinner;
  faCircleDot = faCircleDot;
  faCheckCircle = faCheckCircle;

  // Data
  reclamations: Reclamation[] = [];
  filteredReclamations: Reclamation[] = [];

  // Loading state
  isLoading = true;

  // Filter properties
  searchTerm = '';
  selectedStatus = '';
  selectedDate = '';

  // Pagination properties
  currentPage = 1;
  itemsPerPage = 5;

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Simulate loading data from an API
    setTimeout(() => {
      this.loadReclamations();
      this.isLoading = false;
    }, 1000);
  }

  // Load mock reclamations data
  loadReclamations(): void {
    this.reclamations = [
      {
        id: 'REC-001',
        date: '2023-06-15',
        clientName: 'Client',
        clientNickname: 'Client',
        subject: 'Résultats incorrects',
        description: 'Les résultats d\'analyse reçus ne correspondent pas à ma demande initiale.',
        status: 'pending'
      },
      {
        id: 'REC-002',
        date: '2023-06-20',
        clientName: 'Client',
        clientNickname: 'Client',
        subject: 'Délai de traitement trop long',
        description: 'Ma demande d\'analyse prend beaucoup plus de temps que prévu.',
        status: 'in_progress',
        demandeId: 'DEM-2023-456'
      },
      {
        id: 'REC-003',
        date: '2023-07-05',
        clientName: 'Client2',
        clientNickname: 'Client2',
        subject: 'Erreur de facturation',
        description: 'Le montant facturé ne correspond pas au devis initial.',
        status: 'resolved',
        demandeId: 'DEM-2023-789'
      },
      {
        id: 'REC-004',
        date: '2023-07-10',
        clientName: 'Client2',
        clientNickname: 'Client2',
        subject: 'Problème avec le rapport',
        description: 'Le rapport d\'analyse contient des informations erronées.',
        status: 'pending',
        demandeId: 'DEM-2023-101'
      },
      {
        id: 'REC-005',
        date: '2023-07-15',
        clientName: 'Client3',
        clientNickname: 'Client3',
        subject: 'Demande d\'information complémentaire',
        description: 'J\'aimerais obtenir plus de détails sur les méthodes d\'analyse utilisées.',
        status: 'in_progress'
      },
      {
        id: 'REC-006',
        date: '2023-07-20',
        clientName: 'Client',
        clientNickname: 'Client',
        subject: 'Échantillon perdu',
        description: 'Mon échantillon semble avoir été perdu lors du processus d\'analyse.',
        status: 'resolved',
        demandeId: 'DEM-2023-202'
      }
    ];

    // Initialize filtered reclamations
    this.filteredReclamations = [...this.reclamations];
  }

  // Apply filters based on search term, status, and date
  applyFilters(): void {
    this.filteredReclamations = this.reclamations.filter(reclamation => {
      // Filter by search term (ID, client name, or subject)
      const matchesSearch = this.searchTerm === '' ||
        reclamation.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        reclamation.clientName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        reclamation.clientNickname.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        reclamation.subject.toLowerCase().includes(this.searchTerm.toLowerCase());

      // Filter by status
      const matchesStatus = this.selectedStatus === '' || reclamation.status === this.selectedStatus;

      // Filter by date
      const matchesDate = this.selectedDate === '' ||
        reclamation.date === this.selectedDate;

      return matchesSearch && matchesStatus && matchesDate;
    });

    // Reset to first page when filters change
    this.currentPage = 1;
  }

  // Reset all filters
  resetFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedDate = '';
    this.filteredReclamations = [...this.reclamations];
    this.currentPage = 1;
  }

  // Navigate to reclamation details
  viewReclamationDetails(id: string): void {
    this.router.navigate(['/receptionist/reclamation-details', id]);
  }
}
