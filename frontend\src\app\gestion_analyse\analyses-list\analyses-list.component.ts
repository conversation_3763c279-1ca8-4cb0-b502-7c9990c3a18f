import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormBuilder, Validators, FormsModule } from '@angular/forms';
import { Analyse } from '../../../models/analyse.model';
import { AnalysesService } from '../../services/analyses.service';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { NgxPaginationModule } from 'ngx-pagination';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSearch, faEraser, faEye, faFilter, faEdit, faTrash, faPlus } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-analyses-list',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, NgxPaginationModule, FontAwesomeModule],
  templateUrl: './analyses-list.component.html',
  styleUrls: ['./analyses-list.component.css']
})
export class AnalysesListComponent implements OnInit {
  // Font Awesome icons
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faFilter = faFilter;
  faEdit = faEdit;
  faTrash = faTrash;
  faPlus = faPlus;
  // Data and UI state
  allAnalyses: Analyse[] = [];
  filteredAnalyses: Analyse[] = [];
  displayedAnalyses: Analyse[] = [];
  analyseForm: FormGroup;
  filterForm: FormGroup;
  selectedAnalyse: Analyse | null = null;
  isModalOpen = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  // Make Math available to the template
  Math = Math;

  // Pagination properties
  currentPage = 1;
  itemsPerPage = 10;
  itemsPerPageOptions = [5, 10, 20, 50];
  totalPages = 1;

  // Filtering properties
  searchTerm = '';

  constructor(private analysesService: AnalysesService, private fb: FormBuilder) {
    // Initialize analysis form
    this.analyseForm = this.fb.group({
      id: [null],
      analyse: ['', [Validators.required]],
      parameter: ['', [Validators.required]],
      price: [0, [Validators.required, Validators.min(0)]],
      is_accredited: [false]
    });

    // Initialize filter form
    this.filterForm = this.fb.group({
      searchTerm: ['']
    });
  }

  ngOnInit(): void {
    this.loadAnalyses();

    // Subscribe to filter form changes
    this.filterForm.get('searchTerm')?.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(value => {
        this.searchTerm = value;
        this.applyFilters();
      });
  }

  loadAnalyses(): void {
    this.isLoading = true;
    this.analysesService.getAllAnalyses().subscribe({
      next: (data) => {
        // Ensure data is an array
        this.allAnalyses = Array.isArray(data) ? data : [];
        console.log('Loaded analyses:', this.allAnalyses);
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching analyses:', error);
        this.errorMessage = 'Erreur lors du chargement des analyses.';
        this.isLoading = false;
        // Initialize with empty arrays on error
        this.allAnalyses = [];
        this.filteredAnalyses = [];
        this.displayedAnalyses = [];
      }
    });
  }

  /**
   * Apply filters to the analyses list
   */
  applyFilters(): void {
    // Start with all analyses
    let result = [...this.allAnalyses];

    // Apply search term filter
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      const term = this.searchTerm.toLowerCase().trim();
      result = result.filter(analyse =>
        analyse.analyse.toLowerCase().includes(term) ||
        analyse.parameter.toLowerCase().includes(term)
      );
    }

    // Update filtered analyses
    this.filteredAnalyses = result;

    // Update pagination
    this.updatePagination();
  }

  /**
   * Update pagination
   */
  updatePagination(): void {
    // Calculate total pages
    this.totalPages = Math.ceil(this.filteredAnalyses.length / this.itemsPerPage);

    // Ensure current page is valid
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages || 1;
    }
  }

  /**
   * Change the number of items per page
   */
  changeItemsPerPage(value: number): void {
    this.itemsPerPage = value;
    this.currentPage = 1; // Reset to first page
    this.updatePagination();
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    // Always return integer value without decimal places
    return Math.round(price).toString();
  }

  openModal(): void {
    this.isModalOpen = true;
    this.analyseForm.reset();
    this.selectedAnalyse = null;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.analyseForm.reset();
    this.selectedAnalyse = null;
  }

  editAnalyse(analyse: Analyse): void {
    this.selectedAnalyse = analyse;
    this.analyseForm.patchValue({
      id: analyse.id,
      analyse: analyse.analyse,
      parameter: analyse.parameter,
      price: analyse.price,
      is_accredited: analyse.is_accredited === 'Yes'
    });
    this.isModalOpen = true;
  }

  deleteAnalyse(id: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette analyse ?')) {
      this.isLoading = true;
      this.analysesService.deleteAnalysis(id).subscribe({
        next: () => {
          this.successMessage = 'Analyse supprimée avec succès.';
          this.loadAnalyses(); // Reload all analyses
          setTimeout(() => {
            this.successMessage = '';
          }, 3000);
        },
        error: (error) => {
          console.error('Error deleting analysis:', error);
          this.errorMessage = 'Erreur lors de la suppression de l\'analyse.';
          this.isLoading = false;
          setTimeout(() => {
            this.errorMessage = '';
          }, 3000);
        }
      });
    }
  }

  saveAnalyse(): void {
    if (this.analyseForm.valid) {
      this.isLoading = true;
      const formValue = this.analyseForm.value;

      // Convert checkbox value to "Yes" or "No"
      const analyseData: Analyse = {
        ...formValue,
        is_accredited: formValue.is_accredited ? 'Yes' : 'No'
      };

      if (analyseData.id) {
        // Update existing analysis
        this.analysesService.updateAnalysis(analyseData.id, analyseData).subscribe({
          next: () => {
            this.successMessage = 'Analyse mise à jour avec succès.';
            this.loadAnalyses(); // Reload all analyses
            this.closeModal();
            setTimeout(() => {
              this.successMessage = '';
            }, 3000);
          },
          error: (error) => {
            console.error('Error updating analysis:', error);
            this.errorMessage = 'Erreur lors de la mise à jour de l\'analyse.';
            this.isLoading = false;
            setTimeout(() => {
              this.errorMessage = '';
            }, 3000);
          }
        });
      } else {
        // Create new analysis
        this.analysesService.createAnalysis(analyseData).subscribe({
          next: () => {
            this.successMessage = 'Analyse créée avec succès.';
            this.loadAnalyses(); // Reload all analyses
            this.closeModal();
            setTimeout(() => {
              this.successMessage = '';
            }, 3000);
          },
          error: (error) => {
            console.error('Error creating analysis:', error);
            this.errorMessage = 'Erreur lors de la création de l\'analyse.';
            this.isLoading = false;
            setTimeout(() => {
              this.errorMessage = '';
            }, 3000);
          }
        });
      }
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.analyseForm.controls).forEach(key => {
        const control = this.analyseForm.get(key);
        control?.markAsTouched();
      });
    }
  }
}
