<?php

namespace App\Http\Controllers;
use App\Models\Facture;
use App\Models\Demande;
use App\Models\ResultatClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FactureController extends Controller
{
    public function index()
    {
        $factures = Facture::all();
        return response()->json($factures);
    }

    /**
     * Display the specified facture with its associated devis.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the facture by its ID
        $facture = Facture::find($id);
        if (!$facture) {
            return response()->json(['message' => 'Facture not found'], 404);
        }

        // Find the demande using the facture's demande field (e.g., "0014-077")
        $demande = Demande::where('demande_id', $facture->demande)->first();

        // Get the devis if demande exists, otherwise return an empty collection
        $devis = $demande ? $demande->devis : collect();

        // Return the facture and its associated devis
        return response()->json([
            'facture' => $facture,
            'devis' => $devis
        ]);
    }

    /**
     * Fetch all valid reports (validation = 1)
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getValidReports()
    {
        // Retrieve all rapports with validation = 1
        $validReports = \App\Models\Rapport::where('validation', 1)
            ->with('demande:id,demande_id,user_id')
            ->get();

        // Format the response
        $formattedReports = $validReports->map(function ($rapport) {
            return [
                'id' => $rapport->id,
                'demande_id' => $rapport->demande ? $rapport->demande->demande_id : 'N/A',
                'creation_date' => $rapport->creation_date,
                'status' => $rapport->status,
                'validation' => $rapport->validation,
                'demande_numero' => $rapport->demande_numero,
                'updated_at' => $rapport->updated_at
            ];
        });

        return response()->json($formattedReports);
    }

    /**
     * Upload report or invoice file for a specific demande
     *
     * @param Request $request
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadFile(Request $request, $demandeId)
    {
        // Log the request for debugging
        Log::info('File upload method called', [
            'demande_id' => $demandeId,
            'request_data' => $request->all(),
            'files' => $request->hasFile('file') ? 'File present' : 'No file'
        ]);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png|max:25600', // 25MB max, accept PDF and images
            'type' => 'required|string|in:report,invoice', // Type must be either 'report' or 'invoice'
        ]);

        if ($validator->fails()) {
            Log::error('File upload validation failed', ['errors' => $validator->errors()]);
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Find the demande by demande_id (string identifier)
        $demande = Demande::where('demande_id', $demandeId)->first();

        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Handle file upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $fileType = $request->input('type');

            // Use a simpler naming convention based on demande_id but preserve file extension
            $filePrefix = $fileType === 'report' ? 'rapport' : 'facture';
            $extension = $file->getClientOriginalExtension();
            $fileName = $filePrefix . '_' . $demandeId . '.' . $extension;

            // Store the file in the appropriate directory within the storage
            $directory = $fileType === 'report' ? 'rapports' : 'factures';
            $filePath = $file->storeAs($directory, $fileName, 'public');

            // Check if a resultat already exists for this demande
            $existingResultat = ResultatClient::where('demande_id', $demandeId)->first();

            if ($existingResultat) {
                // If it's a report file
                if ($fileType === 'report') {
                    // Delete the old file if it exists
                    if ($existingResultat->rapport_file) {
                        $oldFilePath = storage_path('app/public/' . $existingResultat->rapport_file);
                        if (file_exists($oldFilePath)) {
                            unlink($oldFilePath);
                        }
                    }
                    // Update the existing resultat with the report file
                    $existingResultat->rapport_file = $filePath;
                    $existingResultat->save();
                }
                // If it's an invoice file
                else if ($fileType === 'invoice') {
                    // Delete the old file if it exists
                    if ($existingResultat->facture_file) {
                        $oldFilePath = storage_path('app/public/' . $existingResultat->facture_file);
                        if (file_exists($oldFilePath)) {
                            unlink($oldFilePath);
                        }
                    }
                    // Update the existing resultat with the invoice file
                    $existingResultat->facture_file = $filePath;
                    $existingResultat->save();
                }

                $resultat = $existingResultat;
            } else {
                // Create a new resultat
                $resultat = new ResultatClient();
                $resultat->demande_id = $demandeId;
                $resultat->status = 'pending';

                // Set the appropriate file path based on type
                if ($fileType === 'report') {
                    $resultat->rapport_file = $filePath;
                } else if ($fileType === 'invoice') {
                    $resultat->facture_file = $filePath;
                }

                $resultat->save();
            }

            Log::info('File uploaded successfully', [
                'demande_id' => $demande->demande_id,
                'resultat_id' => $resultat->id,
                'file_type' => $fileType,
                'file_path' => $filePath
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'File uploaded successfully',
                'data' => [
                    'demande_id' => $demande->demande_id,
                    'resultat_id' => $resultat->id,
                    'file_type' => $fileType,
                    'file_path' => $filePath
                ]
            ]);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'No file was provided'
        ], 400);
    }

    /**
     * Get all resultat clients
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllResultatClients()
    {
        $resultats = ResultatClient::with('demande:id,demande_id,user_id')->
        where('status','sent')
        ->get();

        return response()->json($resultats);
    }

    /**
     * Get a specific resultat client by demande ID
     *
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getResultatClient($demandeId)
    {
        $resultat = ResultatClient::where('demande_id', $demandeId)->first();

        if (!$resultat) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        return response()->json($resultat);
    }

    /**
     * Delete the report file for a specific demande
     *
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteReportFile($demandeId)
    {
        // Find the resultat for this demande
        $resultat = ResultatClient::where('demande_id', $demandeId)->first();

        if (!$resultat) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        // Check if there is a report file
        if (!$resultat->rapport_file) {
            return response()->json([
                'status' => 'error',
                'message' => 'No report file found for this demande'
            ], 404);
        }

        // Delete the file from storage
        $filePath = storage_path('app/public/' . $resultat->rapport_file);
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // Update the resultat
        $resultat->rapport_file = null;

        // If both files are null, set status to pending
        if (!$resultat->facture_file) {
            $resultat->status = 'pending';
            $resultat->sent_at = null;
        }

        $resultat->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Report file deleted successfully'
        ]);
    }

    /**
     * Delete the invoice file for a specific demande
     *
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteInvoiceFile($demandeId)
    {
        // Find the resultat for this demande
        $resultat = ResultatClient::where('demande_id', $demandeId)->first();

        if (!$resultat) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        // Check if there is an invoice file
        if (!$resultat->facture_file) {
            return response()->json([
                'status' => 'error',
                'message' => 'No invoice file found for this demande'
            ], 404);
        }

        // Delete the file from storage
        $filePath = storage_path('app/public/' . $resultat->facture_file);
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // Update the resultat
        $resultat->facture_file = null;

        // If both files are null, set status to pending
        if (!$resultat->rapport_file) {
            $resultat->status = 'pending';
            $resultat->sent_at = null;
        }

        $resultat->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Invoice file deleted successfully'
        ]);
    }

    /**
     * Send results to client
     *
     * @param string $demandeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendResultsToClient($demandeId)
    {
        // Find the demande by demande_id (string identifier)
        $demande = Demande::where('demande_id', $demandeId)->first();

        if (!$demande) {
            return response()->json([
                'status' => 'error',
                'message' => 'Demande not found'
            ], 404);
        }

        // Find the resultat for this demande
        $resultat = ResultatClient::where('demande_id', $demandeId)->first();

        if (!$resultat) {
            return response()->json([
                'status' => 'error',
                'message' => 'No results found for this demande'
            ], 404);
        }

        // Check if at least one file is uploaded
        if (!$resultat->rapport_file && !$resultat->facture_file) {
            return response()->json([
                'status' => 'error',
                'message' => 'No files available to send to client'
            ], 400);
        }

        // Update the resultat status to 'sent'
        $resultat->status = 'sent';
        $resultat->sent_at = Carbon::now();
        $resultat->save();

        // TODO: Send email notification to client
        // This would typically involve using Laravel's Mail facade to send an email
        // with links to download the files

        Log::info('Results sent to client', [
            'demande_id' => $demandeId,
            'resultat_id' => $resultat->id,
            'sent_at' => $resultat->sent_at
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Results sent to client successfully',
            'data' => [
                'demande_id' => $demandeId,
                'resultat_id' => $resultat->id,
                'rapport_file' => $resultat->rapport_file,
                'facture_file' => $resultat->facture_file,
                'sent_at' => $resultat->sent_at
            ]
        ]);
    }
}
