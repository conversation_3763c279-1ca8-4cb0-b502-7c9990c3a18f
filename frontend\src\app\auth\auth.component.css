@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ SECTION PRINCIPALE */
.auth-section {

  height: 100vh;
  background: radial-gradient(circle at top left, #0c0032, #190061);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  animation: fadeIn 1.5s ease-in-out forwards;
}

/* ✅ CONTENEUR FORMULAIRE */
.container {
  width: 500px;
  padding: 50px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
  animation: slideIn 1.2s ease-in-out forwards;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.container:hover {
  transform: scale(1.02);
  box-shadow: 0px 20px 50px rgba(255, 255, 255, 0.4);
}

/* ✅ TITRE */
h1 {
  font-size: 18px;
  font-weight: bold;
  background: linear-gradient(90deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-bottom: 4px solid #2496d3;
  padding-bottom: 15px;
  margin-bottom: 30px;
  animation: fadeInDown 1.5s ease-in-out forwards;
}

/* ✅ CHAMPS DU FORMULAIRE */
.input-box {
  position: relative;
  width: 100%;
  margin: 15px 0;
}

.input-box input {
  width: 100%;
  padding: 18px;
  font-size: 15px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  outline: none;
  transition: 0.3s ease-in-out;
}

.input-box input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14.5px;
}

.input-box i {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-size: 15px;
}

/* ✅ EFFET SUR INPUT */
.input-box input:focus {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
}

/* ✅ BOUTON */
.btn-primary {
  width: 100%;
  padding: 20px;
  font-size: 17px;
  font-weight: bold;
  color: white;
  background: linear-gradient(90deg, #ff007f, #2496d3);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: 0.3s ease-in-out;
}

.btn-primary:hover {
  transform: scale(1.08);
  box-shadow: 0px 12px 30px rgba(255, 0, 128, 0.6);
}

/* ✅ LIENS */
p {
  font-size: 15px;
  margin-top: 15px;
}

a {
  font-size: 15px;
  color: #ff007f;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease-in-out;
}

a:hover {
  color: #ff33a6;
  text-decoration: underline;
}

/* ✅ RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .container {
    width: 90%;
    padding: 40px;
  }

  h1 {
    font-size: 17px;
  }

  .input-box input {
    font-size: 14.5px;
  }

  .btn-primary {
    font-size: 2.5rem;
  }

  p, a {
    font-size: 14.5px;
  }
}

/* ✅ ANIMATIONS */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-50px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}
