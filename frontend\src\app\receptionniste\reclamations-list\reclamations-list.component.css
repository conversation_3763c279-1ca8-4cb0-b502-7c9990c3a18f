@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.reclamations-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 70px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}

/* ✅ Titre */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes glowText {
  from { text-shadow: 0 0 5px rgba(36, 150, 211, 0.3); }
  to { text-shadow: 0 0 10px rgba(36, 150, 211, 0.6); }
}

/* ✅ Barre de filtrage */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0px 3px 10px rgba(36, 150, 211, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 200px;
}

.filter-group label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.filter-input, .filter-select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: 'Montserrat', sans-serif;
  transition: all 0.3s;
}

.filter-input:focus, .filter-select:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* Bouton de réinitialisation */
.btn-reset {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 5px;
  color: #3e3e3f;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
  align-self: flex-end;
}

.btn-reset:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ✅ Table */
.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ En-têtes */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
  text-align: center;
}

/* ✅ Lignes */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
  vertical-align: middle;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

tr:hover {
  background-color: #f0f7fc;
}

/* Actions column */
.actions-col {
  text-align: center;
  min-width: 150px;
}

/* ✅ Statuts */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
}

.status-tag.pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-tag.in-progress {
  background-color: #cce5ff;
  color: #004085;
}

.status-tag.resolved {
  background-color: #d4edda;
  color: #155724;
}

/* Bouton de détails */
.btn-details {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: #2496d3;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

.btn-details:hover {
  background-color: #1a7bb9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading spinner */
.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.3);
  border-radius: 50%;
  border-top-color: #2496d3;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.empty-row td {
  padding: 30px;
  color: #6c757d;
  font-style: italic;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Responsive design */
@media (max-width: 992px) {
  .reclamations-container {
    padding: 40px 20px;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }

  .btn-reset {
    align-self: center;
    margin-top: 10px;
  }
}

@media (max-width: 768px) {
  table {
    font-size: 14px;
  }

  th, td {
    padding: 10px 8px;
  }

  .btn-details {
    padding: 6px 10px;
    font-size: 14px;
  }

  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
  }
}