import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StatisticsRefreshService {
  private refreshSubject = new Subject<void>();

  // Observable that components can subscribe to
  refresh$ = this.refreshSubject.asObservable();

  /**
   * Trigger a refresh of statistics
   * Call this method when an action is taken that affects statistics
   */
  triggerRefresh(): void {
    this.refreshSubject.next();
  }
}
