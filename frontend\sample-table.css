.sample-table-container {
  margin: 20px auto;
  font-family: 'Arial', sans-serif;
}

.sample-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #000;
}

.sample-table th, 
.sample-table td {
  border: 1px solid #000;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
  height: 40px;
}

.sample-table thead th {
  background-color: #f8f8f8;
  font-weight: bold;
}

/* Special styling for the "Analyses demandées" column */
.sample-table th:last-child {
  text-decoration: underline;
  color: #ff0066;
}

/* Empty rows for data entry */
.sample-table tbody tr td {
  height: 60px; /* Taller rows for data entry */
}

/* Observations section */
.observations {
  text-align: left;
  font-weight: bold;
  padding-left: 10px;
}

.observations-field {
  height: 80px; /* Taller row for observations */
}

/* Responsive design */
@media (max-width: 768px) {
  .sample-table {
    font-size: 12px;
  }
  
  .sample-table th, 
  .sample-table td {
    padding: 4px;
  }
}