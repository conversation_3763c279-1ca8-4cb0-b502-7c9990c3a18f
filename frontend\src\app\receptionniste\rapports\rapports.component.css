/* ------------------------------------------------------
   1) Importation des Polices
   ------------------------------------------------------ */
   @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

   /* ------------------------------------------------------
      2) Global Styling
      ------------------------------------------------------ */
   * {
     margin: 0;
     padding: 0;
     box-sizing: border-box;
     font-family: 'Montserrat', sans-serif;
   }

   body {
     background-color: #f7f7f7;
     color: #333;
     animation: fadeInBg 1.2s ease-in-out forwards;
   }

   /* On peut appliquer une animation de fondu général au body ou à .presentation-section */


   h2.demandes-title {

      font-family: 'Orbitron', sans-serif;
      font-size: 22px;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 2px;
      margin-bottom: 20px;
      border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
      display: inline-block;
      padding-bottom: 8px;
      animation: glowText 1.5s infinite alternate;
      background: linear-gradient(90deg, black, grey);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
   /* ------------------------------------------------------
      3) Admin Panel
      ------------------------------------------------------ */
   .admin-panel {
     margin: 50px auto;
     padding: 30px;
     background: #fff;
     border-radius: 10px;
     box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
     max-width: 90%;
     text-align: center;
     /* Animation plus visible */
     animation: fadeInUp 1s ease-in-out forwards;
   }

   /* ------------------------------------------------------
      3.1) Filter Bar
      ------------------------------------------------------ */
   .filter-bar {
     display: flex;
     flex-wrap: wrap; /* Allows wrapping on smaller screens */
     align-items: center; /* Vertically centers all items */
     justify-content: center; /* Horizontally centers all items */
     gap: 1rem; /* Spacing between filter groups and button */
     padding: 1rem;
     background-color: #f5f5f5;
     border-bottom: 1px solid #ddd;
     max-width: 60%; /* Limits the width as in the validation component */
     margin: 0 auto; /* Centers the filter bar within its parent */
   }

   .filter-group {
     display: flex;
     flex-direction: column; /* Stacks label and input vertically */
     gap: 0.5rem; /* Maintains spacing between label and input */
   }

   .filter-group label {
     font-weight: 600;
     font-size: 14px;
     color: #444;
   }

   .filter-group input,
   .filter-group select {
     width: 150px;
     padding: 0.5rem;
     border: 1px solid #ccc;
     border-radius: 4px;
     background-color: white;
     transition: border-color 0.2s, box-shadow 0.2s;
   }

   .status-select {
     width: 180px; /* Slightly wider for status dropdown */
     appearance: none; /* Remove default arrow */
     background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0h12L6 6z" fill="%23666"/></svg>');
     background-repeat: no-repeat;
     background-position: right 10px center;
     padding-right: 30px; /* Space for the arrow */
   }

   #search {
     width: 250px;
   }

   .filter-group input:focus,
   .filter-group select:focus {
     border-color: #007bff;
     outline: none;
     box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
   }

   .btn-clear {
     margin-left: auto;
     padding: 10px 15px;
     background-color: #6c757d;
     color: white;
     border: none;
     border-radius: 4px;
     cursor: pointer;
     font-weight: bold;
     display: flex;
     align-items: center;
     justify-content: center;
   }

   .btn-clear:hover {
     background-color: #5a6268;
     transform: translateY(-2px);
   }

   /* Style pour l'icône dans l'input de recherche */
   .input-with-icon {
     position: relative;
     display: flex;
     align-items: center;
   }

   .input-icon {
     position: absolute;
     left: 10px;
     color: #6c757d;
   }

   .filter-input {
     padding-left: 30px !important;
   }

   /* Styles pour l'indicateur de chargement et les messages vides */
   .loading-row, .empty-row {
     height: 100px;
   }

   .loading-row td, .empty-row td {
     text-align: center;
     vertical-align: middle;
     font-size: 18px;
     color: #6c757d;
   }

   .spinner-container {
     display: flex;
     flex-direction: column;
     align-items: center;
     justify-content: center;
     padding: 15px;
   }

   .spinner {
     width: 40px;
     height: 40px;
     border: 4px solid rgba(36, 150, 211, 0.2);
     border-top: 4px solid #2496d3;
     border-radius: 50%;
     animation: spin 1s linear infinite;
     margin-bottom: 10px;
   }

   .text-center {
     text-align: center;
   }

   /* Error notification */
   .error-notification {
     background: #f8d7da;
     color: #721c24;
     padding: 10px;
     border-radius: 5px;
     margin-bottom: 20px;
     border: 1px solid #f5c6cb;
     text-align: center;
     width: 100%;
     font-weight: bold;
   }

   /* Loading Spinner Overlay */
   .loading-overlay {
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-color: rgba(255, 255, 255, 0.7);
     display: flex;
     justify-content: center;
     align-items: center;
     z-index: 1000;
     backdrop-filter: blur(3px);
     animation: fadeIn 0.3s ease-in-out;
   }

   /* Content hidden when loading */
   .content-hidden {
     opacity: 0.3;
     pointer-events: none;
   }

   .loading-popup {
     background-color: white;
     border-radius: 10px;
     padding: 30px;
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
     text-align: center;
     max-width: 400px;
     width: 90%;
   }

   .loading-popup h3 {
     margin-top: 20px;
     color: #333;
     font-size: 18px;
   }

   .loading-popup .spinner {
     border: 4px solid rgba(0, 0, 0, 0.1);
     border-radius: 50%;
     border-top: 4px solid #2496d3;
     width: 50px;
     height: 50px;
     animation: spin 1s linear infinite;
     margin: 0 auto;
   }

   @keyframes spin {
     to { transform: rotate(360deg); }
   }

   @keyframes fadeIn {
     from { opacity: 0; }
     to { opacity: 1; }
   }

   /* ------------------------------------------------------
      4) Table des Rapports
      ------------------------------------------------------ */
   .reports-table {
     width: 100%;
     border-collapse: collapse;
     margin-top: 20px;
     background: white;
     border-radius: 8px;
     overflow: hidden;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
   }
/* Validation Status Styles for Light Design */
.status-pending {
  color: #ffa500; /* Light orange for pending */
  font-size: 13px; /* Slightly smaller text for a lighter feel */
  font-weight: bold;
}

.status-approved {
  color: #28a745; /* Light green for approved */
  font-size: 13px;
  font-weight: bold;
}

.status-rejected {
  color: #dc3545; /* Light red for rejected */
  font-size: 13px;
  font-weight: bold;
}

/* Statut d'envoi column styling */
.status-col {
  min-width: 150px;
  text-align: center;
}

.status-col button, .status-col span {
  margin: 0 auto;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* Status indicator styling */
.status {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
  display: inline-block;
  margin: 0 auto;
}

.status.sent {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  box-shadow: 0px 3px 8px rgba(40, 167, 69, 0.4);
}

/* Send button styling */
.btn-action {
  padding: 8px 12px;
  font-size: 13px;
  min-width: 130px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px auto;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-send {
  background: #28a745;
  color: white;
  box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.3);
}

.btn-send:hover:not(:disabled) {
  background: #218838;
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(40, 167, 69, 0.6);
}

/* Style for the "Créer Facture" button */
.create-invoice-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 25px;
  cursor: pointer;
  background-color: #17a2b8; /* Cyan color for distinction */
  color: white;
  border: none;
  text-transform: uppercase;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
}

.create-invoice-btn:hover {
  background-color: #138496; /* Darker cyan on hover */
  transform: scale(1.05);
}
   /* En-têtes */
   .reports-table thead tr th {
     background-color: #2496d3;
     color: white;
     padding: 15px;
     text-align: center;
     font-weight: 600;
     text-transform: uppercase;
     font-size: 14px;
   }

   /* Lignes du tableau */
   .reports-table tbody tr td {
     padding: 15px;
     text-align: center;
     font-size: 14px;
     color: #333;
     border-bottom: 1px solid #f2f2f2;
     transition: background-color 0.3s ease;
   }

   .reports-table tbody tr:hover td {
     background-color: #f8faff;
   }

   /* ------------------------------------------------------
      5) Boutons d'Action
      ------------------------------------------------------ */
   .action-buttons {
     display: flex;
     justify-content: center;
     gap: 10px;
   }

   .approve-btn,
   .reject-btn,
   .details-btn,
   .send-client-btn {
     padding: 10px 20px;
     font-size: 14px;
     border-radius: 25px;
     cursor: pointer;
     transition: all 0.3s ease;
     border: none;
     text-transform: uppercase;
     outline: none;
   }

   /* Bouton "Valider" (approve) */
   .approve-btn {
     background-color: #2496d3;
     color: white;
     box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
   }
   .approve-btn:hover {
     background-color: #1a6a8e;
     transform: scale(1.05);
   }

   /* Bouton "Rejeter" (reject) */
   .reject-btn {
     background-color: #e74c3c;
     color: white;
     box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
   }
   .reject-btn:hover {
     background-color: #c0392b;
     transform: scale(1.05);
   }

   /* Bouton "Voir Détail" */
   .details-btn {
     background-color: #555;
     color: #fff;
     box-shadow: 0 5px 15px rgba(85, 85, 85, 0.3);
   }
   .details-btn:hover {
     background-color: #333;
     transform: scale(1.05);
   }

   /* Bouton "Envoyer au client" */
   .send-client-btn {
     background-color: #28a745;
     color: white;
     box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
     display: flex;
     align-items: center;
     justify-content: center;
   }
   .send-client-btn:hover {
     background-color: #218838;
     transform: scale(1.05);
   }
   .send-client-btn:disabled {
     background-color: #6c757d;
     cursor: not-allowed;
     transform: none;
     opacity: 0.7;
   }

   /* ------------------------------------------------------
      6) Animations
      ------------------------------------------------------ */
   /* Fondu de l'arrière-plan */
   @keyframes fadeInBg {
     0%   { opacity: 0; }
     100% { opacity: 1; }
   }

   /* Slide vers le haut + fade */
   @keyframes fadeInUp {
     0% {
       opacity: 0;
       transform: translateY(40px);
     }
     100% {
       opacity: 1;
       transform: translateY(0);
     }
   }

   /* Effet "glow" sur le texte du titre */
   @keyframes glowText {
     0% {
       text-shadow: 0 0 10px rgba(36, 150, 211, 0.4);
     }
     100% {
       text-shadow: 0 0 20px rgba(36, 150, 211, 0.8);
     }
   }

   /* Pagination styles */
   .pagination-custom {
     margin-top: 20px;
     display: flex;
     justify-content: center;
   }

   .pagination-custom /deep/ .ngx-pagination {
     margin: 0;
     padding: 0;
     display: flex;
     justify-content: center;
   }

   .pagination-custom /deep/ .ngx-pagination li {
     border-radius: 4px;
     margin: 0 2px;
   }

   .pagination-custom /deep/ .ngx-pagination .current {
     background: #2496d3;
     border-radius: 4px;
   }

   .pagination-custom /deep/ .ngx-pagination a:hover {
     background: rgba(36, 150, 211, 0.1);
     border-radius: 4px;
   }
