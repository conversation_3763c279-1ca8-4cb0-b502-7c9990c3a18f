<div class="demandes-container">
  <h2> Gestion des demandes</h2>

  <!-- Notification de succès -->
  <div *ngIf="successMessage" class="notification">
    {{ successMessage }}
  </div>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro ou client:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
       <option value="derogation"><PERSON><PERSON>roger</option>
        <option value="pending">En attente</option>
        <option value="rejected">Rejeté</option>
        <!-- etc. -->
      </select>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <button (click)="clearFilters()" class="btn-clear"><fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>Effacer les filtres</button>
  </div>

  <!-- Tableau des demandes -->
  <table>
    <thead>
      <tr>
        <th>Demande Numéro</th>
        <th>Nom Client</th>
        <th>Date Réception</th>
        <th>Statut</th>
        <th class="actions-col">Actions</th> <!-- ✅ Nouvelle colonne Actions -->
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="isLoading" class="loading-row">
        <td colspan="5" class="text-center">
          <div class="spinner-container">
            <div class="spinner"></div>
            <span>Chargement...</span>
          </div>
        </td>
      </tr>

      <!-- Message quand aucune demande n'est trouvée -->
      <tr *ngIf="!isLoading && filteredDemandes.length === 0" class="empty-row">
        <td colspan="5" class="text-center">
          Aucune demande trouvée.
        </td>
      </tr>

      <!-- Affichage des demandes -->
      <ng-container *ngIf="!isLoading && filteredDemandes.length > 0">
        <tr
          *ngFor="
            let demande of filteredDemandes
             | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }
          "
          class="clickable-row"
        >
        <td>{{ demande.demande_id }}</td>
        <td>{{ demande.userName || 'Anonyme' }} {{ demande.userNick }}</td>
        <td>{{ demande.demande_date }}</td>
        <td>
          <!-- Badges de statut -->
          <span class="status-tag pending" *ngIf="demande?.status === 'pending'">
            <fa-icon [icon]="faCircleDot" style="color: #ffc107;"></fa-icon> En attente
          </span>
          <span class="status-tag ongoing" *ngIf="demande?.status === 'ongoing_validation'">
            <fa-icon [icon]="faSpinner" style="color: #007bff;"></fa-icon> En cours de validation
          </span>
          <span class="status-tag ongoing" *ngIf="demande?.status === 'derogation'">
            <fa-icon [icon]="faCircleExclamation" style="color: #007bff;"></fa-icon> En cours de validation avec dérogation
          </span>
          <span class="status-tag valid" *ngIf="demande?.status === 'valid'">
            <fa-icon [icon]="faCircleCheck" style="color: #28a745;"></fa-icon> Demande validée
          </span>
          <span class="status-tag rejected" *ngIf="demande?.status === 'rejected'">
            <fa-icon [icon]="faCircleXmark" style="color: #dc3545;"></fa-icon> Demande rejetée
          </span>
        </td>
        <!-- Colonne bouton -->
        <td class="actions-col">
          <button
            class="btn-details"
            (click)="navigateToDemandeDetails(demande)"
          >
            <fa-icon [icon]="faEye" style="margin-right: 8px; font-size: 16px;"></fa-icon>Voir détails
          </button>
        </td>
      </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
  (pageChange)="currentPage = $event"
  previousLabel="Previous"
  nextLabel="Next"
  class="pagination-custom">
</pagination-controls>
</div>
