import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
// Import the specific DemandeService from demande-details folder
import { DemandeService } from '../demande-details/demande.service';

@Component({
  selector: 'app-client-dashboard',
  templateUrl: './client-dashboard.component.html',
  styleUrls: ['./client-dashboard.component.css']
})
export class ClientDashboardComponent implements OnInit {
  // Count of non-valid demandes
  nonValidDemandesCount: number = 0;
  // Count of pending payments for devis
  pendingPaymentsCount: number = 0;

  constructor(private router: Router, private demandeService: DemandeService) {}

 // User Information
 user: any = null;
 name: string | null = null;
 nickname: string | null = null;
 email: string | null = null;
 phone: string | null = null;
 adress: string | null = null;
 fax: string | null = null;
  loadUserInfo() {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        this.user = JSON.parse(userData);
        this.name = this.user?.name || null;
        this.nickname = this.user?.nickname || null;
        this.email = this.user?.email || null;
        this.phone = this.user?.phone || null;
        this.adress = this.user?.adress || null;
        this.fax = this.user?.fax || null;
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }
  ngOnInit() {
    this.loadUserInfo();
    this.loadNonValidDemandesCount();
    this.loadPendingPaymentsCount();
  }

  /**
   * Load the count of non-valid demandes
   */
  loadNonValidDemandesCount() {
    // Use the service method that handles different response formats
    this.demandeService.getCountOfNonValidUserDemandes().subscribe({
      next: (count) => {
        this.nonValidDemandesCount = count;
        console.log('Non-valid demandes count:', this.nonValidDemandesCount);
      },
      error: (error) => {
        console.error('Error loading non-valid demandes count:', error);
        this.nonValidDemandesCount = 0;
      }
    });
  }

  /**
   * Load the count of pending payments for devis
   */
  loadPendingPaymentsCount() {
    this.demandeService.getPendingPaymentsCount().subscribe({
      next: (count) => {
        this.pendingPaymentsCount = count;
        console.log('Pending payments count:', this.pendingPaymentsCount);
      },
      error: (error) => {
        console.error('Error loading pending payments count:', error);
        this.pendingPaymentsCount = 0;
      }
    });
  }

  /**
   * Naviguer vers la gestion des demandes
   */
  goToDemandes() {
    this.router.navigate(['/client/demandes']);
  }

  /**
   * Naviguer vers le suivi des analyses
   */
  goToAnalyses() {
    this.router.navigate(['/client/analyses']);
  }

  /**
   * Naviguer vers la facturation
   */
  goToFacturation() {
    this.router.navigate(['/client/facturation']);
  }

  /**
   * Naviguer vers la consultation du devis
   */
  goToDevis() {
    this.router.navigate(['/client/devis']);
  }

  /**
   * Naviguer vers la consultation de la facture
   */
  goToFacture() {
    this.router.navigate(['/client/facture']);
  }

  /**
   * Naviguer vers la gestion des notifications
   */
  goToNotifications() {
    this.router.navigate(['/client/BringSample/notifications']);
  }

  /**
   * Naviguer vers le formulaire de réclamation
   */
  goToReclamation() {
    this.router.navigate(['/client/reclamation']);
  }

  /**
   * Naviguer vers la liste des réclamations
   */
  goToReclamations() {
    this.router.navigate(['/client/reclamations']);
  }

  /**
   * Naviguer vers le suivi des demandes
   */
  goToSuiviDemande() {
    this.router.navigate(['/client/suivi']);
  }

  /**
   * Naviguer vers les résultats d'analyses
   */
  goToResults() {
    this.router.navigate(['/client/results']);
  }
}
