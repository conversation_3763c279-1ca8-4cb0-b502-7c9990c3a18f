<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DerogationNotification implements ShouldBroadcast
{
    use InteractsWithSockets, SerializesModels;

    public $demande;
    public $derogation;

    public function __construct($demande, $derogation)
    {
        $this->demande = $demande;
        $this->derogation = $derogation;
    }

    public function broadcastOn()
    {
        return ['director-channel'];
    }

    public function broadcastAs()
    {
        return 'derogation-request';
    }
}

