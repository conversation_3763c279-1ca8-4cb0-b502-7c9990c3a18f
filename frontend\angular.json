{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"front-labo": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/front-labo", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, "src/assets"], "styles": ["src/styles.css", "src/assets/css/button-system.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "front-labo:build:production"}, "development": {"buildTarget": "front-labo:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css", "src/assets/css/button-system.css", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": []}}}}}}