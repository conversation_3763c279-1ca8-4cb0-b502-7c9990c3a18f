@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.results-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 70px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  display: block;
  justify-content: center;
  width: 95%;
}

h2 {
  color: #2496d3;
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

/* Loading and error styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #2496d3;
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
}

/* ✅ Filter bar styles */
.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: center; /* Vertically centers all items */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 60%; /* Limits the width as in your original design */
  margin: 0 auto; /* Centers the filter bar within its parent */
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}

.filter-group label {
  font-weight: 600;
  font-size: 14px;
  color: #444;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 250px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear, .btn-refresh {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-refresh {
  background-color: #2496d3;
}

.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.btn-refresh:hover {
  background-color: #1e78b5;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* ✅ Table */
.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ En-têtes */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
  text-align: center;
}

/* ✅ Lignes */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
  vertical-align: middle;
}

tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

tbody tr:hover {
  background-color: #f1f1f1;
}

/* ✅ Statuts */
.status-badge {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
  display: inline-block;
  min-width: 100px;
  text-align: center;
}

.validated {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-sent {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

/* Actions column */
.actions-col {
  text-align: center;
  min-width: 120px;
}

/* Action buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  background-color: #2496d3;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all 0.3s ease;
  width: 100%;
}

.btn-details {
  background-color: #2496d3;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 6px 10px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all 0.3s ease;
  width: auto;
  font-size: 16px;
}

.action-button:hover {
  background-color: #1a7bb9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(36, 150, 211, 0.3);
}

.btn-details:hover {
  background-color: #1a7bb9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(36, 150, 211, 0.3);
}

.action-button fa-icon {
  margin-right: 8px;
}

.btn-details fa-icon {
  margin-right: 5px;
}

.send {
  background-color: #4caf50;
  color: white;
}

.send:hover {
  background-color: #3d8b40;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.send:disabled {
  background-color: #c8e6c9;
  cursor: not-allowed;
}

/* File buttons */
.file-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 6px 10px;
}

.file-label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  font-weight: 500;
}

.file-label fa-icon {
  color: #f44336;
}

.file-label fa-icon.fa-file-invoice {
  color: #2196f3;
}

.file-actions {
  display: flex;
  gap: 5px;
}

.file-action-button {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-action-button.view {
  background-color: #2196f3;
  color: white;
}

.file-action-button.view:hover {
  background-color: #1976d2;
}

.file-action-button.download {
  background-color: #4caf50;
  color: white;
}

.file-action-button.download:hover {
  background-color: #388e3c;
}

.file-action-button.delete {
  background-color: #f44336;
  color: white;
}

.file-action-button.delete:hover {
  background-color: #d32f2f;
}

.no-files {
  font-style: italic;
  color: #757575;
  font-size: 13px;
  text-align: center;
  padding: 5px;
}

/* No results message */
.no-results {
  text-align: center;
  padding: 30px;
  color: #757575;
  font-style: italic;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.notification {
  background: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
  text-align: center;
  width:100%;
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
  .results-container {
    padding: 30px 15px;
  }

  .filter-bar {
    max-width: 100%;
    flex-direction: column;
  }

  table {
    font-size: 14px;
  }

  th, td {
    padding: 8px;
  }

  button {
    padding: 8px 12px;
    font-size: 14px;
  }
}