<div class="admin-panel" [class.content-hidden]="showLoadingModal">
  <h2 class="demandes-title">Les Rapports</h2>

  <!-- Notification d'erreur -->
  <div *ngIf="error" class="error-notification">
    {{ error }}
  </div>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par ID ou demande:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <div class="filter-group">
      <label for="status">Statut d'envoi:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous</option>
        <option value="sent">Envoyés</option>
        <option value="not_sent">Non envoyés</option>
      </select>
    </div>
    <button (click)="clearFilters()" class="btn-clear">
      <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>
      Effacer les filtres
    </button>
  </div>

  <!-- Tableau des rapports -->
  <table class="reports-table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Statut de validation</th>
        <th>Demande Numéro</th>
        <th>Date de Création</th>
        <th>Statut d'envoi</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="isLoading" class="loading-row">
        <td colspan="7" class="text-center">
          <div class="spinner-container">
            <div class="spinner"></div>
            <span>Chargement...</span>
          </div>
        </td>
      </tr>

      <!-- Message quand aucun rapport n'est trouvé -->
      <tr *ngIf="!isLoading && (!filteredReports || filteredReports.length === 0)" class="empty-row">
        <td colspan="7" class="text-center">
          Aucun rapport disponible.
        </td>
      </tr>

      <!-- Affichage des rapports -->
      <ng-container *ngIf="!isLoading && filteredReports && filteredReports.length > 0">
        <tr *ngFor="let report of filteredReports | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }">
          <td>{{ report.id }}</td>
          <!-- Validation status moved to its own column -->
          <td>
            <span *ngIf="report.validation === 2" class="status-pending">En attente de validation</span>
            <span *ngIf="report.validation === 1" class="status-approved">Rapport approuvé</span>
            <span *ngIf="report.validation === 0" class="status-rejected">Rapport rejeté</span>
          </td>
          <td>{{ report.demande_id }}</td>
          <td>{{ report.creation_date | date:'dd/MM/yyyy' }}</td>
          <td class="status-col">
            <!-- Si non envoyé, on affiche un bouton ; sinon on affiche 'Envoyé' -->
            <button class="btn-action btn-send" *ngIf="report.status_director === 'Non envoyé'" (click)="envoyerRapport(report.id)">
              <fa-icon [icon]="faPaperPlane" style="margin-right: 5px;"></fa-icon>
              Envoyer
            </button>
            <span class="status sent" *ngIf="report.status_director === 'Envoyé au directeur'">Envoyé</span>
          </td>

          <td class="action-buttons">
            <!-- Bouton "Voir Détails" -->
            <button class="details-btn" (click)="viewReportDetails(report.id)">
              <fa-icon [icon]="faEye" style="margin-right: 10px;"></fa-icon>
              Voir détails
            </button>

            <!-- Bouton "Créer Facture" uniquement si approuvé -->

          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>
</div>