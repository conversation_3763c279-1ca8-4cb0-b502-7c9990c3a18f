@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ==============================
   1. Conteneur principal
   ============================== */
.devis-container {
  width: 80%;
  margin: 50px auto;
  padding: 30px;
  background: white;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  animation: fadeInUp 1s ease-in-out forwards;
  transition: transform 0.3s ease-in-out;
  margin-bottom: 10px;
}

/* ==============================
   2. Titre stylisé
   ============================== */
.devis-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 28px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 30px;
  position: relative;
  display: inline-block;
  color: #2496d3;
}

/* Soulignement avec effet glow */
.devis-container h2::after {
  content: "";
  position: absolute;
  width: 120px;
  height: 4px;
  background: #2496d3;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
  animation: underlineGlow 1.5s infinite alternate;
}

/* ==============================
   3. Loading Modal
   ============================== */
.loading-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease-in-out;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

/* ==============================
   3. Barre de filtrage
   ============================== */
.filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
  align-items: flex-end;
  justify-content: center;
  width: 75%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

#search {
  width: 200px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #2496d3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.25);
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #777;
}

.filter-input {
  padding: 8px 10px 8px 35px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

.btn-clear {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  font-weight: bold;
}

.btn-clear:hover {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* ==============================
   4. Tableau stylisé
   ============================== */
.styled-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.1);
}

.styled-table th {
  background: #2496d3;
  color: white;
  padding: 14px;
  text-transform: uppercase;
  font-weight: bold;
  border: none;
  text-align: center;
}

.styled-table td {
  border: 1px solid #ddd;
  padding: 14px;
  text-align: center;
  font-size: 16px;
  vertical-align: middle;
}

/* Zebra striping */
.styled-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* ==============================
   5. Styles de lignes
   ============================== */
.table-row {
  transition: background-color 0.3s;
}
.table-row:hover {
  background-color: #f0f0f0;
}

/* ==============================
   6. Status Tags
   ============================== */
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  gap: 6px;
}

.pending {
  color: #ffc107;
}

.ongoing {
  color: #007bff;
}

.valid {
  color: #28a745;
}

.rejected {
  color: #dc3545;
}

.derogation {
  color: #fd7e14;
}

/* ==============================
   7. Action Cell & Buttons
   ============================== */
.action-cell {
  text-align: center;
  padding: 10px;
  width: 20%;
}

.btn-details {
  background-color: #2496d3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-details:hover {
  background-color: #1a7bb9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* ==============================
   8. Messages : Chargement & Erreur
   ============================== */
.loading-message {
  font-size: 18px;
  color: #007bff;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-message::before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #007bff;
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

.error-message {
  font-size: 18px;
  color: #dc3545;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message::before {
  content: "⚠️";
  margin-right: 10px;
}

.empty-row td {
  padding: 30px;
  text-align: center;
  color: #777;
  font-style: italic;
}

/* ==============================
   9. Pagination
   ============================== */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:host ::ng-deep .pagination-custom .ngx-pagination {
  padding: 0;
  margin: 0;
  display: flex;
  gap: 5px;
}

:host ::ng-deep .pagination-custom .ngx-pagination li {
  border-radius: 4px;
  overflow: hidden;
}

:host ::ng-deep .pagination-custom .ngx-pagination a,
:host ::ng-deep .pagination-custom .ngx-pagination button {
  padding: 8px 14px;
  color: #333;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

:host ::ng-deep .pagination-custom .ngx-pagination a:hover,
:host ::ng-deep .pagination-custom .ngx-pagination button:hover {
  background: #e9ecef;
  color: #2496d3;
}

:host ::ng-deep .pagination-custom .ngx-pagination .current {
  background: #2496d3;
  color: white;
  padding: 8px 14px;
  border: 1px solid #2496d3;
}

/* ==============================
   10. Responsive
   ============================== */
@media (max-width: 768px) {
  .styled-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .filter-bar {
    max-width: 95%;
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .filter-group input,
  .filter-group select {
    width: 100%;
  }

  .btn-group {
    margin-top: 0.5rem;
  }

  .btn-clear {
    width: 100%;
    justify-content: center;
  }
}

/* ==============================
   11. Animations
   ============================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes underlineGlow {
  from {
    box-shadow: 0 0 5px rgba(36, 150, 211, 0.5);
  }
  to {
    box-shadow: 0 0 15px rgba(36, 150, 211, 0.8);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}