/* =========================================================
   1. IMPORTATION DES POLICES
   ========================================================= */
   @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

   /* =========================================================
      2. POLICE GLOBALE & TYPOGRAPHIE
      ========================================================= */
   /* Utilisation de la police Montserrat pour les textes généraux
      et Orbitron pour les titres.
      --------------------------------------------------------- */
   body {
     font-family: 'Montserrat', sans-serif;
     margin: 0;
     padding: 0;
     background-color: #f5f7fa; /* Couleur de fond claire pour un meilleur contraste */
   }

   /* =========================================================
      3. CONTENEUR PRINCIPAL
      (Fusion .payment-container & .demandes-container)
      ========================================================= */
   .payment-container {
     width: 80%;
     max-width: 1000px;
     margin: 50px auto;
     padding: 120px;
     background: #fff;
     box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1);
     border-radius: 12px;
     font-family: 'Montserrat', sans-serif;
     animation: fadeIn 0.5s ease-in-out;
   }

   @keyframes fadeIn {
     from {
       opacity: 0;
       transform: translateY(20px);
     }
     to {
       opacity: 1;
       transform: translateY(0);
     }
   }

   .demandes-container {
     /* Ancien style pour .demandes-container ajusté
        pour éviter la superposition de styles */
     margin: auto;
     text-align: center;
     font-family: 'Montserrat', sans-serif;
     background: none;
     padding: 0;
     border-radius: 10px;
     box-shadow: none;
     transition: all 0.4s ease-in-out;
   }

   /* =========================================================
      4. TITRES : h2 (principal) et h3 (secondaire)
      ========================================================= */
   /* Titre principal du composant paiement */
   .payment-title {
     /* Correction du padding : 75px */
     padding: 75px;
padding-bottom:20px;
     font-family: 'Orbitron', sans-serif;
     font-size: 22px;
     font-weight: bold;
     text-transform: uppercase;
     letter-spacing: 2px;
     margin-bottom: 0; /* Alignement avec d'autres éléments */
     border-bottom: 4px solid #2496d3;
     display: inline-block;
     padding-bottom: 8px; /* Conserve la bordure inférieure stylisée */
     animation: glowText 1.5s infinite alternate;
     background: linear-gradient(90deg, black, grey);
     -webkit-background-clip: text;
     background-clip: text;
     -webkit-text-fill-color: transparent;
   }

   @keyframes glowText {
     from {
       text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
     }
     to {
       text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
     }
   }

   /* Sous-titres (h3) */
   h3 {
     font-family: 'Orbitron', sans-serif;
     font-size: 20px;
     font-weight: bold;
     text-transform: uppercase;
     letter-spacing: 1.5px;
     margin-bottom: 15px;
     border-bottom: 3px solid #2496d3;
     display: inline-block;
     padding-bottom: 6px;
     background: linear-gradient(90deg, black, grey);
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
   }

   /* =========================================================
      5. SECTION TÉLÉVERSEMENT DE FICHIER
      (conteneur, label, etc.)
      ========================================================= */
   .file-upload-container {
     position: relative;
     display: flex;
     flex-direction: column;

     gap: 10px;
     margin-bottom: 20px;
max-width:50%;
     /* Apparence (fond, bordure, ombre) */
     background-color: #f9fcff; /* Bleu très clair */
     border: 1px dashed #2496d3;
     border-radius: 8px;
     padding: 15px;
     box-shadow: 0 2px 8px rgba(36, 150, 211, 0.1);
     transition: transform 0.3s ease-in-out;
   }
   /* Conteneur du bouton "Soumettre le justificatif" */
.form-actions.submit-actions {
  /* Supprime le fond ou tout style conflictuel éventuel */
  background: transparent !important;
  /* Centre le contenu horizontalement */
  display: flex;
  justify-content: center;
  /* Ajustez la marge selon vos besoins */
  margin-top: 20px;
  /* Optionnel : hauteur automatique */
  height: auto;
  /* Supprime toute bordure si présente */
  border: none;
  padding-bottom:20px;
}

/* Style spécifique du bouton à l’intérieur */
.form-actions.submit-actions .btn-submit {
  background-color: #28a745;  /* Vert */
  color: #fff;
  /* Légère remontée (facultatif) */
  margin-top: -20px;
  border-radius: 30px; /* Coins arrondis */
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s;
}

/* Survol (hover) du bouton, hors état désactivé */
.form-actions.submit-actions .btn-submit:hover:not(:disabled) {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* État désactivé */
.form-actions.submit-actions .btn-submit:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7; /* Indique qu'il est inactif */
}
.form-group .form-group-proof{
display: flex;
align-items: center;
  justify-content: center;
}

   .file-upload-container:hover {
     transform: scale(1.01);
     box-shadow: 0 4px 16px rgba(36, 150, 211, 0.2);

   }

   /* Input file masqué, gestion via le label */
   .file-input-field {
     position: absolute;
     left: -9999px;
   }

   /* Label pour sélectionner le fichier */
   .file-upload-label {
     display: inline-flex;
     align-items: center;
     justify-content: center;
     gap: 8px;
     background-color: #2496d3;
     color: #fff;
     border: none;
     border-radius: 4px;
     padding: 12px 20px;
     font-family: 'Montserrat', sans-serif;
     font-size: 14px;
     font-weight: 600;
     cursor: pointer;
     transition: background-color 0.3s, box-shadow 0.3s;
   }

   .file-upload-label:hover {
     background-color: #1e7bbd;
     box-shadow: 0 3px 8px rgba(36, 150, 211, 0.3);
   }

   /* Nom du fichier sélectionné */
   .file-name {
     font-size: 14px;
     color: #333;
     margin-top: 5px;
     font-style: italic;
   }
.amount-text{
  font-size: 24px;
  font-weight: bold;
}
   /* =========================================================
      6. APERÇU DU FICHIER (PRÉVIEW)
      ========================================================= */
   .file-preview {
     background-color: #fff;      /* Fond blanc */
     border: 1px solid #ddd;     /* Légère bordure grise */
     border-radius: 8px;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

     padding-bottom: 20px;
     margin: 20px 0;
     text-align: center;         /* Centre le titre et l'image */
   }

   .file-preview h4 {
     font-size: 18px;
     font-weight: 600;
     margin-bottom: 15px;
     color: #333;
   }

   /* Aperçu image */
   .image-preview {
     max-width: 100%;
     height: auto;
     margin: 0 auto;
     display: block;
     border-radius: 6px;
     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
     object-fit: contain;       /* Évite toute déformation */
     background-color: #f9f9f9; /* Fond clair si l'image est transparente */
     padding: 10px;
   }

   /* Aperçu PDF */
   .pdf-preview-container {
     display: flex;
     flex-direction: column;
     align-items: center;
     gap: 6px;
     background-color: #f7f7f7;
     border-radius: 4px;
     padding: 15px;
   }

   .pdf-file-label {
     font-weight: 600;
     color: #2496d3;
   }

   .pdf-file-name {
     font-size: 14px;
     color: #333;
   }

   /* =========================================================
      7. SPINNER, MESSAGES DE CHARGEMENT, ERREUR, SUCCESS
      ========================================================= */
   /* Overlay de chargement */
   .spinner-overlay {
     display: flex;
     flex-direction: column;
     align-items: center;
     justify-content: center;
     padding: 40px;
     position: fixed;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0;
     background-color: rgba(255, 255, 255, 0.8);
     z-index: 1500; /* Higher than content but lower than success modal */
   }

   .spinner-icon {
     width: 40px;
     height: 40px;
     border: 4px solid rgba(36, 150, 211, 0.2);
     border-top: 4px solid #2496d3;
     border-radius: 50%;
     animation: spin 1s linear infinite;
     margin-bottom: 15px;
   }

   .spinner-text {
     font-size: 16px;
     color: #555;
   }

   @keyframes spin {
     0% { transform: rotate(0deg); }
     100% { transform: rotate(360deg); }
   }

   /* Messages d'erreur */
   .error-message {
     padding: 15px;
     border-radius: 5px;
     margin-bottom: 20px;
     text-align: center;
     background-color: #f8d7da;
     color: #721c24;
     border: 1px solid #f5c6cb;
   }

   /* Success Modal Styles */
   .success-modal-overlay {
     position: fixed;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0;
     background-color: rgba(0, 0, 0, 0.5);
     display: flex;
     align-items: center;
     justify-content: center;
     z-index: 2000; /* Higher z-index to ensure it appears above the loading spinner */
     animation: fadeIn 0.3s ease-out;
   }

   .success-modal {
     background-color: white;
     border-radius: 10px;
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
     width: 90%;
     max-width: 400px;
     padding: 0;
     overflow: hidden;
     animation: slideIn 0.4s ease-out;
   }

   .success-modal-content {
     padding: 30px;
     display: flex;
     flex-direction: column;
     align-items: center;
     text-align: center;
   }

   .success-icon {
     color: #28a745;
     margin-bottom: 20px;
   }

   .success-title {
     font-family: 'Orbitron', sans-serif;
     font-size: 24px;
     margin-bottom: 15px;
     color: #28a745;
   }

   .success-message {
     font-size: 16px;
     margin-bottom: 25px;
     color: #333;
   }

   .success-close-btn {
     background-color: #28a745;
     color: white;
     border: none;
     border-radius: 30px;
     padding: 10px 25px;
     font-size: 16px;
     font-weight: bold;
     cursor: pointer;
     transition: background-color 0.3s, transform 0.3s;
   }

   .success-close-btn:hover {
     background-color: #218838;
     transform: translateY(-2px);
   }

   @keyframes fadeIn {
     from { opacity: 0; }
     to { opacity: 1; }
   }

   @keyframes slideIn {
     from { transform: translateY(-50px); opacity: 0; }
     to { transform: translateY(0); opacity: 1; }
   }

   /* =========================================================
      8. BOUTONS (BACK, REFRESH, SUBMIT)
      ========================================================= */
   /* Bouton de retour */
   .btn-back {
     background-color: #2496d3;
     border: none;
     color: white;
     font-size: 16px;
     font-weight: bold;
     cursor: pointer;
     padding:10px;
     display: flex;
     align-items: center;
     gap: 8px;

     margin: 15px;
     border-radius: 5px;
     transition: color 0.3s;
   }

   .btn-back:hover {
     color: #1a7bad;
     text-decoration: underline;
   }

   /* Bouton d'actualisation (Refresh) */
   .refresh-container {
     display: flex;
     justify-content: center;
     margin: 15px 0;
   }

   .btn-refresh {
     background-color: #f8f9fa;
     border: 1px solid #ddd;
     border-radius: 4px;
     padding: 8px 16px;
     font-size: 14px;
     color: #555;
     cursor: pointer;
     display: flex;
     align-items: center;
     gap: 8px;
     transition: all 0.2s;
   }

   .btn-refresh:hover:not(:disabled) {
     background-color: #e9ecef;
     color: #333;
   }

   .btn-refresh:disabled {
     opacity: 0.7;
     cursor: not-allowed;
   }



   /* =========================================================
      9. INFORMATIONS DEMANDE
      ========================================================= */
   .demande-info {
     background-color: #f8f9fa;
     padding: 20px;
     border-radius: 8px;
     margin-bottom: 30px;
     text-align: left;
   }

   .info-grid {
     display: grid;
     grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
     gap: 15px;
   }

   .info-item {
     display: flex;
     flex-direction: column;
     align-items: flex-start;
   }

   .label {
     font-weight: 600;
     color: #555;
     margin-bottom: 5px;
   }

   .value {
     font-size: 16px;
     font-weight: 400;
   }

   /* =========================================================
      10. INSTRUCTIONS DE PAIEMENT & FORMULAIRE
      ========================================================= */
   .instructions-and-form {
     text-align: left;
     margin-top: 20px;
   }

   .payment-instructions {
     margin-bottom: 30px;
   }

   .instruction-box {
     background-color: #e9f7fe;
     border-left: 4px solid #2496d3;
     padding: 15px;
     border-radius: 4px;
   }

   .important {
     font-weight: bold;
     font-size: 18px;
     color: #2496d3;
     margin-bottom: 10px;
   }

   .payment-form {
     margin-top: 30px;
   }

   .form-group {
     margin-bottom: 20px;
     padding-bottom:20px;
   }

   label {
     display: block;
     margin-bottom: 8px;
     font-weight: 600;
     color: #333;
   }

   input[type="number"],
   textarea {
     width: 100%;
     padding: 12px;
     border: 1px solid #ddd;
     border-radius: 4px;
     font-size: 16px;
     transition: border-color 0.3s;
   }

   input[type="number"]:focus,
   textarea:focus {
     border-color: #2496d3;
     outline: none;
     box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.2);
   }

   /* Champ en lecture seule (ex: calcul auto) */
   .readonly-input {
     background-color: #f8f9fa;
     color: #333;
     font-weight: 600;
     font-size: 18px;
     cursor: not-allowed;
   }

   .amount-display {
     position: relative;
   }

   .amount-info {
     font-size: 12px;
     color: #6c757d;
     margin-top: 5px;
     font-style: italic;
   }

   /* =========================================================
      11. CHAMP NOTES
      ========================================================= */
   .form-group-notes {
     margin-bottom: 20px;
   }

   .notes-label {
     font-weight: 600;
     color: #333;
     margin-bottom: 6px;
     display: inline-block;
   }

   .notes-textarea {
     width: 100%;
     border: 1px solid #ddd;
     border-radius: 4px;
     padding: 12px;
     font-size: 14px;
     font-family: 'Montserrat', sans-serif;
     transition: border-color 0.3s, box-shadow 0.3s;
     resize: vertical;
   }

   .notes-textarea:focus {
     outline: none;
     border-color: #2496d3;
     box-shadow: 0 0 0 2px rgba(36, 150, 211, 0.2);
   }

   /* =========================================================
      12. SECTION HISTORIQUE DE PAIEMENT
      ========================================================= */
   .payment-history-section {
     margin-top: 40px;
     border-top: 2px solid #2496d3;
     background-color: #f9f9f9;
     border-radius: 8px;
     padding: 20px;
     box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
     text-align: left;
   }

   .payment-history-section h3 {
     color: #2496d3;
     margin-bottom: 20px;
     font-size: 20px;
     font-weight: 600;
   }
   /* Force le style global pour le bouton de soumission */
.btn-submit.submit-btn-proof {
  background-color: #28a745;  /* Vert */
  color: #fff;
  /* Remonte légèrement le bouton */
  margin-top: -20px;
  /* Coins arrondis (look “pill”) */
  border-radius: 30px;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-family: 'Montserrat', sans-serif;
  cursor: pointer;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  /* Animation au survol */
  transition: transform 0.3s ease-in-out, box-shadow 0.3s;
}

.btn-submit.submit-btn-proof:hover:not(:disabled) {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* État désactivé (disabled) */
.btn-submit.submit-btn-proof:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.8; /* Optionnel, pour l’effet “inactif” */
}


   /* =========================================================
      13. RESPONSIVE
      ========================================================= */
   @media (max-width: 768px) {
     .payment-container {
       width: 95%;
       padding: 20px;
     }

     .info-grid {
       grid-template-columns: 1fr;
     }
   }
