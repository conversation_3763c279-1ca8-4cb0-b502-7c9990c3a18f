<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'demande_id',
        'is_read',
        'devis_id',
        'fiche_id',
        'rapport_id',
        'demande'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
