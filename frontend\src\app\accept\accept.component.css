/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Orbitron:wght@500;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

/* ✅ SECTION PRINCIPALE */
.accept-section {
    padding: 40px 2%;
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 80vh;
}

/* ✅ Conteneur principal */
.accept-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 90%;
    max-width: 1400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border-radius: 15px;
    background-color: white;
    padding: 30px;
}

/* ✅ En-tête amé<PERSON> */
.accept-header {
    width: 100%;
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #2496d3, #1a749f);
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ✅ Animation du logo */
.logo-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    margin-right: 15px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

/* ✅ Effet diamant */
.diamond {
    color: #ffffff;
    font-size: 20px;
    animation: rotateDiamond 10s infinite linear;
}

@keyframes rotateDiamond {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ✅ Texte introductif */
.accept-intro {
    font-size: 18px;
    color: white;
    text-align: center;
    opacity: 1;
    animation: fadeIn 1.5s ease-in forwards;
    width: 100%;
    margin: 0;
    font-weight: 500;
    line-height: 1.6;
}

/* ✅ Sous-titre avec icône */
.subtitle-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.subtitle-icon {
    color: #ff9800;
    font-size: 24px;
    margin-right: 10px;
}

.accept-subtitle {
    color: #ff9800;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ✅ Boutons avant le sous-titre */
.top-buttons {
    display: flex;
    gap: 20px;
    padding: 25px 0;
    margin-top: 15px;
    justify-content: center;
}

/* ✅ Style du bouton principal */
.accept-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2496d3;
    color: white;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease-in-out;
    box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.4);
    position: relative;
    overflow: hidden;
}

.accept-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
    background: #1a87c4;
}

.accept-btn.primary {
    background: linear-gradient(135deg, #2496d3, #1a749f);
}

.btn-icon {
    margin-right: 10px;
    font-size: 18px;
}

/* ✅ Style du bouton secondaire */
.accept-btn.secondary {
    background: #fff;
    color: #2496d3;
    border: 2px solid #2496d3;
    box-shadow: none;
}

.accept-btn.secondary:hover {
    background: #f0f8ff;
    color: #2496d3;
    border-color: #2496d3;
}

/* ✅ Section du titre principal */
.title-section {
    margin: 30px 0;
    position: relative;
    display: inline-block;
}

/* ✅ Titre principal avec animation */
.accept-title {
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 3px;
    color: #2496d3;
    margin: 0;
    padding-bottom: 10px;
    background: linear-gradient(to right, #2496d3, #1a749f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: glowText 2s ease-in-out infinite alternate;
}

.title-underline {
    height: 3px;
    width: 100%;
    background: linear-gradient(to right, #2496d3, #1a749f);
    margin-top: 5px;
    border-radius: 3px;
    animation: expandLine 1.5s ease-in-out forwards;
}

@keyframes expandLine {
    from { width: 0; }
    to { width: 100%; }
}

/* ✅ Animation de glow sur le titre */
@keyframes glowText {
    from { text-shadow: 0px 0px 5px rgba(36, 150, 211, 0.3); }
    to { text-shadow: 0px 0px 15px rgba(36, 150, 211, 0.7); }
}

/* ✅ Carte d'information */
.info-card {
    background-color: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
    width: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* ✅ Description */
.accept-description {
    font-size: 18px;
    line-height: 1.8;
    text-align: justify;
    margin: 0 0 30px 0;
    color: #555;
}

/* ✅ Bouton tableau */
.accept-table-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2496d3;
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease-in-out;
    margin: 0 auto;
    box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.4);
}

.accept-table-btn:hover,
.accept-table-btn:focus {
    background: #1a87c4;
    transform: translateY(-3px);
    box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
    outline: none;
}

/* ✅ Effets d'animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✅ Responsive design */
@media (max-width: 1024px) {
    .accept-section {
        padding: 30px 5%;
    }

    .accept-container {
        width: 95%;
        padding: 20px;
    }

    .accept-title {
        font-size: 22px;
    }

    .accept-description {
        font-size: 16px;
    }

    .accept-btn {
        font-size: 15px;
        padding: 12px 25px;
    }
}

@media (max-width: 768px) {
    .accept-container {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
    }

    .logo-animation {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .title-section {
        margin: 20px 0;
    }

    .accept-title {
        font-size: 20px;
    }

    .accept-subtitle {
        font-size: 18px;
    }

    .accept-description {
        font-size: 15px;
    }

    .top-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .accept-btn {
        font-size: 14px;
        padding: 12px 20px;
    }

    .info-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .accept-title {
        font-size: 18px;
        letter-spacing: 2px;
    }

    .accept-subtitle {
        font-size: 16px;
    }

    .accept-description {
        font-size: 14px;
    }

    .accept-btn, .accept-table-btn {
        font-size: 13px;
        padding: 10px 20px;
    }
}
