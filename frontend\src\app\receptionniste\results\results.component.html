<div class="results-container">
  <h2>Résultats d’analyse</h2>

  <!-- Loading indicator -->
  <div *ngIf="loading" class="loading-container">
    <fa-icon [icon]="faSpinner" class="fa-spin fa-2x"></fa-icon>
    <p>Chargement les résultats d’analyse...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="error" class="error-message">
    <p>{{ error }}</p>
  </div>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="applyFilters()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="date">Date de réception:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="applyFilters()" />
    </div>
    <button (click)="clearFilters()" class="btn-clear">
      <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>
      Effacer les filtres
    </button>
  </div>

  <!-- Results table -->
  <div class="table-container" *ngIf="!loading && filteredReports.length > 0">
    <table>
      <thead>
        <tr>
         
          <th>numéro de demande</th>
          <th>Date de réception</th>

          <th>Statut d'envoi</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let report of filteredReports">
          
          <td>{{ report.demande_id }}</td>
          <td>{{ report.creation_date }}</td>

          <td>
            <span class="status-badge" [ngClass]="getClientStatusClass(report.clientStatus)">
              {{ getClientStatusLabel(report.clientStatus) }}
            </span>
          </td>

          <td class="actions-col">
            <button class="btn-details" (click)="sendResults(report)">
              <fa-icon [icon]="faPaperPlane" style="margin-right: 5px;"></fa-icon>
              Soumettre des fiches
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- No results message -->
  <div *ngIf="!loading && filteredReports.length === 0" class="no-results">
    <p>Aucun rapport validé trouvé.</p>
  </div>
</div>
