@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css'); /* Font Awesome */

/* General Styles */
.client-dashboard {
  text-align: center;
  padding: 3rem 5%;
  font-family: 'Montserrat', sans-serif;
  background: white;
  color: black;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Title Container */
.dashboard-title-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

/* Circle Icon */
.circle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 2px solid #2496d3;
  border-radius: 50%;
  font-size: 2rem;
  color: #2496d3;
  box-shadow: 0 0 10px rgba(36, 150, 211, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.circle-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(36, 150, 211, 0.4);
}

/* Dashboard Title */
.dashboard-title {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(1.8rem, calc(1vw + 1.2rem), 2.5rem);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.15rem;
  margin: 0;
  border-bottom: 2px solid #2496d3;
  padding-bottom: 0.4rem;
  color: black; /* Fallback */
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Welcome Message */
.welcome-message p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #555;
}
.welcome-message strong {
  font-weight: bold;
  color: #2496d3;
}

/* Statistics Section */
.statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.statistic-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeInUp 0.5s ease-in-out;
}
.statistic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.statistic-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #2496d3;
}

.statistic-title {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.statistic-value {
  font-size: 2.8rem;
  font-weight: bold;
  color: #007bff;
}

/* Loading and Error States for Statistics */
.statistic-value.loading {
  color: #6c757d;
  font-size: 2rem;
}

.statistic-value.loading i {
  animation: spin 1s linear infinite;
}

.statistic-value.error {
  color: #dc3545;
  font-size: 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Action Buttons */
.client-actions {
  display: flex;
  justify-content: center;
  gap: 1.25rem;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.btn-client {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-size: clamp(1rem, calc(0.5rem + 0.8vw), 1.2rem);
  border-radius: 1.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  font-weight: 600;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
  text-transform: capitalize;
  gap: 0.625rem;
  min-width: 12.5rem;
  color: white;
}
.btn-client:hover {
  transform: translateY(-0.25rem);
}
.btn-client:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
}
/* Button-Specific Colors */
.analyses {
  background: linear-gradient(to right, #007bff, #0056b3);
}
.analyses:hover {
  background: linear-gradient(to right, #6eb5ff, #4d9de3);
}

.notifications {
  background: linear-gradient(to right, #f12f0c, #f81100);
}
.notifications:hover {
  background: linear-gradient(to right, #e94427, #e91b0c);
}

.rapports {
  background: linear-gradient(to right, #17a2b8, #117a8b);
}
.rapports:hover {
  background: linear-gradient(to right, #5bc0de, #2a9fd6);
}

/* Responsive Design */
@media (max-width: 57.8125rem) {
  .statistics {
    grid-template-columns: 1fr;
  }
  .statistic-card {
    width: 90%;
    margin: 0 auto 1.5rem;
  }
  .client-actions {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }
  .btn-client {
    width: 85%;
    padding: 0.8rem 1rem;
  }
  .client-dashboard {
    padding: 2rem 3%;
  }
}

@media (max-width: 37.5rem) {
  .btn-client {
    width: 100%;
    font-size: 0.9rem;
    padding: 0.7rem 0.8rem;
  }
  .dashboard-title {
    font-size: 1.3rem;
  }
  .client-dashboard {
    padding: 1.5rem 2%;
  }
}

/* Animations */
.client-dashboard > * {
  opacity: 0;
  animation: fadeIn 0.5s ease-in-out forwards;
}
.client-dashboard > *:nth-child(1) { animation-delay: 0.1s; }
.client-dashboard > *:nth-child(2) { animation-delay: 0.2s; }
.client-dashboard > *:nth-child(3) { animation-delay: 0.3s; }
.client-dashboard > *:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}