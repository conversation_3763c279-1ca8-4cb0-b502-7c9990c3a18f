import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import * as XLSX from 'xlsx';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-excel-viewer',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './excel-viewer.component.html',
  styleUrls: ['./excel-viewer.component.css']
})
export class ExcelViewerComponent implements OnInit {
  @Input() excelData: Blob | null = null;
  @Input() fileName: string = 'Excel File';
  @Output() close = new EventEmitter<void>();

  faTimes = faTimes;
  isLoading: boolean = true;
  error: string | null = null;
  headers: string[] = [];
  data: any[][] = [];
  sheets: string[] = [];
  activeSheet: string = '';

  constructor() {}

  ngOnInit(): void {
    this.loadExcelData();
  }

  loadExcelData(): void {
    if (!this.excelData) {
      this.error = 'No Excel data provided';
      this.isLoading = false;
      return;
    }

    const reader = new FileReader();
    
    reader.onload = (e: any) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get all sheet names
        this.sheets = workbook.SheetNames;
        
        if (this.sheets.length === 0) {
          this.error = 'No sheets found in the Excel file';
          this.isLoading = false;
          return;
        }
        
        // Set the first sheet as active by default
        this.activeSheet = this.sheets[0];
        this.loadSheet(this.activeSheet);
        
        this.isLoading = false;
      } catch (error) {
        console.error('Error parsing Excel file:', error);
        this.error = 'Failed to parse Excel file. The file might be corrupted or in an unsupported format.';
        this.isLoading = false;
      }
    };
    
    reader.onerror = () => {
      this.error = 'Failed to read the Excel file';
      this.isLoading = false;
    };
    
    reader.readAsArrayBuffer(this.excelData);
  }

  loadSheet(sheetName: string): void {
    try {
      const reader = new FileReader();
      
      reader.onload = (e: any) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get the worksheet
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert the worksheet to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length === 0) {
          this.headers = [];
          this.data = [];
          return;
        }
        
        // The first row contains the headers
        this.headers = jsonData[0] as string[];
        
        // The rest of the rows contain the data
        this.data = jsonData.slice(1) as any[][];
        
        this.activeSheet = sheetName;
      };
      
      reader.readAsArrayBuffer(this.excelData as Blob);
    } catch (error) {
      console.error('Error loading sheet:', error);
      this.error = `Failed to load sheet "${sheetName}"`;
    }
  }

  closeViewer(): void {
    this.close.emit();
  }
}
