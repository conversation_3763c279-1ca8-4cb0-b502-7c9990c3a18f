import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Demande } from './demande.model';

@Injectable({
  providedIn: 'root'
})
export class DemandeService {
  private demandesApiUrl = 'http://127.0.0.1:8000/api/demandesAll'; // e.g., http://127.0.0.1:8000/api/demandes
  private userDetailsApiUrl = 'http://127.0.0.1:8000/api/userdetails'; // Base URL for user details
  private apiUrl = 'http://127.0.0.1:8000/api';
  constructor(private http: HttpClient) {}

  getAllDemandes(): Observable<Demande[]> {
    return this.http.get<{ data: Demande[] }>(this.demandesApiUrl).pipe(
      map(response => {
        if (response.data) {
          return response.data;
        } else {
          throw new Error('Invalid API response format.');
        }
      }),
      catchError(error => {
        console.error('Error fetching demandes:', error);
        return throwError(() => new Error('Failed to load demandes. Please try again.'));
      })
    );
  }

  // Updated getUserDetails method to extract the nested data
  getUserDetails(userId: number): Observable<any> {
    return this.http.get<{ success: boolean; data: any }>(`${this.userDetailsApiUrl}/${userId}`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data; // Extracts the actual user data
        } else {
          throw new Error('Invalid API response format for user details.');
        }
      }),
      catchError(error => {
        console.error('Error fetching user details:', error);
        return throwError(() => new Error('Failed to load user details.'));
      })
    );
  }
  getUsersDetails(userIds: number[]): Observable<any[]> {
    return this.http.post<any[]>(`${this.apiUrl}/users/details`, { user_ids: userIds });
  }
  getDemandeDetails(demandeId: string): Observable<Demande> {
      console.log(`Fetching demande details for ID: ${demandeId}`);

      return this.http.get<{ demande: Demande }>(`http://localhost:8000/api/demande/${demandeId}`).pipe(
        map(response => {
          if (!response || !response.demande) {
            throw new Error('Invalid API response structure');
          }
          console.log('Processed API response:', response.demande);
          return response.demande;
        }),
        catchError(error => {
          console.error('API error:', error);
          return throwError(() => new Error('Failed to fetch demande details.'));
        })
      );
    }
     getDemandeBy(demandeId: number): Observable<Demande> {
        console.log(`Fetching demande details for ID: ${demandeId}`);

        return this.http.get<{ demande: Demande }>(`http://localhost:8000/api/demandes/${demandeId}`).pipe(
          map(response => {
            if (!response || !response.demande) {
              throw new Error('Invalid API response structure');
            }
            console.log('Processed API response:', response.demande);
            return response.demande;
          }),
          catchError(error => {
            console.error('API error:', error);
            return throwError(() => new Error('Failed to fetch demande details.'));
          })
        );
      }

      // Get demande by ID (alias for getDemandeBy for consistency)
      getDemandeById(demandeId: number): Observable<Demande> {
        return this.getDemandeBy(demandeId);
      }

      // Get all valid demandes
      getValidDemandes(): Observable<Demande[]> {
        return this.http.get<{ data: Demande[] }>(`${this.apiUrl}/demandess/valid`).pipe(
          map(response => {
            if (response.data) {
              return response.data;
            } else {
              throw new Error('Invalid API response format.');
            }
          }),
          catchError(error => {
            console.error('Error fetching valid demandes:', error);
            return throwError(() => new Error('Failed to load valid demandes. Please try again.'));
          })
        );
      }
}
