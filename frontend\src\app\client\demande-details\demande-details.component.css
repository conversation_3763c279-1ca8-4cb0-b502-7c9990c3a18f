@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Main Container */
.demandes-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}

/* Results Date Message */
.results-date-message {
  background-color: #f0f8ff; /* Light blue background */
  border-left: 4px solid #2496d3; /* Blue left border */
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
  text-align: left;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.results-date-message i {
  font-size: 24px;
  color: #2496d3;
  margin-right: 15px;
}

.results-date-message span {
  color: #333;
  font-size: 15px;
  line-height: 1.5;
}

.results-date-message strong {
  color: #2496d3;
  font-weight: bold;
}
/* Devis Section Styling */
.devis-section {
  margin-top: 30px;
}

.devis-container {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.devis-container h3 {
  margin-bottom: 10px;
  color: #333;
}

.devis-container p {
  margin: 5px 0;
}

/* Payment Notice Styling */
.payment-notice {
  background-color: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 15px;
  gap: 10px;
  margin: 15px 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.payment-notice span {
  color: #333;
  font-size: 15px;
  line-height: 1.5;
  font-weight: normal;
  padding: 0;
  border-radius: 0;
  text-transform: none;
}

.payment-notice strong {
  color: #007bff;
  font-weight: bold;
}

/* Devis Actions Styling */
.devis-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.btn-payment {
  background: #28a745;
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.3);
  display: flex;
  align-items: center;
}

.btn-payment:hover {
  background: #218838;
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(40, 167, 69, 0.6);
}

/* Devis Loading Styling */
.devis-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  margin-top: 20px;
}

.no-devis {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #ddd;
  margin-top: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}
/* Header Actions Container */
.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  position: relative;
}

/* ✅ Title Styling */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0; /* Changed from 20px to align with button */
  border-bottom: 4px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text; /* Standard property for compatibility */
  -webkit-text-fill-color: transparent;
}

/* Print Button Styling */
.btn-print {
  /* Position the print button on the right */
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);

  /* Button styling */
  background: #2496d3;
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 4px 10px rgba(36, 150, 211, 0.3);
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-print:hover {
  background: #1e7bbd;
  transform: translateY(-50%) scale(1.05); /* Keep vertical centering while scaling */
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
}

/* ✅ Loading and Error Messages */
.loading {
  font-size: 18px;
  color: #2496d3;
  font-weight: bold;
  margin-top: 20px;
}

/* =========================
   Loading Spinner Overlay
   ========================= */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Content hidden when loading */
.content-hidden {
  opacity: 0.3;
  pointer-events: none;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2496d3;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}

/* ✅ Table Styling */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ Table Header */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Table Rows */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Hover Effects */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* ✅ Status Styling */
span {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
}

/* ✅ Status Colors */
.validée {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  box-shadow: 0px 3px 8px rgba(40, 167, 69, 0.4);
}

.refusée {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  box-shadow: 0px 3px 8px rgba(220, 53, 69, 0.4);
}

.en-attente {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  box-shadow: 0px 3px 8px rgba(255, 193, 7, 0.4);
}

/* ✅ Button Styling */
button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px;
  transition: all 0.3s ease-in-out;
}

/* ✅ Accept Button */
.btn-valider {
  background: #28a745;
  color: white;
  box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.3);
}
/* ✅ Green Highlight Tag for Validated Demande */
.validated-tag {
  background-color: #28a745; /* Green */
  color: white;
  font-weight: bold;
  padding: 10px 15px;
  border-radius: 20px;
  text-align: center;
  margin-bottom: 15px;
  display: inline-block;
  box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.3);
}

.btn-valider:hover {
  background: #218838;
  transform: scale(1.1);
  box-shadow: 0px 8px 20px rgba(40, 167, 69, 0.6);
}
.notifi-section{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top:40px;
  margin-bottom:40px;
  font-size: 50px;
}
.notifi-section strong{

  font-size: 30px;
}
/* ✅ Reject Button */
.btn-refuser {
  background: #dc3545;
  color: white;
  box-shadow: 0px 4px 10px rgba(220, 53, 69, 0.3);
}
/* ✅ Pre-validation Button */
.btn-prevalider {
  background: #2496d3; /* Blue color */
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 4px 10px rgba(36, 150, 211, 0.3);
}

.btn-prevalider:hover {
  background: #1e7bbd;
  transform: scale(1.03);
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
}

.btn-refuser:hover {
  background: #b22234;
  transform: scale(1.03);
  box-shadow: 0px 8px 20px rgba(220, 53, 69, 0.6);
}

/* ✅ Glow Animation */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}
/* ✅ Notification Styling */
.notification-details {
  margin-top: 30px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
}

.notification-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border-left: 5px solid #2496d3;
  transition: transform 0.3s ease-in-out;
}

.notification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0px 6px 20px rgba(36, 150, 211, 0.2);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.notification-type {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-success {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.type-error {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.type-warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.type-info {
  background: rgba(36, 150, 211, 0.1);
  color: #2496d3;
}

.notification-status {
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 15px;
}

.read {
  background: #e9ecef;
  color: #6c757d;
}

.unread {
  background: rgba(36, 150, 211, 0.1);
  color: #2496d3;
  font-weight: bold;
}

.notification-body {
  margin: 10px 0;
}

.notification-body p:first-child {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.notification-body p:last-child {
  font-size: 14px;
  color: #666;
}

.notification-footer {
  text-align: right;
}

.notification-footer p {
  font-size: 12px;
  color: #999;
  margin: 0;
}

/* ✅ Existing Status Tag Styling (Updated for consistency) */
.status-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
}

.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.ongoing {
  background: rgba(36, 150, 211, 0.1);
  color: #2496d3;
}

.derogation {
  background: rgba(255, 107, 7, 0.1);
  color: #ff6b07;
}

.valid {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}
.rejected {
  background: rgba(235, 9, 9, 0.1);
  color: #000000;
}
.status-container {
  margin: 15px 0;
}

/* Rejection Reason Styling */
.rejection-reason {
  background-color: rgba(220, 53, 69, 0.1); /* Light red background */
  border-left: 4px solid #dc3545; /* Red left border */
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.rejection-content {
  flex: 1;
}

.rejection-content h4 {
  color: #dc3545;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.rejection-content p {
  color: #333;
  font-size: 15px;
  line-height: 1.5;
  margin: 0;
  text-align: left;
}




.status-message {
  display: block; /* Pour que le message apparaisse sous le statut */
  font-size: 20px;
  font-weight: bold;
  color: #000000;
  margin-top: 8px;
  font-family: 'Poppins', serif;
  text-transform: none; /* ✅ Ensures normal text casing */
}

/* ✅ Ensure h3 follows similar styling to h2 */
h3 {
  font-family: 'Orbitron', sans-serif;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  margin-bottom: 15px;
  border-bottom: 3px solid #2496d3;
  display: inline-block;
  padding-bottom: 6px;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  background-clip: text; /* Standard property for compatibility */
  -webkit-text-fill-color: transparent;
}