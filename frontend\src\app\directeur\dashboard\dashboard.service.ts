import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface DashboardStatistics {
  pending_derogations: number;
  pending_rapports: number;
}

export interface DashboardStatisticsResponse {
  success: boolean;
  data: DashboardStatistics;
  message?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private apiUrl = 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) {}

  /**
   * Get dashboard statistics for directeur
   * Returns counts of pending derogations and rapports
   */
  getDashboardStatistics(): Observable<DashboardStatisticsResponse> {
    return this.http.get<DashboardStatisticsResponse>(`${this.apiUrl}/director/dashboard/statistics`);
  }
}
