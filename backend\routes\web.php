<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\NotificationEmail;
use App\Mail\ValidationEmail;
use App\Mail\RejectionEmail;
use App\Models\User;
use App\Models\Demande;
use App\Models\Notification;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/test-mail', function () {
    try {
        $variables = [
            'user' => (object)[
                'name' => 'Test User'
            ],
            'notification' => (object)[
                'title' => 'Test Notification',
                'message' => 'This is a test notification sent using Gmail SMTP',
                'type' => 'test',
                'created_at' => now()
            ]
        ];

        Mail::to('<EMAIL>')->send(new NotificationEmail('Test Email from Gmail SMTP', $variables));

        return 'Test email sent successfully! Check your inbox.';
    } catch (\Exception $e) {
        return 'Error sending email: ' . $e->getMessage();
    }
});

Route::get('/test-validation-email', function () {
    try {
        // Clear the log file first
        file_put_contents(storage_path('logs/laravel.log'), '');

        // Log the start of the test
        Log::info('Starting validation email test');

        // Get a test user
        $user = User::find(1); // Assuming user ID 1 exists
        if (!$user) {
            return 'Error: Test user not found';
        }

        // Get a test demande
        $demande = Demande::first(); // Get the first demande
        if (!$demande) {
            return 'Error: Test demande not found';
        }

        // Create a test notification
        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => 'Test Validation',
            'message' => 'This is a test validation notification',
            'type' => 'demande',
            'demande_id' => null,
            'demande' => $demande->demande_id,
            'is_read' => 0
        ]);

        // Send the validation email
        Mail::to('<EMAIL>')
            ->send(new ValidationEmail(
                $notification,
                $demande,
                $user,
                null, // No devis
                date('Y-m-d', strtotime('+7 days')) // Results date in 7 days
            ));

        // Log the result
        Log::info('Validation email sent successfully', [
            'user_email' => '<EMAIL>',
            'demande_id' => $demande->demande_id
        ]);

        return 'Test validation email sent successfully!';
    } catch (\Exception $e) {
        // Log the error
        Log::error('Failed to send validation email', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return 'Error sending validation email: ' . $e->getMessage();
    }
});

Route::get('/test-rejection-email', function () {
    try {
        // Clear the log file first
        file_put_contents(storage_path('logs/laravel.log'), '');

        // Log the start of the test
        Log::info('Starting rejection email test');

        // Get a test user
        $user = User::find(1); // Assuming user ID 1 exists
        if (!$user) {
            return 'Error: Test user not found';
        }

        // Get a test demande
        $demande = Demande::first(); // Get the first demande
        if (!$demande) {
            return 'Error: Test demande not found';
        }

        // Create a test notification
        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => 'Test Rejection',
            'message' => 'This is a test rejection notification',
            'type' => 'demande',
            'demande_id' => null,
            'demande' => $demande->demande_id,
            'is_read' => 0
        ]);

        // Send the rejection email
        Mail::to('<EMAIL>')
            ->send(new RejectionEmail(
                $notification,
                $demande,
                $user,
                'This is a test rejection reason'
            ));

        // Log the result
        Log::info('Rejection email sent successfully', [
            'user_email' => '<EMAIL>',
            'demande_id' => $demande->demande_id
        ]);

        return 'Test rejection email sent successfully!';
    } catch (\Exception $e) {
        // Log the error
        Log::error('Failed to send rejection email', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return 'Error sending rejection email: ' . $e->getMessage();
    }
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});
