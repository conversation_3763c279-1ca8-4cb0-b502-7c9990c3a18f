@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* Section principale */
.accept-table-section {
    min-height: 100vh;
    padding: 50px 2%;
    background: white;
    color: black;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
}

/* Conteneur principal */
.container {
    width: 95%;
    max-width: 1400px;
    margin: auto;
}

/* Titre stylisé */
.title {
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin-bottom: 30px;
    border-bottom: 4px solid #2496d3;
    padding-bottom: 10px;
    display: inline-block;
   
}

/* Animation du titre */
@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4); }
    to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8); }
}

/* Table Responsive */
.table-responsive {
    overflow-x: auto;
}

/* Table Styling */
.table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
    table-layout: fixed;
}

.table thead th {
    background-color: #2496d3;
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 12px;
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.table tbody td {
    padding: 10px;
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(36, 150, 211, 0.05);
}

.table-bordered {
    border: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: rgba(36, 150, 211, 0.1);
}

/* Note Styling */
.note {
    margin-top: 15px;
    font-style: italic;
    color: #666;
    font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
    .title {
        font-size: 20px;
    }

    .container {
        width: 95%;
        padding: 10px;
    }

    .table thead th {
        padding: 8px;
        font-size: 14px;
    }

    .table tbody td {
        padding: 8px;
        font-size: 13px;
    }

    .note {
        font-size: 12px;
    }
}
