<div class="demandes-container">
  <h2>Soumission d'une dérogation</h2>

  <!-- Informations Client Auto-remplies -->
  <div class="client-info">
    <!-- Display client information here if needed -->
  </div>
  <!-- Notification inline (will be replaced by overlay) -->
  <div *ngIf="successMessage" class="notification">
    ✅ {{ successMessage }}
  </div>
  <form [formGroup]="derogationForm" (ngSubmit)="submitDerogation()">
    <div *ngFor="let sampleForm of sampleFormGroups; let i = index"
         class="echantillon-card"
         [formGroup]="sampleForm">
      <h3>Échantillon {{ i + 1 }}</h3>
      <div class="input-group">
        <label>Identification echantillon:</label>
      <select formControlName="identification_echantillon" (change)="selectSample(i)">
        <option value="" disabled selected>-- Sélectionnez un échantillon --</option>
        <option *ngFor="let sample of getFilteredSamples(i)" [value]="sample.identification_echantillon">
          {{ sample.identification_echantillon }}
        </option>
      </select>
      </div>

      <!-- Display sample details if a sample is selected -->
      <div *ngIf="sampleForm.get('identification_echantillon')?.value" class="sample-details">
        <p><strong>Référence:</strong> {{ sampleForm.get('reference')?.value }}</p>
        <p><strong>Nature:</strong> {{ sampleForm.get('nature_echantillon')?.value }}</p>
        <p><strong>Masse:</strong> {{ sampleForm.get('masse_echantillon')?.value }} g</p>
        <p><strong>État:</strong> {{ sampleForm.get('etat')?.value }}</p>
        <p><strong>Analyse souhaitée:</strong> {{ sampleForm.get('analyse_souhaite')?.value }}</p>
        <p><strong>Provenance:</strong> {{ sampleForm.get('provenance')?.value }}</p>
        <p><strong>Lot:</strong> {{ sampleForm.get('lot')?.value }}</p>
      </div>

      <!-- Manually filled fields -->
      <div class="input-group">
        <label>Description Écart:</label>
        <textarea formControlName="description_ecart" required></textarea>
      </div>
      <div class="input-group">
        <label>Réponse du client:</label>
        <input type="text" formControlName="reponse_du_client" required>
      </div>
      <!-- <div class="input-group">
        <label>Décision:</label>
        <input type="text" formControlName="decision" required>
      </div>
      <div class="input-group">
        <label>Date et Visa du DL:</label>
        <input type="date" formControlName="date_et_visa_du_DL">
      </div>
      <div class="input-group">
        <label>Date et Visa du Demander:</label>
        <input type="date" formControlName="date_et_visa_du_demander">
      </div> -->

      <button type="button" class="btn-delete" *ngIf="samples.length > 1" (click)="removeEnregistrement(i)">
        🗑️ Supprimer
      </button>
    </div>

    <div class="btns">
      <button type="button" class="btn-add" (click)="addEnregistrement()">➕ Ajouter un autre échantillon</button>
      <button type="submit" class="btn-submit">📩 Envoyer dérogation</button>
    </div>
  </form>
</div>

<!-- Loading Spinner Overlay -->
<div *ngIf="showLoadingModal" class="loading-overlay">
  <div class="loading-popup">
    <div class="spinner"></div>
    <h3>{{ loadingMessage }}</h3>
  </div>
</div>

<!-- Notification de soumission -->
<div *ngIf="showNotification" class="notification-overlay">
  <div class="notification-popup">
    <div class="notification-icon" [style.color]="notificationColor">{{ notificationIcon }}</div>
    <div class="notification-message">{{ notificationMessage }}</div>
  </div>
</div>