<div class="profile-container">
  <!-- Back button -->
  <button class="back-btn" (click)="goToDashboard()">
    ⬅ Retour au Tableau de Bord
  </button>

  <!-- Main title -->
  <div class="profile-header">
    <div class="profile-title-container">
      <h2 class="profile-title">Mon Profil</h2>
    </div>
  </div>

  <!-- Profile information card -->
  <div class="profile-card">
    <div class="profile-avatar">
      <div class="avatar-circle">
        <span class="initials">{{ name?.charAt(0) }}{{ nickname?.charAt(0) }}</span>
      </div>
    </div>

    <div class="profile-info">
      <h3 class="profile-name">{{ name }} {{ nickname }}</h3>
      <span class="profile-role">{{ formatRole(role) }}</span>
      
      <div class="info-section">
        <div class="info-item">
          <fa-icon [icon]="faEnvelope" class="info-icon"></fa-icon>
          <span class="info-label">Email:</span>
          <span class="info-value">{{ email }}</span>
        </div>
        
        <div class="info-item">
          <fa-icon [icon]="faPhone" class="info-icon"></fa-icon>
          <span class="info-label">Téléphone:</span>
          <span class="info-value">{{ phone }}</span>
        </div>
        
        <div class="info-item" *ngIf="fax">
          <fa-icon [icon]="faFax" class="info-icon"></fa-icon>
          <span class="info-label">Fax:</span>
          <span class="info-value">{{ fax }}</span>
        </div>
        
        <div class="info-item">
          <fa-icon [icon]="faMapMarkerAlt" class="info-icon"></fa-icon>
          <span class="info-label">Adresse:</span>
          <span class="info-value">{{ adress }}</span>
        </div>
        
        <div class="info-item">
          <fa-icon [icon]="faIdCard" class="info-icon"></fa-icon>
          <span class="info-label">Membre depuis:</span>
          <span class="info-value">{{ formatDate(createdAt) }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Action buttons -->
  <div class="profile-actions">
    <button class="action-btn change-password-btn" (click)="goToChangePassword()">
      Changer le mot de passe
    </button>
  </div>

  <!-- Notification modal -->
  <div class="notification-modal" *ngIf="showNotification" [style.background-color]="notificationColor">
    <span class="notification-icon">{{ notificationIcon }}</span>
    <span class="notification-message">{{ notificationMessage }}</span>
  </div>
</div>
