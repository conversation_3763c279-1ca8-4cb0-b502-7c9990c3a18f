/* --------------------------------------------
   1) Import & Reset
--------------------------------------------- */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}
html, body {
  overflow-x: hidden;
  background-color: #f7f7f7;
  color: #333;
}

/* --------------------------------------------
   2) Container principal
--------------------------------------------- */
.facturation-container {
  margin: 40px auto;
  background: #fff;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  text-align: center;
}

/* --------------------------------------------
   3) Titre principal (H2)
--------------------------------------------- */
.facturation-container h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;

  /* Animation "glow" sur le texte */
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* --------------------------------------------
   4) Liste d'infos principales (ul/li)
--------------------------------------------- */
.facture-info-list {
  list-style: none;            /* Pas de puces */
  text-align: left;            /* Aligné à gauche */
  margin: 0 auto 30px auto;    /* Espace en bas, centré horizontalement */
  max-width: 450px;            /* Largeur max pour une meilleure lisibilité */
  line-height: 1.6;
}
.facture-info-list li {
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 5px;
  background: #f9fcff;         /* Léger fond */
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}
.facture-info-list li i,
.facture-info-list li fa-icon {
  margin-right: 12px;
  color: #2496d3;              /* Couleur d'icône bleue */
}

/* Style pour les icônes */
.icon-match-text {
  color: #2496d3; /* Couleur bleue pour toutes les icônes */
  margin-right: 12px;
}
.facture-info-list li strong {
  color: #2496d3;
  font-weight: 600;
}
.info-value {
  margin-left: 5px;
  color: #333;
  font-weight: 500;
}

/* Status item with spacing */
.status-item {
  display: flex;
  align-items: center;
}

.status-item strong {
  margin-right: 15px;
}

/* Payment status text styling */
.payment-status-text {
  font-size: 1.2em;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
}

.payment-status-text fa-icon {
  margin-right: 12px;
  /* No color override here to preserve status colors */
}

/* Status text colors */
.status-text-approved {
  color: #28a745;
}

.status-text-rejected {
  color: #dc3545;
}

.status-text-pending {
  color: #ffc107;
}

/* Status icons should keep their status colors */
.status-icon {
  margin-right: 12px;
}

.status-text-approved .status-icon {
  color: #28a745;
}

.status-text-rejected .status-icon {
  color: #dc3545;
}

.status-text-pending .status-icon {
  color: #ffc107;
}

/* --------------------------------------------
   5) Si pas de facture
--------------------------------------------- */
.no-facture {
  font-size: 18px;
  color: #666;
  margin-top: 20px;
  font-style: italic;
}

/* --------------------------------------------
   6) Table (analyses)
--------------------------------------------- */
table {
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
  width: 100%;                 /* S'adapte à la largeur du parent */
}
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-transform: uppercase;
}
td {
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* Mise en avant ex.: "Lipide" */
.text-primary {
  color: #2496d3;
}
.fw-bold {
  font-weight: 600;
}

/* Ligne total */
.total-row {
  font-weight: bold;
  background: #f9f9f9;
}
.text-end {
  text-align: right;
}

/* --------------------------------------------
   7) Bouton "Imprimer Facture"
--------------------------------------------- */
.actions {
  text-align: center;
  margin-top: 20px;
}
.actions button {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  padding: 10px 20px;
  background-color: #2496d3;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Print icon styling */
.print-icon {
  color: white;
  margin-right: 12px;
}
.actions button:hover {
  background-color: #1a7aa8;
}

/* --------------------------------------------
   8) Animation "glow" pour le titre
--------------------------------------------- */
@keyframes glowText {
  0% {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  100% {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* --------------------------------------------
   9) Loading Overlay
--------------------------------------------- */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.spinner-large {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(36, 150, 211, 0.2);
  border-radius: 50%;
  border-top-color: #2496d3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

.loading-spinner-container p {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

.blur-content {
  filter: blur(3px);
  pointer-events: none;
}

/* --------------------------------------------
   10) Responsive
--------------------------------------------- */
@media (max-width: 768px) {
  .facturation-container {
    padding: 20px;
  }
  .facture-info-list {
    max-width: 100%;
  }
  table th, table td {
    padding: 10px;
    font-size: 14px;
  }
  .actions button {
    font-size: 16px;
    padding: 8px 16px;
  }
}

/* --------------------------------------------
   7) Section des paiements
--------------------------------------------- */
.payments-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.payments-section h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #2496d3;
  padding-bottom: 8px;
  display: inline-block;
}

.loading-payments, .payment-error, .no-payments {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 14px;
}

.loading-payments {
  background-color: #e8f4fd;
  color: #0c63e4;
}

.payment-error {
  background-color: #ffebee;
  color: #d32f2f;
}

.no-payments {
  background-color: #f5f5f5;
  color: #757575;
  text-align: center;
  padding: 20px;
}

.payments-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payments-table th, .payments-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.payments-table th {
  background-color: #f2f2f2;
  font-weight: 600;
  color: #333;
}

.payments-table tr:hover {
  background-color: #f5f5f5;
}

.payment-status {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 12px;
}

.payment-status .status-icon {
  color: inherit; /* Inherit color from parent status class */
}

.status-pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.status-approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-rejected {
  background-color: #ffebee;
  color: #c62828;
}

/* Rejection reason styling */
.rejection-reason {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 3px solid #dc3545;
  border-radius: 4px;
  font-size: 13px;
  color: #dc3545;
  display: flex;
  align-items: center;
}

.rejection-icon {
  color: #dc3545;
  margin-right: 8px;
}

.payments-table a {
  color: #2496d3;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: color 0.2s;
}

.payments-table a:hover {
  color: #1976d2;
  text-decoration: underline;
}

.payments-table i {
  font-size: 14px;
}

/* Action buttons */
.actions-cell {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
  white-space: nowrap;
}

.btn-action {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px 8px;
  margin-right: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-action.view {
  color: #007bff;
}

.btn-action.view.active {
  background-color: #007bff;
  color: white;
}

.btn-action.download {
  color: #28a745;
}

.btn-action:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Legacy button styles for backward compatibility */
.btn-action-new {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-icon {
  margin-right: 6px;
}

.btn-action-new.view {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.btn-action-new.view:hover, .btn-action-new.view.active {
  background-color: #1976d2;
  color: white;
  border-color: #1976d2;
}

.btn-action-new.download {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.btn-action-new.download:hover {
  background-color: #2e7d32;
  color: white;
  border-color: #2e7d32;
}

/* Preview row styles */
.preview-row {
  background-color: #f5f5f5;
}

.preview-cell {
  padding: 0 !important;
}

.preview-container {
  padding: 20px;
  border-top: 1px dashed #ddd;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #2496d3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
}

.btn-retry {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
}

.btn-retry:hover {
  background-color: #5a6268;
}

.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-preview {
  width: 100%;

  margin: 0 auto;
  text-align: center;
}

.file-preview h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.image-container {

  margin-bottom: 15px;
}

.preview-image {
  max-width: 100%;

  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.file-name {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.pdf-preview, .unknown-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.pdf-icon, .file-icon {
  margin-bottom: 15px;
  color: #f44336;
}

.file-icon {
  color: #607d8b;
}

.btn-open-pdf,
.btn-download,
.btn-open-direct {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  margin-right: 5px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.btn-open-pdf:hover {
  background-color: #0069d9;
}

.btn-download {
  background-color: #28a745;
}

.btn-download:hover {
  background-color: #218838;
}

.btn-open-direct {
  background-color: #6c757d;
}

.btn-open-direct:hover {
  background-color: #5a6268;
}

/* Notification overlay */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.notification {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 400px;
  width: 90%;
}

.notification-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.notification-message {
  font-size: 18px;
  text-align: center;
  margin-bottom: 10px;
}
