import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { FormsModule } from '@angular/forms'; // ✅ Pour [(ngModel)]
import { DemandeService } from './demande.service';
import { Demande } from './demande.model';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faFilter, faSearch, faCalendarAlt, faEraser, faEye, faSpinner, faCircleCheck, faCircleXmark, faCircleExclamation, faCircleDot } from '@fortawesome/free-solid-svg-icons';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';

@Component({
  selector: 'app-demandes',
  standalone: true,
  templateUrl: './demandes.component.html',
  styleUrls: ['./demandes.component.css'],
  imports: [
    CommonModule,
    NgxPaginationModule,
    RouterModule,
    FormsModule, // ✅ Import du FormsModule pour ngModel
    FontAwesomeModule // Import FontAwesomeModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Pour permettre l'attribut [spin] sur fa-icon
})
export class DemandesComponent implements OnInit {
  // Font Awesome icons
  faFilter = faFilter;
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faEraser = faEraser;
  faEye = faEye;
  faSpinner = faSpinner;
  faCircleCheck = faCircleCheck;
  faCircleXmark = faCircleXmark;
  faCircleExclamation = faCircleExclamation;
  faCircleDot = faCircleDot;
  // Tableau complet de demandes
  demandes: Demande[] = [];
  // Tableau filtré (utilisé pour l'affichage et la pagination)
  filteredDemandes: Demande[] = [];

  isLoading = true;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;


  // ✅ Propriétés pour le filtrage
  searchTerm: string = '';
  selectedStatus: string = '';
selectedDate: string = '';
  // Map for status translations
  statusTranslations: { [key: string]: string } = {
    pending: 'En attente',
    ongoing_validation: 'Validation en cours',
    valid: 'Valide',
    derogation: 'Déroger',
    rejected: 'Rejeté'
  };

  constructor(
    private route: ActivatedRoute,
    private demandeService: DemandeService,
    private router: Router,
    private library: FaIconLibrary
  ) {
    // Add the icons to the library so they can be used in the template
    this.library.addIcons(faFilter, faSearch, faCalendarAlt, faEraser, faEye, faSpinner, faCircleCheck, faCircleXmark, faCircleExclamation, faCircleDot);
  }

  ngOnInit(): void {
    this.fetchAllDemandes();
    this.successMessage = localStorage.getItem('successMessage');

    // Clear message after displaying it
    if (this.successMessage) {
      setTimeout(() => {
        localStorage.removeItem('successMessage');
        this.successMessage = null;
      }, 4000); // Hide after 5 seconds
    }
  }

  fetchAllDemandes(): void {
    this.isLoading = true;
    this.demandeService.getAllDemandes().subscribe({
      next: (data: Demande[]) => {
        if (data && data.length > 0) {
          this.demandes = data;

          // Optionally fetch user names for each demande
          this.demandes.forEach((demande: Demande) => {
            this.fetchUserName(demande);
          });

          // ✅ Initialisation du tableau filtré
          this.filteredDemandes = [...this.demandes];
        } else {
          this.errorMessage = 'Aucune demande trouvée.';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching demandes:', error);
        this.errorMessage = 'Échec du chargement des demandes.';
        this.isLoading = false;
      }
    });
  }

  fetchUserName(demande: Demande): void {
    this.demandeService.getUserDetails(demande.user_id).subscribe({
      next: (user) => {
        demande.userName = user.name;
        demande.userNick = user.nickname;
      },
      error: (error) => {
        console.error(`Error fetching user for demande ${demande.demande_id}:`, error);
        demande.userName = 'Inconnu';
      }
    });
  }

  navigateToDemandeDetails(demande: Demande): void {
    if (demande.demande_id) {
      this.router.navigate(['/demande', demande.demande_id]);
    } else {
      console.error('Demande ID is missing:', demande);
    }
  }

  // Translate status using the map
  getStatusTranslation(status: string): string {
    return this.statusTranslations[status] || status;
  }

  // ===============================
  //  MÉTHODES DE FILTRAGE
  // ===============================
 // Replace the existing onFilterChange method with this
// Add these properties near searchTerm and selectedStatus


// Replace the existing onFilterChange method with this


  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page
    const term = this.searchTerm.toLowerCase();
    const status = this.selectedStatus;
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredDemandes = this.demandes.filter((demande) => {
      const matchesSearch =
        demande.demande_id.toLowerCase().includes(term) ||
        (demande.userName && demande.userName.toLowerCase().includes(term));
      const matchesStatus = status ? demande.status === status : true;
      const demandeDate = new Date(demande.demande_date);
      const matchesDate = filterDate ? demandeDate.toDateString() === filterDate.toDateString() : true;
      return matchesSearch && matchesStatus && matchesDate;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedDate = '';
    this.onFilterChange();
  }
}
