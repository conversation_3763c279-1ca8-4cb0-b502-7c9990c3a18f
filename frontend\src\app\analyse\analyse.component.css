/* ✅ Importation des polices */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@700&family=Poppins:wght@400;600&display=swap');

/* ✅ Section principale */
.analyses-section {
    min-height: 100vh;
    padding: 50px 2%;
    background: white;
    color: black;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
}
.description {
  font-size: 18px;
  font-family: 'Poppins', sans-serif;
  max-width: 80%;
  margin: auto;
  line-height: 1.8;
}
/* ✅ Conteneur principal */
.container {
    width: 95%;
    max-width: 1400px;
    margin: auto;
}

/* ✅ Titre animé */
.title {
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin-bottom: 30px;
    border-bottom: 4px solid #2496d3;
    padding-bottom: 10px;
    display: inline-block;
    
}

/* ✅ Animation du titre */
@keyframes glowText {
    from { text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4); }
    to { text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8); }
}

/* ✅ Description styling updated - merged with previous definition */

/* Button styles removed as they are no longer needed */

/* ✅ Liste des Analyses */
.analyses-list {
    margin-top: 30px;
    text-align: left;
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0px 10px 25px rgba(36, 150, 211, 0.15);
    max-width: 100%;
    width: 100%;
    transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
    opacity: 1;
    animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ✅ Table Styling */
/* ✅ Table Styling */
.table-responsive {
    overflow-x: auto;
    border-radius: 12px;
    margin: 20px 0;
    padding: 5px;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.table thead th {
    background-color: #2496d3;
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 15px 12px;
    vertical-align: middle;
    border: none;
    position: relative;
}

/* First header cell - top left rounded corner */
.table thead th:first-child {
    border-top-left-radius: 12px;
}

/* Last header cell - top right rounded corner */
.table thead th:last-child {
    border-top-right-radius: 12px;
}

.table tbody td {
    padding: 12px;
    vertical-align: middle;
    border: none;
    border-bottom: 1px solid #eaeaea;
    border-right: 1px solid #eaeaea;
}

/* Remove right border from last column */
.table tbody td:last-child {
    border-right: none;
}

/* Remove bottom border from last row */
.table tbody tr:last-child td {
    border-bottom: none;
}

/* Bottom left corner */
.table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 12px;
}

/* Bottom right corner */
.table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 12px;
}

.table-striped tbody tr {
    background-color: white !important; /* Uniform background for all rows */
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: white !important; /* Force white background on odd rows */
}

.table-striped tbody tr:nth-of-type(even) {
    background-color: white !important; /* Force white background on even rows */
}

.table-bordered {
    border: none;
}

.table tbody tr:hover {
    background-color: rgba(36, 150, 211, 0.05) !important; /* Light blue on hover */
}

/* ✅ Method and Price Column Styling */
.table td:nth-child(2) {
    text-align: center;
}

.table td:last-child {
    text-align: center;
    font-weight: bold;
    color: #2496d3;
    font-size: 1.05em;
}

/* Add subtle zebra striping for better readability */
.table tbody tr:nth-child(even) {
    background-color: rgba(36, 150, 211, 0.02) !important;
}

/* Add transition for hover effect */
.table tbody tr {
    transition: background-color 0.2s ease;
}

/* Fix for the "Autre acides aminés" row */
.table tbody tr td[style*="color:black"] {
    color: black !important;
}

/* ✅ Note Styling */
.note {
    margin-top: 15px;
    font-style: italic;
    color: #666;
    font-size: 14px;
}

/* ✅ Style du bouton principal */
.analyse-btn {
    background: #2496d3;
    color: white;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    padding: 15px 40px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease-in-out;
    box-shadow: 0px 5px 15px rgba(0, 170, 255, 0.4);
    position: relative;
    overflow: hidden;
    display: inline-block;
    margin-top: 20px;
}

/* Style for the icon in the button */
.btn-icon {
    margin-right: 8px;
    font-size: 1.1em;
}

/* ✅ Effet au survol du bouton principal */
.analyse-btn:hover {
    background: #1a749f;
    transform: translateY(-3px);
    box-shadow: 0px 8px 20px rgba(0, 170, 255, 0.6);
    color: white;
    text-decoration: none;
}

/* ✅ Effet d'onde au clic */
.analyse-btn:active {
    transform: scale(0.98);
    box-shadow: 0px 5px 10px rgba(0, 170, 255, 0.8);
}

/* ✅ Responsive */
@media (max-width: 768px) {
    .title {
        font-size: 20px;
    }

    .description {
        font-size: 16px;
        max-width: 95%;
    }

    .container {
        width: 95%;
        padding: 10px;
    }

    .analyses-list {
        padding: 15px 10px;
        border-radius: 12px;
    }

    .table-responsive {
        margin: 10px 0;
        padding: 0;
    }

    .table {
        border-radius: 10px;
    }

    .table thead th {
        padding: 10px 8px;
        font-size: 14px;
    }

    .table thead th:first-child {
        border-top-left-radius: 10px;
    }

    .table thead th:last-child {
        border-top-right-radius: 10px;
    }

    .table tbody td {
        padding: 8px;
        font-size: 13px;
    }

    .table tbody tr:last-child td:first-child {
        border-bottom-left-radius: 10px;
    }

    .table tbody tr:last-child td:last-child {
        border-bottom-right-radius: 10px;
    }

    .note {
        font-size: 12px;
        margin-top: 10px;
        padding: 0 5px;
    }

    .analyse-btn {
        padding: 12px 30px;
        font-size: 14px;
    }

    .btn-icon {
        font-size: 1em;
    }
}
