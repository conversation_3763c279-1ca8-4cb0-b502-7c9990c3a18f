import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ResultsService {
  private apiUrl = 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) {}

  // Get all valid reports (validation = 1)
  getValidReports(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/facture/valid-reports`);
  }

  // Get report details by ID
  getReportDetails(reportId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/rapports/${reportId}`);
  }

  // Get demande details by demande_id
  getDemandeDetails(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/demande/${demandeId}`);
  }

  // Send report to client
  sendReportToClient(demandeId: string): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/results/${demandeId}/send`, {});
  }

  // Upload report file
  uploadReportFile(demandeId: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'report');

    return this.http.post<any>(`${this.apiUrl}/results/${demandeId}/upload`, formData);
  }

  // Upload invoice file
  uploadInvoiceFile(demandeId: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'invoice');

    return this.http.post<any>(`${this.apiUrl}/results/${demandeId}/upload`, formData);
  }

  // Get all resultat clients
  getResultatClients(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/results`);
  }

  // Get resultat client by demande ID
  getResultatClient(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/results/${demandeId}`);
  }

  // Get file URL for viewing
  getFileUrl(filePath: string): string {
    if (!filePath) return '';
    return `${this.apiUrl}/direct-file/${encodeURIComponent(filePath)}`;
  }

  // Download file
  downloadFile(filePath: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/direct-file/${encodeURIComponent(filePath)}`, {
      responseType: 'blob'
    });
  }

  // Delete report file
  deleteReportFile(demandeId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/results/${demandeId}/report`);
  }

  // Delete invoice file
  deleteInvoiceFile(demandeId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/results/${demandeId}/invoice`);
  }
}
