<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Sample extends Model
{
    protected $fillable = [
        'demande_id', 'identification_echantillon', 'nature_echantillon', 'provenance',
        'masse_echantillon', 'etat', 'analyses_demandees', 'delai_souhaite','analyse_souhaite',
        'lot','reference','date_prelevement','origine_prelevement','nom_preleveur','site'
    ];

    protected $casts = [
        'analyses_demandees' => 'array', // Decode JSON automatically
    ];

    public function demande()
    {
        return $this->belongsTo(Demande::class);
    }
    
}
