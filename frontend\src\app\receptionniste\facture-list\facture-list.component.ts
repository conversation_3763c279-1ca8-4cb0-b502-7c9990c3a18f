import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FactureService, Facture, Payment } from '../facturation/facture.service'; // Import interfaces
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { forkJoin, of } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faFilter, faSearch, faEraser, faEye } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-facture-list',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule, NgxPaginationModule], // Import nécessaire pour *ngFor, [(ngModel)], etc.
  templateUrl: './facture-list.component.html',
  styleUrls: ['./facture-list.component.css'],
})
export class FactureListComponent implements OnInit {
  // Font Awesome icons
  faFilter = faFilter;
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;

  // Tableau des factures chargées depuis le backend
  factures: Facture[] = [];

  // Tableau filtré à afficher
  filteredFactures: Facture[] = [];

  // Factures with payment information
  facturesToDisplay: Facture[] = [];

  // Payments data
  payments: Payment[] = [];

  // Map to store demande details by demande_id
  demandes: { [key: string]: any } = {};

  // Loading state
  isLoading: boolean = true;

  // Champs de recherche / filtre
  searchDemande: string = '';
  selectedStatus: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  constructor(
    private factureService: FactureService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.isLoading = true;

    // Step 1: Fetch all factures
    this.factureService.getFactures().subscribe({
      next: (data) => {
        this.factures = data;
        console.log('Factures loaded:', this.factures);

        // Step 2: Fetch all payments
        this.fetchAllPayments();
      },
      error: (err) => {
        console.error('❌ Error fetching factures:', err);
        this.isLoading = false;
      },
    });
  }

  /**
   * Fetch all payments from the API
   */
  fetchAllPayments(): void {
    this.factureService.getAllPayments().subscribe({
      next: (response) => {
        if (response && response.status === 'success') {
          this.payments = response.data;
          console.log('Payments loaded:', this.payments);

          // Process factures with payment information
          this.processFacturesWithPayments();
        } else {
          console.error('Unexpected response format from payments API');
          this.processFacturesWithoutPayments();
        }
      },
      error: (err) => {
        console.error('❌ Error fetching payments:', err);
        this.processFacturesWithoutPayments();
      }
    });
  }

  /**
   * Process factures with payment information
   */
  processFacturesWithPayments(): void {
    // Create a map of demande_id to payment for quick lookup
    const paymentsByDemandeId: { [key: string]: Payment } = {};

    // Group payments by demande_id
    this.payments.forEach(payment => {
      if (payment.demande_id) {
        // If multiple payments exist for a demande, prioritize by status:
        // approved > pending > rejected
        if (!paymentsByDemandeId[payment.demande_id] ||
            (payment.status === 'approved') ||
            (payment.status === 'pending' && paymentsByDemandeId[payment.demande_id].status === 'rejected')) {
          paymentsByDemandeId[payment.demande_id] = payment;
        }
      }
    });

    // Process each facture
    this.facturesToDisplay = this.factures.map(facture => {
      const payment = paymentsByDemandeId[facture.demande];

      // Create a new facture object with payment status
      return {
        ...facture,
        payment: payment,
        // Use payment status if available, otherwise use facture status
        status: payment ? this.mapPaymentStatusToFactureStatus(payment.status) : facture.status
      };
    });

    // Update filtered factures
    this.filteredFactures = [...this.facturesToDisplay];
    this.isLoading = false;

    console.log('Processed factures with payments:', this.facturesToDisplay);
  }

  /**
   * Map payment status to facture status
   */
  mapPaymentStatusToFactureStatus(paymentStatus: string): string {
    switch (paymentStatus) {
      case 'approved':
        return 'paye';
      case 'rejected':
        return 'paiement_rejete';
      case 'pending':
      default:
        return 'en_attente';
    }
  }

  /**
   * Process factures without payment information (fallback)
   */
  processFacturesWithoutPayments(): void {
    // Just use the factures as they are
    this.facturesToDisplay = [...this.factures];
    this.filteredFactures = [...this.factures];
    this.isLoading = false;

    console.log('Processed factures without payments:', this.facturesToDisplay);
  }

  // ➜ Méthode pour naviguer vers le détail
  viewDetails(id: number): void {
    this.router.navigate(['/facture', id]);
  }

  // ➜ Appliquer les filtres
  applyFilter(): void {
    let temp = [...this.facturesToDisplay];

    // 1) Filtrer par numéro de demande
    if (this.searchDemande.trim() !== '') {
      temp = temp.filter((facture) =>
        facture.demande
          ?.toString()
          .toLowerCase()
          .includes(this.searchDemande.toLowerCase())
      );
    }

    // 2) Filtrer par statut
    if (this.selectedStatus) {
      temp = temp.filter(
        (facture) => facture.status === this.selectedStatus
      );
    }

    // On assigne le résultat filtré
    this.filteredFactures = temp;

    console.log('Filtered factures:', this.filteredFactures);
  }

  // Clear filters
  clearFilters(): void {
    this.searchDemande = '';
    this.selectedStatus = '';
    this.filteredFactures = [...this.facturesToDisplay];
  }
}
