<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegistreSuivi extends Model {
    use HasFactory;

    protected $fillable = ['demande_id', 'nom_client', 'date_reception'];

    public function registres() {
        return $this->hasMany(Registre::class);
    }

    public function demande() {
        return $this->belongsTo(Demande::class);
    }
}
