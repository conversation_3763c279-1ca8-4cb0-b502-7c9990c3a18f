import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FicheTransmissionService } from '../../responsableLabo/fiche-transmission/fiche.service';
import { DemandeService } from '../../receptionniste/demandes/demande.service';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faFileAlt,
  faArrowLeft,
  faSpinner,
  faExclamationTriangle,
  faInfoCircle,
  faClipboardList,
  faPrint
} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-analyst-fiche-details',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './fiche-details.component.html',
  styleUrls: ['./fiche-details.component.css']
})
export class AnalystFicheDetailsComponent implements OnInit {
  // Font Awesome icons
  faFileAlt = faFileAlt;
  faArrowLeft = faArrowLeft;
  faSpinner = faSpinner;
  faExclamationTriangle = faExclamationTriangle;
  faInfoCircle = faInfoCircle;
  faClipboardList = faClipboardList;
  faPrint = faPrint;
  fiches: any[] = [];
  loading: boolean = true;
  errorMessage: string = '';
  ficheTransmissionId!: number;
  userCache: { [key: number]: { name: string; nickname: string } } = {};
  clientName: string = '';
  dateTransmissionGlobal: string = '';

  // Rapport notes properties
  rapportNotes: any = null;

  constructor(
    private ficheService: FicheTransmissionService,
    private demandeService: DemandeService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    // Get fiche_transmission_id from URL
    this.route.params.subscribe(params => {
      this.ficheTransmissionId = params['id'];
      if (this.ficheTransmissionId) {
        this.loadFiches(this.ficheTransmissionId);
      }
    });
  }

  // Load fiche details by transmission ID
  loadFiches(ficheTransmissionId: number) {
    this.ficheService.getFichesByTransmissionId(ficheTransmissionId).subscribe({
      next: (response) => {
        console.log('Fiches response:', response);

        // Handle new response structure with fiches and notes
        if (response && response.fiches) {
          this.fiches = response.fiches;

          // Set rapport notes if available
          if (response.notes) {
            this.rapportNotes = response.notes;
            console.log('📝 Rapport notes loaded from backend:', this.rapportNotes);
          } else {
            this.rapportNotes = null;
            console.log('📋 No rapport notes available');
          }
        } else {
          // Fallback for old response structure (just array of fiches)
          this.fiches = Array.isArray(response) ? response : [];
        }

        this.loading = false;

        // Fetch client name if data is available
        if (this.fiches && this.fiches.length > 0 && this.fiches[0].demande?.user_id) {
          this.fetchClientName(this.fiches[0].demande.user_id);
        }

        // Set date if available
        if (this.fiches && this.fiches.length > 0 && this.fiches[0].date_transmission) {
          this.dateTransmissionGlobal = this.fiches[0].date_transmission;
        }
      },
      error: (error) => {
        this.errorMessage = 'Échec du chargement des fiches';
        console.error('Error fetching fiches:', error);
        this.loading = false;
      }
    });
  }

  // Go back to fiche list
  goToFicheList() {
    this.router.navigate(['/analysits/fiche-transmission']);
  }

  /**
   * Fetch client name from user ID
   */
  fetchClientName(userId: number): void {
    if (!userId) return;

    // Check cache first
    if (this.userCache[userId]) {
      this.clientName = `${this.userCache[userId].name} ${this.userCache[userId].nickname || ''}`;
      return;
    }

    // Fetch from API if not in cache
    this.demandeService.getUserDetails(userId).subscribe({
      next: (user) => {
        this.userCache[userId] = { name: user.name, nickname: user.nickname || '' };
        this.clientName = `${user.name} ${user.nickname || ''}`;
      },
      error: (error) => {
        console.error(`Error fetching user details for ID ${userId}:`, error);
        this.clientName = 'Inconnu';
      }
    });
  }

  /**
   * Print the fiche de transmission
   */
  printFiche(): void {
    // Generate table rows dynamically from fiches data
    const tableRows = this.fiches.map(fiche => {
      // Convert analyses_demandees array to an unordered list
      const analysesList = fiche.analyses_demandees?.length > 0
        ? `<ul style="margin: 0; padding-left: 20px;">${fiche.analyses_demandees.map((analysis:any) => `<li>${analysis}</li>`).join('')}</ul>`
        : '—';

      return `
        <tr>
          <td>${fiche.code_laboratoire || '—'}</td>
          <td>${fiche.nature_echantillon || '—'} / ${fiche.masse_echantillon || '—'}g</td>
          <td>${fiche.date_transmission || '—'}</td>
          <td>${fiche.date_remise_resultats || '—'}</td>
          <td>${analysesList}</td>
          <td>${fiche.observations || '—'}</td>
        </tr>
      `;
    }).join('');

    const printHtml = `
  <!DOCTYPE html>
  <html lang="fr">
  <head>
  <meta charset="UTF-8">
  <title>Formulaire d'enregistrement et transmission #${this.ficheTransmissionId}</title>
  <style>
   @media print {
              @page {
                size: A4;
                margin: 5mm 5mm;
              }

              body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
            }
  body {
    font-family: Arial, sans-serif;
    margin: 20px;
  }
    .footer-container {
              width: 100%;
              max-width: 1200px;
              margin: 0 auto;
              padding: 20px 40px;
              box-sizing: border-box;
               padding-top: 50px;
          }

          .footer-section {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 30px;
          }

          .left-text {
              font-size: 18px;
              font-weight: bold;
          }

          .right-text {
              font-size: 18px;
              font-weight: bold;

              padding: 5px 10px;
          }

          .bottom-text {
              border-top: 1px solid #000;
              padding-top: 20px;
              font-size: 14px;
              text-align: center;
              font-style: italic;
              margin-top: 30px;
          }
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px auto;
  }

  th, td {
    border: 1px solid black;
    padding: 8px;
    text-align: left;
    vertical-align: top; /* Ensures list aligns properly */
  }

  .logo-cell {
    vertical-align: middle;
    text-align: center;
  }

  .second-column {
    background-color: #e0e0e0;
    text-align: center;
  }

  .third-column {
    text-align: right;
  }

  .date-header {
    text-align: center;
  }

  .sub-header {
    text-align: center;
  }

  .header-table {
    margin-bottom: 40px;
  }
  </style>
  </head>
  <body>

  <!-- Formulaire d'enregistrement -->
  <table class="header-table">
    <tr>
      <td rowspan="4" class="logo-cell">
        <div>
                <img src="${window.location.origin}/kls.png" alt="Logo Left" class="logo-left" style=" width: 120px;
              height: auto;">

      </td>
      <td rowspan="2" class="second-column">FORMULAIRE D'ENREGISTREMENT</td>
      <td class="third-column">CODE: FE/03-PRT/08</td>
    </tr>
    <tr>
      <td class="third-column">VERSION : 01</td>
    </tr>
    <tr>
      <td rowspan="2" class="second-column">Fiche de transmission des échantillons au laboratoire</td>
      <td class="third-column">DATE : ${this.dateTransmissionGlobal || '23/02/2021'}</td>
    </tr>
    <tr>
      <td class="third-column">PAGE 1 / 1</td>
    </tr>
  </table>

  <!-- Fiche de transmission -->
  <table>
    <tr>
      <th rowspan="2">Code échantillon (laboratoire)</th>
      <th rowspan="2">Nature/ Masse de l'échantillon</th>
      <th colspan="2" class="date-header">Date</th>
      <th rowspan="2">Analyses demandées*</th>
      <th rowspan="2">Observations</th>
    </tr>
    <tr>
      <th class="sub-header">Transmission</th>
      <th class="sub-header">Remise des résultats</th>
    </tr>
    ${tableRows}
  </table>
   <div class="footer-section">
              <div class="left-text">Réceptionnaire</div>
              <div class="right-text">Responsables des analyses</div>
          </div>
  <div class="footer-container">

          <div class="bottom-text">
              Ce document est la propriété du Laboratoire B³Aqua-Toute, utilisation, reproduction modification est soumise à un accord du propriétaire
          </div>
      </div>
  </body>
  </html>
    `;

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHtml);
      printWindow.document.close();

      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
          // printWindow.close(); // Optional: close after printing
        }, 500);
      };
    } else {
      console.error('Failed to open print window. Please allow pop-ups.');
    }
  }



  /**
   * Get validation status text
   */
  getValidationStatus(validation: number): string {
    switch (validation) {
      case 0: return 'Rejeté';
      case 1: return 'Approuvé';
      case 2: return 'En attente';
      default: return 'Inconnu';
    }
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
