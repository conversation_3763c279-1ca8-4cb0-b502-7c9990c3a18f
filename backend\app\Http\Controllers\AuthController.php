<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function getUsersDetails(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'user_ids' => 'required|array', // Ensure user_ids is an array
            'user_ids.*' => 'integer|exists:users,id' // Each ID must be an integer and exist in the users table
        ]);

        // Get the array of user IDs from the request
        $userIds = $request->input('user_ids');

        // Fetch users from the database
        $users = User::whereIn('id', $userIds)
            ->select('id', 'name', 'nickname') // Select only needed fields
            ->get();

        // Return the response as JSON
        return response()->json($users);
    }
    public function register(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'phone'=>['required', 'string','max:255'],
            'adress'=>['required', 'string','max:255'],
            'fax'=>['nullable', 'string',''],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);
        User::create([
            'name' => $request->name,
            'nickname'=> $request->nickname,
            'email' => $request->email,
            'phone'=> $request->phone,
            'adress'=>$request->adress,
            'fax'=> $request->fax ?? null,
            'password' => Hash::make($request->password),
        ]);
        return response()->json('account created successfully',201);
    }
    public function login(Request $request){
           $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string', 'min:8'],//confirmer mot de passe avec un champs dans frontend
        ]);
         if(!Auth::attempt($request->only('email','password')))
            return response()->json('email or password is incorrect',401);
        $user = User::where('email', $request->email)->firstOrFail();
        $token= $user->createToken('auth_Token')->plainTextToken;
        return response()->json(['message'=>'login successfully','Token'=>$token,'role' => $user->role,'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'nickname'=> $user->nickname,
                'email' => $user->email,
                'phone' => $user->phone,
                'adress'=> $user->adress,
                'fax'=> $user->fax ?? null,
                'role' => $user->role,
            ]],201);
    }
    public function logout(Request $request){
        $request->user()->currentAccessToken()->delete();
        return response()->json('logout successfully',200);

    }
    
}