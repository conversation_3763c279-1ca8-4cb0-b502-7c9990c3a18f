import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormGroup, FormBuilder, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { DemandeService } from '../demande-details/demande.service';
import { Demande, Sample } from '../demande-details/demande.model';
import { DerogationService } from './derogation.service';
import { Derogation } from '../../../models/derogation.model';

@Component({
  selector: 'app-fiche-derogation',
  standalone: true,
  templateUrl: './fiche-derogation.component.html',
  styleUrls: ['./fiche-derogation.component.css'],
  imports: [CommonModule, FormsModule, ReactiveFormsModule]
})
export class FicheDerogationComponent implements OnInit {
  derogationForm: FormGroup;
  demande: Demande | null = null;
  isLoading = true;
  errorMessage: string | null = null;
  dropdownOpen: boolean[] = [];
  selectedSample: Sample | null = null;
  demandeId: string = "";
  count: number = 0;
  successMessage: string | null = null;
  showNotification = false;

  // Loading and notification properties
  showLoadingModal = false;
  loadingMessage = 'Traitement en cours...';
  notificationMessage = '';
  notificationIcon = '✅';
  notificationColor = '#28a745';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private demandeService: DemandeService,
    private derogationService: DerogationService
  ) {
    this.derogationForm = this.fb.group({
      samples: this.fb.array([])
    });
  }

  ngOnInit() {
    this.getDemandeID();
    this.successMessage = localStorage.getItem('successMessage');
    if (this.successMessage) {
      setTimeout(() => {
        localStorage.removeItem('successMessage');
        this.successMessage = null;
      }, 3000); // Hide message after 3 seconds
    }
  }

  // Get demandeId from route params
  demande_id = this.route.snapshot.paramMap.get('demande_id');

  getDemandeID(): void {
    const demandeId = this.route.snapshot.paramMap.get('demande_id');
    if (!demandeId) {
      this.errorMessage = 'Aucun ID de demande fourni !';
      this.isLoading = false;
      return;
    }
    this.demandeId = demandeId;
    this.demandeService.getDemandeDetails(demandeId).subscribe({
      next: (response) => {
        this.demande = { ...response, samples: response.samples || [] };
        this.isLoading = false;
        this.initializeSamples();
      },
      error: (error) => {
        console.error('Error fetching demande:', error);
        this.errorMessage = 'Échec du chargement des détails de la demande.';
        this.isLoading = false;
      }
    });
  }

  get samples(): FormArray {
    return this.derogationForm.get('samples') as FormArray;
  }

  // Getter to return controls as FormGroup[]
  get sampleFormGroups(): FormGroup[] {
    return this.samples.controls as FormGroup[];
  }

  // Initialize form controls for samples
  initializeSamples() {
    this.samples.push(this.createSampleFormGroup({} as Sample));
    this.dropdownOpen.push(false);
  }

  // Create a sample form group
  createSampleFormGroup(sample: Sample): FormGroup {
    return this.fb.group({
      demande_id: [this.demandeId],
      identification_echantillon: [sample.identification_echantillon || ''],
      reference: [sample.reference || ''],
      nature_echantillon: [sample.nature_echantillon || ''],
      masse_echantillon: [sample.masse_echantillon || ''],
      etat: [sample.etat || ''],
      delai_souhaite: [sample.delai_souhaite || ''],
      analyse_souhaite: [sample.analyse_souhaite || ''],
      provenance: [sample.provenance || ''],
      lot: [sample.lot || ''],
      analyses_demandees: [sample.analyses_demandees || []],
      description_ecart: [''],
      reponse_du_client: [''],
      decision: [''],
      date_et_visa_du_DL: [''],
      date_et_visa_du_demander: ['']
    });
  }

  // Pre-fill form group when a sample is selected
  selectSample(i: number): void {
    const sampleFormGroup = this.samples.at(i) as FormGroup;
    const selectedIdentification = sampleFormGroup.get('identification_echantillon')?.value;
    const selectedSample = this.demande?.samples.find(sample => sample.identification_echantillon === selectedIdentification);

    if (selectedSample) {
      sampleFormGroup.patchValue({
        reference: selectedSample.reference,
        nature_echantillon: selectedSample.nature_echantillon,
        masse_echantillon: selectedSample.masse_echantillon,
        etat: selectedSample.etat,
        delai_souhaite: selectedSample.delai_souhaite,
        analyse_souhaite: selectedSample.analyse_souhaite,
        provenance: selectedSample.provenance,
        lot: selectedSample.lot,
        analyses_demandees: selectedSample.analyses_demandees
      });
    }
  }

  getTotalSamples(): number {
    return this.demande?.samples?.length || 0;
  }

  addEnregistrement() {
    const totalSamples = this.getTotalSamples();
    if (this.samples.length < totalSamples) {
      this.samples.push(this.createSampleFormGroup({} as Sample));
      this.dropdownOpen.push(false);
    } else {
      console.log('Maximum number of samples reached');
    }
  }

  removeEnregistrement(index: number) {
    if (this.samples.length > 1) {
      this.samples.removeAt(index);
      this.dropdownOpen.splice(index, 1);
    }
  }

  getFilteredSamples(currentIndex: number) {
    const otherSelectedValues = this.samples.controls
      .filter((_, index) => index !== currentIndex)
      .map((ctrl: any) => ctrl.value.identification_echantillon);
    return this.demande?.samples.filter(
      (sample) => !otherSelectedValues.includes(sample.identification_echantillon)
    ) || [];
  }

  // Submit the derogation request
  submitDerogation() {
    console.log("Submitting Derogation...");

    if (this.derogationForm.invalid) {
      console.error("Form is invalid. Errors:");
      (this.samples as FormArray).controls.forEach((control, index) => {
        console.log(`Sample ${index + 1} Errors:`, control.errors);
        Object.keys((control as FormGroup).controls).forEach(key => {
          if ((control as FormGroup).get(key)?.invalid) {
            console.error(`Field ${key} is invalid`, (control as FormGroup).get(key)?.errors);
          }
        });
      });
      return;
    }

    if (!this.demandeId) {
      console.error("No demandeId available.");
      return;
    }

    const derogationData: Derogation = {
      samples: (this.samples as FormArray).value.map((sample: any) => ({
        identification_echantillon: sample.identification_echantillon,
        masse_echantillon: parseFloat(sample.masse_echantillon),
        description_ecart: sample.description_ecart,
        reponse_du_client: sample.reponse_du_client,
        decision: sample.decision,
        date_et_visa_du_DL: sample.date_et_visa_du_DL ? new Date(sample.date_et_visa_du_DL).toISOString().split('T')[0] : null,
        date_et_visa_du_demander: sample.date_et_visa_du_demander ? new Date(sample.date_et_visa_du_demander).toISOString().split('T')[0] : null
      }))
    };

    console.log("Final Payload:", JSON.stringify(derogationData, null, 2));

    // Show loading spinner
    this.showLoadingModal = true;
    this.loadingMessage = 'Envoi de la dérogation...';

    this.derogationService.createDerogation(derogationData, this.demandeId).subscribe({
      next: (res) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.log("Submission Success:", res);

        // Show success notification
        this.notificationMessage = 'Votre dérogation a été envoyée avec succès !';
        this.notificationIcon = '✅';
        this.notificationColor = '#28a745';
        this.showNotification = true;

        // Hide notification after 3 seconds and navigate
        setTimeout(() => {
          this.showNotification = false;
          this.router.navigate(['/receptionist/demandes']);
        }, 3000);
      },
      error: (err) => {
        // Hide loading spinner
        this.showLoadingModal = false;

        console.error("Submission Failed:", err);
        if (err.error?.errors) {
          console.error("Validation Errors:", err.error.errors);
        }

        // Show error notification
        this.notificationMessage = 'Erreur lors de l\'envoi de la dérogation. Veuillez réessayer.';
        this.notificationIcon = '❌';
        this.notificationColor = '#dc3545';
        this.showNotification = true;

        // Hide notification after 5 seconds
        setTimeout(() => {
          this.showNotification = false;
        }, 4000);
      }
    });
  }

  navigateTo(path: string): void {
    this.router.navigate([path]);
  }

  clearForm() {
    this.derogationForm.reset();
  }

  getFormGroup(index: number): FormGroup {
    return this.samples.at(index) as FormGroup;
  }
}