printDemande(): void {
    if (!this.demande) {
      console.error('Aucune demande à imprimer');
      return;
    }

    // Générer les lignes du tableau d'échantillons
    const sampleRows = this.demande.samples.map(sample => {
      // Convertir le tableau d'analyses en liste HTML
      const analysesList = sample.analyses_demandees?.length > 0
        ? `<ul style="margin: 0; padding-left: 20px;">${sample.analyses_demandees.map(analysis => `<li>${analysis}</li>`).join('')}</ul>`
        : '—';

      // Déterminer l'état de l'échantillon avec des cases à cocher
      const etatLiq = sample.etat === 'Liquide' ? '✓' : '';
      const etatSol = sample.etat === 'Solide' ? '✓' : '';
      const etatRig = sample.etat === 'Rigide' ? '✓' : '';
      const etatCgl = sample.etat === 'Congelé' ? '✓' : '';

      return `
        <tr>
          <td><div contenteditable="true" class="editable-field">${sample.identification_echantillon || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${sample.nature_echantillon || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${sample.provenance || '—'}</div></td>
          <td><div contenteditable="true" class="editable-field">${sample.masse_echantillon || '—'}</div></td>
          <td>${etatLiq}</td>
          <td>${etatSol}</td>
          <td>${etatRig}</td>
          <td>${etatCgl}</td>
          <td><div contenteditable="true" class="editable-field">${analysesList}</div></td>
        </tr>
      `;
    }).join('');

    // Devis section removed as requested

    // Formater la date pour l'affichage
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    };

    // Calculate total pages based on samples
    const samplesPerPage = 5; // Assuming 5 samples can fit on a page
    const totalSamples = this.demande.samples.length;
    const samplePages = Math.ceil(totalSamples / samplesPerPage);
    const totalPages = Math.max(1, samplePages); // At least 1 page

    // Générer le HTML complet pour l'impression avec des champs éditables
    const printHtml = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire Client Complet</title>
    <style>
        /* Print Settings */
        @media print {
            @page {
                size: A4;
                margin: 5mm;
                counter-increment: page;
            }
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                counter-reset: page 1;
            }
            .logo-cell img {
                width: 30mm;
                height: auto;
            }
            .header-table th, .header-table td {
                font-size: 10pt;
            }
            /* Page break after sample table if needed */
            .sample-table-container {
                page-break-after: auto;
            }
            /* Make header repeat on each page */
            thead {
                display: table-header-group;
            }
            tfoot {
                display: table-footer-group;
            }
            /* Ensure proper page breaks */
            tr {
                page-break-inside: avoid;
            }
            .header-table {
                page-break-after: avoid;
            }
            /* Add space at top of each page for the header */
            .content-wrapper {
                page-break-before: auto;
            }
            /* First page already has the header */
            .first-page {
                page-break-before: avoid;
            }
            /* Page number styling */
            .page-number::after {
                content: counter(page) " / " attr(data-total-pages);
            }
            .page-number {
                position: relative;
            }
            .page-number span {
                display: none;
            }
        }

        /* Global Styles */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        /* Editable Fields Styles */
        .editable-field {
            min-height: 20px;
            border: 1px dashed #ccc;
            padding: 2px 5px;
            background-color: #f9f9f9;
        }
        .editable-field:focus {
            outline: none;
            border: 1px dashed #2496d3;
            background-color: #e9f7fe;
        }

        /* Label styles */
        .label {
            font-weight: bold;
        }

        @media print {
            .editable-field {
                border: none;
                background-color: transparent;
            }
        }



        /* Print Controls */
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #2496d3;
            color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            cursor: move;
            user-select: none;
        }
        .print-controls .drag-handle {
            padding: 5px 0;
            margin-bottom: 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            cursor: move;
        }
        .print-controls button {
            background-color: white;
            color: #2496d3;
            border: none;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            display: block;
            width: 100%;
        }
        .print-controls button:hover {
            background-color: #f0f0f0;
        }

        @media print {
            .print-controls {
                display: none;
            }
            .editable-field {
                border: none;
                background-color: transparent;
            }
        }

        /* Header Table */
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px auto;
            margin-bottom: 40px;
            page-break-inside: avoid;
            page-break-after: avoid;
        }
        /* Main table for page layout */
        body > table {
            width: 100%;
            border-collapse: collapse;
            border: none;
        }
        body > table > thead > tr > td,
        body > table > tbody > tr > td,
        body > table > tfoot > tr > td {
            padding: 0;
            border: none;
        }
        .header-table th, .header-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        /* Content wrapper to handle page breaks properly */
        .content-wrapper {
            page-break-before: always;
        }
        .first-page {
            page-break-before: avoid;
        }
        .logo-cell {
            vertical-align: middle;
            text-align: center;
        }
        .second-column {

            background-color: #e0e0e0;
            text-align: center;
            font-weight: bold;
            font-size: 24pt;
            vertical-align: middle;
            padding: 20px;
        }
        .third-column {
            text-align: right;
        }

        /* Client Info Section */
        .form-section {
            max-width: 1200px;
            margin: 40px auto;
            background: transparent;
            padding: 30px;
            border-radius: 8px;

        }
        .client-info-container {
            margin-bottom: 40px;
        }
        .header-info {
            display: flex;
            justify-content: space-between;
            border-bottom: 2px solidrgb(0, 0, 0);
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .header-label {
            font-size: 18px;
            color:rgb(0, 0, 0);
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .client-table {
            width: 100%;
            border-collapse: collapse;
        }
        .client-table td {
            border: 1px solid #000;
            padding: 12px;
            vertical-align: top;
        }
        .label-cell {
            font-weight: bold;
            width: 25%;
            background-color: transparent;
        }
        .data-cell {
            width: 75%;
            padding-left: 15px;
        }

        /* Sample Table Section */
        .sample-table-container {
            margin: 20px auto;
        }
        .sample-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }
        .sample-table th, .sample-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .sample-table thead th {
            font-weight: bold;
        }

        /* Payment Section */
        .payment-section {
            margin: 20px auto;
            width: 100%;
            max-width: 800px;
            padding: 20px;
        }
        .payment-section h3 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
        }
        .payment-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .option {
            display: flex;
            align-items: center;
        }
        .option input {
            margin-right: 5px;
        }
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-block-left, .signature-block-right {
            width: 45%;
        }
        .signature-block-left {
            text-align: left;
        }
        .signature-block-right {
            text-align: right;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            height: 30px;
            margin-top: 10px;
        }

        /* Footer Section */
        .footer-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 40px;
            box-sizing: border-box;
            padding-top: 50px;
        }
        .bottom-text {
            border-top: 1px solid #000;
            padding-top: 20px;
            font-size: 14px;
            text-align: center;
            font-style: italic;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <table>
        <thead>
            <!-- Document Header that will repeat on each page -->
            <tr>
                <td colspan="100%">
                    <table class="header-table">
                        <tr>
                            <td rowspan="4" class="logo-cell">
                                <img src="${window.location.origin}/kls.png" alt="Logo Left" style="width: 120px; height: auto;">
                            </td>
                            <td class="third-column" rowspan="4" style="vertical-align: middle; text-align:center;font-size:20px;font-weight:bold;" contenteditable="true">
                        DEMANDE D'ANALYSE
                      </td>
                            <td class="third-column"><span class="label">CODE:</span> <span contenteditable="true" class="editable-field">FE/03-PRT/08</span></td>
                        </tr>
                        <tr>
                            <td class="third-column"><span class="label">VERSION :</span> <span contenteditable="true" class="editable-field">01</span></td>
                        </tr>
                        <tr>
                            <td class="third-column"><span class="label">DATE :</span> <span contenteditable="true" class="editable-field">${formatDate(this.demande.demande_date)}</span></td>
                        </tr>
                        <tr>
                            <td class="third-column"><span class="label">PAGE :</span> <span contenteditable="true" class="editable-field page-number" data-total-pages="${totalPages}"><span>1 / ${totalPages}</span></span></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan="100%">
                    <div class="content-wrapper first-page">

    <!-- Form Structure -->
    <div style="margin: 20px 0;">
      <style>
        .form-container {
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        }

        .form-row {
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
        }

        .form-label {
          font-weight: bold;
          margin-right: 10px;
        }

        .input-boxes {
          display: flex;
          gap: 2px;
        }

        .input-box {
          width: 30px;
          height: 30px;
          border: 1px solid black;
          display: inline-block;
          text-align: center;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .separator {
          margin: 0 5px;
        }

        .underlined-label {
          font-style: italic;
          text-decoration: underline;
          margin-bottom: 10px;
          display: block;
        }

        .dotted-line {
          border-bottom: 1px dotted black;
          flex-grow: 1;
          height: 1.2em;
          margin-right: 10px;
        }

        .amount-field {
          text-align: right;
          flex-grow: 1;
        }

        .date-group {
          display: flex;
          gap: 10px;
        }
      </style>

      <div class="form-container">
        <div class="form-row">
          <div style="flex: 1;">
            <div class="form-label">Demande N°:</div>
            <div class="input-boxes">
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(0, 1) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(1, 2) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(2, 3) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(3, 4) : ''}</div>
              <span class="separator"></span>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(4, 5) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(5, 6) : ''}</div>
              <div class="input-box" contenteditable="true">${this.demande.demande_id ? this.demande.demande_id.toString().replace(/-/g, '').substring(6, 7) : ''}</div>
            </div>
          </div>

          <div style="flex: 1;">
            <div class="form-label">Date d'enregistrement :</div>
            <div class="date-group">
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(0, 1)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(1, 2)}</div>
              </div>
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(3, 4)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(4, 5)}</div>
              </div>
              <div class="input-boxes">
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(8, 9)}</div>
                <div class="input-box" contenteditable="true">${formatDate(this.demande.demande_date).substring(9, 10)}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <span class="underlined-label">Référence document client :</span>
        </div>

        <div class="form-row">
          <div style="flex: 2;">
            <span  style="font-weight:bold;">Quittance</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:200px; display:inline-block; margin-left:10px;" contenteditable="true"></span>
          </div>
        </div>

        <div class="form-row">
          <div style="flex: 2;">
            <span style="font-weight:bold;">Bon de commande</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:200px; display:inline-block; margin-left:10px;" contenteditable="true"></span>
          </div>
          <div style="flex: 1;">
            <span  style="font-weight:bold;">Montant HT</span>
            <span class="dotted-line" style="border-bottom: 2px dotted #666; min-width:100px; display:inline-block; margin-left:10px;" contenteditable="true">${this.getTotal()} DT</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Information Section -->
    <div class="form-section">
<table class="client-table">
    <tr>
        <td class="label-cell">Client</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.user_name}</span></td>
        <td class="label-cell">Tél.</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.telephone || ''}</span></td>
    </tr>
    <tr>
        <td rowspan="2" class="label-cell">Adresse</td>
        <td rowspan="2" class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.adresse || ''}</span></td>
        <td class="label-cell">Fax</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.fax || ''}</span></td>
    </tr>
    <tr>
        <td class="label-cell">Mail</td>
        <td class="data-cell"><span contenteditable="true" class="editable-field">${this.demande.user_email}</span></td>
    </tr>
</table>
    </div>

    <!-- Sample Table Section -->
    <div class="sample-table-container">
        <table class="sample-table">
            <thead>
                <tr>
                    <th rowspan="3">Identification échantillon</th>
                    <th rowspan="3">Nature échantillon</th>
                    <th rowspan="3">Provenance</th>
                    <th colspan="5">Description de l'échantillon</th>
                    <th rowspan="3">Analyses demandées*</th>
                </tr>
                <tr>
                    <th rowspan="2">Qté<br>/Echantillon</th>
                    <th colspan="4">Etat</th>
                </tr>
                <tr>
                    <th>Liq</th>
                    <th>Sol</th>
                    <th>Rig</th>
                    <th>Cgl</th>
                </tr>
            </thead>
            <tbody>
                ${sampleRows}
            </tbody>
        </table>
    </div>

    <!-- Mode de règlement Section -->
    <div style="margin: 30px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
        <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">Mode de règlement :</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="carte" ${this.demande.mode_reglement === 'Carte bancaire' ? 'checked' : ''}>
                <label for="carte" style="margin-left: 5px;">Sur présentation de facture</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="virement" ${this.demande.mode_reglement === 'Virement bancaire' ? 'checked' : ''}>
                <label for="virement" style="margin-left: 5px;">Virement bancaire</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="convention" ${this.demande.mode_reglement === 'Convention' ? 'checked' : ''}>
                <label for="convention" style="margin-left: 5px;">Convention</label>
            </div>
            <div style="display: flex; align-items: center;">
                <input type="checkbox" id="collaboration" ${this.demande.mode_reglement === 'Collaboration' ? 'checked' : ''}>
                <label for="collaboration" style="margin-left: 5px;">Collaboration</label>
            </div>
        </div>

        <!-- NB Section -->
        <div style="margin-top: 20px; font-size: 14px;">
            <p style="font-weight: bold; margin-bottom: 10px;">NB :</p>
            <p style="margin-bottom: 5px;">-La durée des analyses est d'une semaine ouvrable pour un échantillon standard.</p>
            <p style="margin-bottom: 5px;">-Si N≥6 contacter le DL pour définir le délai à communiquer au client.</p>
            <p style="margin-bottom: 5px;">**Délai d'exécution souhaité par le client, méthode d'analyse autre que celle utilisée par le laboratoire ; état de l'emballage de l'échantillon….</p>
        </div>
    </div>

    <!-- Signatures Section -->
    <div style="display: flex; justify-content: space-between; margin: 40px 0;">
        <div style="width: 45%; text-align: left;">
            <p style="font-weight: bold; margin-bottom: 10px;">Signature du Client</p>

        </div>
        <div style="width: 45%; text-align: right;">
            <p style="font-weight: bold; margin-bottom: 10px;">Signature du réceptionnaire</p>

        </div>
    </div>

    <!-- Footer Section -->
    <div class="footer-container">
        <div class="bottom-text">
            Ce document est strictement confidentiel et ne doit pas être reproduit sans autorisation
        </div>
    </div>
    </div> <!-- Close content-wrapper -->
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="100%">
                    <!-- Footer content if needed -->
                </td>
            </tr>
        </tfoot>
    </table>

    <!-- Print Controls (Draggable) -->
    <div class="print-controls" id="draggable-print-controls">
        <div class="drag-handle">⋮⋮ Déplacer</div>
        <button id="print-button">Imprimer</button>
        <button id="cancel-button">Annuler</button>
    </div>

    <script>
        // Add event listeners to buttons
        document.getElementById('print-button').addEventListener('click', function() {
            // Hide controls before printing
            document.querySelector('.print-controls').style.display = 'none';
            // Update page numbers before printing
            updatePageNumbers();
            // Print the document
            setTimeout(function() {
                window.print();
                // Show controls again after printing
                document.querySelector('.print-controls').style.display = 'block';
            }, 100);
        });

        // Function to update page numbers - only needed for screen preview
        function updatePageNumbers() {
            const pageNumberElement = document.querySelector('.page-number');
            if (pageNumberElement) {
                const totalPages = pageNumberElement.getAttribute('data-total-pages') || '1';
                const pageNumberSpan = pageNumberElement.querySelector('span');
                if (pageNumberSpan) {
                    pageNumberSpan.textContent = '1 / ' + totalPages;
                }
            }
        }

        document.getElementById('cancel-button').addEventListener('click', function() {
            window.close();
        });

        // Make the print controls draggable
        (function() {
            const dragElement = document.getElementById('draggable-print-controls');
            if (!dragElement) return;

            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            const dragHandle = document.querySelector('.drag-handle');

            if (dragHandle) {
                // If drag handle exists, attach mousedown to it
                dragHandle.onmousedown = dragMouseDown;
            } else {
                // Otherwise, attach mousedown to the whole element
                dragElement.onmousedown = dragMouseDown;
            }

            function dragMouseDown(e) {
                e.preventDefault();
                // Get the mouse cursor position at startup
                pos3 = e.clientX;
                pos4 = e.clientY;
                document.onmouseup = closeDragElement;
                // Call a function whenever the cursor moves
                document.onmousemove = elementDrag;
            }

            function elementDrag(e) {
                e.preventDefault();
                // Calculate the new cursor position
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;
                // Set the element's new position
                dragElement.style.top = (dragElement.offsetTop - pos2) + 'px';
                dragElement.style.left = (dragElement.offsetLeft - pos1) + 'px';
                dragElement.style.right = 'auto';
                dragElement.style.bottom = 'auto';
            }

            function closeDragElement() {
                // Stop moving when mouse button is released
                document.onmouseup = null;
                document.onmousemove = null;
            }
        })();
    </script>
</body>
</html>
    `;

    // Ouvrir une nouvelle fenêtre pour l'édition et l'impression
    this.editablePrintWindow = window.open('', '_blank', 'width=800,height=600');
    if (this.editablePrintWindow) {
      this.editablePrintWindow.document.write(printHtml);
      this.editablePrintWindow.document.close();
      this.isEditingPdf = true;
    } else {
      console.error('Impossible d\'ouvrir la fenêtre d\'impression. Veuillez autoriser les pop-ups.');
    }
  }