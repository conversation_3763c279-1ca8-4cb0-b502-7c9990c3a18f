import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { Demande } from './deviss.model';
import { DemandeService } from './demande.service';
import { PaiementService } from '../paiement/paiement.service';
import { Subscription, interval, forkJoin } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';

import { finalize, map, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faEye, faMoneyBill, faFilter, faSearch, faCalendarAlt, faEraser } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-devis',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, FormsModule, NgxPaginationModule],
  templateUrl: './devis.component.html',
  styleUrls: ['./devis.component.css'],
})
export class DevisComponent implements OnInit, OnDestroy {
  // Data properties
  isLoading: boolean = true;
  hasError: boolean = false;
  errorMessage: string | null = null;
  demandes: Demande[] = [];
  filteredDemandes: Demande[] = [];
  hoveredDemande: string | null = null;

  // Filtering properties
  searchTerm: string = '';
  selectedDate: string = '';
  selectedStatus: string = '';

  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 5;

  // Font Awesome icons
  faEye = faEye;
  faMoneyBill = faMoneyBill;
  faFilter = faFilter;
  faSearch = faSearch;
  faCalendarAlt = faCalendarAlt;
  faEraser = faEraser;

  // For auto-refresh
  private refreshSubscription: Subscription | null = null;
  private refreshInterval = 30000; // 30 seconds

  constructor(
    private devisService: DemandeService,
    private router: Router,
    private paiementService: PaiementService
  ) {}

  ngOnInit() {
    this.fetchDevis();
    this.startAutoRefresh();
  }

  ngOnDestroy() {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  startAutoRefresh() {
    // Auto-refresh the devis list every 30 seconds
    this.refreshSubscription = interval(this.refreshInterval).subscribe(() => {
      this.fetchDevis();
    });
  }
  fetchDevis() {
    console.log('Fetching devis data...');
    this.isLoading = true;
    this.hasError = false;
    this.errorMessage = null;

    this.devisService.getUserDevis().pipe(
      finalize(() => {
        console.log('Request completed.');
      })
    ).subscribe({
      next: (data) => {
        if (Array.isArray(data)) {
          this.demandes = data;
          this.filteredDemandes = [...this.demandes]; // Initialize filtered demandes

          // For each demande with status 'valid', check for payments
          // This ensures we have the latest payment status information
          const validDemandes = this.demandes.filter(
            demande => demande.status === 'valid'
          );

          if (validDemandes.length > 0) {
            this.checkForPayments(validDemandes);
          }
        } else {
          this.demandes = [];
          this.filteredDemandes = [];
          console.error('Received data is not an array:', data);
        }

        this.isLoading = false;
      },
      error: (error: HttpErrorResponse) => {
        console.error('Error fetching devis:', error);
        this.errorMessage = 'Impossible de charger les devis. Veuillez réessayer plus tard.';
        this.hasError = true;
        this.isLoading = false;
      }
    });
  }

  /**
   * Check for payments for demandes that don't have payment_id
   * This helps ensure the UI is up-to-date with the latest payment status
   */
  checkForPayments(demandes: Demande[]) {
    // Create an array of observables for each payment check
    const paymentChecks = demandes.map(demande => {
      return this.paiementService.getPaymentByDemandeId(demande.demande_id).pipe(
        map(response => {
          // If there are payments, update the demande object
          if (response?.data?.length > 0) {
            // Find the demande in our array and update it
            const index = this.demandes.findIndex(d => d.demande_id === demande.demande_id);
            if (index !== -1) {
              this.demandes[index].payment_id = response.data[0].id;
              // Store the full payment data in the demande object
              this.demandes[index].payments = response.data;
            }
            return { demandeId: demande.demande_id, hasPayment: true, status: response.data[0].status };
          }
          return { demandeId: demande.demande_id, hasPayment: false };
        }),
        catchError(error => {
          console.error(`Error checking payment for demande ${demande.demande_id}:`, error);
          return [{ demandeId: demande.demande_id, hasPayment: false, error: true }];
        })
      );
    });

    // Execute all payment checks in parallel
    if (paymentChecks.length > 0) {
      forkJoin(paymentChecks).subscribe({
        next: results => {
          console.log('Payment check results:', results);
        },
        error: error => {
          console.error('Error checking payments:', error);
        }
      });
    }
  }
  /**
   * Calculate the total price for a demande based on its devis items
   */
  getTotalPrice(demande: any): number {
    if (demande.devis && Array.isArray(demande.devis)) {
      return demande.devis.reduce((sum: number, item: any) => {
        const price = parseFloat(item.prix_total || '0');
        return isNaN(price) ? sum : sum + price;
      }, 0);
    }
    return 0;
  }

  /**
   * Navigate to the demande details page
   */
  navigateToDetails(demandeId: string) {
    this.router.navigate(['/demandeClient', demandeId]);
  }

  /**
   * Navigate to the payment page
   */
  navigateToPayment(demandeId: string) {
    this.router.navigate(['/paiement', demandeId]);
  }

  /**
   * Check if a demande has a payment
   */
  hasPayment(demande: Demande): boolean {
    return demande.payment_id !== null && demande.payment_id !== undefined;
  }

  /**
   * Get the payment status for a demande
   */
  getPaymentStatus(demande: Demande): string {
    // If the demande has payments array with payment status
    if (demande.payments && demande.payments.length > 0) {
      return demande.payments[0].status;
    }

    // Default to 'pending' if we can't determine the status
    return 'pending';
  }

  // Filtering methods
  onFilterChange(): void {
    this.currentPage = 1; // Reset to first page when filter changes
    const term = this.searchTerm.toLowerCase();
    const status = this.selectedStatus;
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredDemandes = this.demandes.filter((demande) => {
      const matchesSearch = demande.demande_id.toLowerCase().includes(term);
      const matchesStatus = status ? this.getPaymentStatus(demande) === status : true;
      const demandeDate = new Date(demande.demande_date);
      const matchesDate = filterDate ? demandeDate.toDateString() === filterDate.toDateString() : true;

      return matchesSearch && matchesStatus && matchesDate;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.selectedStatus = '';
    this.filteredDemandes = [...this.demandes];
    this.currentPage = 1;
  }
}
