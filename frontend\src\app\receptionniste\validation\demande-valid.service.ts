import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Demande } from '../demande-details/demande.model';


@Injectable({
  providedIn: 'root'
})
export class DemandeValidService {
  private baseApiUrl = 'http://127.0.0.1:8000/api';
  private demandesApiUrl = this.baseApiUrl + '/demandess/valid'; // e.g., http://127.0.0.1:8000/api/demandes
  private userDetailsApiUrl = this.baseApiUrl + '/userdetails'; // Base URL for user details
  private paymentsApiUrl = this.baseApiUrl + '/payments'; // Base URL for payments

  constructor(private http: HttpClient) {}

  getValidDemandes(): Observable<Demande[]> {
    return this.http.get<{ data: Demande[] }>(this.demandesApiUrl).pipe(
      map(response => {
        if (response.data) {
          return response.data;
        } else {
          throw new Error('Invalid API response format.');
        }
      }),
      catchError(error => {
        console.error('Error fetching demandes:', error);
        return throwError(() => new Error('Failed to load demandes. Please try again.'));
      })
    );
  }
  getUserDetails(userId: number): Observable<any> {
    return this.http.get<{ success: boolean; data: any }>(`${this.userDetailsApiUrl}/${userId}`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data; // Extracts the actual user data
        } else {
          throw new Error('Invalid API response format for user details.');
        }
      }),
      catchError(error => {
        console.error('Error fetching user details:', error);
        return throwError(() => new Error('Failed to load user details.'));
      })
    );
  }
  // Updated getUserDetails method to extract the nested data

  /**
   * Get payment information for a specific demande ID
   * @param demandeId The demande ID to get payment information for
   * @returns Observable of the payment information
   */
  getPaymentsByDemandeId(demandeId: string): Observable<any> {
    if (!demandeId) {
      return throwError(() => new Error('Demande ID is required'));
    }

    return this.http.get<any>(`${this.paymentsApiUrl}/demande/${demandeId}`).pipe(
      map(response => {
        return response;
      }),
      catchError(error => {
        console.error(`Error fetching payments for demande ${demandeId}:`, error);
        return throwError(() => new Error('Failed to load payment information'));
      })
    );
  }
}
