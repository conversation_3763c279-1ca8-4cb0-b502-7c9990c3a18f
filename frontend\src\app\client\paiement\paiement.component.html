<!-- Composant Paiement : paiement.component.html -->
<div class="payment-container demandes-container">
  <h2 class="payment-title">Justification de Paiement</h2>

  <!-- Loading spinner for initial loading (only show when success modal is not visible) -->
  <div *ngIf="isLoading && !showSuccessModal" class="loading-spinner spinner-overlay">
    <div class="spinner spinner-icon"></div>
    <p class="spinner-text">Chargement des informations...</p>
  </div>

  <!-- Loading spinner for form submission -->
  <div *ngIf="isSubmitting" class="loading-spinner spinner-overlay">
    <div class="spinner spinner-icon"></div>
    <p class="spinner-text">Envoi du justificatif en cours...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message error-alert error">
    <p class="error-text">{{ errorMessage }}</p>
  </div>

  <!-- Success Modal -->
  <div *ngIf="showSuccessModal" class="success-modal-overlay" (click)="closeSuccessModal()">
    <div class="success-modal" (click)="$event.stopPropagation()">
      <div class="success-modal-content">
        <div class="success-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>
        <h3 class="success-title">Succès!</h3>
        <p class="success-message">{{ modalMessage }}</p>
        <button class="success-close-btn" (click)="closeSuccessModal()">
          Fermer
        </button>
      </div>
    </div>
  </div>



  <!-- Main content (show even when success modal is visible) -->
  <div *ngIf="(!isLoading || showSuccessModal) && !errorMessage && demande" class="payment-content main-payment-content" style="padding-top: 25px;">

    <!-- Back button -->
    <button class="btn-back back-navigation-btn" (click)="goBack()">
      <fa-icon [icon]="faArrowLeft" class="icon-arrow-left"></fa-icon>
      Retour à la liste des devis
    </button>

    <!-- Demande information -->
    <div class="demande-info demande-info-section">
      <h3 class="demande-info-title">Informations de la demande</h3>
      <div class="info-grid info-grid-layout">
        <div class="info-item info-item-demand-id">
          <span class="label info-label">Numéro de demande :</span>
          <span class="value info-value">{{ demande.demande_id }}</span>
        </div>
        <div class="info-item info-item-date">
          <span class="label info-label">Date de soumission :</span>
          <span class="value info-value">{{ demande.demande_date }}</span>
        </div>
        <div class="info-item info-item-mode">
          <span class="label info-label">Mode de règlement :</span>
          <span class="value info-value">{{ demande.mode_reglement }}</span>
        </div>
      </div>
    </div>

    <!-- Payment instructions and form - Only show if no payment exists -->
    <div *ngIf="!hasPayment()" class="instructions-and-form">

      <!-- Instructions de paiement -->
      <div class="payment-instructions instructions-section">
        <h3 class="instructions-title">Instructions de paiement</h3>
        <div class="instruction-box instructions-box-content">
          <p class="important important-message">
            Montant à virer sur le CCP n°17001000000273374803
          </p>
          <p class="payment-advice">
            Veuillez effectuer votre paiement et télécharger un justificatif ci-dessous.
          </p>
          <p class="formats-accepted">
            Les formats acceptés sont : JPG, PNG, PDF (taille maximale : 25 MB)
          </p>
        </div>
      </div>

      <!-- Payment form -->
      <form
        [formGroup]="paymentForm"
        (ngSubmit)="onSubmit()"
        class="payment-form payment-form-wrapper"
      >
        <!-- Montant -->
        <div class="form-group form-group-amount">
          <label for="amount" class="amount-label">Montant à payer : <div class="amount-text">{{ paymentForm.get('amount')?.value }} DT</div></label>
          <div class="amount-display amount-display-wrapper">

          </div>
        </div>

        <!-- Justificatif de paiement -->
        <div class="form-group form-group-proof">
          <label for="payment_proof" class="proof-label">Justificatif de paiement *</label>
          <div class="file-upload-container file-upload-wrapper" style="text-align: center; margin: 0 auto;">
            <input
              type="file"
              id="payment_proof"
              (change)="onFileSelected($event)"
              accept=".jpg,.jpeg,.png"
              class="file-input file-input-field"
            >
            <label for="payment_proof" class="file-upload-label select-file-label">
              <fa-icon [icon]="faUpload" class="icon-upload"></fa-icon>
              <span>Choisir un fichier</span>
            </label>
            <span *ngIf="selectedFile" class="file-name file-selected-name">
              {{ selectedFile.name }}
            </span>
          </div>
        </div>

        <!-- File preview -->
        <div *ngIf="filePreview" class="file-preview file-preview-container" style="margin-bottom: 30px;">
          <h4 class="preview-title">Aperçu du fichier</h4>

          <!-- Image preview -->
          <img
            *ngIf="selectedFile && selectedFile.type && selectedFile.type.includes('image')"
            [src]="filePreview"
            alt="Aperçu du justificatif"
            class="image-preview"
          >

          <!-- PDF preview -->
          <div
            *ngIf="selectedFile && selectedFile.type && selectedFile.type.includes('pdf')"
            class="pdf-preview pdf-preview-container"
          >
            <p class="pdf-file-label">Fichier PDF sélectionné</p>
            <p class="pdf-file-name">{{ selectedFile.name }}</p>
          </div>
        </div>



        <!-- Submit button -->
        <div class="form-actions submit-actions">
          <button
            type="submit"
            class="btn-submit submit-btn-proof"
            [disabled]="!selectedFile || isSubmitting"
          >
            <fa-icon [icon]="faPaperPlane" class="icon-save"></fa-icon>
            {{ isSubmitting ? 'Envoi en cours...' : 'Envoyer' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Payment History Component -->
    <div
      *ngIf="hasPayment() || hasPaymentHistory"
      class="payment-history-section payment-history-container"
    >
      <app-payment-history
        [demandeId]="demande.demande_id"
        (paymentDeleted)="onPaymentDeleted()"
      ></app-payment-history>
    </div>
  </div>
</div>
